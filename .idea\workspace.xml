<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="ChangeListManager">
    <list default="true" id="b02f27d6-cd0b-4cb3-89ff-da56e13c0835" name="Default Changelist" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Python Script" />
      </list>
    </option>
  </component>
  <component name="ProjectId" id="2qsLyh8Ptnxqw70wXOBBJOj1c5R" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showExcludedFiles" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">
    <property name="RunOnceActivity.ShowReadmeOnStart" value="true" />
    <property name="last_opened_file_path" value="$PROJECT_DIR$/kcs_ex_struct_test.py" />
    <property name="settings.editor.selected.configurable" value="preferences.pluginManager" />
  </component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="C:\Tribon\M3\Vitesse\Trigger" />
      <recent name="C:\Tribon\M3\Vitesse" />
    </key>
  </component>
  <component name="RunManager" selected="Python.test">
    <configuration name="kcs_ex_struct_test" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="Vitesse" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/kcs_ex_struct_test.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="test" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="Vitesse" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="C:\Python23\python.exe" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$" />
      <option name="IS_MODULE_SDK" value="false" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/test.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="trig_draft_init" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="Vitesse" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/Trigger" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/Trigger/trig_draft_init.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="wingdbstub" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="Vitesse" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="C:\Python23\python.exe" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$" />
      <option name="IS_MODULE_SDK" value="false" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/wingdbstub.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <list>
      <item itemvalue="Python.kcs_ex_struct_test" />
      <item itemvalue="Python.test" />
      <item itemvalue="Python.trig_draft_init" />
      <item itemvalue="Python.wingdbstub" />
    </list>
    <recent_temporary>
      <list>
        <item itemvalue="Python.test" />
        <item itemvalue="Python.wingdbstub" />
        <item itemvalue="Python.kcs_ex_struct_test" />
        <item itemvalue="Python.trig_draft_init" />
      </list>
    </recent_temporary>
  </component>
  <component name="SvnConfiguration">
    <configuration />
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="b02f27d6-cd0b-4cb3-89ff-da56e13c0835" name="Default Changelist" comment="" />
      <created>1735446591508</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1735446591508</updated>
    </task>
    <servers />
  </component>
  <component name="WindowStateProjectService">
    <state x="530" y="113" key="FileChooserDialogImpl" timestamp="1744639818237">
      <screen x="0" y="0" width="1609" height="840" />
    </state>
    <state x="530" y="113" key="FileChooserDialogImpl/0.0.1609.840@0.0.1609.840" timestamp="1744639818237" />
    <state x="215" y="0" key="SettingsEditor" timestamp="1748053957302">
      <screen x="0" y="0" width="1918" height="840" />
    </state>
    <state x="181" y="0" key="SettingsEditor/0.0.1609.840@0.0.1609.840" timestamp="1744639930159" />
    <state x="182" y="0" key="SettingsEditor/0.0.1622.840@0.0.1622.840" timestamp="1748053557079" />
    <state x="215" y="0" key="SettingsEditor/0.0.1918.840@0.0.1918.840" timestamp="1748053957302" />
    <state x="1109" y="224" width="811" height="674" key="find.popup" timestamp="1747991092694">
      <screen x="0" y="0" width="1920" height="1032" />
    </state>
    <state x="1109" y="224" width="811" height="674" key="find.popup/0.0.1920.1032@0.0.1920.1032" timestamp="1747991092694" />
    <state x="389" y="0" width="843" height="838" key="search.everywhere.popup" timestamp="1748052282837">
      <screen x="0" y="0" width="1622" height="840" />
    </state>
    <state x="388" y="-1" width="840" height="1034" key="search.everywhere.popup/0.0.1618.836@0.0.1618.836" timestamp="1745934501902" />
    <state x="389" y="0" width="843" height="838" key="search.everywhere.popup/0.0.1622.840@0.0.1622.840" timestamp="1748052282837" />
    <state x="460" y="0" width="998" height="1030" key="search.everywhere.popup/0.0.1920.1032@0.0.1920.1032" timestamp="1747989440206" />
    <state x="675" y="409" key="vcs.readOnlyHandler.ReadOnlyStatusDialog" timestamp="1747987932555">
      <screen x="0" y="0" width="1920" height="1032" />
    </state>
    <state x="675" y="409" key="vcs.readOnlyHandler.ReadOnlyStatusDialog/0.0.1920.1032@0.0.1920.1032" timestamp="1747987932555" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/test.py</url>
          <line>28</line>
          <option name="timeStamp" value="2" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/Lib/KcsPoint2D.py</url>
          <line>14</line>
          <option name="timeStamp" value="3" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/kcs_ex_struct_test.py</url>
          <line>38</line>
          <option name="timeStamp" value="4" />
        </line-breakpoint>
      </breakpoints>
    </breakpoint-manager>
  </component>
</project>