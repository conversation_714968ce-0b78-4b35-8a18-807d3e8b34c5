﻿<table><tr><th colspan="5"><a name="chmtopic1"></a>Tribon M3 </th></tr>
<tr><td colspan="5"></td></tr>
<tr><td></td><td></td><td> </td><td> </td><td>![](qnhzfu02.001.png)[](#chmtopic1)</td></tr>
</table>

<a name="chmbookmark1"></a>*User's Guide* 

<a name="chmbookmark2"></a>*Abbreviations* 


<table><tr><th valign="bottom"><a name="chmbookmark3"></a><a name="chmbookmark4"></a><a name="chmbookmark5"></a><a name="chmbookmark6"></a><a name="chmbookmark7"></a><a name="chmbookmark8"></a>![Next Page][ ](#chmtopic2)</th><th></th><th></th><th> </th><th> </th></tr>
<tr><td colspan="5"></td></tr>
<tr><td colspan="4">Copyright © 1993-2005 AVEVA AB </td><td>![HOME - Generated from AbbreviationsFP.fm ][](#chmtopic1)</td></tr>
</table>

<table><tr><th colspan="5" valign="bottom"><a name="chmtopic2"></a>User's Guide Abbreviations </th><th valign="bottom">![Version]</th></tr>
<tr><td colspan="6"></td></tr>
<tr><td valign="top">![Previous Page][ ](#chmtopic1)</td><td valign="top">![Next Page][ ](#chmtopic3)</td><td colspan="4" valign="top"></b> </td></tr>
</table>

<a name="chmbookmark9"></a>**1 Abbreviations** 

<table><tr><th valign="bottom">![Previous Page][ ](#chmtopic1)</th><th valign="bottom">![Next Page][ ](#chmtopic3)</th><th> </th><th> </th><th></th></tr>
<tr><td colspan="5"></td></tr>
<tr><td colspan="4">Copyright © 1993-2005 AVEVA AB </td><td>![HOME - Generated from Abbreviations.fm ][HOME - Generated from AbbreviationsFP.fm ][](#chmtopic1)</td></tr>
</table>

<table><tr><th colspan="5" valign="bottom"><a name="chmtopic3"></a>User's Guide Abbreviations </th><th valign="bottom">![Version]</th></tr>
<tr><td colspan="6"></td></tr>
<tr><td valign="top">![Previous Page][ ](#chmtopic2)</td><td valign="top"></td><td colspan="4" valign="top"><b>Chapter: [Abbreviations](#chmtopic2)</b> </td></tr>
</table>

<a name="chmbookmark10"></a>**1.1 Abbreviations in Tribon** 

<a name="chmbookmark11"></a>The table below contains a list of abbreviations that have been created and used in the documentation of Tribon. The abbreviations are specific for Tribon and probably not used anywhere else. 

<a name="chmbookmark12"></a>The table does not contain common abbreviations, used in the shipbuilding or information technology knowledge domains. Further, it does not contain abbreviations, used as variables, as databank names, as interactive function names, as commands in various input files or in programming interfaces in Tribon. Such abbreviations are described in detail in the ordinary Tribon user documentation. 

|<a name="chmbookmark13"></a>AP|<a name="chmbookmark14"></a>Assembly Planning |
| :- | :- |
|<a name="chmbookmark15"></a>APL |<a name="chmbookmark16"></a>Assembly Parts List |
|<a name="chmbookmark17"></a>AWR |<a name="chmbookmark18"></a>Assembly Weld Report |
|<a name="chmbookmark19"></a>CAT |<a name="chmbookmark20"></a>Curve and Table |
|<a name="chmbookmark21"></a>CVBA |<a name="chmbookmark22"></a>Continuously Varying Bevel Angle |
|<a name="chmbookmark23"></a>DM |<a name="chmbookmark24"></a>Design Manager |
|<a name="chmbookmark25"></a>FD |<a name="chmbookmark26"></a>Functional Description |
|<a name="chmbookmark27"></a>GPP |<a name="chmbookmark28"></a>Generic Postprocessor |
|<a name="chmbookmark29"></a>GSD |<a name="chmbookmark30"></a>Gütesicherungsdreiecke (Quality assurance triangles) |
|<a name="chmbookmark31"></a>IP |<a name="chmbookmark32"></a>Information to the Program |
|<a name="chmbookmark33"></a>MIS |<a name="chmbookmark34"></a>Management Information System |
|<a name="chmbookmark35"></a>PDI |<a name="chmbookmark36"></a>Production Data Interface |
|<a name="chmbookmark37"></a>PIM |<a name="chmbookmark38"></a>Product Information Model |
|<a name="chmbookmark39"></a>PLCM |<a name="chmbookmark40"></a>Panel Line Control Module |
|<a name="chmbookmark41"></a>PM |<a name="chmbookmark42"></a>Production Manager |
|<a name="chmbookmark43"></a>PML |<a name="chmbookmark44"></a>Pipe Modelling Language |
|<a name="chmbookmark45"></a>PPI |<a name="chmbookmark46"></a>Production Program Interface |
|<a name="chmbookmark47"></a>RSO |<a name="chmbookmark48"></a>Reference Surface Object |
|<a name="chmbookmark49"></a>RWS |<a name="chmbookmark50"></a>Read, Write and Change Status |
|<a name="chmbookmark51"></a>TDM |<a name="chmbookmark52"></a>Tribon Data Management |
|<a name="chmbookmark53"></a>TDT |<a name="chmbookmark54"></a>Tribon Developer's Toolkit |
|<a name="chmbookmark55"></a>TID |<a name="chmbookmark56"></a>Tribon Initial Design |
|<a name="chmbookmark57"></a>TIL |<a name="chmbookmark58"></a>Tribon Interpretative Language |
|<a name="chmbookmark59"></a>TJL |<a name="chmbookmark60"></a>Tribon Job Launcher |
|<a name="chmbookmark61"></a>TRI |<a name="chmbookmark62"></a>Tribon Robot Interface |
|<a name="chmbookmark63"></a>WOP |<a name="chmbookmark64"></a>Work Preparation |
|<a name="chmbookmark65"></a>WP |<a name="chmbookmark66"></a>Weld Planning |


<table><tr><th valign="bottom"><a name="chmbookmark67"></a>![Previous Page][ ](#chmtopic2)</th><th valign="bottom"></th><th> </th><th> </th><th></th></tr>
<tr><td colspan="5"></td></tr>
<tr><td colspan="4">Copyright © 1993-2005 AVEVA AB </td><td>![HOME - Generated from Abbreviations.fm ][HOME - Generated from AbbreviationsFP.fm ][](#chmtopic1)</td></tr>
</table>

[Next Page]: qnhzfu02.002.png
[HOME - Generated from AbbreviationsFP.fm ]: qnhzfu02.003.png
[Version]: qnhzfu02.004.png
[Previous Page]: qnhzfu02.005.png
