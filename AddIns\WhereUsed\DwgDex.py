
# -*- coding: mbcs -*-
# Created by makepy.py version 0.4.8
# By python version 2.3.2 (#49, Nov 13 2003, 10:34:54) [MSC v.1200 32 bit (Intel)]
# From type library 'tbdexint.exe'
# On Tue Jul 05 10:52:39 2005
"""tbdexint 1.0 Type Library"""
makepy_version = '0.4.8'
python_version = 0x20302f0

import win32com.client.CLSIDToClass, pythoncom
import win32com.client.util
from pywintypes import IID
from win32com.client import Dispatch

# The following 3 lines may need tweaking for the particular server
# Candidates are pythoncom.Missing and pythoncom.Empty
defaultNamedOptArg=pythoncom.Empty
defaultNamedNotOptArg=pythoncom.Empty
defaultUnnamedArg=pythoncom.Empty

CLSID = IID('{FEE7B652-EF27-11D1-A709-0000F87A9053}')
MajorVersion = 1
MinorVersion = 0
LibraryFlags = 8
LCID = 0x0

from win32com.client import DispatchBaseClass
class ITBDex(DispatchBaseClass):
	"""ITBDex Interface"""
	CLSID = IID('{FEE7B65F-EF27-11D1-A709-0000F87A9053}')
	coclass_clsid = IID('{FEE7B660-EF27-11D1-A709-0000F87A9053}')

	def ConvertToImperial(self, value=defaultNamedNotOptArg, quantity=defaultNamedNotOptArg, nDecimals=defaultNamedNotOptArg, notation=defaultNamedNotOptArg, syntax=defaultNamedNotOptArg):
		"""method ConvertToImperial"""
		return self._ApplyTypes_(4, 1, (12, 0), ((12, 1), (2, 1), (2, 1), (2, 1), (2, 1)), 'ConvertToImperial', None,value, quantity, nDecimals, notation, syntax)

	def DoDataExtraction(self, newVal=defaultNamedNotOptArg):
		"""method DoDataExtraction"""
		return self._oleobj_.InvokeTypes(1, LCID, 1, (24, 0), ((8, 1),),newVal)

	def GetResTree(self):
		"""method GetResTree"""
		return self._ApplyTypes_(3, 1, (12, 0), (), 'GetResTree', None,)

	def GetValue(self):
		"""method GetValue"""
		return self._ApplyTypes_(2, 1, (12, 0), (), 'GetValue', None,)

	_prop_map_get_ = {
	}
	_prop_map_put_ = {
	}

from win32com.client import CoClassBaseClass
# This CoClass is known by the name 'TBDex.TBDex.1'
class TBDex(CoClassBaseClass): # A CoClass
	# TBDex Class
	CLSID = IID('{FEE7B660-EF27-11D1-A709-0000F87A9053}')
	coclass_sources = [
	]
	coclass_interfaces = [
		ITBDex,
	]
	default_interface = ITBDex

ITBDex_vtables_dispatch_ = 1
ITBDex_vtables_ = [
	(('DoDataExtraction', 'newVal'), 1, (1, (), [(8, 1, None, None)], 1, 1, 4, 0, 28, (3, 0, None, None), 0)),
	(('GetValue', 'pVal'), 2, (2, (), [(16396, 10, None, None)], 1, 1, 4, 0, 32, (3, 0, None, None), 0)),
	(('GetResTree', 'pVar'), 3, (3, (), [(16396, 10, None, None)], 1, 1, 4, 0, 36, (3, 0, None, None), 0)),
	(('ConvertToImperial', 'value', 'quantity', 'nDecimals', 'notation', 'syntax', 'pVal'), 4, (4, (), [(12, 1, None, None), (2, 1, None, None), (2, 1, None, None), (2, 1, None, None), (2, 1, None, None), (16396, 10, None, None)], 1, 1, 4, 0, 40, (3, 0, None,

]

RecordMap = {
}

CLSIDToClassMap = {
	'{FEE7B65F-EF27-11D1-A709-0000F87A9053}' : ITBDex,
	'{FEE7B660-EF27-11D1-A709-0000F87A9053}' : TBDex,
}
CLSIDToPackageMap = {}
win32com.client.CLSIDToClass.RegisterCLSIDsFromDict( CLSIDToClassMap )
VTablesToPackageMap = {}
VTablesToClassMap = {
	'{FEE7B65F-EF27-11D1-A709-0000F87A9053}' : 'ITBDex',
}


NamesToIIDMap = {
	'ITBDex' : '{FEE7B65F-EF27-11D1-A709-0000F87A9053}',
}

