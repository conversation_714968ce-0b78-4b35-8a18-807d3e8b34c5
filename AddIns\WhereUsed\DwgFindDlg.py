#Boa:Dialog:wxDialog1

from wxPython.wx import *
import kcs_dex
import kcs_util
import kcs_draft
import string
import kcs_ui
import math
import KcsModel
import KcsPoint2D
import kcs_model
from win32com.client import gencache
from win32com.client import Dispatch


def create(parent):
    return wxDialog1(parent)

[wxID_WXDIALOG1, wxID_WXDIALOG1CLOSEBTN, wxID_WXDIALOG1COMPONENTNAME,
 wxID_WXDIALOG1DATABANK, wxID_WXDIALOG1DRAWINGNAMEEDIT,
 wxID_WXDIALOG1GETCOMPONENT, wxID_WXDIALOG1GETMODEL, wxID_WXDIALOG1MODELNAME,
 wxID_WXDIALOG1MODELNAMEEDIT, wxID_WXDIALOG1MODELTYPECOMBO,
 wxID_WXDIALOG1MODELTYPECOMBO1, wxID_WXDIALOG1OPENBTN, wxID_WXDIALOG1PROGRESS,
 wxID_WXDIALOG1RESULTS, wxID_WXDIALOG1RESULTSCTRL1, wxID_WXDIALOG1SEARCHBTN,
 wxID_WXDIALOG1SEARCHCOMP, wxID_WXDIALOG1STATICBOX1, wxID_WXDIALOG1STATICBOX2,
 wxID_WXDIALOG1STATICLINE1, wxID_WXDIALOG1STATICLINE2,
 wxID_WXDIALOG1STATICTEXT1, wxID_WXDIALOG1STATICTEXT10,
 wxID_WXDIALOG1STATICTEXT2, wxID_WXDIALOG1STATICTEXT3,
 wxID_WXDIALOG1STATICTEXT4, wxID_WXDIALOG1STATICTEXT5,
 wxID_WXDIALOG1STATICTEXT6, wxID_WXDIALOG1STATICTEXT7,
 wxID_WXDIALOG1STATICTEXT8, wxID_WXDIALOG1STATICTEXT9, wxID_WXDIALOG1ZOOMBTN,
] = map(lambda _init_ctrls: wxNewId(), range(32))

class wxDialog1(wxDialog):
    def _init_coll_ResultsCtrl1_Columns(self, parent):
        # generated method, don't edit

        parent.InsertColumn(col=0, format=wxLIST_FORMAT_LEFT, heading='Name',
              width=114)
        parent.InsertColumn(col=1, format=wxLIST_FORMAT_LEFT, heading='Type',
              width=114)

    def _init_ctrls(self, prnt):
        # generated method, don't edit
        wxDialog.__init__(self, id=wxID_WXDIALOG1, name='', parent=prnt,
              pos=wxPoint(502, 257), size=wxSize(505, 378),
              style=wxDEFAULT_DIALOG_STYLE, title='Where Used')
        self.SetClientSize(wxSize(497, 346))
        EVT_CLOSE(self, self.OnWxdialog1Close)

        self.staticBox2 = wxStaticBox(id=wxID_WXDIALOG1STATICBOX2,
              label='Component', name='staticBox2', parent=self, pos=wxPoint(8,
              0), size=wxSize(232, 144), style=0)

        self.ComponentName = wxTextCtrl(id=wxID_WXDIALOG1COMPONENTNAME,
              name='ComponentName', parent=self, pos=wxPoint(88, 24),
              size=wxSize(116, 21), style=0, value='')
        EVT_SET_FOCUS(self.ComponentName, self.OnComponentNameSetFocus)

        self.GetComponent = wxButton(id=wxID_WXDIALOG1GETCOMPONENT, label='...',
              name='GetComponent', parent=self, pos=wxPoint(208, 24),
              size=wxSize(24, 24), style=0)
        EVT_BUTTON(self.GetComponent, wxID_WXDIALOG1GETCOMPONENT,
              self.OnGetComponentButton)

        self.ModelTypeCombo = wxChoice(choices=['All models', 'pipe',
              'ventilation', 'cable way', 'equipment', "struct", "pipe spool"],
              id=wxID_WXDIALOG1MODELTYPECOMBO, name='ModelTypeCombo',
              parent=self, pos=wxPoint(88, 56), size=wxSize(144, 24), style=0)
        self.ModelTypeCombo.SetSelection(0)

        self.ModelNameEdit = wxTextCtrl(id=wxID_WXDIALOG1MODELNAMEEDIT,
              name='ModelNameEdit', parent=self, pos=wxPoint(88, 88),
              size=wxSize(144, 21), style=0, value='*')

        self.SearchComp = wxButton(id=wxID_WXDIALOG1SEARCHCOMP, label='Search',
              name='SearchComp', parent=self, pos=wxPoint(160, 112),
              size=wxSize(75, 23), style=0)
        EVT_BUTTON(self.SearchComp, wxID_WXDIALOG1SEARCHCOMP,
              self.OnSearchcompButton)

        self.ResultsCtrl1 = wxListCtrl(id=wxID_WXDIALOG1RESULTSCTRL1,
              name='ResultsCtrl1', parent=self, pos=wxPoint(8, 168),
              size=wxSize(232, 136), style=wxLC_REPORT)
        self._init_coll_ResultsCtrl1_Columns(self.ResultsCtrl1)
        EVT_LIST_ITEM_SELECTED(self.ResultsCtrl1, wxID_WXDIALOG1RESULTSCTRL1,
              self.OnResultsCtrl1ListItemSelected)

        self.Progress = wxGauge(id=wxID_WXDIALOG1PROGRESS, name='Progress',
              parent=self, pos=wxPoint(80, 320), range=100, size=wxSize(160,
              16), style=wxGA_HORIZONTAL, validator=wxDefaultValidator)

        self.staticBox1 = wxStaticBox(id=wxID_WXDIALOG1STATICBOX1,
              label='Model', name='staticBox1', parent=self, pos=wxPoint(256,
              0), size=wxSize(232, 144), style=0)

        self.ModelName = wxTextCtrl(id=wxID_WXDIALOG1MODELNAME,
              name='ModelName', parent=self, pos=wxPoint(344, 24),
              size=wxSize(108, 21), style=0, value='')
        EVT_SET_FOCUS(self.ModelName, self.OnModelNameSetFocus)

        self.GetModel = wxButton(id=wxID_WXDIALOG1GETMODEL, label='...',
              name='GetModel', parent=self, pos=wxPoint(455, 24),
              size=wxSize(24, 24), style=0)
        EVT_BUTTON(self.GetModel, wxID_WXDIALOG1GETMODEL, self.OnGetModelButton)

        self.ModelTypeCombo1 = wxChoice(choices=['All models', 'pipe',
              'cable way', 'equipment', 'ventilation', 'curved panel',
              'hull curve', 'plane panel', 'spool', 'volume', 'struct', 'cable',
              'longitudinal', 'transversal'], id=wxID_WXDIALOG1MODELTYPECOMBO1,
              name='ModelTypeCombo1', parent=self, pos=wxPoint(344, 52),
              size=wxSize(136, 24), style=0)
        self.ModelTypeCombo1.SetSelection(0)

        self.DrawingNameEdit = wxTextCtrl(id=wxID_WXDIALOG1DRAWINGNAMEEDIT,
              name='DrawingNameEdit', parent=self, pos=wxPoint(344, 83),
              size=wxSize(136, 21), style=0, value='*')

        self.DataBank = wxComboBox(choices=["SB_ASSDB", "SB_ASSINSTR",
              "SB_ASSPDB", "SB_CGDB", "SB_DOC", "SB_MORVOLDB", "SB_NPL",
              "SB_NSTD", "SB_OGDB", "SB_OUTFSTDDB", "SB_PDB", "SB_PLDB",
              "SB_PPDB", "SB_PSDB", "SB_REFDB", "SB_SETTINGS_DB", "SB_SPECDB",
              "SB_TID", "SB_VDIAGDB", "SB_VSDB", "SB_WELDDB", "SBC_CABSTRDB",
              "SBC_CAWSTRDB", "SBD_BACKUP", "SBD_PICT", "SBD_STD",
              "SBD_VOL_PLAC", "SBD_VOLUME", "SBE_GENCMPDB", "SBE_GENCMPDB_EXT",
              "SBE_GENEQPDB", "SBE_GENPROJDB", "SBE_GENVOLDB", "SBF_DB_FSTD",
              "SBF_DB_FSTRU", "SBH_BACKUP", "SBH_BENDTEMPL_DWG",
              "SBH_CPART_DWG", "SBH_MARK_PICT", "SBH_NEST_PROFDB",
              "SBH_NSKETCH_DWG", "SBH_PAINT_AREA_DB", "SBH_PARTLIST_DWG",
              "SBH_PINJIG_DWG", "SBH_PLJIG_DWG", "SBH_PPART_DWG", "SBH_PROFDB",
              "SBH_PSKETCH_DWG", "SBH_RECEIPT", "SBH_WCOG_DWG",
              "SBP_SKETCH_DB"], id=wxID_WXDIALOG1DATABANK, name='DataBank',
              parent=self, pos=wxPoint(344, 112), size=wxSize(136, 24), style=0,
              value='SB_PDB')
        self.DataBank.SetLabel('SB_PDB')

        self.SearchBtn = wxButton(id=wxID_WXDIALOG1SEARCHBTN, label='Search',
              name='SearchBtn', parent=self, pos=wxPoint(416, 150),
              size=wxSize(72, 23), style=0)
        self.SearchBtn.Show(True)
        self.SearchBtn.SetThemeEnabled(False)
        EVT_BUTTON(self.SearchBtn, wxID_WXDIALOG1SEARCHBTN,
              self.OnSearchbtnButton)

        self.Results = wxListBox(choices=[], id=wxID_WXDIALOG1RESULTS,
              name='Results', parent=self, pos=wxPoint(256, 176),
              size=wxSize(232, 96), style=0, validator=wxDefaultValidator)

        self.ZoomBtn = wxButton(id=wxID_WXDIALOG1ZOOMBTN, label='Zoom',
              name='ZoomBtn', parent=self, pos=wxPoint(328, 280),
              size=wxSize(75, 23), style=0)
        EVT_BUTTON(self.ZoomBtn, wxID_WXDIALOG1ZOOMBTN, self.OnZoombtnButton)

        self.OpenBtn = wxButton(id=wxID_WXDIALOG1OPENBTN, label='Open',
              name='OpenBtn', parent=self, pos=wxPoint(416, 280),
              size=wxSize(75, 23), style=0)
        EVT_BUTTON(self.OpenBtn, wxID_WXDIALOG1OPENBTN, self.OnOpenbtnButton)

        self.CloseBtn = wxButton(id=wxID_WXDIALOG1CLOSEBTN, label='Close',
              name='CloseBtn', parent=self, pos=wxPoint(416, 312),
              size=wxSize(75, 23), style=0)
        EVT_BUTTON(self.CloseBtn, wxID_WXDIALOG1CLOSEBTN, self.OnCloseBtnButton)

        self.staticText1 = wxStaticText(id=wxID_WXDIALOG1STATICTEXT1,
              label='Drawings:', name='staticText1', parent=self,
              pos=wxPoint(256, 152), size=wxSize(64, 16), style=0)

        self.staticText2 = wxStaticText(id=wxID_WXDIALOG1STATICTEXT2,
              label='Models:', name='staticText2', parent=self, pos=wxPoint(8,
              144), size=wxSize(37, 13), style=0)

        self.staticLine1 = wxStaticLine(id=wxID_WXDIALOG1STATICLINE1,
              name='staticLine1', parent=self, pos=wxPoint(248, 520),
              size=wxSize(2, 200), style=0)

        self.staticText3 = wxStaticText(id=wxID_WXDIALOG1STATICTEXT3,
              label='Model type:', name='staticText3', parent=self,
              pos=wxPoint(14, 56), size=wxSize(55, 13), style=0)

        self.staticText4 = wxStaticText(id=wxID_WXDIALOG1STATICTEXT4,
              label='Drawing name:', name='staticText4', parent=self,
              pos=wxPoint(264, 80), size=wxSize(71, 32), style=0)

        self.staticText5 = wxStaticText(id=wxID_WXDIALOG1STATICTEXT5,
              label='Progress:', name='staticText5', parent=self, pos=wxPoint(8,
              320), size=wxSize(64, 24), style=0)

        self.staticLine2 = wxStaticLine(id=wxID_WXDIALOG1STATICLINE2,
              name='staticLine2', parent=self, pos=wxPoint(248, 8),
              size=wxSize(2, 296), style=wxLI_VERTICAL)

        self.staticText6 = wxStaticText(id=wxID_WXDIALOG1STATICTEXT6,
              label='Model name:', name='staticText6', parent=self,
              pos=wxPoint(14, 88), size=wxSize(61, 32), style=0)

        self.staticText7 = wxStaticText(id=wxID_WXDIALOG1STATICTEXT7,
              label='Component:', name='staticText7', parent=self,
              pos=wxPoint(14, 24), size=wxSize(72, 24), style=0)

        self.staticText8 = wxStaticText(id=wxID_WXDIALOG1STATICTEXT8,
              label='Model name:', name='staticText8', parent=self,
              pos=wxPoint(264, 24), size=wxSize(61, 13), style=0)

        self.staticText9 = wxStaticText(id=wxID_WXDIALOG1STATICTEXT9,
              label='Model type:', name='staticText9', parent=self,
              pos=wxPoint(264, 56), size=wxSize(55, 13), style=0)

        self.staticText10 = wxStaticText(id=wxID_WXDIALOG1STATICTEXT10,
              label='Data bank', name='staticText10', parent=self,
              pos=wxPoint(264, 114), size=wxSize(62, 16), style=0)

    def __init__(self, parent):
        self._init_ctrls(parent)

    def DisplayModelSubview(self, handle):

        try:

            rect = kcs_draft.element_extent_get(handle)

            width = int(math.fabs(rect.Corner1.X-rect.Corner2.X))

            height = int(math.fabs(rect.Corner1.Y-rect.Corner2.Y))

            factor = max(width, height) * 1

            rect.Corner1.X = rect.Corner1.X-factor

            rect.Corner2.X = rect.Corner2.X+factor

            rect.Corner1.Y = rect.Corner1.Y-factor

            rect.Corner2.Y = rect.Corner2.Y+factor

            kcs_draft.dwg_zoom(rect)

            kcs_ui.app_window_refresh()



        except Exception, e:

            kcs_ui.message_confirm('DisplayModelComponent: ' + str(e))


    def OnOpenbtnButton(self, event):
        name = self.Results.GetStringSelection()
        model = self.ModelName.GetValue()
        self.Subviews = []
        self.Index = -1
        if len(name)>0:
            try:
                kcs_draft.dwg_close()
            except:
                pass
            try:
                kcs_draft.dwg_open(name)
                try:
                    view = kcs_draft.element_child_first_get()
                    while 1:
                        try:
                            subview = kcs_draft.element_child_first_get(view)
                            while 1:
                                subname = kcs_draft.subpicture_name_get(subview)
                                if string.find(subname, model)!=-1:
                                    kcs_draft.element_highlight(subview)
                                    self.Subviews.append(subview)
                                    self.Index = 0
                                subview = kcs_draft.element_sibling_next_get(subview)
                        except:
                            pass
                        view = kcs_draft.element_sibling_next_get(view)
                except:
                    pass
                kcs_ui.app_window_refresh()
            except:
                wxMessageBox('Error when opening drawing: ' + name)
        event.Skip()

    def OnSearchbtnButton(self, event):
        self.Results.Clear()
        self.ModelName.SetValue( string.upper( self.ModelName.GetValue() ) )
        self.DrawingNameEdit.SetValue( string.upper( self.DrawingNameEdit.GetValue() ) )
        self.DataBank.SetValue( string.upper( self.DataBank.GetValue() ) )
        name = self.ModelName.GetValue()
        results = self.SearchDrawing(name)
        for res in results:
            self.Results.Append(res)
        if len(results)==0:
            wxMessageBox('Drawings not found!')
        else:
            wxMessageBox("Search complete!")

        event.Skip()

    def GetDwgNameFilter(self, name):
        if len(name)==0:
            return "*'"
        if name[0]=='*':
            return "*'"+name[1:]+""
        if name[-1]=='*':
            return "'"+name[0:-1]+""
        return "'"+name+""

    def GetDwgNames(self):
        name = self.DrawingNameEdit.GetValue()
        drawings = []
        try:
            filter = self.GetDwgNameFilter(name)
            data_bank = self.DataBank.GetValue()
            if data_bank != "":
                data_bank = "@" + data_bank
            asterisk = ""
            if (len(name) > 1 and name[-1] == "*"):
                asterisk = "*"
            if kcs_dex.extract("drawing("+filter+data_bank+"'"+asterisk+").name") == kcs_util.success():
                valtype = 0
                while valtype != -1:
                    if valtype == 3:
                        drawings.append(kcs_dex.get_string())
                    valtype = kcs_dex.next_result()
        except:
            wxMessageBox('GetDwgNames - error')
            pass
        kcs_draft.DwgNames = drawings
        kcs_draft.DwgName = name
        return drawings

    def CompareModelNames(self, mod1, mod2):
        mod1 = string.replace(mod1, ':', '-')
        mod1 = string.replace(mod1, '#', '-')
        mod1 = string.replace(mod1, '+', '-')

        mod2 = string.replace(mod2, ':', '-')
        mod2 = string.replace(mod2, '#', '-')
        mod2 = string.replace(mod2, '+', '-')

        if string.find(mod1, mod2)!=-1:
            return True

    def SearchForModel(self, model, command):
        try:
            if kcs_dex.extract(command) == kcs_util.success():
                valtype = 0
                while valtype != -1:
                    if valtype == 3:
                        if self.CompareModelNames(kcs_dex.get_string(), model):
                            return True
                    valtype = kcs_dex.next_result()
        except:
            pass
        return False

    def SearchDrawing(self, name):
        results = []
        dwgs = self.GetDwgNames()
        self.Progress.SetRange(len(dwgs))
        index = 1
        data_bank = self.DataBank.GetValue()
        if data_bank != "":
            # drawing's data_bank name is required from service pack 4
            data_bank = "@" + data_bank
        for dwg in dwgs:
            filter = self.ModelTypeCombo1.GetStringSelection()
            command = "drawing('"+dwg+data_bank+"').view(*).model(1:9999).name"
            if filter=='pipe':
                command = "drawing('"+dwg+data_bank+"').view(*).pipe(1:9999).name"
            elif filter=='cable way':
                command = "drawing('"+dwg+""+data_bank+"').view(*).cway(1:9999).name"
            elif filter=='equipment':
                command = "drawing('"+dwg+data_bank+"').view(*).equipment(1:9999).name"
            elif filter=='curved panel':
                command = "drawing('"+dwg+data_bank+"').view(*).cpanel(1:9999).name"
            elif filter=='hull curve':
                command = "drawing('"+dwg+data_bank+"').view(*).hullcurve(1:9999).name"
            elif filter=='plane panel':
                command = "drawing('"+dwg+data_bank+"').view(*).panel(1:9999).name"
            elif filter=='spool':
                command = "drawing('"+dwg+data_bank+"').view(*).spool(1:9999).name"
            elif filter=='volume':
                command = "drawing('"+dwg+data_bank+"').view(*).volume(1:9999).name"
            elif filter=='struct':
                command = "drawing('"+dwg+data_bank+"').view(*).structure(1:9999).name"
            elif filter=='cable':
                command = "drawing('"+dwg+data_bank+"').view(*).cable(1:9999).name"
            elif filter=='longitudinal':
                command = "drawing('"+dwg+data_bank+"').view(*).longitudinal(1:9999).name"
            elif filter=='transversal':
                command = "drawing('"+dwg+data_bank+"').view(*).transversal(1:9999).name"
            if self.SearchForModel(name, command):
                results.append(dwg)
            self.Progress.SetValue(index)
            index = index+1
        self.Progress.SetValue(0)
        return results;

    def OnWxdialog1Close(self, event):
        try:
            kcs_draft.highlight_off(0)
        except:
            pass
        self.Destroy()
        event.Skip()

    def SearchForComp(self, component, command, dex):
        try:
            dex.DoDataExtraction(command)
            while 1:
                value = dex.GetValue()
                if type(value) == type(False):
                    return False
                if component[0]=='*':
                    if string.find(value, component[1:])==len(value)-len(component[1:]):
                        return True
                if component[-1]=='*':
                    if string.find(value, component[0:-1])==0:
                        return True
                if component == value:
                    return True
        except:
            pass
        return False

    def SearchForComp2(self, modelName, modelType, component):
        try:
            model = KcsModel.Model(modelType, str(modelName))
            if kcs_model.component_is_used( model, component ) == 1:
               return True
        except:
            wxMessageBox('exception')
            import sys
            wxMessageBox(str(sys.exc_info()[1]))
            pass

        return False

    def GetModels(self, command, dex):
        results = []
        try:
            dex.DoDataExtraction(command)
            while 1:
                value = dex.GetValue()
                if type(value) == type(False) and value==False:
                    break
                if value != None:
                    results.append(value)
        except:
            pass
        return results

    def InsertResult(self, model, type):
        item = self.ResultsCtrl1.GetItemCount()
        self.ResultsCtrl1.InsertStringItem(item, model)
        self.ResultsCtrl1.SetStringItem(item, 1, type)

    def GetNameFilter(self, name):
        if len(name)==0:
            return '*'
        if name[0]=='*':
            return "*'"+name[1:]+"'"
        if name[-1]=='*':
            return "'"+name[0:-1]+"'*"
        return "'"+name+"'"

    def OnSearchcompButton(self, event):
        self.ComponentName.SetValue( string.upper( self.ComponentName.GetValue() ) )
        self.ModelNameEdit.SetValue( string.upper( self.ModelNameEdit.GetValue() ) )
        comp = self.ComponentName.GetValue()
        filter = self.ModelTypeCombo.GetStringSelection()
        namefilter = self.GetNameFilter(self.ModelNameEdit.GetValue())
        self.ResultsCtrl1.DeleteAllItems()
        gencache.EnsureModule('{FEE7B652-EF27-11D1-A709-0000F87A9053}', 0, 1, 0)
        dex = Dispatch("TBDex.TBDex.1")
        projectName = kcs_util.TB_environment_get('SB_PROJ')

        bUseDex = 0

        if len(comp)>0:
            if filter=='All models' or filter=='pipe':
                pipes = self.GetModels("pipe('"+projectName+"').pipm("+namefilter+").name", dex)
                index = 1
                self.Progress.SetRange(len(pipes))
                for pipe in pipes:
                    command = "pipe('"+projectName+"').pipm('"+pipe+"').part(1:9999).comp_name"
                    pipeName = projectName + '-' + str(pipe)
                    if bUseDex:
                        bResult = self.SearchForComp(comp, command, dex)
                    else:
                        bResult = self.SearchForComp2( pipeName, 'pipe', comp )

                    if bResult:
                        self.InsertResult(pipe, 'Pipe')

                    self.Progress.SetValue(index)
                    index = index + 1
                self.Progress.SetValue(0)
            if filter=='All models' or filter=='ventilation':
                vents = self.GetModels("vent('"+projectName+"').ventm("+namefilter+").name", dex)
                index = 1
                self.Progress.SetRange(len(vents))
                for vent in vents:
                    command = "vent('"+projectName+"').ventm('"+vent+"').part(1:9999).comp_name"
                    ventName = projectName + '-' + str(vent)

                    if bUseDex:
                        bResult = self.SearchForComp(comp, command, dex)
                    else:
                        bResult = self.SearchForComp2( ventName, 'ventilation', comp )

                    if bResult:
                        self.InsertResult(vent, 'Ventilation')

                    self.Progress.SetValue(index)
                    index = index + 1
                self.Progress.SetValue(0)
            if filter=='All models' or filter=='cable way':
                cables = self.GetModels("cable('"+projectName+"').cway("+namefilter+").name", dex)
                index = 1
                self.Progress.SetRange(len(cables))
                for cable in cables:
                    command = "cable('"+projectName+"').cway('"+cable+"').part(1:9999).comp_name"
                    cableName = projectName + '-' + str(cable)

                    if bUseDex:
                        bResult = self.SearchForComp(comp, command, dex)
                    else:
                        bResult = self.SearchForComp2( cableName, 'cable way', comp )

                    if bResult:
                        self.InsertResult(cable, 'Cable way')

                    self.Progress.SetValue(index)
                    index = index + 1
                self.Progress.SetValue(0)
            if filter=='All models' or filter=='equipment':
                equips = self.GetModels("equip('"+projectName+"').item("+namefilter+").name", dex)
                index = 1
                self.Progress.SetRange(len(equips))
                for equip in equips:
                    command = "equip('"+projectName+"').item('"+equip+"').comp_name"
                    equipName = projectName + '-' + str( equip )

                    if bUseDex:
                        bResult = self.SearchForComp(comp, command, dex)
                    else:
                        bResult = self.SearchForComp2( equipName, 'equipment', comp )

                    if bResult:
                        self.InsertResult(equip, 'Equipment')
                    self.Progress.SetValue(index)
                    index = index + 1
                self.Progress.SetValue(0)
            if filter=='All models' or filter=='struct':
                componentName = string.upper( self.ComponentName.GetValue() )
                modules = self.GetModels("structure('"+projectName+"').module(*).name", dex)
                items = []
                for module in modules:
                    items = items + self.GetModels("structure('"+projectName+"').module('"+module+"').item(1:9999).name", dex)
                try:
                    if len(items) > 0:
                        index = 1
                        self.Progress.SetRange(len(items))

                        for item in items:
                            components = self.GetModels("structure('"+projectName+"').item('"+item+"').group(1:9999).part(1:9999).comp_name", dex)
                            for component in components:
                                if str(component) == str(componentName):
                                    add = 1
                                    count = self.ResultsCtrl1.GetItemCount()
                                    for row in range(count):
                                        pos = self.ResultsCtrl1.GetItem(row, 0).GetText()
                                        if pos == item:
                                            add = 0
                                    if add == 1:
                                        self.InsertResult(item, 'Struct')

                            self.Progress.SetValue(index)
                            index = index + 1

                        self.Progress.SetValue(0)
                except:
                    pass
            if filter=='All models' or filter=='pipe spool':
                pipes = self.GetModels("pipe('"+projectName+"').pipm("+namefilter+").name", dex)
                index = 1
                self.Progress.SetRange(len(pipes))
                for pipe in pipes:
                    command = "pipe('"+projectName+"').pipm('"+pipe+"').part(1:9999).spool_name"
                    pipeName = projectName + '-' + str(pipe)
                    if bUseDex:
                        bResult = self.SearchForComp(comp, command, dex)
                    else:
                        bResult = self.SearchForComp2( pipeName, 'pipe', comp )

                    if bResult:
                        self.InsertResult(pipe, 'Spool')

                    self.Progress.SetValue(index)
                    index = index + 1
                self.Progress.SetValue(0)
            wxMessageBox("Search complete!")
        event.Skip()

    def OnZoombtnButton(self, event):
        kcs_draft.highlight_off(0)
        if self.Index != -1:
            self.DisplayModelSubview(self.Subviews[self.Index])
            kcs_draft.element_highlight(self.Subviews[self.Index])
            kcs_ui.app_window_refresh()
            self.Index = self.Index + 1
            if self.Index>=len(self.Subviews):
                self.Index = 0

        event.Skip()

    def OnCloseBtnButton(self, event):
        kcs_draft.highlight_off(0)
        self.Destroy()
        self.EndModal(0)
        event.Skip()

    def OnResultsCtrl1ListItemSelected(self, event):
        itemIdx = -1;
        itemIdx = self.ResultsCtrl1.GetNextItem(itemIdx, wxLIST_NEXT_ALL, wxLIST_STATE_SELECTED)
        if itemIdx != -1:
            self.ModelName.SetValue(self.ResultsCtrl1.GetItemText(itemIdx))
            self.ModelTypeCombo1.SetStringSelection(self.ResultsCtrl1.GetItem(itemIdx, 1).GetText())

        event.Skip()

    def GetModelFromPoint(self):
        result = []
        projectName = kcs_util.TB_environment_get('SB_PROJ')
        projectNameLength = len(projectName)
        try:
            point = KcsPoint2D.Point2D()
            model = KcsModel.Model()
            res = kcs_ui.point2D_req("Indicate point", point )
            if res[0] == kcs_util.ok():
                # extracting modelname from projectname-modelname
                point = res[1]
                res = kcs_draft.model_identify(point, model)
                if model.Type in ["equipment", "cable way", "pipe", "ventilation"]:
                    result.append( model.Name[projectNameLength+1:] )
                else:
                    result.append( model.Name )
                result.append( model.Type )
                result.append( str(model.PartId) )
                return result
        except:
            result = ['', '', '']
            return result

    def OnGetModelButton(self, event):
        self.Show(False)
        try:
            model = []
            model = self.GetModelFromPoint()
            self.ModelName.SetValue( model[0] )

            # for "pipe spool" return just "spool"
            if string.find(model[1], "spool") != -1: # "spool" included
                model[1] = "spool"
            # for "placed volume" return just "volume"
            if string.find(model[1], "volume") != -1:
                model[1] = "volume"

            item = self.ModelTypeCombo1.FindString( model[1] )
            if item != -1:
                self.ModelTypeCombo1.SetSelection( item )
            else:
                self.ModelTypeCombo1.SetSelection( 0 )
        except:
            self.ModelName.SetValue("")
            self.ModelTypeCombo1.SetSelection( 0 )
        self.Show(True)

        event.Skip()

    def OnGetComponentButton(self, event):
        self.Show(False)
        try:
            model = []
            model = self.GetModelFromPoint()
            projectName = kcs_util.TB_environment_get('SB_PROJ')
            command = ""

            item = self.ModelTypeCombo.FindString( model[1] )
            if item != -1:
                self.ModelTypeCombo.SetSelection( item )
            else:
                self.ModelTypeCombo.SetSelection( 0 )
            if model[1] == "pipe":
                command = "pipe('"+projectName+"').pipm('"+model[0]+"').part("+model[2]+").comp_name"
            elif model[1] == "ventilation":
                command = "vent('"+projectName+"').ventm('"+model[0]+"').part("+model[2]+").comp_name"
            elif model[1] == "cable way":
                command = "cable('"+projectName+"').cway('"+model[0]+"').part("+model[2]+").comp_name"
            elif model[1] == "equipment":
                command = "equip('"+projectName+"').item('"+model[0]+"').comp_name"
            elif model[1] == "pipe spool":
                # extracting pipemodelname from pipemodelname-spoolname
                pos = string.rfind(model[0], "-")
                command = "pipe('"+projectName+"').pipm('"+model[0][0:pos]+"').part("+model[2]+").spool_name"
            elif model[1] == "struct":
                group_count_command = "structure('"+projectName+"').item('"+model[0]+"').ngroup"
                if kcs_dex.extract(group_count_command) == 0:
                    if kcs_dex.next_result() == 1:  # integer
                        group_count = kcs_dex.get_int()
                        if group_count > 0:
                            for group in range(1, group_count+1):
                                part_count_command = "structure('"+projectName+"').item('"+model[0]+"').group("+str(group)+").npart"
                                if kcs_dex.extract(part_count_command) == 0:
                                    if kcs_dex.next_result() == 1:
                                        part_count = kcs_dex.get_int()
                                        if part_count > 0:
                                            for part in range(1, part_count+1):
                                                parts_id_command = "structure('"+projectName+"').item('"+model[0]+"').group("+str(group)+").part("+str(part)+").part_id"
                                                if kcs_dex.extract(parts_id_command) == 0:
                                                    if kcs_dex.next_result() == 1:
                                                        if int(model[2]) == kcs_dex.get_int():
                                                            command = "structure('"+projectName+"').item('"+model[0]+"').group("+str(group)+").part("+str(part)+").comp_name"
                                                            break
            if command != "":
                if kcs_dex.extract(command) == 0:
                    if kcs_dex.next_result() == 3:  # string
                        self.ComponentName.SetValue( kcs_dex.get_string() )
                    else:
                        self.ComponentName.SetValue("")
                else:
                    self.ComponentName.SetValue("")
            else:
                self.ComponentName.SetValue("")
        except:
            self.ComponentName.SetValue("")
            self.ModelTypeCombo.SetSelection( 0 )
        self.Show(True)

        event.Skip()

    def OnComponentNameSetFocus(self, event):
        self.SearchComp.SetDefault()

        event.Skip()

    def OnModelNameSetFocus(self, event):
        self.SearchBtn.SetDefault()

        event.Skip()
