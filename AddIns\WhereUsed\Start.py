
#!/usr/bin/env python
#Boa:PyApp:main

AddInInfo = { 'name' : 'find' }

from wxPython.wx import *

import DwgFindDlg

modules ={'DwgFindDlg': [0, '', 'DwgFindDlg.py'],
 }

class BoaApp(wxApp):

    def OnInit(self):

        return true



def main():

    application = wxPySimpleApp()

    dlg = DwgFindDlg.wxDialog1(None)

    application.SetTopWindow(dlg)

    dlg.Show(true)

    dlg.app = application

    application.MainLoop()





def run(*args):

    main()



if __name__ == '__main__':

    main()
