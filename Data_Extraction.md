﻿<table><tr><th colspan="5"><a name="chmtopic1"></a>Tribon M3 </th></tr>
<tr><td colspan="5"></td></tr>
<tr><td></td><td></td><td> </td><td> </td><td>![](ahh2b1y4.001.png)[](#chmtopic1)</td></tr>
</table>

<a name="chmbookmark1"></a><a name="chmbookmark2"></a>*User's Guide* 

<a name="chmbookmark3"></a>*Data Extraction* 

<table><tr><th valign="bottom">![Next Page][ ](#chmtopic2)</th><th></th><th></th><th> </th><th> </th></tr>
<tr><td colspan="5"></td></tr>
<tr><td colspan="4">Copyright © 1993-2005 AVEVA AB </td><td>![HOME - Generated from Data_ExtractionFP.fm in Data_Extraction.book][](#chmtopic1)</td></tr>
</table>

<table><tr><th colspan="5" valign="bottom"><a name="chmtopic2"></a>User's Guide Data Extraction </th><th valign="bottom">![Version]</th></tr>
<tr><td colspan="6"></td></tr>
<tr><td valign="top">![Previous Page][ ](#chmtopic1)</td><td valign="top">![Next Page][ ](#chmtopic3)</td><td colspan="4" valign="top"></b> </td></tr>
</table>

<a name="chmbookmark4"></a>**1 Basic Data Extraction** 

<table><tr><th valign="bottom">![Previous Page][ ](#chmtopic1)</th><th valign="bottom">![Next Page][ ](#chmtopic3)</th><th> </th><th> </th><th></th></tr>
<tr><td colspan="5"></td></tr>
<tr><td colspan="4">Copyright © 1993-2005 AVEVA AB </td><td>![HOME - Generated from Data_Extraction_basic.fm in Data_Extraction.book][HOME - Generated from Data_ExtractionFP.fm in Data_Extraction.book][](#chmtopic1)</td></tr>
</table>

<table><tr><th colspan="5" valign="bottom"><a name="chmtopic3"></a>User's Guide Data Extraction </th><th valign="bottom">![Version]</th></tr>
<tr><td colspan="6"></td></tr>
<tr><td valign="top">![Previous Page][ ](#chmtopic2)</td><td valign="top">![Next Page][ ](#chmtopic4)</td><td colspan="4" valign="top"><b>Chapter: [Basic Data Extraction](#chmtopic2)</b> </td></tr>
</table>

<a name="chmbookmark5"></a>**1.1 General** 

<a name="chmbookmark6"></a>Data in the Tribon data banks are accessible via the Data Extraction facilities. It is possible to retrieve specific information from one or several Tribon objects by specifying the type and name of the model object and the wanted item within the object. 

<a name="chmbookmark7"></a>There are three ways to utilize the Data Extraction functions. With a specific query program, SX700, it is possible to ask for specific terms and get the answer at the terminal. The Data Extraction commands are also available within the Geometry Macro language and Vitesse. A COM-object with Data Extraction procedures is available for development of customized programs. 

<table><tr><th valign="bottom">![Previous Page][ ](#chmtopic2)</th><th valign="bottom">![Next Page][ ](#chmtopic4)</th><th> </th><th> </th><th></th></tr>
<tr><td colspan="5"></td></tr>
<tr><td colspan="4">Copyright © 1993-2005 AVEVA AB </td><td>![HOME - Generated from Data_Extraction_basic.fm in Data_Extraction.book][HOME - Generated from Data_ExtractionFP.fm in Data_Extraction.book][](#chmtopic1)</td></tr>
</table>

<table><tr><th colspan="5" valign="bottom"><a name="chmtopic4"></a>User's Guide Data Extraction </th><th valign="bottom">![Version]</th></tr>
<tr><td colspan="6"></td></tr>
<tr><td valign="top">![Previous Page][ ](#chmtopic3)</td><td valign="top">![Next Page][ ](#chmtopic5)</td><td colspan="4" valign="top"><b>Chapter: [Basic Data Extraction](#chmtopic2)</b> </td></tr>
</table>

<a name="chmbookmark8"></a>**1.2 Syntax** 

<a name="chmbookmark9"></a>To describe the wanted item, a specific Data Extraction syntax is used. With this syntax the type of object is specified as well as the name of the object and a description of the wanted item within the object. 

<table><tr><th valign="bottom">![Previous Page][ ](#chmtopic3)</th><th valign="bottom">![Next Page][ ](#chmtopic5)</th><th> </th><th> </th><th></th></tr>
<tr><td colspan="5"></td></tr>
<tr><td colspan="4">Copyright © 1993-2005 AVEVA AB </td><td>![HOME - Generated from Data_Extraction_basic.fm in Data_Extraction.book][HOME - Generated from Data_ExtractionFP.fm in Data_Extraction.book][](#chmtopic1)</td></tr>
</table>

<table><tr><th colspan="5" valign="bottom"><a name="chmtopic5"></a>User's Guide Data Extraction </th><th valign="bottom">![Version]</th></tr>
<tr><td colspan="6"></td></tr>
<tr><td valign="top">![Previous Page][ ](#chmtopic4)</td><td valign="top">![Next Page][ ](#chmtopic6)</td><td colspan="4" valign="top"><b>Chapter: [Basic Data Extraction](#chmtopic2)</b> </td></tr>
</table>

<a name="chmbookmark10"></a>**1.2.1 Keywords** 

<a name="chmbookmark11"></a>The hierarchical data extraction syntax is a combination of keywords that reflects how the user has built up the model. 

<a name="chmbookmark12"></a>The first keyword level is normally the Tribon application e.g. Pipe, Hull, Cable, etc. The second keyword level is a specific object within the Tribon application. The following keyword levels determine a specific term within the object. The levels and keywords are of course different for the different kinds of object. 

<a name="chmbookmark13"></a>The levels are separated by the character '.'. Each keyword can normally be abbreviated to the number of characters where the keyword is still unique. The complete lists of keywords (and abbreviations allowed) are documented in the following documents: 

|<a name="chmbookmark14"></a>DRAWING |<a name="chmbookmark15"></a>see [Drafting Keywords](#chmbookmark32). |
| :- | :- |
|<a name="chmbookmark16"></a>VOLUME |<a name="chmbookmark17"></a>see [Drafting Keywords](#chmbookmark32). |
|<a name="chmbookmark18"></a>EQUIP |<a name="chmbookmark19"></a>see [Equipment Keywords](#chmbookmark33). |
|<a name="chmbookmark20"></a>STRUCTURE |<a name="chmbookmark21"></a>see [Structure Keywords](#chmbookmark34). |
|<a name="chmbookmark22"></a>HULL |<a name="chmbookmark23"></a>see [Hull Keywords](#chmbookmark35). |
|<a name="chmbookmark24"></a>CABLE |<a name="chmbookmark25"></a>see [Cable Keywords](#chmbookmark36). |
|<a name="chmbookmark26"></a>COMP |<a name="chmbookmark27"></a>see [Component Keywords](#chmbookmark37). |
|<a name="chmbookmark28"></a>PIPE |<a name="chmbookmark29"></a>see [Pipe Keywords](#chmbookmark38). |
|<a name="chmbookmark30"></a>VENT |<a name="chmbookmark31"></a>see [Ventilation Keywords](#chmbookmark39). |

<table><tr><th valign="bottom">![Previous Page][ ](#chmtopic4)</th><th valign="bottom">![Next Page][ ](#chmtopic6)</th><th> </th><th> </th><th></th></tr>
<tr><td colspan="5"></td></tr>
<tr><td colspan="4">Copyright © 1993-2005 AVEVA AB </td><td>![HOME - Generated from Data_Extraction_basic.fm in Data_Extraction.book][HOME - Generated from Data_ExtractionFP.fm in Data_Extraction.book][](#chmtopic1)</td></tr>
</table>

<table><tr><th colspan="5" valign="bottom"><a name="chmtopic6"></a>User's Guide Data Extraction </th><th valign="bottom">![Version]</th></tr>
<tr><td colspan="6"></td></tr>
<tr><td valign="top">![Previous Page][ ](#chmtopic5)</td><td valign="top">![Next Page][ ](#chmtopic15)</td><td colspan="4" valign="top"><b>Chapter: [Basic Data Extraction](#chmtopic2)</b> </td></tr>
</table>

<a name="chmbookmark40"></a>**The Help Keyword** 

<a name="chmbookmark41"></a>The HELP keyword, valid in all system parts, can replace any valid keyword. The result of a command ended with the HELP keyword is a list of keywords that can replace HELP at its current position. 

<table><tr><th valign="bottom">![Previous Page][ ](#chmtopic5)</th><th valign="bottom">![Next Page][ ](#chmtopic15)</th><th> </th><th> </th><th></th></tr>
<tr><td colspan="5"></td></tr>
<tr><td colspan="4">Copyright © 1993-2005 AVEVA AB </td><td>![HOME - Generated from Data_Extraction_basic.fm in Data_Extraction.book][HOME - Generated from Data_ExtractionFP.fm in Data_Extraction.book][](#chmtopic1)</td></tr>
</table>

<table><tr><th colspan="5" valign="bottom"><a name="chmtopic7"></a>User's Guide Data Extraction </th><th valign="bottom">![Version]</th></tr>
<tr><td colspan="6"></td></tr>
<tr><td valign="top">![Previous Page][ ](#chmtopic16)</td><td valign="top">![Next Page][ ](#chmtopic17)</td><td colspan="4" valign="top"></b> </td></tr>
</table>

<a name="chmbookmark32"></a>**2 Drafting Keywords** 

<table><tr><th valign="bottom">![Previous Page][ ](#chmtopic16)</th><th valign="bottom">![Next Page][ ](#chmtopic17)</th><th> </th><th> </th><th></th></tr>
<tr><td colspan="5"></td></tr>
<tr><td colspan="4">Copyright © 1993-2005 AVEVA AB </td><td>![HOME - Generated from Data_Extraction_drafting.FM in Data_Extraction.book][HOME - Generated from Data_ExtractionFP.fm in Data_Extraction.book][](#chmtopic1)</td></tr>
</table>

<table><tr><th colspan="5" valign="bottom"><a name="chmtopic8"></a>User's Guide Data Extraction </th><th valign="bottom">![Version]</th></tr>
<tr><td colspan="6"></td></tr>
<tr><td valign="top">![Previous Page][ ](#chmtopic18)</td><td valign="top">![Next Page][ ](#chmtopic19)</td><td colspan="4" valign="top"></b> </td></tr>
</table>

<a name="chmbookmark33"></a>**4 Equipment Keywords** 

<table><tr><th valign="bottom">![Previous Page][ ](#chmtopic18)</th><th valign="bottom">![Next Page][ ](#chmtopic19)</th><th> </th><th> </th><th></th></tr>
<tr><td colspan="5"></td></tr>
<tr><td colspan="4">Copyright © 1993-2005 AVEVA AB </td><td>![HOME - Generated from Data_Extraction_equipment.FM in Data_Extraction.book][HOME - Generated from Data_ExtractionFP.fm in Data_Extraction.book][](#chmtopic1)</td></tr>
</table>

<table><tr><th colspan="5" valign="bottom"><a name="chmtopic9"></a>User's Guide Data Extraction </th><th valign="bottom">![Version]</th></tr>
<tr><td colspan="6"></td></tr>
<tr><td valign="top">![Previous Page][ ](#chmtopic20)</td><td valign="top">![Next Page][ ](#chmtopic21)</td><td colspan="4" valign="top"></b> </td></tr>
</table>

<a name="chmbookmark34"></a>**5 Structure Keywords** 

<table><tr><th valign="bottom">![Previous Page][ ](#chmtopic20)</th><th valign="bottom">![Next Page][ ](#chmtopic21)</th><th> </th><th> </th><th></th></tr>
<tr><td colspan="5"></td></tr>
<tr><td colspan="4">Copyright © 1993-2005 AVEVA AB </td><td>![HOME - Generated from Data_Extraction_structure.fm in Data_Extraction.book][HOME - Generated from Data_ExtractionFP.fm in Data_Extraction.book][](#chmtopic1)</td></tr>
</table>

<table><tr><th colspan="5" valign="bottom"><a name="chmtopic10"></a>User's Guide Data Extraction </th><th valign="bottom">![Version]</th></tr>
<tr><td colspan="6"></td></tr>
<tr><td valign="top">![Previous Page][ ](#chmtopic22)</td><td valign="top">![Next Page][ ](#chmtopic23)</td><td colspan="4" valign="top"></b> </td></tr>
</table>

<a name="chmbookmark35"></a>**6 Hull Keywords** 

<table><tr><th valign="bottom">![Previous Page][ ](#chmtopic22)</th><th valign="bottom">![Next Page][ ](#chmtopic23)</th><th> </th><th> </th><th></th></tr>
<tr><td colspan="5"></td></tr>
<tr><td colspan="4">Copyright © 1993-2005 AVEVA AB </td><td>![HOME - Generated from Data_Extraction_hull.FM in Data_Extraction.book][HOME - Generated from Data_ExtractionFP.fm in Data_Extraction.book][](#chmtopic1)</td></tr>
</table>

<table><tr><th colspan="5" valign="bottom"><a name="chmtopic11"></a>User's Guide Data Extraction </th><th valign="bottom">![Version]</th></tr>
<tr><td colspan="6"></td></tr>
<tr><td valign="top">![Previous Page][ ](#chmtopic24)</td><td valign="top">![Next Page][ ](#chmtopic25)</td><td colspan="4" valign="top"></b> </td></tr>
</table>

<a name="chmbookmark36"></a>**7 Cable Keywords** 

<table><tr><th valign="bottom">![Previous Page][ ](#chmtopic24)</th><th valign="bottom">![Next Page][ ](#chmtopic25)</th><th> </th><th> </th><th></th></tr>
<tr><td colspan="5"></td></tr>
<tr><td colspan="4">Copyright © 1993-2005 AVEVA AB </td><td>![HOME - Generated from Data_Extraction_cable.fm in Data_Extraction.book][HOME - Generated from Data_ExtractionFP.fm in Data_Extraction.book][](#chmtopic1)</td></tr>
</table>

<table><tr><th colspan="5" valign="bottom"><a name="chmtopic12"></a>User's Guide Data Extraction </th><th valign="bottom">![Version]</th></tr>
<tr><td colspan="6"></td></tr>
<tr><td valign="top">![Previous Page][ ](#chmtopic26)</td><td valign="top">![Next Page][ ](#chmtopic27)</td><td colspan="4" valign="top"></b> </td></tr>
</table>

<a name="chmbookmark37"></a>**8 Component Keywords** 

<table><tr><th valign="bottom">![Previous Page][ ](#chmtopic26)</th><th valign="bottom">![Next Page][ ](#chmtopic27)</th><th> </th><th> </th><th></th></tr>
<tr><td colspan="5"></td></tr>
<tr><td colspan="4">Copyright © 1993-2005 AVEVA AB </td><td>![HOME - Generated from Data_Extraction_components.fm in Data_Extraction.book][HOME - Generated from Data_ExtractionFP.fm in Data_Extraction.book][](#chmtopic1)</td></tr>
</table>

<table><tr><th colspan="5" valign="bottom"><a name="chmtopic13"></a>User's Guide Data Extraction </th><th valign="bottom">![Version]</th></tr>
<tr><td colspan="6"></td></tr>
<tr><td valign="top">![Previous Page][ ](#chmtopic28)</td><td valign="top">![Next Page][ ](#chmtopic29)</td><td colspan="4" valign="top"></b> </td></tr>
</table>

<a name="chmbookmark38"></a>**9 Pipe Keywords** 

<table><tr><th valign="bottom">![Previous Page][ ](#chmtopic28)</th><th valign="bottom">![Next Page][ ](#chmtopic29)</th><th> </th><th> </th><th></th></tr>
<tr><td colspan="5"></td></tr>
<tr><td colspan="4">Copyright © 1993-2005 AVEVA AB </td><td>![HOME - Generated from Data_Extraction_pipe.FM in Data_Extraction.book][HOME - Generated from Data_ExtractionFP.fm in Data_Extraction.book][](#chmtopic1)</td></tr>
</table>

<table><tr><th colspan="5" valign="bottom"><a name="chmtopic14"></a>User's Guide Data Extraction </th><th valign="bottom">![Version]</th></tr>
<tr><td colspan="6"></td></tr>
<tr><td valign="top">![Previous Page][ ](#chmtopic30)</td><td valign="top">![Next Page][ ](#chmtopic31)</td><td colspan="4" valign="top"></b> </td></tr>
</table>

<a name="chmbookmark39"></a>**10 Ventilation Keywords** 

<table><tr><th valign="bottom">![Previous Page][ ](#chmtopic30)</th><th valign="bottom">![Next Page][ ](#chmtopic31)</th><th> </th><th> </th><th></th></tr>
<tr><td colspan="5"></td></tr>
<tr><td colspan="4">Copyright © 1993-2005 AVEVA AB </td><td>![HOME - Generated from Data_Extraction_ventilation.FM in Data_Extraction.book][HOME - Generated from Data_ExtractionFP.fm in Data_Extraction.book][](#chmtopic1)</td></tr>
</table>

<table><tr><th colspan="5" valign="bottom"><a name="chmtopic15"></a>User's Guide Data Extraction </th><th valign="bottom">![Version]</th></tr>
<tr><td colspan="6"></td></tr>
<tr><td valign="top">![Previous Page][ ](#chmtopic6)</td><td valign="top">![Next Page][ ](#chmtopic32)</td><td colspan="4" valign="top"><b>Chapter: [Basic Data Extraction](#chmtopic2)</b> </td></tr>
</table>

<a name="chmbookmark42"></a>**Example** 

<table><tr><th colspan="2"><a name="chmbookmark43"></a><b>Command string: CABLE.HELP</b> </th></tr>
<tr><td colspan="2"><a name="chmbookmark44"></a>The following keywords are valid: </td></tr>
<tr><td></td><td><p><a name="chmbookmark45"></a>CAB_MOD</p><p>CWAY</p><p>PNTR</p></td></tr>
<tr><td colspan="2"><a name="chmbookmark46"></a><b>Command string: CABLE.PNTR(*).HELP</b> </td></tr>
<tr><td colspan="2"><a name="chmbookmark47"></a>The following keywords are valid: </td></tr>
<tr><td></td><td><p><a name="chmbookmark48"></a>POINT</p><p>TYPE</p><p>TOTAL_AREA</p><p>USED_AREA</p><p>COMP_NAME</p><p>NCWAY</p><p>CWAY_LOC</p><p>NAME</p></td></tr>
</table>

<table><tr><th valign="bottom">![Previous Page][ ](#chmtopic6)</th><th valign="bottom">![Next Page][ ](#chmtopic32)</th><th> </th><th> </th><th></th></tr>
<tr><td colspan="5"></td></tr>
<tr><td colspan="4">Copyright © 1993-2005 AVEVA AB </td><td>![HOME - Generated from Data_Extraction_basic.fm in Data_Extraction.book][HOME - Generated from Data_ExtractionFP.fm in Data_Extraction.book][](#chmtopic1)</td></tr>
</table>

<table><tr><th colspan="5" valign="bottom"><a name="chmtopic16"></a>User's Guide Data Extraction </th><th valign="bottom">![Version]</th></tr>
<tr><td colspan="6"></td></tr>
<tr><td valign="top">![Previous Page][ ](#chmtopic33)</td><td valign="top">![Next Page][ ](#chmtopic7)</td><td colspan="4" valign="top"><b>Chapter: [Basic Data Extraction](#chmtopic2)</b> </td></tr>
</table>

<a name="chmbookmark49"></a>**1.7 Data Extraction in Query Program** 

<a name="chmbookmark50"></a>The program SX700 is an interactive data extraction program run from a command prompt. 

<a name="chmbookmark51"></a>Input to the program is a Data Extraction command given from a command prompt. The resulting values will be presented on the screen 


<table><tr><th valign="bottom"><a name="chmbookmark52"></a>![Previous Page][ ](#chmtopic33)</th><th valign="bottom">![Next Page][ ](#chmtopic7)</th><th> </th><th> </th><th></th></tr>
<tr><td colspan="5"></td></tr>
<tr><td colspan="4">Copyright © 1993-2005 AVEVA AB </td><td>![HOME - Generated from Data_Extraction_basic.fm in Data_Extraction.book][HOME - Generated from Data_ExtractionFP.fm in Data_Extraction.book][](#chmtopic1)</td></tr>
</table>

<table><tr><th colspan="5" valign="bottom"><a name="chmtopic17"></a>User's Guide Data Extraction </th><th valign="bottom">![Version]</th></tr>
<tr><td colspan="6"></td></tr>
<tr><td valign="top">![Previous Page][ ](#chmtopic7)</td><td valign="top">![Next Page][ ](#chmtopic34)</td><td colspan="4" valign="top"><b>Chapter: [Drafting Keywords](#chmtopic7)</b> </td></tr>
</table>

<a name="chmbookmark53"></a>**2.1 General** 

<a name="chmbookmark54"></a>This section contains a description of the currently available keywords that can be used in the data extraction of Drawing & Volume information. A description of general principles, and especially of the syntax of the data extraction commands, can be found in [Basic Data Extraction](#chmbookmark4). 

<a name="chmbookmark55"></a>The hierarchical structure of the keywords reflects how the user has built up the model in Tribon Drafting. 

<a name="chmbookmark56"></a>Unless otherwise stated measures are given in mm's, areas in square mm's and weights in kg's. 

<a name="chmbookmark57"></a>The column OUTPUT specifies the type of the resulting output value. 

- <a name="chmbookmark58"></a>i for integer 
- <a name="chmbookmark59"></a>r for real 
- <a name="chmbookmark60"></a>s for character string 

<a name="chmbookmark61"></a>If more than one value is delivered, the number of values is specified as a factor. 

<table><tr><th valign="bottom">![Previous Page][ ](#chmtopic7)</th><th valign="bottom">![Next Page][ ](#chmtopic34)</th><th> </th><th> </th><th></th></tr>
<tr><td colspan="5"></td></tr>
<tr><td colspan="4">Copyright © 1993-2005 AVEVA AB </td><td>![HOME - Generated from Data_Extraction_drafting.FM in Data_Extraction.book][HOME - Generated from Data_ExtractionFP.fm in Data_Extraction.book][](#chmtopic1)</td></tr>
</table>

<table><tr><th colspan="5" valign="bottom"><a name="chmtopic18"></a>User's Guide Data Extraction </th><th valign="bottom">![Version]</th></tr>
<tr><td colspan="6"></td></tr>
<tr><td valign="top">![Previous Page][ ](#chmtopic35)</td><td valign="top">![Next Page][ ](#chmtopic8)</td><td colspan="4" valign="top"><b>Chapter: [TDM (Tribon Data Management) Keywords](#chmtopic36)</b> </td></tr>
</table>

<a name="chmbookmark62"></a>**3.2 Information from TDM (Tribon Data Management)** 

<a name="chmbookmark63"></a>General TDM information can be extracted from different objects in the Tribon databanks The following is an overview:</sup> 

![Excel document]<a name="chmbookmark64"></a>  Datext\_TDM.xls

<a name="chmbookmark65"></a>**Note:**   The TDM keywords may also be valid for other objects. Refer to each separate Data Extraction chapter. 


<table><tr><th valign="bottom"><a name="chmbookmark66"></a>![Previous Page][ ](#chmtopic35)</th><th valign="bottom">![Next Page][ ](#chmtopic8)</th><th> </th><th> </th><th></th></tr>
<tr><td colspan="5"></td></tr>
<tr><td colspan="4">Copyright © 1993-2005 AVEVA AB </td><td>![HOME - Generated from Data_Extraction_TDM.FM in Data_Extraction.book][HOME - Generated from Data_ExtractionFP.fm in Data_Extraction.book][](#chmtopic1)</td></tr>
</table>

<table><tr><th colspan="5" valign="bottom"><a name="chmtopic19"></a>User's Guide Data Extraction </th><th valign="bottom">![Version]</th></tr>
<tr><td colspan="6"></td></tr>
<tr><td valign="top">![Previous Page][ ](#chmtopic8)</td><td valign="top">![Next Page][ ](#chmtopic20)</td><td colspan="4" valign="top"><b>Chapter: [Equipment Keywords](#chmtopic8)</b> </td></tr>
</table>

<a name="chmbookmark67"></a>**4.1 Overview** 

<a name="chmbookmark68"></a>This document contains a description of the currently available keywords that can be used in data extraction of equipment information. A description of general principles and especially of the syntax of the data extraction commands can be found in [Basic Data Extraction](#chmbookmark4). 

<a name="chmbookmark69"></a>Unless otherwise stated measures are given in mm's, areas in square mm's and weights in kg's. 

<a name="chmbookmark70"></a>The column Output specifies the type of the resulting output value. 

- <a name="chmbookmark71"></a>i for integer 
- <a name="chmbookmark72"></a>r for real 
- <a name="chmbookmark73"></a>s for character string 

<a name="chmbookmark74"></a>If more than one value is delivered, the number of values is specified as a factor. 

<table><tr><th valign="bottom">![Previous Page][ ](#chmtopic8)</th><th valign="bottom">![Next Page][ ](#chmtopic20)</th><th> </th><th> </th><th></th></tr>
<tr><td colspan="5"></td></tr>
<tr><td colspan="4">Copyright © 1993-2005 AVEVA AB </td><td>![HOME - Generated from Data_Extraction_equipment.FM in Data_Extraction.book][HOME - Generated from Data_ExtractionFP.fm in Data_Extraction.book][](#chmtopic1)</td></tr>
</table>

<table><tr><th colspan="5" valign="bottom"><a name="chmtopic20"></a>User's Guide Data Extraction </th><th valign="bottom">![Version]</th></tr>
<tr><td colspan="6"></td></tr>
<tr><td valign="top">![Previous Page][ ](#chmtopic19)</td><td valign="top">![Next Page][ ](#chmtopic9)</td><td colspan="4" valign="top"><b>Chapter: [Equipment Keywords](#chmtopic8)</b> </td></tr>
</table>

<a name="chmbookmark75"></a>**4.2 Information from Equipment** 

<a name="chmbookmark76"></a>The following is an overview of keywords available for all equipment objects:</sup> 

![Excel document]<a name="chmbookmark77"></a>  DatExt\_Equipment.xls


<table><tr><th valign="bottom"><a name="chmbookmark78"></a>![Previous Page][ ](#chmtopic19)</th><th valign="bottom">![Next Page][ ](#chmtopic9)</th><th> </th><th> </th><th></th></tr>
<tr><td colspan="5"></td></tr>
<tr><td colspan="4">Copyright © 1993-2005 AVEVA AB </td><td>![HOME - Generated from Data_Extraction_equipment.FM in Data_Extraction.book][HOME - Generated from Data_ExtractionFP.fm in Data_Extraction.book][](#chmtopic1)</td></tr>
</table>

<table><tr><th colspan="5" valign="bottom"><a name="chmtopic21"></a>User's Guide Data Extraction </th><th valign="bottom">![Version]</th></tr>
<tr><td colspan="6"></td></tr>
<tr><td valign="top">![Previous Page][ ](#chmtopic9)</td><td valign="top">![Next Page][ ](#chmtopic37)</td><td colspan="4" valign="top"><b>Chapter: [Structure Keywords](#chmtopic9)</b> </td></tr>
</table>

<a name="chmbookmark79"></a>**5.1 General** 

<a name="chmbookmark80"></a>This document contains a description of the currently available keywords that can be used in the data extraction of structure information. A description of general principles, and especially of the syntax of the data extraction commands can be found in [Basic Data Extraction](#chmbookmark4). 

<a name="chmbookmark81"></a>The hierarchical structure of the keywords reflects how the user has built up the model in Tribon Structure. 

<a name="chmbookmark82"></a>Unless otherwise stated measures are given in mm's, areas in square mm's and weights in kg's. 

<a name="chmbookmark83"></a>The column OUTPUT specifies the type of the resulting output value. 

- <a name="chmbookmark84"></a>i for integer 
- <a name="chmbookmark85"></a>r for real 
- <a name="chmbookmark86"></a>s for character string 

<a name="chmbookmark87"></a>If more than one value is delivered, the number of values are specified as a factor. 

<table><tr><th valign="bottom">![Previous Page][ ](#chmtopic9)</th><th valign="bottom">![Next Page][ ](#chmtopic37)</th><th> </th><th> </th><th></th></tr>
<tr><td colspan="5"></td></tr>
<tr><td colspan="4">Copyright © 1993-2005 AVEVA AB </td><td>![HOME - Generated from Data_Extraction_structure.fm in Data_Extraction.book][HOME - Generated from Data_ExtractionFP.fm in Data_Extraction.book][](#chmtopic1)</td></tr>
</table>

<table><tr><th colspan="5" valign="bottom"><a name="chmtopic22"></a>User's Guide Data Extraction </th><th valign="bottom">![Version]</th></tr>
<tr><td colspan="6"></td></tr>
<tr><td valign="top">![Previous Page][ ](#chmtopic38)</td><td valign="top">![Next Page][ ](#chmtopic10)</td><td colspan="4" valign="top"><b>Chapter: [Structure Keywords](#chmtopic9)</b> </td></tr>
</table>

<a name="chmbookmark88"></a>**5.2.3 Reference to belonging Parts via Running Number** 

<table><tr><th colspan="8" valign="top"><a name="chmbookmark89"></a><b>Keyword</b> </th><th valign="top"><a name="chmbookmark90"></a><b>Explanation</b> </th><th colspan="2" valign="top"><a name="chmbookmark91"></a><b>Output</b> </th></tr>
<tr><td valign="top"><a name="chmbookmark92"></a><b>1</b> </td><td valign="top"><a name="chmbookmark93"></a><b>2</b> </td><td valign="top"><a name="chmbookmark94"></a><b>3</b> </td><td valign="top"><a name="chmbookmark95"></a><b>4</b> </td><td valign="top"><a name="chmbookmark96"></a><b>5</b> </td><td colspan="3" valign="top"><a name="chmbookmark97"></a><b>6</b> </td><td colspan="2" valign="top"></td><td valign="top"></td></tr>
<tr><td colspan="4" valign="top"></td><td colspan="4" valign="top"><a name="chmbookmark98"></a><a name="chmbookmark99"></a>.NPART_B[ELONGING] </td><td colspan="2" valign="top"><a name="chmbookmark100"></a>Number of belonging parts </td><td valign="top"><a name="chmbookmark101"></a>i </td></tr>
<tr><td colspan="4" valign="top"></td><td colspan="6" valign="top"><a name="chmbookmark102"></a>.PART_B[ELONGING](i),    I=1,NPART_B</td><td valign="top"></td></tr>
<tr><td colspan="8" valign="top"></td><td colspan="2" valign="top"><a name="chmbookmark103"></a><a name="chmbookmark104"></a><a name="chmbookmark105"></a>Belonging part reference by index </td><td valign="top"><a name="chmbookmark106"></a>- </td></tr>
</table>

<a name="chmbookmark107"></a>Hole part data can be used under belonging parts 

<table><tr><th valign="bottom">![Previous Page][ ](#chmtopic38)</th><th valign="bottom">![Next Page][ ](#chmtopic10)</th><th> </th><th> </th><th></th></tr>
<tr><td colspan="5"></td></tr>
<tr><td colspan="4">Copyright © 1993-2005 AVEVA AB </td><td>![HOME - Generated from Data_Extraction_structure.fm in Data_Extraction.book][HOME - Generated from Data_ExtractionFP.fm in Data_Extraction.book][](#chmtopic1)</td></tr>
</table>

<table><tr><th colspan="5" valign="bottom"><a name="chmtopic23"></a>User's Guide Data Extraction </th><th valign="bottom">![Version]</th></tr>
<tr><td colspan="6"></td></tr>
<tr><td valign="top">![Previous Page][ ](#chmtopic10)</td><td valign="top">![Next Page][ ](#chmtopic24)</td><td colspan="4" valign="top"><b>Chapter: [Hull Keywords](#chmtopic10)</b> </td></tr>
</table>

<a name="chmbookmark108"></a>**6.1 General** 

<a name="chmbookmark109"></a>This document contains a description of the currently available keywords that can be used in the data extraction of hull information. A description of general principles, and especially of the syntax of the data extraction commands, can be found in [Basic Data Extraction](#chmbookmark4). 

<a name="chmbookmark110"></a>The hierarchical structure of the keywords reflects how the user has built up the model in Tribon Hull. 

<a name="chmbookmark111"></a>The currently available keywords do not cover all data items within all types of hull objects. 

<a name="chmbookmark112"></a>Unless otherwise stated measures are given in mm's, areas in square mm's and weights in kg's. 

<a name="chmbookmark113"></a>All keywords are presented on Excel Sheets. In these sheets, all hierarchies of keywords can be found, together with explanations. 

<a name="chmbookmark114"></a>The column OUTPUT specifies the type of the resulting output value. 

- <a name="chmbookmark115"></a>i for integer 
- <a name="chmbookmark116"></a>r for real 
- <a name="chmbookmark117"></a>s for character string 

<a name="chmbookmark118"></a>If more than one value is delivered, the number of values is specified as a factor. 

<table><tr><th valign="bottom">![Previous Page][ ](#chmtopic10)</th><th valign="bottom">![Next Page][ ](#chmtopic24)</th><th> </th><th> </th><th></th></tr>
<tr><td colspan="5"></td></tr>
<tr><td colspan="4">Copyright © 1993-2005 AVEVA AB </td><td>![HOME - Generated from Data_Extraction_hull.FM in Data_Extraction.book][HOME - Generated from Data_ExtractionFP.fm in Data_Extraction.book][](#chmtopic1)</td></tr>
</table>

<table><tr><th colspan="5" valign="bottom"><a name="chmtopic24"></a>User's Guide Data Extraction </th><th valign="bottom">![Version]</th></tr>
<tr><td colspan="6"></td></tr>
<tr><td valign="top">![Previous Page][ ](#chmtopic23)</td><td valign="top">![Next Page][ ](#chmtopic11)</td><td colspan="4" valign="top"><b>Chapter: [Hull Keywords](#chmtopic10)</b> </td></tr>
</table>

<a name="chmbookmark119"></a>**6.2 Information from a Hull Object** 

<table><tr><th valign="top"><a name="chmbookmark120"></a><b>Hull, Overview</b> </th><th colspan="2" valign="top"><p><a name="chmbookmark121"></a>The various objects in Tribon Hull are all accessed via the Hull keyword. The following is an overview of the top level keywords, including access to surfaces. </p><p>![Excel document]<a name="chmbookmark122"></a>  DatExt_Hull.xls</p></th></tr>
<tr><td valign="top"><a name="chmbookmark123"></a><b>Surface</b> </td><td colspan="2" valign="top"><p><a name="chmbookmark124"></a>Surfaces can be used to access shell profiles. </p><p>![Excel document]<a name="chmbookmark125"></a>  DatExt_Surface.xls</p></td></tr>
<tr><td valign="top"><a name="chmbookmark126"></a><b>Blocks</b> </td><td colspan="2" valign="top"><p><a name="chmbookmark127"></a>For each block it is possible to get information about all the panels which are referred to from that block. For each panel the information can be extracted that is stored together with the reference i.e. on block level. For information stored in the panel itself, see Panel below. </p><p>![Excel document]<a name="chmbookmark128"></a>  DatExt_Blocks.xls</p></td></tr>
<tr><td valign="top"><a name="chmbookmark129"></a><b>Curved Panels</b> </td><td colspan="2" valign="top"><p><a name="chmbookmark130"></a>For curved panels, much of the information stored in the panel is available, especially regarding. </p><p>![Excel document]<a name="chmbookmark131"></a>  DatExt_Curved_Panels.xls</p></td></tr>
<tr><td valign="top"><a name="chmbookmark132"></a><b>Shell Profiles</b> </td><td colspan="2" valign="top"><p><a name="chmbookmark133"></a>For shell profiles, that information is primarily available which is interesting for the profile as a part. </p><p><a name="chmbookmark134"></a>In selecting a certain profile part within a given longitudinal/transversal two main options are available: </p><p>- <a name="chmbookmark135"></a>the profile part is selected via its name (PART) </p><p>- <a name="chmbookmark136"></a>the part is selected via a principal plane perpendicular to any of the main axes at a given coordinate. </p><p><a name="chmbookmark137"></a>In the latter case an x-coordinate can be selected via a frame term, built up in the same way as in the panel generation, etc., e.g. FRA ('FR107+100') </p><p>![Excel document]<a name="chmbookmark138"></a>  DatExt_Shell_Profiles.xls</p></td></tr>
<tr><td rowspan="14" valign="top"><a name="chmbookmark139"></a><b>Panels</b> </td><td colspan="2" valign="top"><p><a name="chmbookmark140"></a>For plane panels, much of the information is available that is stored in the panel, especially regarding </p><p>- <a name="chmbookmark141"></a>plates </p><p>- <a name="chmbookmark142"></a>brackets </p><p>- <a name="chmbookmark143"></a>doubling </p><p>- <a name="chmbookmark144"></a>stiffeners </p><p>- <a name="chmbookmark145"></a>flanges </p><p>- <a name="chmbookmark146"></a>pillar </p><p>- <a name="chmbookmark147"></a>boundaries </p><p><a name="chmbookmark148"></a>i.e. mainly about those items that will result in parts (except the boundaries). Coordinates are expressed in the local coordinate system of panel. </p><p><a name="chmbookmark149"></a>For brackets, information is available about its profiles in the same way as for profiles on the panel itself. Coordinates are then in the coordinate system of the bracket. </p><p>![Excel document]<a name="chmbookmark150"></a>  DatExt_Panels.xls</p></td></tr>
<tr><td valign="top"><a name="chmbookmark151"></a><b>Boundary</b> </td><td valign="top">![Excel document]<a name="chmbookmark152"></a>  DatExt_Boundary.xls</td></tr>
<tr><td valign="top"><a name="chmbookmark153"></a><b>Plate</b> </td><td valign="top">![Excel document]<a name="chmbookmark154"></a>  DatExt_Plate.xls</td></tr>
<tr><td valign="top"><a name="chmbookmark155"></a><b>Bracket</b> </td><td valign="top">![Excel document]<a name="chmbookmark156"></a>  DatExt_Bracket.xls</td></tr>
<tr><td valign="top"><a name="chmbookmark157"></a><b>Doubling</b> </td><td valign="top">![Excel document]<a name="chmbookmark158"></a>  DatExt_Doubling.xls</td></tr>
<tr><td valign="top"><a name="chmbookmark159"></a><b>Stiffener</b> </td><td valign="top">![Excel document]<a name="chmbookmark160"></a>  DatExt_Stiffener.xls</td></tr>
<tr><td valign="top"><a name="chmbookmark161"></a><b>Flanges</b> </td><td valign="top">![Excel document]<a name="chmbookmark162"></a>  DatExt_Flange.xls</td></tr>
<tr><td valign="top"><a name="chmbookmark163"></a><b>Pillar</b> </td><td valign="top">![Excel document]<a name="chmbookmark164"></a>  DatExt_Pillar.xls</td></tr>
<tr><td valign="top"><a name="chmbookmark165"></a><b>Bead</b> </td><td valign="top">![Excel document]<a name="chmbookmark166"></a>  DatExt_Bead.xls</td></tr>
<tr><td valign="top"><a name="chmbookmark167"></a><b>Hole</b> </td><td valign="top">![Excel document]<a name="chmbookmark168"></a>  DatExt_Hole.xls</td></tr>
<tr><td valign="top"><a name="chmbookmark169"></a><b>Notch</b> </td><td valign="top">![Excel document]<a name="chmbookmark170"></a>  DatExt_Notch.xls</td></tr>
<tr><td valign="top"><a name="chmbookmark171"></a><b>Cutout</b> </td><td valign="top">![Excel document]<a name="chmbookmark172"></a>  DatExt_Cutout.xls</td></tr>
<tr><td valign="top"><a name="chmbookmark173"></a><b>Seams</b> </td><td valign="top">![Excel document]<a name="chmbookmark174"></a>  DatExt_Seams.xls</td></tr>
<tr><td valign="top"><a name="chmbookmark175"></a><b>TAP</b> </td><td valign="top">![Excel document]<a name="chmbookmark176"></a>  DatExt_Tap.xls</td></tr>
<tr><td valign="top"><a name="chmbookmark177"></a><b>Plates</b> </td><td colspan="2" valign="top"><p><a name="chmbookmark178"></a>Objects stored as plates are selected via its name. Its coordinates are represented in a local uv-system. By using the transformation matrix, it is possible to transform to the ship's xyz-system. </p><p>![Excel document]<a name="chmbookmark179"></a>  DatExt_Plates.xls</p></td></tr>
<tr><td valign="top"><a name="chmbookmark180"></a><b>Curves</b> </td><td colspan="2" valign="top"><p><a name="chmbookmark181"></a>Objects stored as curves are selected via its name. Its coordinates are represented in a local uv-system. By using the transformation matrix, it is possible to transform to the ship's xyz-system. </p><p>![Excel document]<a name="chmbookmark182"></a>  DatExt_Curves.xls</p></td></tr>
<tr><td valign="top"><a name="chmbookmark183"></a><b>Nested Profiles</b> </td><td colspan="2" valign="top"><p><a name="chmbookmark184"></a>For each nested profile it is possible to extract the following information </p><p>![Excel document]<a name="chmbookmark185"></a>  DatExt_Nested_Profiles.xls</p></td></tr>
<tr><td valign="top"><a name="chmbookmark186"></a><b>Nested Plates</b> </td><td colspan="2" valign="top"><p><a name="chmbookmark187"></a>For each nested plate following information is available </p><p>![Excel document]<a name="chmbookmark188"></a>  DatExt_Nested_Plates.xls</p></td></tr>
<tr><td valign="top"><a name="chmbookmark189"></a><b>Raw Plates</b> </td><td colspan="2" valign="top"><p><a name="chmbookmark190"></a>For parent plates only names and quantity used are available information. </p><p>![Excel document]<a name="chmbookmark191"></a>  DatExt_Raw_Plates.xls</p></td></tr>
<tr><td valign="top"><a name="chmbookmark192"></a><b>Assembly Plates</b> </td><td colspan="2" valign="top">![Excel document]<a name="chmbookmark193"></a>  DatExt_Assembly_Plates.xls</td></tr>
</table>

<table><tr><th valign="bottom">![Previous Page][ ](#chmtopic23)</th><th valign="bottom">![Next Page][ ](#chmtopic11)</th><th> </th><th> </th><th></th></tr>
<tr><td colspan="5"></td></tr>
<tr><td colspan="4">Copyright © 1993-2005 AVEVA AB </td><td>![HOME - Generated from Data_Extraction_hull.FM in Data_Extraction.book][HOME - Generated from Data_ExtractionFP.fm in Data_Extraction.book][](#chmtopic1)</td></tr>
</table>

<table><tr><th colspan="5" valign="bottom"><a name="chmtopic25"></a>User's Guide Data Extraction </th><th valign="bottom">![Version]</th></tr>
<tr><td colspan="6"></td></tr>
<tr><td valign="top">![Previous Page][ ](#chmtopic11)</td><td valign="top">![Next Page][ ](#chmtopic39)</td><td colspan="4" valign="top"><b>Chapter: [Cable Keywords](#chmtopic11)</b> </td></tr>
</table>

<a name="chmbookmark194"></a>**7.1 General** 

<a name="chmbookmark195"></a>This document contains a description of the currently available keywords that can be used in the data extraction of cable information. A description of general principles, and especially of the syntax of the data extraction commands, can be found in [Basic Data Extraction](#chmbookmark4). 

<a name="chmbookmark196"></a>The hierarchical structure of the keywords reflects how the user has built up the model in Tribon Structure. 

<a name="chmbookmark197"></a>Unless otherwise stated measures are given in mm's, areas in square mm's and weights in kg's. 

<a name="chmbookmark198"></a>The column OUTPUT specifies the type of the resulting output value, 

- <a name="chmbookmark199"></a>I -for integer 
- <a name="chmbookmark200"></a>r -for real 
- <a name="chmbookmark201"></a>s -for character string 

<a name="chmbookmark202"></a>If more than one value is delivered, the number of values is specified as a factor. 

<table><tr><th valign="bottom">![Previous Page][ ](#chmtopic11)</th><th valign="bottom">![Next Page][ ](#chmtopic39)</th><th> </th><th> </th><th></th></tr>
<tr><td colspan="5"></td></tr>
<tr><td colspan="4">Copyright © 1993-2005 AVEVA AB </td><td>![HOME - Generated from Data_Extraction_cable.fm in Data_Extraction.book][HOME - Generated from Data_ExtractionFP.fm in Data_Extraction.book][](#chmtopic1)</td></tr>
</table>

<table><tr><th colspan="5" valign="bottom"><a name="chmtopic26"></a>User's Guide Data Extraction </th><th valign="bottom">![Version]</th></tr>
<tr><td colspan="6"></td></tr>
<tr><td valign="top">![Previous Page][ ](#chmtopic40)</td><td valign="top">![Next Page][ ](#chmtopic12)</td><td colspan="4" valign="top"><b>Chapter: [Cable Keywords](#chmtopic11)</b> </td></tr>
</table>

<a name="chmbookmark203"></a>**7.6 Cable Connection Status** 

<a name="chmbookmark204"></a>The matrix below describes the CABLE.CAB\_MODEL.CONNECTION. STATUS output. 

A = Equipment name defined 

B = Equipment location defined (room name) 

C = Equipment placed 

D = Cable end point defined by coordinates, not by equipment 

![](ahh2b1y4.007.jpeg)

<a name="chmbookmark205"></a><a name="chmbookmark206"></a>values is specified as a factor. 


<table><tr><th valign="bottom"><a name="chmbookmark207"></a>![Previous Page][ ](#chmtopic40)</th><th valign="bottom">![Next Page][ ](#chmtopic12)</th><th> </th><th> </th><th></th></tr>
<tr><td colspan="5"></td></tr>
<tr><td colspan="4">Copyright © 1993-2005 AVEVA AB </td><td>![HOME - Generated from Data_Extraction_cable.fm in Data_Extraction.book][HOME - Generated from Data_ExtractionFP.fm in Data_Extraction.book][](#chmtopic1)</td></tr>
</table>

<table><tr><th colspan="5" valign="bottom"><a name="chmtopic27"></a>User's Guide Data Extraction </th><th valign="bottom">![Version]</th></tr>
<tr><td colspan="6"></td></tr>
<tr><td valign="top">![Previous Page][ ](#chmtopic12)</td><td valign="top">![Next Page][ ](#chmtopic28)</td><td colspan="4" valign="top"><b>Chapter: [Component Keywords](#chmtopic12)</b> </td></tr>
</table>

<a name="chmbookmark208"></a>**8.1 General** 

<a name="chmbookmark209"></a>This document contains a description of the currently available keywords that can be used in the data extraction of component information. A description of general principles, and especially of the syntax of the data extraction commands, can be found in [Basic Data Extraction](#chmbookmark4). 

<a name="chmbookmark210"></a>The hierarchical structure of the keywords reflects how the user has built up the model in the Tribon structure system. 

<a name="chmbookmark211"></a>Unless otherwise stated measures are given in mm's, areas in square mm's and weights an kg's. 

<a name="chmbookmark212"></a>the column OUTPUT specifies the type of the resulting output value. 

- <a name="chmbookmark213"></a>i -for integer 
- <a name="chmbookmark214"></a>r -for real 
- <a name="chmbookmark215"></a>s -for character string 

<a name="chmbookmark216"></a>If more than one value is delivered, the number of values is specified as a factor. 

<table><tr><th valign="bottom">![Previous Page][ ](#chmtopic12)</th><th valign="bottom">![Next Page][ ](#chmtopic28)</th><th> </th><th> </th><th></th></tr>
<tr><td colspan="5"></td></tr>
<tr><td colspan="4">Copyright © 1993-2005 AVEVA AB </td><td>![HOME - Generated from Data_Extraction_components.fm in Data_Extraction.book][HOME - Generated from Data_ExtractionFP.fm in Data_Extraction.book][](#chmtopic1)</td></tr>
</table>

<table><tr><th colspan="5" valign="bottom"><a name="chmtopic28"></a>User's Guide Data Extraction </th><th valign="bottom">![Version]</th></tr>
<tr><td colspan="6"></td></tr>
<tr><td valign="top">![Previous Page][ ](#chmtopic27)</td><td valign="top">![Next Page][ ](#chmtopic13)</td><td colspan="4" valign="top"><b>Chapter: [Component Keywords](#chmtopic12)</b> </td></tr>
</table>

<a name="chmbookmark217"></a>**8.2 Information from a Component** 

<a name="chmbookmark218"></a>The following is an overview of keywords available for all component objects:</sup> 

![Excel document]<a name="chmbookmark219"></a>  DatExt\_Component.xls


<table><tr><th valign="bottom"><a name="chmbookmark220"></a>![Previous Page][ ](#chmtopic27)</th><th valign="bottom">![Next Page][ ](#chmtopic13)</th><th> </th><th> </th><th></th></tr>
<tr><td colspan="5"></td></tr>
<tr><td colspan="4">Copyright © 1993-2005 AVEVA AB </td><td>![HOME - Generated from Data_Extraction_components.fm in Data_Extraction.book][HOME - Generated from Data_ExtractionFP.fm in Data_Extraction.book][](#chmtopic1)</td></tr>
</table>

<table><tr><th colspan="5" valign="bottom"><a name="chmtopic29"></a>User's Guide Data Extraction </th><th valign="bottom">![Version]</th></tr>
<tr><td colspan="6"></td></tr>
<tr><td valign="top">![Previous Page][ ](#chmtopic13)</td><td valign="top">![Next Page][ ](#chmtopic41)</td><td colspan="4" valign="top"><b>Chapter: [Pipe Keywords](#chmtopic13)</b> </td></tr>
</table>

<a name="chmbookmark221"></a>**9.1 General** 

<a name="chmbookmark222"></a>This document contains a description of the currently available keywords that can be used in the data extraction of pipe information. A description of general principles, and especially of the syntax of the data extraction commands, can be found in [Basic Data Extraction](#chmbookmark4). 

<a name="chmbookmark223"></a>The hierarchical structure of the keywords reflects how the user has built up the model in Tribon Pipe. 

<a name="chmbookmark224"></a>Unless otherwise stated measures are given in mm's, areas in square mm's and weights in kg's. 

<a name="chmbookmark225"></a>The column OUTPUT specifies the type of the resulting output value. 

- <a name="chmbookmark226"></a>i -for integer 
- <a name="chmbookmark227"></a>r -for real 
- <a name="chmbookmark228"></a>s -for character string 

<a name="chmbookmark229"></a>If more than one value is delivered, the number of values are specified as a factor, e.g. 5s, 2\*3r, xi (x means varying). 

<table><tr><th valign="bottom">![Previous Page][ ](#chmtopic13)</th><th valign="bottom">![Next Page][ ](#chmtopic41)</th><th> </th><th> </th><th></th></tr>
<tr><td colspan="5"></td></tr>
<tr><td colspan="4">Copyright © 1993-2005 AVEVA AB </td><td>![HOME - Generated from Data_Extraction_pipe.FM in Data_Extraction.book][HOME - Generated from Data_ExtractionFP.fm in Data_Extraction.book][](#chmtopic1)</td></tr>
</table>

<table><tr><th colspan="5" valign="bottom"><a name="chmtopic30"></a>User's Guide Data Extraction </th><th valign="bottom">![Version]</th></tr>
<tr><td colspan="6"></td></tr>
<tr><td valign="top">![Previous Page][ ](#chmtopic42)</td><td valign="top">![Next Page][ ](#chmtopic14)</td><td colspan="4" valign="top"><b>Chapter: [Pipe Keywords](#chmtopic13)</b> </td></tr>
</table>

<a name="chmbookmark230"></a>**9.7 Information from Surface Treatment** 

<a name="chmbookmark231"></a>The following is an overview of keywords available for surface treatment objects:</sup> 

![Excel document]<a name="chmbookmark232"></a>  DatExt\_Pipe\_Surface\_Treatment.xls


<table><tr><th valign="bottom"><a name="chmbookmark233"></a><a name="chmbookmark234"></a>![Previous Page][ ](#chmtopic42)</th><th valign="bottom">![Next Page][ ](#chmtopic14)</th><th> </th><th> </th><th></th></tr>
<tr><td colspan="5"></td></tr>
<tr><td colspan="4">Copyright © 1993-2005 AVEVA AB </td><td>![HOME - Generated from Data_Extraction_pipe.FM in Data_Extraction.book][HOME - Generated from Data_ExtractionFP.fm in Data_Extraction.book][](#chmtopic1)</td></tr>
</table>

<table><tr><th colspan="5" valign="bottom"><a name="chmtopic31"></a>User's Guide Data Extraction </th><th valign="bottom">![Version]</th></tr>
<tr><td colspan="6"></td></tr>
<tr><td valign="top">![Previous Page][ ](#chmtopic14)</td><td valign="top">![Next Page][ ](#chmtopic43)</td><td colspan="4" valign="top"><b>Chapter: [Ventilation Keywords](#chmtopic14)</b> </td></tr>
</table>

<a name="chmbookmark235"></a>**10.1 General** 

<a name="chmbookmark236"></a>This document contains a description of the currently available keywords that can be used in the data extraction of ventilation information. A description of general principles, and especially of the syntax of the data extraction commands, can be found in [Basic Data Extraction](#chmbookmark4). 

<a name="chmbookmark237"></a>The hierarchical structure of the keywords reflects how the user has built up the model in Tribon Ventilation. 

<a name="chmbookmark238"></a>Unless otherwise stated measures are given in mm's, areas in square mm's and weights in kg's. 

<a name="chmbookmark239"></a>the column OUTPUT specifies the type of the resulting output value. 

- <a name="chmbookmark240"></a>i for integer 
- <a name="chmbookmark241"></a>r for real 
- <a name="chmbookmark242"></a>s for character string 

<a name="chmbookmark243"></a>If more than one value is delivered, the number of values are specified as a factor. 

<table><tr><th valign="bottom">![Previous Page][ ](#chmtopic14)</th><th valign="bottom">![Next Page][ ](#chmtopic43)</th><th> </th><th> </th><th></th></tr>
<tr><td colspan="5"></td></tr>
<tr><td colspan="4">Copyright © 1993-2005 AVEVA AB </td><td>![HOME - Generated from Data_Extraction_ventilation.FM in Data_Extraction.book][HOME - Generated from Data_ExtractionFP.fm in Data_Extraction.book][](#chmtopic1)</td></tr>
</table>

<table><tr><th colspan="5" valign="bottom"><a name="chmtopic32"></a>User's Guide Data Extraction </th><th valign="bottom">![Version]</th></tr>
<tr><td colspan="6"></td></tr>
<tr><td valign="top">![Previous Page][ ](#chmtopic15)</td><td valign="top">![Next Page][ ](#chmtopic44)</td><td colspan="4" valign="top"><b>Chapter: [Basic Data Extraction](#chmtopic2)</b> </td></tr>
</table>

<a name="chmbookmark244"></a>**1.2.2 Arguments** 

<a name="chmbookmark245"></a>Each keyword can be combined with an argument or a list of arguments. An argument is always surrounded by brackets. Three different types of argument exist: string, integer number or real number. A string is surrounded by apostrophes. The kind of argument depends on the keyword (see the keyword documentation). 

<a name="chmbookmark246"></a>The following operators can occur in an argument (or argument list): 

|**\***|<a name="chmbookmark247"></a>wildcard character where \* replaces one or several characters. It is valid only for string arguments. |
| :- | :- |
||<a name="chmbookmark248"></a>**Example:** ('PUMP'\*)|
|**:**|<a name="chmbookmark249"></a>defines an interval. Valid for all argument types. |
||<a name="chmbookmark250"></a>**Examples:** (1:5), ('A':'C'\*)|
|**,**|<a name="chmbookmark251"></a>repetition operator. Valid for all argument types. |
||<a name="chmbookmark252"></a>**Example:** (1,5)|

<a name="chmbookmark253"></a>Combinations of these operators are allowed in one argument. 

<table><tr><th valign="bottom">![Previous Page][ ](#chmtopic15)</th><th valign="bottom">![Next Page][ ](#chmtopic44)</th><th> </th><th> </th><th></th></tr>
<tr><td colspan="5"></td></tr>
<tr><td colspan="4">Copyright © 1993-2005 AVEVA AB </td><td>![HOME - Generated from Data_Extraction_basic.fm in Data_Extraction.book][HOME - Generated from Data_ExtractionFP.fm in Data_Extraction.book][](#chmtopic1)</td></tr>
</table>

<table><tr><th colspan="5" valign="bottom"><a name="chmtopic33"></a>User's Guide Data Extraction </th><th valign="bottom">![Version]</th></tr>
<tr><td colspan="6"></td></tr>
<tr><td valign="top">![Previous Page][ ](#chmtopic45)</td><td valign="top">![Next Page][ ](#chmtopic16)</td><td colspan="4" valign="top"><b>Chapter: [Basic Data Extraction](#chmtopic2)</b> </td></tr>
</table>

<a name="chmbookmark254"></a>**1.6 Data Extraction from Vitesse** 

<a name="chmbookmark255"></a>Data extraction can be used from Vitesse to extract model information, which can be further processed in the Python language and by using Tribon Vitesse API calls. See the document *Tribon M3 Vitesse, "Functions in Vitesse Data Extraction Interface"*.

<table><tr><th valign="bottom">![Previous Page][ ](#chmtopic45)</th><th valign="bottom">![Next Page][ ](#chmtopic16)</th><th> </th><th> </th><th></th></tr>
<tr><td colspan="5"></td></tr>
<tr><td colspan="4">Copyright © 1993-2005 AVEVA AB </td><td>![HOME - Generated from Data_Extraction_basic.fm in Data_Extraction.book][HOME - Generated from Data_ExtractionFP.fm in Data_Extraction.book][](#chmtopic1)</td></tr>
</table>

<table><tr><th colspan="5" valign="bottom"><a name="chmtopic34"></a>User's Guide Data Extraction </th><th valign="bottom">![Version]</th></tr>
<tr><td colspan="6"></td></tr>
<tr><td valign="top">![Previous Page][ ](#chmtopic17)</td><td valign="top">![Next Page][ ](#chmtopic46)</td><td colspan="4" valign="top"><b>Chapter: [Drafting Keywords](#chmtopic7)</b> </td></tr>
</table>

<a name="chmbookmark256"></a>**2.2 Information from a Volume** 

<a name="chmbookmark257"></a>The following is an overview of keywords available for all volume objects:</sup> 

![Excel document]<a name="chmbookmark258"></a>  Datext\_Volume.xls

<a name="chmbookmark259"></a>**Note:**   It is possible to specify the source data bank for the VOLUME keyword. This is done by suffixing the volume name with the '@' sign followed by the "volume type". If no optional data bank is given, the default data bank is "Project Volume" ( SBD\_VOLUME.). 

<a name="chmbookmark260"></a>At present, there are two different volume types, namely: 

- <a name="chmbookmark261"></a>the project dependant volume DB (SBD\_VOLUME) 
- <a name="chmbookmark262"></a>the component volume DB (SBE\_GENVOLDB) 

<a name="chmbookmark263"></a>The volume type can be given as 

- <a name="chmbookmark264"></a>the logical name of the data bank (SBD\_VOLUME or SBE\_GENVOLDB) 
- <a name="chmbookmark265"></a>the description of the data bank ("Project volume" or "Component volume" 
- <a name="chmbookmark266"></a>an integer corresponding to the type ( 1 for SBD\_VOLUME and 2 for SBE\_GENVOLDB) 

  |<a name="chmbookmark267"></a>**Example 1**|
  | :- |
  |<p>VOLUME( 'XYZ'\*).NAME</p><p>VOLUME( 'XYZ@SBD\_VOLUME'\*).NAME</p><p>VOLUME( 'XYZ@Project Volume'\*).NAME</p><p>VOLUME( 'XYZ@1'\*).NAME</p>|

<a name="chmbookmark268"></a>Equivalent ways to extract the names of all volumes starting with *XYZ* in the SBD\_VOLUME data bank 

|<a name="chmbookmark269"></a>**Example 2** |
| :- |
|<p>VOLUME( 'XYZ@SBE\_GENVOLDB'\*).NAME</p><p>VOLUME( 'XYZ@Component Volume'\*).NAME</p><p>VOLUME( 'XYZ@2'\*).NAME</p>|

<a name="chmbookmark270"></a>Equivalent ways to extract the names of all volumes starting with XYZ in the SBE\_GENVOLDB data bank. 

|<a name="chmbookmark271"></a>**Example 3** |
| :- |
|<p>VOLUME( 'XYZ@SBD\_VOLUME'\*, 'ABC@SBE\_GENVOLDB'\*).NAME</p><p>VOLUME( 'XYZ@SBD\_VOLUME'\*, 'ABC@2'\*).NAME</p><p>VOLUME( 'XYZ'\*, 'ABC@2'\*).NAME</p><p>VOLUME( 'XYZ@SBD\_VOLUME'\*, 'ABC@Component Volume'\*).NAME</p><p>VOLUME( 'XYZ@Project Volume'\*, 'ABC@Component Volume'\*).NAME</p><p>VOLUME( 'XYZ@1'\*, 'ABC@Component Volume'\*).NAME</p>|

<a name="chmbookmark272"></a>Equivalent ways to extract the names of all volumes starting with XYZ in the SBD\_VOLUME data bank and all volumes starting with ABC in the SBE\_GENVOLDB. 

|<a name="chmbookmark273"></a>**Example 4** |
| :- |
|<p>VOLUME( 'XYZ90' :  'XYZ99').NAME</p><p>VOLUME( 'XYZ90@SBD\_VOLUME' :  'XYZ99').NAME</p><p>VOLUME( 'XYZ90@Project Volume' :  'XYZ999').NAME</p><p>VOLUME( 'XYZ90@1' :  'XYZ999').NAME</p><p>VOLUME( 'XYZ90@SBE\_GENVOLDB' : 'XYZ99').NAME</p><p>VOLUME( 'XYZ90@Component Volume' :  'XYZ99').NAME</p><p>VOLUME( 'XYZ90@2' :  'XYZ99').NAME</p>|

<a name="chmbookmark274"></a>Equivalent ways to extract the names of all volumes in the interval XYZ90 to XYZ99 in the SBD\_VOLUME data bank and three equivalent ways to extract the names of all volumes in the interval XYZ90 to XYZ99 in the SBE\_GENVOLDB data bank. 

<a name="chmbookmark275"></a>**Note:**   Note that when giving intervals, the optional volume type is given in the lower (first) limit. A volume type in the upper (second) limit will be ignored. 

<a name="chmbookmark276"></a>**Example** 

- <a name="chmbookmark277"></a>VOLUME( 'XYZ90@SBE\_GENVOLDB' : 'XYZ99@SBD\_VOLUME').NAME 

  <a name="chmbookmark278"></a>is the same as

  <a name="chmbookmark279"></a>VOLUME( 'XYZ90@SBE\_GENVOLDB' : 'XYZ99').NAME

<a name="chmbookmark280"></a>and

- <a name="chmbookmark281"></a>VOLUME( 'XYZ90' : 'XYZ99@SBE\_GENVOLDB').NAME 

  <a name="chmbookmark282"></a>is the same as

  <a name="chmbookmark283"></a>VOLUME( 'XYZ90@SBD\_VOLUME' : 'XYZ99').NAME

<table><tr><th valign="bottom">![Previous Page][ ](#chmtopic17)</th><th valign="bottom">![Next Page][ ](#chmtopic46)</th><th> </th><th> </th><th></th></tr>
<tr><td colspan="5"></td></tr>
<tr><td colspan="4">Copyright © 1993-2005 AVEVA AB </td><td>![HOME - Generated from Data_Extraction_drafting.FM in Data_Extraction.book][HOME - Generated from Data_ExtractionFP.fm in Data_Extraction.book][](#chmtopic1)</td></tr>
</table>

<table><tr><th colspan="5" valign="bottom"><a name="chmtopic35"></a>User's Guide Data Extraction </th><th valign="bottom">![Version]</th></tr>
<tr><td colspan="6"></td></tr>
<tr><td valign="top">![Previous Page][ ](#chmtopic36)</td><td valign="top">![Next Page][ ](#chmtopic18)</td><td colspan="4" valign="top"><b>Chapter: [TDM (Tribon Data Management) Keywords](#chmtopic36)</b> </td></tr>
</table>

<a name="chmbookmark284"></a>**3.1 General** 

<a name="chmbookmark285"></a>This section contains a description of the currently available keywords that can be used in the data extraction of TDM information. A description of general principles, and especially of the syntax of the data extraction commands, can be found in [Basic Data Extraction](#chmbookmark4). 

<a name="chmbookmark286"></a>Unless otherwise stated measures are given in mm's, areas in square mm's and weights in kg's. 

<a name="chmbookmark287"></a>The column OUTPUT specifies the type of the resulting output value. 

- <a name="chmbookmark288"></a>i for integer 
- <a name="chmbookmark289"></a>r for real 
- <a name="chmbookmark290"></a>s for character string 

<a name="chmbookmark291"></a>If more than one value is delivered, the number of values is specified as a factor. 

<table><tr><th valign="bottom">![Previous Page][ ](#chmtopic36)</th><th valign="bottom">![Next Page][ ](#chmtopic18)</th><th> </th><th> </th><th></th></tr>
<tr><td colspan="5"></td></tr>
<tr><td colspan="4">Copyright © 1993-2005 AVEVA AB </td><td>![HOME - Generated from Data_Extraction_TDM.FM in Data_Extraction.book][HOME - Generated from Data_ExtractionFP.fm in Data_Extraction.book][](#chmtopic1)</td></tr>
</table>

<table><tr><th colspan="5" valign="bottom"><a name="chmtopic36"></a>User's Guide Data Extraction </th><th valign="bottom">![Version]</th></tr>
<tr><td colspan="6"></td></tr>
<tr><td valign="top">![Previous Page][ ](#chmtopic46)</td><td valign="top">![Next Page][ ](#chmtopic35)</td><td colspan="4" valign="top"></b> </td></tr>
</table>

<a name="chmbookmark292"></a>**3 TDM (Tribon Data Management) Keywords** 

<table><tr><th valign="bottom">![Previous Page][ ](#chmtopic46)</th><th valign="bottom">![Next Page][ ](#chmtopic35)</th><th> </th><th> </th><th></th></tr>
<tr><td colspan="5"></td></tr>
<tr><td colspan="4">Copyright © 1993-2005 AVEVA AB </td><td>![HOME - Generated from Data_Extraction_TDM.FM in Data_Extraction.book][HOME - Generated from Data_ExtractionFP.fm in Data_Extraction.book][](#chmtopic1)</td></tr>
</table>

<table><tr><th colspan="5" valign="bottom"><a name="chmtopic37"></a>User's Guide Data Extraction </th><th valign="bottom">![Version]</th></tr>
<tr><td colspan="6"></td></tr>
<tr><td valign="top">![Previous Page][ ](#chmtopic21)</td><td valign="top">![Next Page][ ](#chmtopic47)</td><td colspan="4" valign="top"><b>Chapter: [Structure Keywords](#chmtopic9)</b> </td></tr>
</table>

<a name="chmbookmark293"></a>**5.2 Information from a Structure Object** 

<table><tr><th colspan="8" valign="top"><a name="chmbookmark294"></a><b>Keyword</b></th><th valign="top"><a name="chmbookmark295"></a><b>Explanation</b> </th><th colspan="2" valign="top"><a name="chmbookmark296"></a><b>Output</b> </th></tr>
<tr><td valign="top"><a name="chmbookmark297"></a><b>1</b> </td><td valign="top"><a name="chmbookmark298"></a><b>2</b> </td><td valign="top"><a name="chmbookmark299"></a><b>3</b> </td><td valign="top"><a name="chmbookmark300"></a><b>4</b> </td><td valign="top"><a name="chmbookmark301"></a><b>5</b> </td><td colspan="2" valign="top"><a name="chmbookmark302"></a><b>6</b> </td><td valign="top"></td><td colspan="2" valign="top"></td><td valign="top"></td></tr>
<tr><td colspan="7" valign="top"><a name="chmbookmark303"></a><a name="chmbookmark304"></a><a name="chmbookmark305"></a>STR[UCTURE][(s)],</td><td valign="top">s=<project></td><td colspan="2" valign="top"><a name="chmbookmark306"></a>Application structure </td><td valign="top"><a name="chmbookmark307"></a>- </td></tr>
<tr><td valign="top"></td><td colspan="6" valign="top">.MOD[ULE](s), </td><td valign="top">s=<name></td><td colspan="2" valign="top"><a name="chmbookmark308"></a>Module object by name </td><td valign="top"><a name="chmbookmark309"></a>- </td></tr>
<tr><td colspan="2" valign="top"></td><td colspan="6" valign="top">.NAM[E]</td><td colspan="2" valign="top"><a name="chmbookmark310"></a>Module object name </td><td valign="top"><a name="chmbookmark311"></a>s </td></tr>
<tr><td colspan="2" valign="top"></td><td colspan="6" valign="top">.BOX</td><td colspan="2" valign="top"><a name="chmbookmark312"></a>Module circumscribed box </td><td valign="top"><a name="chmbookmark313"></a>6*r </td></tr>
<tr><td colspan="2" valign="top"></td><td colspan="6" valign="top">.NITE[M]</td><td colspan="2" valign="top"><a name="chmbookmark314"></a>Number of objects in module object</td><td valign="top"><a name="chmbookmark315"></a>i </td></tr>
<tr><td colspan="2" valign="top"></td><td colspan="5" valign="top">.ITE[M](i),</td><td valign="top">i=1,NITE</td><td colspan="2" valign="top"><a name="chmbookmark316"></a>Object reference by index </td><td valign="top"><a name="chmbookmark317"></a>- </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.NAM[E]</td><td colspan="2" valign="top"><a name="chmbookmark318"></a>Object name </td><td valign="top"><a name="chmbookmark319"></a>s </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.BOX</td><td colspan="2" valign="top"><a name="chmbookmark320"></a>Circumscribed object box </td><td valign="top"><a name="chmbookmark321"></a>6*r </td></tr>
<tr><td valign="top"></td><td colspan="6" valign="top">.ITE[M](s),</td><td valign="top">s=<name></td><td colspan="2" valign="top"><a name="chmbookmark322"></a>Structure object by name </td><td valign="top"><a name="chmbookmark323"></a>- </td></tr>
<tr><td colspan="2" valign="top"></td><td colspan="6" valign="top">.NAM[E]</td><td colspan="2" valign="top"><a name="chmbookmark324"></a>Structure object name </td><td valign="top"><a name="chmbookmark325"></a>s </td></tr>
<tr><td colspan="2" valign="top"></td><td colspan="6" valign="top">.MOD[ULE]</td><td colspan="2" valign="top"></td><td valign="top"></td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top"><a name="chmbookmark326"></a><a name="chmbookmark327"></a>.NAM[E]</td><td colspan="2" valign="top"><a name="chmbookmark328"></a>Module object name </td><td valign="top"><a name="chmbookmark329"></a>s </td></tr>
<tr><td colspan="2" valign="top"></td><td colspan="6" valign="top">.BOX</td><td colspan="2" valign="top"><a name="chmbookmark330"></a>Circumscribed box </td><td valign="top"><a name="chmbookmark331"></a>6*r </td></tr>
<tr><td colspan="2" valign="top"></td><td colspan="6" valign="top">.WEI[GHT]</td><td colspan="2" valign="top"><a name="chmbookmark332"></a>Weight </td><td valign="top"><a name="chmbookmark333"></a>r </td></tr>
<tr><td colspan="2" valign="top"></td><td colspan="6" valign="top">.COG</td><td colspan="2" valign="top"><a name="chmbookmark334"></a>Centre Of Gravity </td><td valign="top"><a name="chmbookmark335"></a>3*r </td></tr>
<tr><td colspan="2" valign="top"></td><td colspan="6" valign="top">.STA[NDARD_REF]</td><td colspan="2" valign="top"><a name="chmbookmark336"></a>Standard reference name </td><td valign="top"><a name="chmbookmark337"></a>s </td></tr>
<tr><td colspan="2" valign="top"></td><td colspan="6" valign="top">.ASS[EMBLY] </td><td colspan="2" valign="top"><a name="chmbookmark338"></a>Assembly reference name </td><td valign="top"><a name="chmbookmark339"></a>s </td></tr>
<tr><td colspan="2" valign="top"></td><td colspan="6" valign="top">.HULL_M[ARKING]</td><td colspan="2" valign="top"><a name="chmbookmark340"></a>Indicator for Hull Marking creation </td><td valign="top"><a name="chmbookmark341"></a>s </td></tr>
<tr><td colspan="2" valign="top"></td><td colspan="6" valign="top">.REA[DY]</td><td colspan="2" valign="top"><a name="chmbookmark342"></a>Indicator if structure is split </td><td valign="top"><a name="chmbookmark343"></a>i </td></tr>
<tr><td colspan="8" valign="top"></td><td colspan="2" valign="top"><a name="chmbookmark344"></a>0 = No, 1=Yes. </td><td valign="top"></td></tr>
<tr><td colspan="2" valign="top"></td><td colspan="6" valign="top"><a name="chmbookmark345"></a>.PDI_T[RANSFERED]</td><td colspan="2" valign="top"><a name="chmbookmark346"></a>Indicator if structure is trans- ferred to PDI. 0=No, 1=Yes. </td><td valign="top"><a name="chmbookmark347"></a>i </td></tr>
<tr><td colspan="2" valign="top"></td><td colspan="6" valign="top">.NUSER_ATT[RIBUTE]</td><td colspan="2" valign="top"><a name="chmbookmark348"></a>Number of user attributes </td><td valign="top"><a name="chmbookmark349"></a>i </td></tr>
<tr><td colspan="2" valign="top"></td><td colspan="6" valign="top">.USER_ATT[RIBUTE](i),</td><td colspan="2" valign="top"><a name="chmbookmark350"></a>See [Information from a Drawing in Chapter Drafting Keywords](#chmbookmark438)</td><td valign="top"></td></tr>
<tr><td colspan="2" valign="top"></td><td colspan="6" valign="top"><a name="chmbookmark351"></a>.NCW_CON(NECTION)</td><td colspan="2" valign="top"><a name="chmbookmark352"></a>Number of cableway connections </td><td valign="top"><a name="chmbookmark353"></a>i </td></tr>
<tr><td colspan="2" valign="top"></td><td colspan="5" valign="top">.CW_CON(NECTION)(i)</td><td valign="top">i=1,NCW_CON</td><td colspan="2" valign="top"><a name="chmbookmark354"></a>Cableway connection </td><td valign="top"><a name="chmbookmark355"></a>- </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.NAM(E)</td><td colspan="2" valign="top"><a name="chmbookmark356"></a>Name of cableway </td><td valign="top"><a name="chmbookmark357"></a>s </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.PRO(J)</td><td colspan="2" valign="top"><a name="chmbookmark358"></a>Project of cableway </td><td valign="top"><a name="chmbookmark359"></a>s </td></tr>
<tr><td colspan="2" valign="top"></td><td colspan="6" valign="top">.NPIPE_CON(NECTION)</td><td colspan="2" valign="top"><a name="chmbookmark360"></a>Number of pipe connections </td><td valign="top"><a name="chmbookmark361"></a>i </td></tr>
<tr><td colspan="2" valign="top"></td><td colspan="5" valign="top">.PIPE_CON(NECTION)(i)</td><td valign="top">i=1,NPIPE_CON</td><td colspan="2" valign="top"><a name="chmbookmark362"></a>Pipe connection </td><td valign="top"><a name="chmbookmark363"></a>- </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.NAM(E)</td><td colspan="2" valign="top"><a name="chmbookmark364"></a>Name of pipe (- as delimiter) </td><td valign="top"><a name="chmbookmark365"></a>s </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.PRO(J)</td><td colspan="2" valign="top"><a name="chmbookmark366"></a>Project of pipe </td><td valign="top"><a name="chmbookmark367"></a>s </td></tr>
<tr><td colspan="2" valign="top"></td><td colspan="6" valign="top">.NVENT_CON(NECTION)</td><td colspan="2" valign="top"><a name="chmbookmark368"></a>Number of ventilation connections </td><td valign="top"><a name="chmbookmark369"></a>i </td></tr>
<tr><td colspan="2" valign="top"></td><td colspan="5" valign="top">.VENT_CON(NECTION)(i)</td><td valign="top">i=1,NVENT_CON</td><td colspan="2" valign="top"><a name="chmbookmark370"></a>Ventilation connection (- as delimiter) </td><td valign="top"><a name="chmbookmark371"></a>- </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.NAM(E)</td><td colspan="2" valign="top"><a name="chmbookmark372"></a>Name of ventilation </td><td valign="top"><a name="chmbookmark373"></a>s </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.PRO(J)</td><td colspan="2" valign="top"><a name="chmbookmark374"></a>Project of ventilation </td><td valign="top"><a name="chmbookmark375"></a>s </td></tr>
<tr><td colspan="2" valign="top"></td><td colspan="6" valign="top">.CW_COM(P)</td><td colspan="2" valign="top"><a name="chmbookmark376"></a>Cableway component </td><td valign="top"><a name="chmbookmark377"></a>- </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.NOD(E)</td><td colspan="2" valign="top"><a name="chmbookmark378"></a>Cableway component data </td><td valign="top"><a name="chmbookmark379"></a>3*r </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.DIR(ECTION)</td><td colspan="2" valign="top"><a name="chmbookmark380"></a>Cableway component data </td><td valign="top"><a name="chmbookmark381"></a>3*r </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.ROT(ATION)</td><td colspan="2" valign="top"><a name="chmbookmark382"></a>Cableway component data </td><td valign="top"><a name="chmbookmark383"></a>3*r </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.HEI(GHT)</td><td colspan="2" valign="top"><a name="chmbookmark384"></a>Cableway component data </td><td valign="top"><a name="chmbookmark385"></a>r </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">WID(TH)</td><td colspan="2" valign="top"><a name="chmbookmark386"></a>Cableway component data </td><td valign="top"><a name="chmbookmark387"></a>r </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.LEN(GTH)</td><td colspan="2" valign="top"><a name="chmbookmark388"></a>Cableway component data </td><td valign="top"><a name="chmbookmark389"></a>r </td></tr>
<tr><td colspan="2" valign="top"></td><td colspan="6" valign="top">.NGRO[UP]</td><td colspan="2" valign="top"><a name="chmbookmark390"></a>Number of groups </td><td valign="top"><a name="chmbookmark391"></a>i </td></tr>
<tr><td colspan="2" valign="top"></td><td colspan="5" valign="top">.GRO[UP](i),</td><td valign="top">i=1,NGRO</td><td colspan="2" valign="top"><a name="chmbookmark392"></a>Group reference by index </td><td valign="top"><a name="chmbookmark393"></a>- </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.WEI[GHT]</td><td colspan="2" valign="top"><a name="chmbookmark394"></a>Weight </td><td valign="top"><a name="chmbookmark395"></a>r </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.COG</td><td colspan="2" valign="top"><a name="chmbookmark396"></a>Centre Of Gravity </td><td valign="top"><a name="chmbookmark397"></a>3*r </td></tr>
<tr><td colspan="2" valign="top"></td><td colspan="6" valign="top">.TDM_I[NFORMATION]</td><td colspan="2" valign="top"><a name="chmbookmark398"></a>Data management </td><td valign="top"><a name="chmbookmark399"></a>- </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.ALIAS1</td><td colspan="2" valign="top"><a name="chmbookmark400"></a>Alias information </td><td valign="top"><a name="chmbookmark401"></a>s </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.ALIAS2</td><td colspan="2" valign="top"><a name="chmbookmark402"></a>Alias information </td><td valign="top"><a name="chmbookmark403"></a>s </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.ALIAS3</td><td colspan="2" valign="top"><a name="chmbookmark404"></a>Alias information </td><td valign="top"><a name="chmbookmark405"></a>s </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.ALIAS4</td><td colspan="2" valign="top"><a name="chmbookmark406"></a>Alias information </td><td valign="top"><a name="chmbookmark407"></a>s </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.DESC[RIPTION]</td><td colspan="2" valign="top"><a name="chmbookmark408"></a>Description </td><td valign="top"><a name="chmbookmark409"></a>s </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.REM[ARKS]</td><td colspan="2" valign="top"><a name="chmbookmark410"></a>Remarks </td><td valign="top"><a name="chmbookmark411"></a>s </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.TYPE1</td><td colspan="2" valign="top"><a name="chmbookmark412"></a>Type </td><td valign="top"><a name="chmbookmark413"></a>s </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.TYPE2</td><td colspan="2" valign="top"><a name="chmbookmark414"></a>Type </td><td valign="top"><a name="chmbookmark415"></a>s </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.TYPE3</td><td colspan="2" valign="top"><a name="chmbookmark416"></a>Type </td><td valign="top"><a name="chmbookmark417"></a>s </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.TYPE4</td><td colspan="2" valign="top"><a name="chmbookmark418"></a>Type </td><td valign="top"><a name="chmbookmark419"></a>s </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.PLA[NINGUNIT]</td><td colspan="2" valign="top"><a name="chmbookmark420"></a>Planning unit </td><td valign="top"><a name="chmbookmark421"></a>s </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.COST[CODE]</td><td colspan="2" valign="top"><a name="chmbookmark422"></a>Cost code </td><td valign="top"><a name="chmbookmark423"></a>s </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.STATUS_D[ESIGN]</td><td colspan="2" valign="top"><a name="chmbookmark424"></a>Status design </td><td valign="top"><a name="chmbookmark425"></a>s </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.STATUS_MAT[ERIAL]</td><td colspan="2" valign="top"><a name="chmbookmark426"></a>Status material </td><td valign="top"><a name="chmbookmark427"></a>s </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.STATUS_MAN[UFACT]</td><td colspan="2" valign="top"><a name="chmbookmark428"></a>Status manufact </td><td valign="top"><a name="chmbookmark429"></a>s </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.STATUS_A[SSEMBLY]</td><td colspan="2" valign="top"><a name="chmbookmark430"></a>Status material </td><td valign="top"><a name="chmbookmark431"></a>s </td></tr>
<tr><td colspan="2" valign="top"></td><td colspan="6" valign="top">.TDM_R[EFERENCES]</td><td colspan="2" valign="top"><a name="chmbookmark432"></a>Drawing references </td><td valign="top"><a name="chmbookmark433"></a>- </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.NREF[ERENCE]</td><td colspan="2" valign="top"><a name="chmbookmark434"></a>Number of references </td><td valign="top"><a name="chmbookmark435"></a>i </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.REF[ERENCE]</td><td colspan="2" valign="top"><a name="chmbookmark436"></a>Drawing references </td><td valign="top"><a name="chmbookmark437"></a>s </td></tr>
</table>

<table><tr><th valign="bottom">![Previous Page][ ](#chmtopic21)</th><th valign="bottom">![Next Page][ ](#chmtopic47)</th><th> </th><th> </th><th></th></tr>
<tr><td colspan="5"></td></tr>
<tr><td colspan="4">Copyright © 1993-2005 AVEVA AB </td><td>![HOME - Generated from Data_Extraction_structure.fm in Data_Extraction.book][HOME - Generated from Data_ExtractionFP.fm in Data_Extraction.book][](#chmtopic1)</td></tr>
</table>

<table><tr><th colspan="5" valign="bottom"><a name="chmtopic38"></a>User's Guide Data Extraction </th><th valign="bottom">![Version]</th></tr>
<tr><td colspan="6"></td></tr>
<tr><td valign="top">![Previous Page][ ](#chmtopic47)</td><td valign="top">![Next Page][ ](#chmtopic22)</td><td colspan="4" valign="top"><b>Chapter: [Structure Keywords](#chmtopic9)</b> </td></tr>
</table>

<a name="chmbookmark439"></a>**5.2.2 General Part Data** 

<table><tr><th colspan="8" valign="top"><a name="chmbookmark440"></a><b>Keyword</b> </th><th valign="top"><a name="chmbookmark441"></a><b>Explanation</b> </th><th colspan="2" valign="top"><a name="chmbookmark442"></a><b>Output</b> </th></tr>
<tr><td valign="top"><a name="chmbookmark443"></a><b>1</b> </td><td valign="top"><a name="chmbookmark444"></a><b>2</b> </td><td valign="top"><a name="chmbookmark445"></a><b>3</b> </td><td valign="top"><a name="chmbookmark446"></a><b>4</b> </td><td valign="top"><a name="chmbookmark447"></a><b>5</b> </td><td colspan="3" valign="top"><a name="chmbookmark448"></a><b>6</b> </td><td colspan="2" valign="top"></td><td valign="top"></td></tr>
<tr><td colspan="4" valign="top"></td><td colspan="4" valign="top"><a name="chmbookmark449"></a><a name="chmbookmark450"></a>.POS[ITION_NUMBER]</td><td colspan="2" valign="top"><a name="chmbookmark451"></a>Part position number </td><td valign="top"><a name="chmbookmark452"></a>s </td></tr>
<tr><td colspan="4" valign="top"></td><td colspan="4" valign="top">.TYP[E]</td><td colspan="2" valign="top"><a name="chmbookmark453"></a>Structure component type </td><td valign="top"><a name="chmbookmark454"></a>s </td></tr>
<tr><td colspan="4" valign="top"></td><td colspan="4" valign="top">.COMP_N[AME]</td><td colspan="2" valign="top"><a name="chmbookmark455"></a>Component name </td><td valign="top"><a name="chmbookmark456"></a>s </td></tr>
<tr><td colspan="4" valign="top"></td><td colspan="4" valign="top">.AREA</td><td colspan="2" valign="top"><a name="chmbookmark457"></a>For plates and holes area </td><td valign="top"><a name="chmbookmark458"></a>r </td></tr>
<tr><td colspan="4" valign="top"></td><td colspan="4" valign="top">.WEI[GHT]</td><td colspan="2" valign="top"><a name="chmbookmark459"></a>Part weight </td><td valign="top"><a name="chmbookmark460"></a>r </td></tr>
<tr><td colspan="4" valign="top"></td><td colspan="4" valign="top">.COG</td><td colspan="2" valign="top"><a name="chmbookmark461"></a>Centre Of Gravity </td><td valign="top"><a name="chmbookmark462"></a>3*r </td></tr>
<tr><td colspan="4" valign="top"></td><td colspan="4" valign="top">.ASS[EMBLY] </td><td colspan="2" valign="top"><a name="chmbookmark463"></a>Assembly reference name </td><td valign="top"><a name="chmbookmark464"></a>s </td></tr>
<tr><td colspan="4" valign="top"></td><td colspan="4" valign="top">.POI[NT]</td><td colspan="2" valign="top"><a name="chmbookmark465"></a>Node point </td><td valign="top"><a name="chmbookmark466"></a>3*r </td></tr>
<tr><td colspan="4" valign="top"></td><td colspan="4" valign="top">.ROU[TE_VECTOR]</td><td colspan="2" valign="top"><a name="chmbookmark467"></a>Route vector </td><td valign="top"><a name="chmbookmark468"></a>3*r </td></tr>
<tr><td colspan="4" valign="top"></td><td colspan="4" valign="top">.ROT[ATION_VECTOR]</td><td colspan="2" valign="top"><a name="chmbookmark469"></a>Rotation vector </td><td valign="top"><a name="chmbookmark470"></a>3*r </td></tr>
<tr><td colspan="4" valign="top"></td><td colspan="4" valign="top">.PART[_ID]</td><td colspan="2" valign="top"><a name="chmbookmark471"></a>Part id </td><td valign="top"><a name="chmbookmark472"></a>i </td></tr>
<tr><td colspan="4" valign="top"></td><td colspan="4" valign="top">.BAR</td><td colspan="2" valign="top"><a name="chmbookmark473"></a>Bar data </td><td valign="top"><a name="chmbookmark474"></a>- </td></tr>
<tr><td colspan="5" valign="top"></td><td colspan="3" valign="top">.DES[IGNATION]</td><td colspan="2" valign="top"><a name="chmbookmark475"></a>Type of bar </td><td valign="top"><a name="chmbookmark476"></a>s </td></tr>
<tr><td colspan="5" valign="top"></td><td colspan="3" valign="top">.LEN[GTH]</td><td colspan="2" valign="top"><a name="chmbookmark477"></a>Bar length </td><td valign="top"><a name="chmbookmark478"></a>r </td></tr>
<tr><td colspan="5" valign="top"></td><td colspan="3" valign="top">.A</td><td colspan="2" valign="top"><a name="chmbookmark479"></a>Cross-section dimension </td><td valign="top"><a name="chmbookmark480"></a>r </td></tr>
<tr><td colspan="5" valign="top"></td><td colspan="3" valign="top">.B</td><td colspan="2" valign="top"><a name="chmbookmark481"></a>Cross-section dimension </td><td valign="top"><a name="chmbookmark482"></a>r </td></tr>
<tr><td colspan="5" valign="top"></td><td colspan="3" valign="top">.S</td><td colspan="2" valign="top"><a name="chmbookmark483"></a>Cross-section dimension </td><td valign="top"><a name="chmbookmark484"></a>r </td></tr>
<tr><td colspan="5" valign="top"></td><td colspan="3" valign="top">.T</td><td colspan="2" valign="top"><a name="chmbookmark485"></a>Cross-section dimension </td><td valign="top"><a name="chmbookmark486"></a>r </td></tr>
<tr><td colspan="5" valign="top"></td><td colspan="3" valign="top">.C</td><td colspan="2" valign="top"><a name="chmbookmark487"></a>Cross-section dimension </td><td valign="top"><a name="chmbookmark488"></a>r </td></tr>
<tr><td colspan="5" valign="top"></td><td colspan="3" valign="top">.U</td><td colspan="2" valign="top"><a name="chmbookmark489"></a>Cross-section dimension </td><td valign="top"><a name="chmbookmark490"></a>r </td></tr>
<tr><td colspan="5" valign="top"></td><td colspan="3" valign="top">.MTRL[_LENGTH]</td><td colspan="2" valign="top"><a name="chmbookmark491"></a>Bar material length </td><td valign="top"><a name="chmbookmark492"></a>r </td></tr>
<tr><td colspan="5" valign="top"></td><td colspan="3" valign="top">.MAT[ERIAL_VECTOR</td><td colspan="2" valign="top"><a name="chmbookmark493"></a>Material vector </td><td valign="top"><a name="chmbookmark494"></a>3*r </td></tr>
<tr><td colspan="5" valign="top"></td><td colspan="3" valign="top">.END(i), i=1,2</td><td colspan="2" valign="top"><a name="chmbookmark495"></a>Bar end </td><td valign="top"><a name="chmbookmark496"></a>- </td></tr>
<tr><td colspan="5" valign="top"></td><td colspan="3" valign="top">.END.POI</td><td colspan="2" valign="top"><a name="chmbookmark497"></a>End point </td><td valign="top"><a name="chmbookmark498"></a>3*r </td></tr>
<tr><td colspan="5" valign="top"></td><td colspan="3" valign="top">.END.CUT</td><td colspan="2" valign="top"><a name="chmbookmark499"></a>End cut data </td><td valign="top"><a name="chmbookmark500"></a>- </td></tr>
<tr><td colspan="5" valign="top"></td><td colspan="3" valign="top">.END.CUT.TYP[E_CODE ]</td><td colspan="2" valign="top"><a name="chmbookmark501"></a>Type code </td><td valign="top"><a name="chmbookmark502"></a>i </td></tr>
<tr><td colspan="5" valign="top"></td><td colspan="3" valign="top">.END.CUT.NPA[RAMETER]</td><td colspan="2" valign="top"><a name="chmbookmark503"></a>Number of parameters </td><td valign="top"><a name="chmbookmark504"></a>i </td></tr>
<tr><td colspan="5" valign="top"></td><td colspan="3" valign="top">.END.CUT.PAR[AMETER]</td><td colspan="2" valign="top"><a name="chmbookmark505"></a>Parameters </td><td valign="top"><a name="chmbookmark506"></a>npar*r </td></tr>
<tr><td colspan="5" valign="top"></td><td colspan="3" valign="top">.STA[RT_POINT]</td><td colspan="2" valign="top"><a name="chmbookmark507"></a>Start point of contour for bent profile </td><td valign="top"><a name="chmbookmark508"></a>2*r </td></tr>
<tr><td colspan="5" valign="top"></td><td colspan="3" valign="top">.NSEG[MENT]</td><td colspan="2" valign="top"><a name="chmbookmark509"></a>Number of segments </td><td valign="top"><a name="chmbookmark510"></a>i </td></tr>
<tr><td colspan="5" valign="top"></td><td colspan="5" valign="top">.SEG[MENT](i),  i=1,NSEG</td><td valign="top"></td></tr>
<tr><td colspan="8" valign="top"></td><td colspan="2" valign="top"><a name="chmbookmark511"></a><a name="chmbookmark512"></a>Segment reference by index </td><td valign="top"></td></tr>
<tr><td colspan="6" valign="top"></td><td colspan="2" valign="top"><a name="chmbookmark513"></a>.AMP[LITUDE]</td><td colspan="2" valign="top"><a name="chmbookmark514"></a>Amplitude </td><td valign="top"><a name="chmbookmark515"></a>r </td></tr>
<tr><td colspan="6" valign="top"></td><td colspan="2" valign="top">.END_[POINT]</td><td colspan="2" valign="top"><a name="chmbookmark516"></a>Segment end point </td><td valign="top"><a name="chmbookmark517"></a>r </td></tr>
<tr><td colspan="4" valign="top"></td><td colspan="4" valign="top">.PLA[TE]</td><td colspan="2" valign="top"><a name="chmbookmark518"></a>Plate data </td><td valign="top"><a name="chmbookmark519"></a>- </td></tr>
<tr><td colspan="5" valign="top"></td><td colspan="3" valign="top">.THI[CKNESS]</td><td colspan="2" valign="top"><a name="chmbookmark520"></a>Plate thickness </td><td valign="top"><a name="chmbookmark521"></a>r </td></tr>
<tr><td colspan="5" valign="top"></td><td colspan="3" valign="top">.LE[NGTH]</td><td colspan="2" valign="top"><a name="chmbookmark522"></a>Length of material </td><td valign="top"><a name="chmbookmark523"></a>r </td></tr>
<tr><td colspan="5" valign="top"></td><td colspan="3" valign="top">.WI[DTH]</td><td colspan="2" valign="top"><a name="chmbookmark524"></a>Width of material </td><td valign="top"><a name="chmbookmark525"></a>r </td></tr>
<tr><td colspan="5" valign="top"></td><td colspan="3" valign="top">.POI[NT]</td><td colspan="2" valign="top"><a name="chmbookmark526"></a>Reference point for geometry </td><td valign="top"><a name="chmbookmark527"></a>3*r </td></tr>
<tr><td colspan="5" valign="top"></td><td colspan="3" valign="top">.MAT[ERIAL_VECTOR]</td><td colspan="2" valign="top"><a name="chmbookmark528"></a>Material vector, perpendicular </td><td valign="top"><a name="chmbookmark529"></a>3*r </td></tr>
<tr><td colspan="5" valign="top"></td><td colspan="3" valign="top">.ROT[ATION_VECTOR]</td><td colspan="2" valign="top"><a name="chmbookmark530"></a>Rotation of plate contour </td><td valign="top"><a name="chmbookmark531"></a>3*r </td></tr>
<tr><td colspan="5" valign="top"></td><td colspan="3" valign="top">.STA[RT_POINT]</td><td colspan="2" valign="top"><a name="chmbookmark532"></a>Plate contour start point </td><td valign="top"><a name="chmbookmark533"></a>2*r </td></tr>
<tr><td colspan="5" valign="top"></td><td colspan="3" valign="top">.NSEG[MENT]</td><td colspan="2" valign="top"><a name="chmbookmark534"></a>Number of segments </td><td valign="top"><a name="chmbookmark535"></a>i </td></tr>
<tr><td colspan="5" valign="top"></td><td colspan="5" valign="top"><a name="chmbookmark536"></a>.SEG[MENT](i),  i=1,NSEG</td><td valign="top"></td></tr>
<tr><td colspan="8" valign="top"></td><td colspan="2" valign="top"><a name="chmbookmark537"></a><a name="chmbookmark538"></a><a name="chmbookmark539"></a>Segment reference by index </td><td valign="top"></td></tr>
<tr><td colspan="6" valign="top"></td><td colspan="2" valign="top"><a name="chmbookmark540"></a>.AMP[LITUDE]</td><td colspan="2" valign="top"><a name="chmbookmark541"></a>Amplitude </td><td valign="top"><a name="chmbookmark542"></a>r </td></tr>
<tr><td colspan="6" valign="top"></td><td colspan="2" valign="top">.END_[POINT]</td><td colspan="2" valign="top"><a name="chmbookmark543"></a>Segment end point </td><td valign="top"><a name="chmbookmark544"></a>r </td></tr>
<tr><td colspan="5" valign="top"></td><td colspan="3" valign="top">.RAD[II]</td><td colspan="2" valign="top"><a name="chmbookmark545"></a>Bending radii for a bent plate </td><td valign="top"><a name="chmbookmark546"></a>r </td></tr>
<tr><td colspan="4" valign="top"></td><td colspan="4" valign="top">.HOLE_S[TANDARD]</td><td colspan="2" valign="top"><a name="chmbookmark547"></a>Standard hole data </td><td valign="top"><a name="chmbookmark548"></a>- </td></tr>
<tr><td colspan="5" valign="top"></td><td colspan="3" valign="top">.DES[IGNATION]</td><td colspan="2" valign="top"><a name="chmbookmark549"></a>Hole type </td><td valign="top"><a name="chmbookmark550"></a>s </td></tr>
<tr><td colspan="5" valign="top"></td><td colspan="3" valign="top">.A</td><td colspan="2" valign="top"><a name="chmbookmark551"></a>Hole length </td><td valign="top"><a name="chmbookmark552"></a>r </td></tr>
<tr><td colspan="5" valign="top"></td><td colspan="3" valign="top">.B</td><td colspan="2" valign="top"><a name="chmbookmark553"></a>Hole width </td><td valign="top"><a name="chmbookmark554"></a>r </td></tr>
<tr><td colspan="5" valign="top"></td><td colspan="3" valign="top">.R</td><td colspan="2" valign="top"><a name="chmbookmark555"></a>Hole corner radius </td><td valign="top"><a name="chmbookmark556"></a>r </td></tr>
<tr><td colspan="5" valign="top"></td><td colspan="3" valign="top">.POI[NT]</td><td colspan="2" valign="top"><a name="chmbookmark557"></a>Ref. point for geometry </td><td valign="top"><a name="chmbookmark558"></a>3*r </td></tr>
<tr><td colspan="5" valign="top"></td><td colspan="3" valign="top">.MAT[ERIAL_VECTOR]</td><td colspan="2" valign="top"><a name="chmbookmark559"></a>Material vector, perpendicular </td><td valign="top"><a name="chmbookmark560"></a>3*r </td></tr>
<tr><td colspan="5" valign="top"></td><td colspan="3" valign="top">.ROT[ATION_VECTOR]</td><td colspan="2" valign="top"><a name="chmbookmark561"></a>Rotation of hole </td><td valign="top"><a name="chmbookmark562"></a>3*r </td></tr>
<tr><td colspan="4" valign="top"></td><td colspan="4" valign="top">.HOLE_G[ENERAL]</td><td colspan="2" valign="top"><a name="chmbookmark563"></a>General hole. </td><td valign="top"><a name="chmbookmark564"></a>- </td></tr>
<tr><td colspan="5" valign="top"></td><td colspan="3" valign="top">.POI[NT]</td><td colspan="2" valign="top"><a name="chmbookmark565"></a>Ref. point for geometry </td><td valign="top"><a name="chmbookmark566"></a>3r </td></tr>
<tr><td colspan="5" valign="top"></td><td colspan="3" valign="top">.MAT[ERIAL_VECTOR]</td><td colspan="2" valign="top"><a name="chmbookmark567"></a>Material vector, perpendicular </td><td valign="top"><a name="chmbookmark568"></a>3r </td></tr>
<tr><td colspan="5" valign="top"></td><td colspan="3" valign="top">.ROT[ATION_VECTOR]</td><td colspan="2" valign="top"><a name="chmbookmark569"></a>Rotation of hole </td><td valign="top"><a name="chmbookmark570"></a>3r </td></tr>
<tr><td colspan="5" valign="top"></td><td colspan="3" valign="top">.NSEG[MENT]</td><td colspan="2" valign="top"><a name="chmbookmark571"></a>Number of segments </td><td valign="top"><a name="chmbookmark572"></a>i </td></tr>
<tr><td colspan="5" valign="top"></td><td colspan="5" valign="top">.SEG[MENT](i),i=1,NSEG</td><td valign="top"></td></tr>
<tr><td colspan="8" valign="top"></td><td colspan="2" valign="top"><a name="chmbookmark573"></a><a name="chmbookmark574"></a>Segment reference by index. </td><td valign="top"><a name="chmbookmark575"></a>- </td></tr>
<tr><td colspan="5" valign="top"></td><td colspan="3" valign="top">.AMP[LITUDE]</td><td colspan="2" valign="top"><a name="chmbookmark576"></a>Amplitude </td><td valign="top"><a name="chmbookmark577"></a>r </td></tr>
<tr><td colspan="5" valign="top"></td><td colspan="3" valign="top">.END_[POINT]</td><td colspan="2" valign="top"><a name="chmbookmark578"></a>Segment end point </td><td valign="top"><a name="chmbookmark579"></a>2r </td></tr>
<tr><td colspan="4" valign="top"></td><td colspan="4" valign="top">.NUSER_ATT[RIBUTE]</td><td colspan="2" valign="top"><a name="chmbookmark580"></a>Number of user attributes </td><td valign="top"><a name="chmbookmark581"></a>i </td></tr>
<tr><td colspan="4" valign="top"></td><td colspan="4" valign="top">.USER_ATT[RIBUTE](i),</td><td colspan="2" valign="top"><a name="chmbookmark582"></a>See [Information from a Drawing in Chapter Drafting Keywords](#chmbookmark438)</td><td valign="top"></td></tr>
</table>

<table><tr><th valign="bottom"><a name="chmbookmark583"></a>![Previous Page][ ](#chmtopic47)</th><th valign="bottom">![Next Page][ ](#chmtopic22)</th><th> </th><th> </th><th></th></tr>
<tr><td colspan="5"></td></tr>
<tr><td colspan="4">Copyright © 1993-2005 AVEVA AB </td><td>![HOME - Generated from Data_Extraction_structure.fm in Data_Extraction.book][HOME - Generated from Data_ExtractionFP.fm in Data_Extraction.book][](#chmtopic1)</td></tr>
</table>

<table><tr><th colspan="5" valign="bottom"><a name="chmtopic39"></a>User's Guide Data Extraction </th><th valign="bottom">![Version]</th></tr>
<tr><td colspan="6"></td></tr>
<tr><td valign="top">![Previous Page][ ](#chmtopic25)</td><td valign="top">![Next Page][ ](#chmtopic48)</td><td colspan="4" valign="top"><b>Chapter: [Cable Keywords](#chmtopic11)</b> </td></tr>
</table>

<a name="chmbookmark584"></a>**7.2 Information from a Cable Object** 

<table><tr><th colspan="8" valign="top"><a name="chmbookmark585"></a><a name="chmbookmark586"></a><b>Keyword</b></th><th valign="top"><a name="chmbookmark587"></a><b>Explanation</b> </th><th colspan="2" valign="top"><a name="chmbookmark588"></a><b>Output</b> </th></tr>
<tr><td valign="top"><a name="chmbookmark589"></a><b>1</b> </td><td valign="top"><a name="chmbookmark590"></a><b>2</b> </td><td valign="top"><a name="chmbookmark591"></a><b>3</b> </td><td valign="top"><a name="chmbookmark592"></a><b>4</b> </td><td valign="top"><a name="chmbookmark593"></a><b>5</b> </td><td valign="top"><a name="chmbookmark594"></a><b>6</b> </td><td colspan="2" valign="top"></td><td colspan="2" valign="top"></td><td valign="top"></td></tr>
<tr><td colspan="6" valign="top"><a name="chmbookmark595"></a><a name="chmbookmark596"></a><a name="chmbookmark597"></a>CAB[LE][(s)],</td><td colspan="2" valign="top">s=<Project></td><td colspan="2" valign="top"><a name="chmbookmark598"></a>Cable application. </td><td valign="top"><a name="chmbookmark599"></a>- </td></tr>
<tr><td valign="top"></td><td colspan="7" valign="top">.CAB_M[OD](s),  s=<System-Name></td><td colspan="2" valign="top"><a name="chmbookmark600"></a>Cable name. </td><td valign="top"><a name="chmbookmark601"></a>- </td></tr>
<tr><td colspan="2" valign="top"></td><td colspan="6" valign="top">.NAME</td><td colspan="2" valign="top"><a name="chmbookmark602"></a>Cable. </td><td valign="top"><a name="chmbookmark603"></a>s </td></tr>
<tr><td colspan="2" valign="top"></td><td colspan="6" valign="top">.CABLE_A[LIAS]</td><td colspan="2" valign="top"><a name="chmbookmark604"></a>Cable alias. See the documentation for the CAB_ALIAS and CAB_ALIAS_REPLACE keywords in the cable default file.</td><td valign="top"><a name="chmbookmark605"></a>s </td></tr>
<tr><td colspan="2" valign="top"></td><td colspan="6" valign="top">.COMP_N[AME]</td><td colspan="2" valign="top"><a name="chmbookmark606"></a>Name of cable component. </td><td valign="top"><a name="chmbookmark607"></a>s </td></tr>
<tr><td colspan="2" valign="top"></td><td colspan="6" valign="top">.COMP_T[YPE]</td><td colspan="2" valign="top"><a name="chmbookmark608"></a>Component type. </td><td valign="top"><a name="chmbookmark609"></a>I </td></tr>
<tr><td colspan="2" valign="top"></td><td colspan="6" valign="top">.ASS[EMBLY]</td><td colspan="2" valign="top"><a name="chmbookmark610"></a>Assembly reference. </td><td valign="top"><a name="chmbookmark611"></a>s </td></tr>
<tr><td colspan="2" valign="top"></td><td colspan="6" valign="top">.DES_STA[TUS]</td><td colspan="2" valign="top"><p><a name="chmbookmark612"></a>Design status </p><p>0 = not ready </p><p>1 = not ready </p><p>2 and 3 = route ready and confirmed </p><p>4 and 5 = route ready but not confirmed </p></td><td valign="top"><a name="chmbookmark613"></a>I </td></tr>
<tr><td colspan="2" valign="top"></td><td colspan="6" valign="top">.PRD_STA[TUS]</td><td colspan="2" valign="top"><a name="chmbookmark614"></a>Production status. </td><td valign="top"><a name="chmbookmark615"></a>I </td></tr>
<tr><td colspan="8" valign="top"></td><td colspan="2" valign="top"><a name="chmbookmark616"></a>(see <i>appendix</i>). </td><td valign="top"></td></tr>
<tr><td colspan="2" valign="top"></td><td colspan="6" valign="top"><a name="chmbookmark617"></a>.REA[DY]</td><td colspan="2" valign="top"><p><a name="chmbookmark618"></a>User defined ready </p><p>1 = YES </p><p>0 = NO </p></td><td valign="top"><a name="chmbookmark619"></a>I </td></tr>
<tr><td colspan="2" valign="top"></td><td colspan="6" valign="top">.PDI_T[RANSFERED]</td><td colspan="2" valign="top"><p><a name="chmbookmark620"></a>Object transferred to PDI </p><p>1 = YES </p><p>0 = NO </p></td><td valign="top"><a name="chmbookmark621"></a>I </td></tr>
<tr><td colspan="2" valign="top"></td><td colspan="6" valign="top">.PDI_N[EEDS_RETRANSFER]</td><td colspan="2" valign="top"><p><a name="chmbookmark622"></a>Object updated after transfer </p><p>1 = YES </p><p>0 = NO </p></td><td valign="top"><a name="chmbookmark623"></a>I </td></tr>
<tr><td colspan="2" valign="top"></td><td colspan="6" valign="top">.LENGTH_N[ORM]</td><td colspan="2" valign="top"><a name="chmbookmark624"></a>Length excluding excess (mm). </td><td valign="top"><a name="chmbookmark625"></a>r </td></tr>
<tr><td colspan="2" valign="top"></td><td colspan="6" valign="top">.LENGTH_T[OT]</td><td colspan="2" valign="top"><a name="chmbookmark626"></a>Length including excess (mm). </td><td valign="top"><a name="chmbookmark627"></a>r </td></tr>
<tr><td colspan="2" valign="top"></td><td colspan="6" valign="top">.LENGTH_EXC1</td><td colspan="2" valign="top"><a name="chmbookmark628"></a>Excess length 1. </td><td valign="top"><a name="chmbookmark629"></a>r </td></tr>
<tr><td colspan="2" valign="top"></td><td colspan="6" valign="top">.LENGTH_EXC2</td><td colspan="2" valign="top"><a name="chmbookmark630"></a>Excess length 2. </td><td valign="top"><a name="chmbookmark631"></a>r </td></tr>
<tr><td colspan="2" valign="top"></td><td colspan="6" valign="top">.LENGTH_EXTRA_EXC1</td><td colspan="2" valign="top"><a name="chmbookmark632"></a>Extra excess length 1. </td><td valign="top"><a name="chmbookmark633"></a>r </td></tr>
<tr><td colspan="2" valign="top"></td><td colspan="6" valign="top">.LENGTH_EXTRA_EXC2</td><td colspan="2" valign="top"><a name="chmbookmark634"></a>Extra excess length 2. </td><td valign="top"><a name="chmbookmark635"></a>r </td></tr>
<tr><td colspan="2" valign="top"></td><td colspan="6" valign="top">.WEI[GHT]</td><td colspan="2" valign="top"><a name="chmbookmark636"></a>Returns the weight in kilograms </td><td valign="top"><a name="chmbookmark637"></a>r </td></tr>
<tr><td colspan="2" valign="top"></td><td colspan="6" valign="top">.COG(Type)</td><td colspan="2" valign="top"><p><a name="chmbookmark638"></a>Returns the centre of gravity as a coordinate. </p><p><a name="chmbookmark639"></a>Type can have the following values: </p><p>0 = An approximative calculation is made as midpoint of the route end points. </p><p>1 = An exact calculation is made. Zone lengths are not considered. Excess lengths are placed at the connection points or at route end points if not connected. </p></td><td valign="top"><a name="chmbookmark640"></a>3r </td></tr>
<tr><td colspan="2" valign="top"></td><td colspan="6" valign="top">.NMARKP[OINT]</td><td colspan="2" valign="top"><a name="chmbookmark641"></a>Number of markpoints. </td><td valign="top"><a name="chmbookmark642"></a>I </td></tr>
<tr><td colspan="2" valign="top"></td><td colspan="6" valign="top">.MARKP[OINT](I),				 I=1,NMARKP</td><td colspan="2" valign="top"><a name="chmbookmark643"></a>Markpoint number. </td><td valign="top"><a name="chmbookmark644"></a>- </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.NAM[E]</td><td colspan="2" valign="top"><a name="chmbookmark645"></a>Penetration name. </td><td valign="top"><a name="chmbookmark646"></a>s </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.LENGTH_B[EF]</td><td colspan="2" valign="top"><a name="chmbookmark647"></a>Length from startpoint to markpoint (mm). </td><td valign="top"><a name="chmbookmark648"></a>r </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.LENGTH_A[FT]</td><td colspan="2" valign="top"><a name="chmbookmark649"></a>Length from markpoint to end point (mm). </td><td valign="top"><a name="chmbookmark650"></a>r </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.POI[NT]</td><td colspan="2" valign="top"><a name="chmbookmark651"></a>Coordinates for markpoint. </td><td valign="top"><a name="chmbookmark652"></a>3r </td></tr>
<tr><td colspan="2" valign="top"></td><td colspan="6" valign="top">.EL_PROP[ERTIES]</td><td colspan="2" valign="top"><a name="chmbookmark653"></a>Electrical property data. </td><td valign="top"><a name="chmbookmark654"></a>- </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.INT_C[LASS]</td><td colspan="2" valign="top"><a name="chmbookmark655"></a>Interference class. </td><td valign="top"><a name="chmbookmark656"></a>s </td></tr>
<tr><td colspan="2" valign="top"></td><td colspan="6" valign="top">.MAN_PROP[ERTIES]</td><td colspan="2" valign="top"><a name="chmbookmark657"></a>Management property data. </td><td valign="top"><a name="chmbookmark658"></a>- </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.WORKO[RDER]</td><td colspan="2" valign="top"><a name="chmbookmark659"></a>Workorder name. </td><td valign="top"><a name="chmbookmark660"></a>s </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.PLAN_U[NIT]</td><td colspan="2" valign="top"><a name="chmbookmark661"></a>Planning unit. </td><td valign="top"><a name="chmbookmark662"></a>s </td></tr>
<tr><td colspan="2" valign="top"></td><td colspan="6" valign="top">.CON[NECTION](I),				 I=1,2</td><td colspan="2" valign="top"><a name="chmbookmark663"></a>Connections. </td><td valign="top"><a name="chmbookmark664"></a>- </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.STAT[US]</td><td colspan="2" valign="top"><a name="chmbookmark665"></a>Status (see <i>appendix</i>). </td><td valign="top"></td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top"><a name="chmbookmark666"></a>.POI[NT]</td><td colspan="2" valign="top"><a name="chmbookmark667"></a>Coordinates for connection. </td><td valign="top"><a name="chmbookmark668"></a>3r </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.PRO[JECT]</td><td colspan="2" valign="top"><a name="chmbookmark669"></a>Equipment project name. </td><td valign="top"><a name="chmbookmark670"></a>s </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.EQUIP[MENT]</td><td colspan="2" valign="top"><a name="chmbookmark671"></a>Equipment name. </td><td valign="top"><a name="chmbookmark672"></a>s </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.PREL_ELC_NAME</td><td colspan="2" valign="top"><a name="chmbookmark673"></a>Name of main connection, if preliminary. </td><td valign="top"><a name="chmbookmark674"></a>s </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.PREL_ELC_ALIAS</td><td colspan="2" valign="top"><a name="chmbookmark675"></a>Name of alias for connection. if prel. </td><td valign="top"><a name="chmbookmark676"></a>s </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.ALL_ELC_NAME</td><td colspan="2" valign="top"><a name="chmbookmark677"></a>Name of main connection, regardless of preliminary. </td><td valign="top"><a name="chmbookmark678"></a>s </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.ALL_ELC_ALIAS</td><td colspan="2" valign="top"><a name="chmbookmark679"></a>Name of alias for connection, regardless of preliminary. </td><td valign="top"><a name="chmbookmark680"></a>s </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.NCOR[E]</td><td colspan="2" valign="top"><a name="chmbookmark681"></a>Number of cores. </td><td valign="top"><a name="chmbookmark682"></a>I </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="4" valign="top">.CORE(j),</td><td valign="top">` `j=1,NCOR</td><td colspan="2" valign="top"><a name="chmbookmark683"></a>Core number. </td><td valign="top"><a name="chmbookmark684"></a>- </td></tr>
<tr><td colspan="4" valign="top"></td><td colspan="4" valign="top">.ELC_N[AME]</td><td colspan="2" valign="top"><a name="chmbookmark685"></a>Main connection name. </td><td valign="top"><a name="chmbookmark686"></a>s </td></tr>
<tr><td colspan="4" valign="top"></td><td colspan="4" valign="top">.CORE_N[AME]</td><td colspan="2" valign="top"><a name="chmbookmark687"></a>Core connection name. </td><td valign="top"><a name="chmbookmark688"></a>s </td></tr>
<tr><td colspan="4" valign="top"></td><td colspan="4" valign="top">.ELC_R[EFNO]</td><td colspan="2" valign="top"><a name="chmbookmark689"></a>Main connection number. </td><td valign="top"><a name="chmbookmark690"></a>I </td></tr>
<tr><td colspan="4" valign="top"></td><td colspan="4" valign="top">.CORE.R[EFNO]</td><td colspan="2" valign="top"><a name="chmbookmark691"></a>Core connection number. </td><td valign="top"><a name="chmbookmark692"></a>I </td></tr>
<tr><td colspan="2" valign="top"></td><td colspan="6" valign="top">.NUSER_ATT[RIBUTE]</td><td colspan="2" valign="top"><a name="chmbookmark693"></a>Number of user attributes </td><td valign="top"><a name="chmbookmark694"></a>i </td></tr>
<tr><td colspan="2" valign="top"></td><td colspan="6" valign="top">.USER_ATT[RIBUTE](i),</td><td colspan="2" valign="top"><a name="chmbookmark695"></a>See [Information from a Drawing in Chapter Drafting Keywords](#chmbookmark438)</td><td valign="top"></td></tr>
<tr><td colspan="2" valign="top"></td><td colspan="6" valign="top"><a name="chmbookmark696"></a>.NROUTE_N[ODE]</td><td colspan="2" valign="top"><a name="chmbookmark697"></a>Number of route nodes. </td><td valign="top"><a name="chmbookmark698"></a>I </td></tr>
<tr><td colspan="2" valign="top"></td><td colspan="6" valign="top">	.ROUTE_N[ODE](I), I=1,  NROUTE_NODE</td><td colspan="2" valign="top"><a name="chmbookmark699"></a>Route node number </td><td valign="top"><a name="chmbookmark700"></a>- </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.NAM[E]</td><td colspan="2" valign="top"><a name="chmbookmark701"></a>Name of route node (penetration) </td><td valign="top"><a name="chmbookmark702"></a>s </td></tr>
<tr><td colspan="2" valign="top"></td><td colspan="6" valign="top">.NLENGT_Z[ONE]</td><td colspan="2" valign="top"><a name="chmbookmark703"></a>Number of length zones </td><td valign="top"><a name="chmbookmark704"></a>I </td></tr>
<tr><td colspan="2" valign="top"></td><td colspan="6" valign="top">.LENGTH_Z[ONE](I),  I=NLENGTH_ZONE</td><td colspan="2" valign="top"><a name="chmbookmark705"></a>Length zone number </td><td valign="top"><a name="chmbookmark706"></a>- </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.NAM[E]</td><td colspan="2" valign="top"><a name="chmbookmark707"></a>Name of length zone </td><td valign="top"><a name="chmbookmark708"></a>s </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.LEN[GTH]</td><td colspan="2" valign="top"><a name="chmbookmark709"></a>Length of length zone </td><td valign="top"><a name="chmbookmark710"></a>r </td></tr>
<tr><td colspan="7" valign="top">CAB[LE][(s)], </td><td valign="top">s=<Project></td><td colspan="2" valign="top"><a name="chmbookmark711"></a>Cable application. </td><td valign="top"><a name="chmbookmark712"></a>- </td></tr>
<tr><td valign="top"></td><td colspan="6" valign="top">.CAB_M[OD](s),  </td><td valign="top">s=<System-Name></td><td colspan="2" valign="top"><a name="chmbookmark713"></a>Cable name. </td><td valign="top"><a name="chmbookmark714"></a>- </td></tr>
<tr><td colspan="2" valign="top"></td><td colspan="6" valign="top">.NBRAN[CH]</td><td colspan="2" valign="top"><a name="chmbookmark715"></a>Number of route branches. </td><td valign="top"><a name="chmbookmark716"></a>I </td></tr>
<tr><td colspan="2" valign="top"></td><td colspan="5" valign="top">.BRAN[CH](I),</td><td valign="top">I=1,NBRAN</td><td colspan="2" valign="top"><a name="chmbookmark717"></a>Branch number. </td><td valign="top"><a name="chmbookmark718"></a>- </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.NPAR[T]</td><td colspan="2" valign="top"><a name="chmbookmark719"></a>Number of parts in branch. </td><td valign="top"><a name="chmbookmark720"></a>I </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="4" valign="top">.PART(j),</td><td valign="top">` `j=1,NPAR</td><td colspan="2" valign="top"><a name="chmbookmark721"></a>Part number in branch. </td><td valign="top"><a name="chmbookmark722"></a>- </td></tr>
<tr><td colspan="4" valign="top"></td><td colspan="4" valign="top">.TYPE</td><td colspan="2" valign="top"><p><a name="chmbookmark723"></a>Type of part. </p><p>1 = cableway reference </p><p>2 = single route </p></td><td valign="top"><a name="chmbookmark724"></a>I </td></tr>
<tr><td colspan="4" valign="top"></td><td colspan="4" valign="top">.PRO[JECT]</td><td colspan="2" valign="top"><a name="chmbookmark725"></a>Cableway project name. </td><td valign="top"><a name="chmbookmark726"></a>s </td></tr>
<tr><td colspan="8" valign="top"></td><td colspan="2" valign="top"><a name="chmbookmark727"></a>if TYPE = 1 </td><td valign="top"></td></tr>
<tr><td colspan="4" valign="top"></td><td colspan="4" valign="top"><a name="chmbookmark728"></a>.CWAY</td><td colspan="2" valign="top"><a name="chmbookmark729"></a>Cable way name </td><td valign="top"><a name="chmbookmark730"></a>s </td></tr>
<tr><td colspan="8" valign="top"></td><td colspan="2" valign="top"><a name="chmbookmark731"></a>if TYPE=1 </td><td valign="top"></td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top"><a name="chmbookmark732"></a>.NPNTR_R[EAL]</td><td colspan="2" valign="top"><a name="chmbookmark733"></a>Number of real penetrations </td><td valign="top"><a name="chmbookmark734"></a>I </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.PNTR_R[EAL](I),			 I=1,NPNTR_R</td><td colspan="2" valign="top"><a name="chmbookmark735"></a>Real penetration number </td><td valign="top"><a name="chmbookmark736"></a>- </td></tr>
<tr><td colspan="4" valign="top"></td><td colspan="4" valign="top">.NAM[E]</td><td colspan="2" valign="top"><a name="chmbookmark737"></a>Penetration name </td><td valign="top"><a name="chmbookmark738"></a>s </td></tr>
<tr><td colspan="4" valign="top"></td><td colspan="4" valign="top">.POI[NT]</td><td colspan="2" valign="top"><a name="chmbookmark739"></a>Coordinates for penetration </td><td valign="top"><a name="chmbookmark740"></a>3r </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.NPNTR_I[MAG]</td><td colspan="2" valign="top"><a name="chmbookmark741"></a>Number of imaginary penetrations </td><td valign="top"><a name="chmbookmark742"></a>i </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.PNTR_I[MAG](I), 			I=1,NPNTR_I</td><td colspan="2" valign="top"><a name="chmbookmark743"></a>Imaginary penetration number </td><td valign="top"><a name="chmbookmark744"></a>- </td></tr>
<tr><td colspan="4" valign="top"></td><td colspan="4" valign="top">.NAM[E]</td><td colspan="2" valign="top"><a name="chmbookmark745"></a>Penetration name </td><td valign="top"><a name="chmbookmark746"></a>s </td></tr>
<tr><td colspan="4" valign="top"></td><td colspan="4" valign="top">.POI[NT]</td><td colspan="2" valign="top"><a name="chmbookmark747"></a>Coordinates for penetration </td><td valign="top"><a name="chmbookmark748"></a>3r </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.NPNTR_A[LL]</td><td colspan="2" valign="top"><a name="chmbookmark749"></a>Number of all penetrations </td><td valign="top"><a name="chmbookmark750"></a>I </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.PNTR_A[LL](I),			 I=1,NPNTR_A</td><td colspan="2" valign="top"><a name="chmbookmark751"></a>Penetration number </td><td valign="top"><a name="chmbookmark752"></a>- </td></tr>
<tr><td colspan="4" valign="top"></td><td colspan="4" valign="top">.NAM[E]</td><td colspan="2" valign="top"><a name="chmbookmark753"></a>Penetration name </td><td valign="top"><a name="chmbookmark754"></a>s </td></tr>
<tr><td colspan="4" valign="top"></td><td colspan="4" valign="top">.POI[NT]</td><td colspan="2" valign="top"><a name="chmbookmark755"></a>Coordinates for penetration </td><td valign="top"><a name="chmbookmark756"></a>3r </td></tr>
<tr><td colspan="2" valign="top"></td><td colspan="6" valign="top">.TDM_I[NFORMATION]</td><td colspan="2" valign="top"><a name="chmbookmark757"></a>Data management </td><td valign="top"><a name="chmbookmark758"></a>- </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.ALIAS1</td><td colspan="2" valign="top"><a name="chmbookmark759"></a>Alias information </td><td valign="top"><a name="chmbookmark760"></a>s </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.ALIAS2</td><td colspan="2" valign="top"><a name="chmbookmark761"></a>Alias information </td><td valign="top"><a name="chmbookmark762"></a>s </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.ALIAS3</td><td colspan="2" valign="top"><a name="chmbookmark763"></a>Alias information </td><td valign="top"><a name="chmbookmark764"></a>s </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.ALIAS4</td><td colspan="2" valign="top"><a name="chmbookmark765"></a>Alias information </td><td valign="top"><a name="chmbookmark766"></a>s </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.DESC[RIPTION]</td><td colspan="2" valign="top"><a name="chmbookmark767"></a>Description </td><td valign="top"><a name="chmbookmark768"></a>s </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.REM[ARKS]</td><td colspan="2" valign="top"><a name="chmbookmark769"></a>Remarks </td><td valign="top"><a name="chmbookmark770"></a>s </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.TYPE1</td><td colspan="2" valign="top"><a name="chmbookmark771"></a>Type </td><td valign="top"><a name="chmbookmark772"></a>s </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.TYPE2</td><td colspan="2" valign="top"><a name="chmbookmark773"></a>Type </td><td valign="top"><a name="chmbookmark774"></a>s </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.TYPE3</td><td colspan="2" valign="top"><a name="chmbookmark775"></a>Type </td><td valign="top"><a name="chmbookmark776"></a>s </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.TYPE4</td><td colspan="2" valign="top"><a name="chmbookmark777"></a>Type </td><td valign="top"><a name="chmbookmark778"></a>s </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.PLA[NINGUNIT]</td><td colspan="2" valign="top"><a name="chmbookmark779"></a>Planning unit </td><td valign="top"><a name="chmbookmark780"></a>s </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.COST[CODE]</td><td colspan="2" valign="top"><a name="chmbookmark781"></a>Cost code </td><td valign="top"><a name="chmbookmark782"></a>s </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.STATUS_D[ESIGN]</td><td colspan="2" valign="top"><a name="chmbookmark783"></a>Status design </td><td valign="top"><a name="chmbookmark784"></a>s </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.STATUS_MAT[ERIAL]</td><td colspan="2" valign="top"><a name="chmbookmark785"></a>Status material </td><td valign="top"><a name="chmbookmark786"></a>s </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.STATUS_MAN[UFACT]</td><td colspan="2" valign="top"><a name="chmbookmark787"></a>Status manufact </td><td valign="top"><a name="chmbookmark788"></a>s </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.STATUS_A[SSEMBLY]</td><td colspan="2" valign="top"><a name="chmbookmark789"></a>Status material </td><td valign="top"><a name="chmbookmark790"></a>s </td></tr>
<tr><td colspan="2" valign="top"></td><td colspan="6" valign="top">.TDM_R[EFERENCES]</td><td colspan="2" valign="top"><a name="chmbookmark791"></a>Drawing references </td><td valign="top"><a name="chmbookmark792"></a>- </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.NREF[ERENCE]</td><td colspan="2" valign="top"><a name="chmbookmark793"></a>Number of references </td><td valign="top"><a name="chmbookmark794"></a>i </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.REF[ERENCE]</td><td colspan="2" valign="top"><a name="chmbookmark795"></a>Drawing references </td><td valign="top"><a name="chmbookmark796"></a>s </td></tr>
<tr><td valign="top"></td><td valign="top"></td><td colspan="6" valign="top">.X_PLACE[MENT]</td><td colspan="2" valign="top"><a name="chmbookmark797"></a>Placement string </td><td valign="top"></td></tr>
<tr><td valign="top"></td><td valign="top"></td><td colspan="6" valign="top"><a name="chmbookmark798"></a>.Y_PLACE[MENT]</td><td colspan="2" valign="top"><a name="chmbookmark799"></a>Placement string </td><td valign="top"></td></tr>
<tr><td valign="top"></td><td valign="top"></td><td colspan="6" valign="top"><a name="chmbookmark800"></a>.Z_PLACE[MENT]</td><td colspan="2" valign="top"><a name="chmbookmark801"></a>Placement string </td><td valign="top"></td></tr>
</table>

<table><tr><th valign="bottom"><a name="chmbookmark802"></a>![Previous Page][ ](#chmtopic25)</th><th valign="bottom">![Next Page][ ](#chmtopic48)</th><th> </th><th> </th><th></th></tr>
<tr><td colspan="5"></td></tr>
<tr><td colspan="4">Copyright © 1993-2005 AVEVA AB </td><td>![HOME - Generated from Data_Extraction_cable.fm in Data_Extraction.book][HOME - Generated from Data_ExtractionFP.fm in Data_Extraction.book][](#chmtopic1)</td></tr>
</table>

<table><tr><th colspan="5" valign="bottom"><a name="chmtopic40"></a>User's Guide Data Extraction </th><th valign="bottom">![Version]</th></tr>
<tr><td colspan="6"></td></tr>
<tr><td valign="top">![Previous Page][ ](#chmtopic49)</td><td valign="top">![Next Page][ ](#chmtopic26)</td><td colspan="4" valign="top"><b>Chapter: [Cable Keywords](#chmtopic11)</b> </td></tr>
</table>

<a name="chmbookmark803"></a>**7.5 Cable Production Status** 

<a name="chmbookmark804"></a>The matrix below describes the CABLE.CAB\_MODEL.PRD\_STATUS output. 

A=Defined in workorder 

B=Precut 

C=Cable run 

D=Connected to equipment in end 1 

E=Connected to equipment in end 2 

<a name="chmbookmark805"></a>![](ahh2b1y4.008.png)[](#chmtopic50)
Click picture to enlarge

<table><tr><th valign="bottom">![Previous Page][ ](#chmtopic49)</th><th valign="bottom">![Next Page][ ](#chmtopic26)</th><th> </th><th> </th><th></th></tr>
<tr><td colspan="5"></td></tr>
<tr><td colspan="4">Copyright © 1993-2005 AVEVA AB </td><td>![HOME - Generated from Data_Extraction_cable.fm in Data_Extraction.book][HOME - Generated from Data_ExtractionFP.fm in Data_Extraction.book][](#chmtopic1)</td></tr>
</table>

<table><tr><th colspan="5" valign="bottom"><a name="chmtopic41"></a>User's Guide Data Extraction </th><th valign="bottom">![Version]</th></tr>
<tr><td colspan="6"></td></tr>
<tr><td valign="top">![Previous Page][ ](#chmtopic29)</td><td valign="top">![Next Page][ ](#chmtopic51)</td><td colspan="4" valign="top"><b>Chapter: [Pipe Keywords](#chmtopic13)</b> </td></tr>
</table>

<a name="chmbookmark806"></a>**9.2 Information from Pipe Model Object** 

<a name="chmbookmark807"></a>The following is an overview of keywords available for all model objects:</sup> 

![Excel document]<a name="chmbookmark808"></a>  DatExt\_Pipe\_Model\_Object.xls

<table><tr><th valign="bottom">![Previous Page][ ](#chmtopic29)</th><th valign="bottom">![Next Page][ ](#chmtopic51)</th><th> </th><th> </th><th></th></tr>
<tr><td colspan="5"></td></tr>
<tr><td colspan="4">Copyright © 1993-2005 AVEVA AB </td><td>![HOME - Generated from Data_Extraction_pipe.FM in Data_Extraction.book][HOME - Generated from Data_ExtractionFP.fm in Data_Extraction.book][](#chmtopic1)</td></tr>
</table>

<table><tr><th colspan="5" valign="bottom"><a name="chmtopic42"></a>User's Guide Data Extraction </th><th valign="bottom">![Version]</th></tr>
<tr><td colspan="6"></td></tr>
<tr><td valign="top">![Previous Page][ ](#chmtopic52)</td><td valign="top">![Next Page][ ](#chmtopic30)</td><td colspan="4" valign="top"><b>Chapter: [Pipe Keywords](#chmtopic13)</b> </td></tr>
</table>

<a name="chmbookmark809"></a>**9.6 Information from Specification** 

<a name="chmbookmark810"></a>Keywords available for specification objects are shown in the following overview:</sup> 

![Excel document]<a name="chmbookmark811"></a>  DatExt\_Pipe\_Specification.xls

<table><tr><th valign="bottom">![Previous Page][ ](#chmtopic52)</th><th valign="bottom">![Next Page][ ](#chmtopic30)</th><th> </th><th> </th><th></th></tr>
<tr><td colspan="5"></td></tr>
<tr><td colspan="4">Copyright © 1993-2005 AVEVA AB </td><td>![HOME - Generated from Data_Extraction_pipe.FM in Data_Extraction.book][HOME - Generated from Data_ExtractionFP.fm in Data_Extraction.book][](#chmtopic1)</td></tr>
</table>

<table><tr><th colspan="5" valign="bottom"><a name="chmtopic43"></a>User's Guide Data Extraction </th><th valign="bottom">![Version]</th></tr>
<tr><td colspan="6"></td></tr>
<tr><td valign="top">![Previous Page][ ](#chmtopic31)</td><td valign="top">![Next Page][ ](#chmtopic53)</td><td colspan="4" valign="top"><b>Chapter: [Ventilation Keywords](#chmtopic14)</b> </td></tr>
</table>

<a name="chmbookmark812"></a>**10.2 Information from Ventilation Model Object** 

<a name="chmbookmark813"></a>The following is an overview of keywords available for all ventilation model objects:</sup> 

![Excel document]<a name="chmbookmark814"></a>  DatExt\_Ventilation.xls


<table><tr><th valign="bottom"><a name="chmbookmark815"></a><a name="chmbookmark816"></a>![Previous Page][ ](#chmtopic31)</th><th valign="bottom">![Next Page][ ](#chmtopic53)</th><th> </th><th> </th><th></th></tr>
<tr><td colspan="5"></td></tr>
<tr><td colspan="4">Copyright © 1993-2005 AVEVA AB </td><td>![HOME - Generated from Data_Extraction_ventilation.FM in Data_Extraction.book][HOME - Generated from Data_ExtractionFP.fm in Data_Extraction.book][](#chmtopic1)</td></tr>
</table>

<table><tr><th colspan="5" valign="bottom"><a name="chmtopic44"></a>User's Guide Data Extraction </th><th valign="bottom">![Version]</th></tr>
<tr><td colspan="6"></td></tr>
<tr><td valign="top">![Previous Page][ ](#chmtopic32)</td><td valign="top">![Next Page][ ](#chmtopic54)</td><td colspan="4" valign="top"><b>Chapter: [Basic Data Extraction](#chmtopic2)</b> </td></tr>
</table>

<a name="chmbookmark817"></a>**1.2.3 Examples** 

<table><tr><th colspan="3" valign="top">EQUIP('TB').ITEM('EQUIP1').PIPE.NCONNECTION</th></tr>
<tr><td valign="top"><a name="chmbookmark818"></a>string arg </td><td valign="top"><a name="chmbookmark819"></a>string arg </td><td valign="top"></td></tr>
<tr><td colspan="3" valign="top"><a name="chmbookmark820"></a>EQUIP('TB').ITEM('EQUIP'*).PIPE.NCONNECTION</td></tr>
<tr><td valign="top"><a name="chmbookmark821"></a>string arg </td><td valign="top"><a name="chmbookmark822"></a>string arg </td><td valign="top"></td></tr>
<tr><td valign="top"></td><td valign="top"><a name="chmbookmark823"></a><a name="chmbookmark824"></a><a name="chmbookmark825"></a>with wildcard </td><td valign="top"></td></tr>
<tr><td colspan="3" valign="top"><a name="chmbookmark826"></a>EQUIP('TB').ITEM('EQUIP'*).DRAWING(1,3,5:10).NAME</td></tr>
<tr><td valign="top"><a name="chmbookmark827"></a>string arg </td><td valign="top"><a name="chmbookmark828"></a>string arg </td><td valign="top"><a name="chmbookmark829"></a>integer arg with </td></tr>
<tr><td valign="top"></td><td valign="top"><a name="chmbookmark830"></a><a name="chmbookmark831"></a>with wildcard </td><td valign="top"><a name="chmbookmark832"></a>interval and repetition </td></tr>
<tr><td colspan="3" valign="top">PIPE('TB').PIPMODEL('230X-TB').CONNECTION(1:5).PART</td></tr>
<tr><td valign="top"><a name="chmbookmark833"></a>string arg </td><td valign="top"><a name="chmbookmark834"></a>string arg </td><td valign="top"><a name="chmbookmark835"></a>integer arg </td></tr>
<tr><td valign="top"></td><td valign="top"></td><td valign="top"><a name="chmbookmark836"></a><a name="chmbookmark837"></a><a name="chmbookmark838"></a>with interval </td></tr>
<tr><td colspan="3" valign="top">COMP('COMP1':'COMP2'*).MAN_PROPERTY.CREATION_DATE</td></tr>
<tr><td colspan="3" valign="top"><a name="chmbookmark839"></a>string arg with wildcards </td></tr>
<tr><td colspan="3" valign="top"><a name="chmbookmark840"></a>(range) and interval </td></tr>
</table>

<table><tr><th valign="bottom">![Previous Page][ ](#chmtopic32)</th><th valign="bottom">![Next Page][ ](#chmtopic54)</th><th> </th><th> </th><th></th></tr>
<tr><td colspan="5"></td></tr>
<tr><td colspan="4">Copyright © 1993-2005 AVEVA AB </td><td>![HOME - Generated from Data_Extraction_basic.fm in Data_Extraction.book][HOME - Generated from Data_ExtractionFP.fm in Data_Extraction.book][](#chmtopic1)</td></tr>
</table>

<table><tr><th colspan="5" valign="bottom"><a name="chmtopic45"></a>User's Guide Data Extraction </th><th valign="bottom">![Version]</th></tr>
<tr><td colspan="6"></td></tr>
<tr><td valign="top">![Previous Page][ ](#chmtopic55)</td><td valign="top">![Next Page][ ](#chmtopic33)</td><td colspan="4" valign="top"><b>Chapter: [Basic Data Extraction](#chmtopic2)</b> </td></tr>
</table>

<a name="chmbookmark841"></a>**1.5.2 Example in Data Extraction** 

<a name="chmbookmark842"></a>The macro in the example file below will extract and list the endcut parameters for all panels beginning with 'TB172-'. Note that the endcut parameters will be stored in an n-dimensional array of real elements (*cf. Section I, Chapter 4, paragraph 5 of this binder*). This is the case even if only one parameter exists. Therefore it is not possible to print the result directly. 

<a name="chmbookmark843"></a>First the number of parameters must be extracted with GET/STRUCTURE. The following loop over the N parameters adds them 3 and 3 in the output list. 

<a name="chmbookmark844"></a>The example file: 

![Text document]  basic\_macro\_stif.txt

<table><tr><th valign="bottom">![Previous Page][ ](#chmtopic55)</th><th valign="bottom">![Next Page][ ](#chmtopic33)</th><th> </th><th> </th><th></th></tr>
<tr><td colspan="5"></td></tr>
<tr><td colspan="4">Copyright © 1993-2005 AVEVA AB </td><td>![HOME - Generated from Data_Extraction_basic.fm in Data_Extraction.book][HOME - Generated from Data_ExtractionFP.fm in Data_Extraction.book][](#chmtopic1)</td></tr>
</table>

<table><tr><th colspan="5" valign="bottom"><a name="chmtopic46"></a>User's Guide Data Extraction </th><th valign="bottom">![Version]</th></tr>
<tr><td colspan="6"></td></tr>
<tr><td valign="top">![Previous Page][ ](#chmtopic34)</td><td valign="top">![Next Page][ ](#chmtopic36)</td><td colspan="4" valign="top"><b>Chapter: [Drafting Keywords](#chmtopic7)</b> </td></tr>
</table>

<a name="chmbookmark438"></a>**2.3 Information from a Drawing** 

<a name="chmbookmark845"></a>For every drawing it is possible to extract detailed information about all objects, shown on the drawing. The following is an overview of all relevant keywords:</sup> 

![Excel document]<a name="chmbookmark846"></a>  Datext\_Drawing.xls

<a name="chmbookmark847"></a>**Note:**   It is possible to specify the source data bank for the DRAWING keyword. This is done by suffixing the drawing name with the '@' sign followed by the "drawing type". If no optional data bank is given, the default data bank is "General Drawing" (SB\_PDB). 

<a name="chmbookmark848"></a>The drawing type can be given as: 

- <a name="chmbookmark849"></a>the logical name of the data bank ( e.g. SB\_PDB) 
- <a name="chmbookmark850"></a>the description of the data bank (e.g. "General Drawing") 
- <a name="chmbookmark851"></a>an integer corresponding to the type ( 1 for SB\_PDB, 2 for SBD\_BACKUP etc) 

  |<a name="chmbookmark852"></a>**Example 1** |
  | :- |
  |<p>DRAWING( 'XYZ'\*).NAME</p><p>DRAWING( 'XYZ@SB\_PDB'\*).NAME</p><p>DRAWING( 'XYZ@General Drawing'\*).NAME</p><p>DRAWING( 'XYZ@1'\*).NAME</p>|

<a name="chmbookmark853"></a>Equivalent ways to extract the names of all drawings starting with XYZ in the SB\_PDB data bank. 

|<a name="chmbookmark854"></a>**Example 2** |
| :- |
|<p>DRAWING( 'XYZ@SBD\_PICT'\*).NAME</p><p>DRAWING( 'XYZ@General Subpicture'\*).NAME</p><p>DRAWING( 'XYZ@3'\*).NAME</p>|

<a name="chmbookmark855"></a>Equivalent ways to extract the names of all drawings starting with XYZ in the SBD\_PICT data bank. 

|<a name="chmbookmark856"></a>**Example 3** |
| :- |
|<p>DRAWING( 'XYZ@SB\_PDB'\*, 'ABC@SBD\_PICT'\*).NAME</p><p>DRAWING( 'XYZ@SB\_PDB'\*, 'ABC@3\*).NAME</p><p>DRAWING( 'XYZ'\*, 'ABC@3'\*).NAME</p><p>DRAWING( 'XYZ@SB\_PDB'\*, 'ABC@General Subpicture'\*).NAME</p><p>DRAWING( 'XYZ@General Drawing'\*, 'ABC@General Subpicture'\*).NAME</p><p>DRAWING( 'XYZ@1'\*, 'ABC@General Subpicture'\*).NAME</p>|

<a name="chmbookmark857"></a>Equivalent ways to extract the names of all drawings starting with XYZ in the SB\_PDB data bank and all drawings starting with ABC in the SBD\_PICT data bank. 

|<a name="chmbookmark858"></a>**Example 4** |
| :- |
|<p>DRAWING( 'XYZ90' :  'XYZ99').NAME</p><p>DRAWING( 'XYZ90@SB\_PDB' :  'XYZ99').NAME</p><p>DRAWING( 'XYZ90@General Drawing' :  'XYZ999').NAME</p><p>DRAWING( 'XYZ90@1' :  'XYZ999').NAME</p><p>DRAWING( 'XYZ90@SBD\_PICT' : 'XYZ99').NAME</p><p>DRAWING( 'XYZ90@General Subpicture' :  'XYZ99').NAME</p><p>DRAWING( 'XYZ90@3' :  'XYZ99').NAME</p>|

<a name="chmbookmark859"></a>Equivalent ways to extract the names of all drawings in the interval XYZ90 to XYZ99 in the SB\_PDB data bank and three equivalent ways to extract the names of all drawings in the interval XYZ90 to XYZ99 in the SBD\_PICT data bank. 

<a name="chmbookmark860"></a>**Note:**   Note that when giving intervals, the optional drawing type is given in the lower (first) limit. A drawing type in the upper (second) limit will be ignored. 

<a name="chmbookmark861"></a>**Example** 

- <a name="chmbookmark862"></a>DRAWING( 'XYZ90@SBD\_PICT' : 'XYZ99@SB\_PDB').NAME 

  <a name="chmbookmark863"></a>is the same as

  <a name="chmbookmark864"></a>DRAWING( 'XYZ90@SBD\_PICT' : 'XYZ99').NAME

<a name="chmbookmark865"></a>and

- <a name="chmbookmark866"></a>DRAWING( 'XYZ90' : 'XYZ99@SBD\_PICT').NAME 

  <a name="chmbookmark867"></a>is the same as

  <a name="chmbookmark868"></a>DRAWING( 'XYZ90@SB\_PDB' : 'XYZ99').NAME

<table><tr><th valign="bottom">![Previous Page][ ](#chmtopic34)</th><th valign="bottom">![Next Page][ ](#chmtopic36)</th><th> </th><th> </th><th></th></tr>
<tr><td colspan="5"></td></tr>
<tr><td colspan="4">Copyright © 1993-2005 AVEVA AB </td><td>![HOME - Generated from Data_Extraction_drafting.FM in Data_Extraction.book][HOME - Generated from Data_ExtractionFP.fm in Data_Extraction.book][](#chmtopic1)</td></tr>
</table>

<table><tr><th colspan="5" valign="bottom"><a name="chmtopic47"></a>User's Guide Data Extraction </th><th valign="bottom">![Version]</th></tr>
<tr><td colspan="6"></td></tr>
<tr><td valign="top">![Previous Page][ ](#chmtopic37)</td><td valign="top">![Next Page][ ](#chmtopic38)</td><td colspan="4" valign="top"><b>Chapter: [Structure Keywords](#chmtopic9)</b> </td></tr>
</table>

<a name="chmbookmark869"></a>**5.2.1 Reference to Parts Via Running Number** 

<table><tr><th colspan="8" valign="top"><a name="chmbookmark870"></a><b>Keyword</b> </th><th valign="top"><a name="chmbookmark871"></a><b>Explanation</b> </th><th colspan="2" valign="top"><a name="chmbookmark872"></a><b>Output</b> </th></tr>
<tr><td valign="top"><a name="chmbookmark873"></a><b>1</b> </td><td valign="top"><a name="chmbookmark874"></a><b>2</b> </td><td valign="top"><a name="chmbookmark875"></a><b>3</b> </td><td valign="top"><a name="chmbookmark876"></a><b>4</b> </td><td valign="top"><a name="chmbookmark877"></a><b>5</b> </td><td colspan="3" valign="top"><a name="chmbookmark878"></a><b>6</b> </td><td colspan="2" valign="top"></td><td valign="top"></td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top"><a name="chmbookmark879"></a><a name="chmbookmark880"></a>.TYP[E]</td><td colspan="2" valign="top"><p><a name="chmbookmark881"></a>Group type </p><p>0 = Default </p><p>1 = Bent Plate </p></td><td valign="top"><a name="chmbookmark882"></a>i </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.THI[CKNESS]</td><td colspan="2" valign="top"><a name="chmbookmark883"></a>Thickness for a bent plate group. Zero for the default group. </td><td valign="top"><a name="chmbookmark884"></a>r </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.NPAR[T]</td><td colspan="2" valign="top"><a name="chmbookmark885"></a>Number of parts </td><td valign="top"><a name="chmbookmark886"></a>i </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="4" valign="top">.PAR[T](i)</td><td valign="top">i=1,NPART</td><td colspan="2" valign="top"><a name="chmbookmark887"></a>Part reference by index </td><td valign="top"><a name="chmbookmark888"></a>- </td></tr>
</table>

<table><tr><th valign="bottom">![Previous Page][ ](#chmtopic37)</th><th valign="bottom">![Next Page][ ](#chmtopic38)</th><th> </th><th> </th><th></th></tr>
<tr><td colspan="5"></td></tr>
<tr><td colspan="4">Copyright © 1993-2005 AVEVA AB </td><td>![HOME - Generated from Data_Extraction_structure.fm in Data_Extraction.book][HOME - Generated from Data_ExtractionFP.fm in Data_Extraction.book][](#chmtopic1)</td></tr>
</table>

<table><tr><th colspan="5" valign="bottom"><a name="chmtopic48"></a>User's Guide Data Extraction </th><th valign="bottom">![Version]</th></tr>
<tr><td colspan="6"></td></tr>
<tr><td valign="top">![Previous Page][ ](#chmtopic39)</td><td valign="top">![Next Page][ ](#chmtopic49)</td><td colspan="4" valign="top"><b>Chapter: [Cable Keywords](#chmtopic11)</b> </td></tr>
</table>

<a name="chmbookmark889"></a>**7.3 Information from Cableway Object** 

<table><tr><th colspan="8" valign="top"><a name="chmbookmark890"></a><a name="chmbookmark891"></a><b>Keyword</b> </th><th valign="top"><a name="chmbookmark892"></a><b>Explanation</b> </th><th colspan="2" valign="top"><a name="chmbookmark893"></a><b>Output</b> </th></tr>
<tr><td valign="top"><a name="chmbookmark894"></a><b>1</b> </td><td valign="top"><a name="chmbookmark895"></a><b>2</b> </td><td valign="top"><a name="chmbookmark896"></a><b>3</b> </td><td valign="top"><a name="chmbookmark897"></a><b>4</b> </td><td valign="top"><a name="chmbookmark898"></a><b>5</b> </td><td colspan="2" valign="top"><a name="chmbookmark899"></a><b>6</b> </td><td valign="top"></td><td colspan="2" valign="top"></td><td valign="top"></td></tr>
<tr><td colspan="7" valign="top"><a name="chmbookmark900"></a><a name="chmbookmark901"></a><a name="chmbookmark902"></a>CAB[LE][(s)],</td><td valign="top">s=<Project></td><td colspan="2" valign="top"><a name="chmbookmark903"></a>Cable application. </td><td valign="top"><a name="chmbookmark904"></a>- </td></tr>
<tr><td valign="top"></td><td colspan="6" valign="top">.CWAY(s),</td><td valign="top">s=<Name></td><td colspan="2" valign="top"><a name="chmbookmark905"></a>Cableway name. </td><td valign="top"><a name="chmbookmark906"></a>- </td></tr>
<tr><td colspan="2" valign="top"></td><td colspan="6" valign="top">.NAME</td><td colspan="2" valign="top"><a name="chmbookmark907"></a>Cableway name. </td><td valign="top"><a name="chmbookmark908"></a>s </td></tr>
<tr><td colspan="2" valign="top"></td><td colspan="6" valign="top">.MODUL[ENAME]</td><td colspan="2" valign="top"><a name="chmbookmark909"></a>Module name. </td><td valign="top"><a name="chmbookmark910"></a>s </td></tr>
<tr><td colspan="2" valign="top"></td><td colspan="6" valign="top">.BOX</td><td colspan="2" valign="top"><a name="chmbookmark911"></a>Box. </td><td valign="top"><a name="chmbookmark912"></a>2x 3r </td></tr>
<tr><td colspan="2" valign="top"></td><td colspan="6" valign="top">.WEI[GHT](Type)</td><td colspan="2" valign="top"><p><a name="chmbookmark913"></a>Returns the weight in kilograms. </p><p><a name="chmbookmark914"></a>Type can have the following values: </p><p>0 = Only those cableway parts dressed with components, that cannot have standard structures as reference, are added. </p><p>1 = The weight of the cables placed on this cableway are added. </p><p>10= Adds the weight of structure objects used as cableway parts, for those components not included when type is 0. </p><p>11= Total weight including both cables and structure objects. </p></td><td valign="top"><a name="chmbookmark915"></a>r </td></tr>
<tr><td colspan="2" valign="top"></td><td colspan="6" valign="top">.COG(Type)</td><td colspan="2" valign="top"><p><a name="chmbookmark916"></a>Returns the centre of gravity as a coordinate. </p><p><a name="chmbookmark917"></a>Type can have the same values as for weight. </p></td><td valign="top"><a name="chmbookmark918"></a>3r </td></tr>
<tr><td colspan="2" valign="top"></td><td colspan="6" valign="top">.REA[DY]</td><td colspan="2" valign="top"><p><a name="chmbookmark919"></a>User defined ready </p><p>1 = YES </p><p>0 = NO </p></td><td valign="top"><a name="chmbookmark920"></a>I </td></tr>
<tr><td colspan="2" valign="top"></td><td colspan="6" valign="top">.PDI_T[RANSFERED]</td><td colspan="2" valign="top"><p><a name="chmbookmark921"></a>Object transferred to PDI </p><p>1 = YES </p><p>0 = NO </p></td><td valign="top"><a name="chmbookmark922"></a>I </td></tr>
<tr><td colspan="2" valign="top"></td><td colspan="6" valign="top">.PDI_N[EEDS_RETRANSFER]</td><td colspan="2" valign="top"><p><a name="chmbookmark923"></a>Object updated after transfer </p><p>1 = YES </p><p>0 = NO </p></td><td valign="top"><a name="chmbookmark924"></a>i </td></tr>
<tr><td colspan="2" valign="top"></td><td colspan="6" valign="top">.NCON[NECTION]</td><td colspan="2" valign="top"><a name="chmbookmark925"></a>Number of connections. </td><td valign="top"><a name="chmbookmark926"></a>I </td></tr>
<tr><td colspan="2" valign="top"></td><td colspan="6" valign="top">.CON[NECTION] (I),	 I=1,NCON</td><td colspan="2" valign="top"><a name="chmbookmark927"></a>Connection number. </td><td valign="top"><a name="chmbookmark928"></a>- </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.BRAN[CH]</td><td colspan="2" valign="top"><a name="chmbookmark929"></a>Connection branch ID. </td><td valign="top"><a name="chmbookmark930"></a>I </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.PART</td><td colspan="2" valign="top"><a name="chmbookmark931"></a>Connection part ID. </td><td valign="top"><a name="chmbookmark932"></a>I </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.ID[ENTITY]</td><td colspan="2" valign="top"><a name="chmbookmark933"></a>Connection point ID. </td><td valign="top"><a name="chmbookmark934"></a>I </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.TYP(E)</td><td colspan="2" valign="top"><a name="chmbookmark935"></a>1=Cableway, 2=Structure </td><td valign="top"><a name="chmbookmark936"></a>I </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.REF[ERENCE]</td><td colspan="2" valign="top"><a name="chmbookmark937"></a>References to connected cableway. </td><td valign="top"><a name="chmbookmark938"></a>- </td></tr>
<tr><td colspan="4" valign="top"></td><td colspan="4" valign="top">.CONN_PR[OJECT]</td><td colspan="2" valign="top"><a name="chmbookmark939"></a>Cableway project name, only valid if TYPE=1 </td><td valign="top"><a name="chmbookmark940"></a>s </td></tr>
<tr><td colspan="4" valign="top"></td><td colspan="4" valign="top">.CONN_C[WAY]</td><td colspan="2" valign="top"><a name="chmbookmark941"></a>Cableway name, only valid if TYPE=1 </td><td valign="top"><a name="chmbookmark942"></a>s </td></tr>
<tr><td colspan="4" valign="top"></td><td colspan="4" valign="top">.CONN_B[RANCH]</td><td colspan="2" valign="top"><a name="chmbookmark943"></a>Cableway branch ID, only valid if TYPE=1 </td><td valign="top"><a name="chmbookmark944"></a>I </td></tr>
<tr><td colspan="4" valign="top"></td><td colspan="4" valign="top">.CONN_PA[RT]</td><td colspan="2" valign="top"><a name="chmbookmark945"></a>Cableway part ID, only valid if TYPE=1 </td><td valign="top"><a name="chmbookmark946"></a>I </td></tr>
<tr><td colspan="4" valign="top"></td><td colspan="4" valign="top">.CONN_I[DENTITY]</td><td colspan="2" valign="top"><a name="chmbookmark947"></a>Cableway point ID, only valid if TYPE=1 </td><td valign="top"><a name="chmbookmark948"></a>I </td></tr>
<tr><td colspan="4" valign="top"></td><td colspan="4" valign="top">.CONN_S(TRUCT)</td><td colspan="2" valign="top"><a name="chmbookmark949"></a>Name of structure, only valid if TYPE=2 </td><td valign="top"><a name="chmbookmark950"></a>s </td></tr>
<tr><td colspan="2" valign="top"></td><td colspan="6" valign="top">.EVEN[T] (ID)</td><td colspan="2" valign="top"><a name="chmbookmark951"></a>Event point. </td><td valign="top"><a name="chmbookmark952"></a>- </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.BRAN[CH]</td><td colspan="2" valign="top"><a name="chmbookmark953"></a>Branch ID. </td><td valign="top"><a name="chmbookmark954"></a>I </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.PART</td><td colspan="2" valign="top"><a name="chmbookmark955"></a>Part ID. </td><td valign="top"><a name="chmbookmark956"></a>I </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.NREF[ERENCE]</td><td colspan="2" valign="top"><a name="chmbookmark957"></a>Number of connected cableway references. </td><td valign="top"><a name="chmbookmark958"></a>I </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.REF[ERENCE] (I),			 I=1,NREF</td><td colspan="2" valign="top"><a name="chmbookmark959"></a>Reference number. </td><td valign="top"><a name="chmbookmark960"></a>- </td></tr>
<tr><td colspan="4" valign="top"></td><td colspan="4" valign="top">.CONN_PR[OJECT]</td><td colspan="2" valign="top"><a name="chmbookmark961"></a>Cableway project name. </td><td valign="top"><a name="chmbookmark962"></a>s </td></tr>
<tr><td colspan="4" valign="top"></td><td colspan="4" valign="top">.CONN_C[WAY]</td><td colspan="2" valign="top"><a name="chmbookmark963"></a>Cableway name. </td><td valign="top"><a name="chmbookmark964"></a>s </td></tr>
<tr><td colspan="4" valign="top"></td><td colspan="4" valign="top">.CONN_B[RANCH]</td><td colspan="2" valign="top"><a name="chmbookmark965"></a>Cableway branch ID. </td><td valign="top"><a name="chmbookmark966"></a>I </td></tr>
<tr><td colspan="4" valign="top"></td><td colspan="4" valign="top">.CONN_PA[RT]</td><td colspan="2" valign="top"><a name="chmbookmark967"></a>Cableway part ID. </td><td valign="top"><a name="chmbookmark968"></a>I </td></tr>
<tr><td colspan="4" valign="top"></td><td colspan="4" valign="top">.CONN_I[DENTITY]</td><td colspan="2" valign="top"><a name="chmbookmark969"></a>Cableway point ID. </td><td valign="top"><a name="chmbookmark970"></a>I </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.NCAB[LE]</td><td colspan="2" valign="top"><a name="chmbookmark971"></a>Number of cables. </td><td valign="top"><a name="chmbookmark972"></a>I </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.CAB[LENO](I),			 I=1,NCAB</td><td colspan="2" valign="top"><a name="chmbookmark973"></a>Cable number. </td><td valign="top"></td></tr>
<tr><td colspan="4" valign="top"></td><td colspan="4" valign="top"><a name="chmbookmark974"></a>.PRO[JECT]</td><td colspan="2" valign="top"><a name="chmbookmark975"></a>Cable project name. </td><td valign="top"><a name="chmbookmark976"></a>s </td></tr>
<tr><td colspan="4" valign="top"></td><td colspan="4" valign="top">.NAM[E]</td><td colspan="2" valign="top"><a name="chmbookmark977"></a>Cable name (System-Name). </td><td valign="top"><a name="chmbookmark978"></a>s </td></tr>
<tr><td colspan="4" valign="top"></td><td colspan="4" valign="top">.GRO[UP]</td><td colspan="2" valign="top"><a name="chmbookmark979"></a>Group name. </td><td valign="top"><a name="chmbookmark980"></a>s </td></tr>
<tr><td colspan="2" valign="top"></td><td colspan="6" valign="top">.NUSER_ATT[RIBUTE]</td><td colspan="2" valign="top"><a name="chmbookmark981"></a>Number of user attributes </td><td valign="top"><a name="chmbookmark982"></a>i </td></tr>
<tr><td colspan="2" valign="top"></td><td colspan="6" valign="top">.USER_ATT[RIBUTE](i),</td><td colspan="2" valign="top"><a name="chmbookmark983"></a>See [Information from a Drawing in Chapter Drafting Keywords](#chmbookmark438)</td><td valign="top"></td></tr>
<tr><td colspan="7" valign="top"><a name="chmbookmark984"></a>CAB[LE][(s)], </td><td valign="top">s=<Project></td><td colspan="2" valign="top"><a name="chmbookmark985"></a>Cable application. </td><td valign="top"><a name="chmbookmark986"></a>- </td></tr>
<tr><td valign="top"></td><td colspan="6" valign="top">.CWAY(s),</td><td valign="top">s=<Name></td><td colspan="2" valign="top"><a name="chmbookmark987"></a>Cableway name. </td><td valign="top"><a name="chmbookmark988"></a>- </td></tr>
<tr><td colspan="2" valign="top"></td><td colspan="6" valign="top">.PNTR(ID)</td><td colspan="2" valign="top"><a name="chmbookmark989"></a>Penetration identity. </td><td valign="top"><a name="chmbookmark990"></a>I </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.TYPE</td><td colspan="2" valign="top"><p><a name="chmbookmark991"></a>Type of penetration </p><p>1 = real </p><p>2 = imaginary </p></td><td valign="top"><a name="chmbookmark992"></a>I </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.NAM[E]</td><td colspan="2" valign="top"><a name="chmbookmark993"></a>Penetration name. </td><td valign="top"></td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top"><a name="chmbookmark994"></a>.POI[NT]</td><td colspan="2" valign="top"><a name="chmbookmark995"></a>Coordinates of penetration. </td><td valign="top"><a name="chmbookmark996"></a>3r </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.USED_A[REA]</td><td colspan="2" valign="top"><a name="chmbookmark997"></a>Used area (if real)(mm2). </td><td valign="top"><a name="chmbookmark998"></a>r </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.TOTAL_A[REA]</td><td colspan="2" valign="top"><a name="chmbookmark999"></a>Total area (if real)(mm2). </td><td valign="top"><a name="chmbookmark1000"></a>r </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.COMP_N[AME]</td><td colspan="2" valign="top"><a name="chmbookmark1001"></a>Component name (if real). </td><td valign="top"><a name="chmbookmark1002"></a>s </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.NCAB[LE]</td><td colspan="2" valign="top"><a name="chmbookmark1003"></a>Number of cables passing (if real). </td><td valign="top"><a name="chmbookmark1004"></a>I </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.CAB[LENO](I),I=1,NCAB</td><td colspan="2" valign="top"><a name="chmbookmark1005"></a>Cable number (if real). </td><td valign="top"><a name="chmbookmark1006"></a>- </td></tr>
<tr><td colspan="4" valign="top"></td><td colspan="4" valign="top">.PRO[JECT]</td><td colspan="2" valign="top"><a name="chmbookmark1007"></a>Cable project name. </td><td valign="top"><a name="chmbookmark1008"></a>s </td></tr>
<tr><td colspan="4" valign="top"></td><td colspan="4" valign="top">.NAM[E]</td><td colspan="2" valign="top"><a name="chmbookmark1009"></a>Cable name. </td><td valign="top"><a name="chmbookmark1010"></a>s </td></tr>
<tr><td colspan="4" valign="top"></td><td colspan="4" valign="top">.GLA[ND_COMPONENT]</td><td colspan="2" valign="top"><a name="chmbookmark1011"></a>Gland component name. </td><td valign="top"><a name="chmbookmark1012"></a>s </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.NGLA[ND]</td><td colspan="2" valign="top"><a name="chmbookmark1013"></a>No. of glands in penetration </td><td valign="top"></td></tr>
<tr><td colspan="8" valign="top"></td><td colspan="2" valign="top"><a name="chmbookmark1014"></a><a name="chmbookmark1015"></a>(if real). </td><td valign="top"><a name="chmbookmark1016"></a>I </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.GLA[ND](I),			 I=1,NGLAN</td><td colspan="2" valign="top"><a name="chmbookmark1017"></a>Gland number (if real). </td><td valign="top"><a name="chmbookmark1018"></a>- </td></tr>
<tr><td colspan="4" valign="top"></td><td colspan="4" valign="top">.GLA[ND_COMPONENT]</td><td colspan="2" valign="top"><a name="chmbookmark1019"></a>Gland component name. </td><td valign="top"><a name="chmbookmark1020"></a>s </td></tr>
<tr><td colspan="4" valign="top"></td><td colspan="4" valign="top">.CABLE_PRO[JECT]</td><td colspan="2" valign="top"><a name="chmbookmark1021"></a>Cable project name. </td><td valign="top"><a name="chmbookmark1022"></a>s </td></tr>
<tr><td colspan="4" valign="top"></td><td colspan="4" valign="top">.CABLE_NAM[E]</td><td colspan="2" valign="top"><a name="chmbookmark1023"></a>Cable name. </td><td valign="top"><a name="chmbookmark1024"></a>s </td></tr>
<tr><td colspan="2" valign="top"></td><td colspan="6" valign="top">.BRAN[CH](ID)</td><td colspan="2" valign="top"><a name="chmbookmark1025"></a>Branch. </td><td valign="top"><a name="chmbookmark1026"></a>- </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.NCON[NECTION]</td><td colspan="2" valign="top"><a name="chmbookmark1027"></a>Number of connections. </td><td valign="top"><a name="chmbookmark1028"></a>I </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.CON[NECTION](I),			 I=1,NCON</td><td colspan="2" valign="top"><a name="chmbookmark1029"></a>Connection number. </td><td valign="top"><a name="chmbookmark1030"></a>- </td></tr>
<tr><td colspan="4" valign="top"></td><td colspan="4" valign="top">.PART</td><td colspan="2" valign="top"><a name="chmbookmark1031"></a>Part number. </td><td valign="top"><a name="chmbookmark1032"></a>I </td></tr>
<tr><td colspan="4" valign="top"></td><td colspan="4" valign="top">.ID[ENTITY]</td><td colspan="2" valign="top"><a name="chmbookmark1033"></a>Identity. </td><td valign="top"><a name="chmbookmark1034"></a>I </td></tr>
<tr><td colspan="4" valign="top"></td><td colspan="4" valign="top">.REF[ERENCE]</td><td colspan="2" valign="top"><a name="chmbookmark1035"></a>Connected cableway reference. </td><td valign="top"><a name="chmbookmark1036"></a>- </td></tr>
<tr><td colspan="5" valign="top"></td><td colspan="3" valign="top">.CONN_PR[OJECT]</td><td colspan="2" valign="top"><a name="chmbookmark1037"></a>Cableway project name. </td><td valign="top"><a name="chmbookmark1038"></a>s </td></tr>
<tr><td colspan="5" valign="top"></td><td colspan="3" valign="top">.CONN_C[WAY]</td><td colspan="2" valign="top"><a name="chmbookmark1039"></a>Cableway name. </td><td valign="top"><a name="chmbookmark1040"></a>s </td></tr>
<tr><td colspan="5" valign="top"></td><td colspan="3" valign="top">.CONN_B[RANCH]</td><td colspan="2" valign="top"><a name="chmbookmark1041"></a>Cableway branch ID. </td><td valign="top"><a name="chmbookmark1042"></a>I </td></tr>
<tr><td colspan="5" valign="top"></td><td colspan="3" valign="top">.CONN_P[ART]</td><td colspan="2" valign="top"><a name="chmbookmark1043"></a>Cableway part ID. </td><td valign="top"><a name="chmbookmark1044"></a>I </td></tr>
<tr><td colspan="5" valign="top"></td><td colspan="3" valign="top">.CONN_I[DENTITY]</td><td colspan="2" valign="top"><a name="chmbookmark1045"></a>Cableway point ID. </td><td valign="top"><a name="chmbookmark1046"></a>I </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.NINT[ERFERENCE]</td><td colspan="2" valign="top"><a name="chmbookmark1047"></a>Number of interference classes. </td><td valign="top"><a name="chmbookmark1048"></a>I </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.INT[ERFERNECE](I),			 I=1,NINT</td><td colspan="2" valign="top"><a name="chmbookmark1049"></a>Interference number. </td><td valign="top"><a name="chmbookmark1050"></a>- </td></tr>
<tr><td colspan="4" valign="top"></td><td colspan="4" valign="top">.C[LASS]</td><td colspan="2" valign="top"><a name="chmbookmark1051"></a>Class. </td><td valign="top"><a name="chmbookmark1052"></a>s </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.START[_POINT]</td><td colspan="2" valign="top"><a name="chmbookmark1053"></a>Coordinate of start point in first branch. </td><td valign="top"><a name="chmbookmark1054"></a>3r </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.END[_POINT]</td><td colspan="2" valign="top"><a name="chmbookmark1055"></a>Coordinate of end point in last branch. </td><td valign="top"><a name="chmbookmark1056"></a>3r </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.CABLE[_EXCESS]</td><td colspan="2" valign="top"><a name="chmbookmark1057"></a>Excess length added to cable routed on branch. </td><td valign="top"><a name="chmbookmark1058"></a>r </td></tr>
<tr><td colspan="7" valign="top">CAB[LE][(s)],</td><td valign="top">s=<Project></td><td colspan="2" valign="top"><a name="chmbookmark1059"></a>Cable application. </td><td valign="top"><a name="chmbookmark1060"></a>- </td></tr>
<tr><td valign="top"></td><td colspan="6" valign="top">.CWAY(s),</td><td valign="top">s=<Name></td><td colspan="2" valign="top"><a name="chmbookmark1061"></a>Cableway name. </td><td valign="top"><a name="chmbookmark1062"></a>- </td></tr>
<tr><td colspan="2" valign="top"></td><td colspan="6" valign="top">.PART(ID</td><td colspan="2" valign="top"><a name="chmbookmark1063"></a>Part identity. </td><td valign="top"><a name="chmbookmark1064"></a>- </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.MAN_PROP[ERTIES]</td><td colspan="2" valign="top"><a name="chmbookmark1065"></a>Management properties. </td><td valign="top"><a name="chmbookmark1066"></a>- </td></tr>
<tr><td colspan="4" valign="top"></td><td colspan="4" valign="top">.POS[ITION_NUMBER]</td><td colspan="2" valign="top"><a name="chmbookmark1067"></a>Position number. </td><td valign="top"><a name="chmbookmark1068"></a>s </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.COMP_T[YPE]</td><td colspan="2" valign="top"><a name="chmbookmark1069"></a>Component type. </td><td valign="top"><a name="chmbookmark1070"></a>I </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.COMP_N[AME]</td><td colspan="2" valign="top"><a name="chmbookmark1071"></a>Component name </td><td valign="top"><a name="chmbookmark1072"></a>s </td></tr>
<tr><td colspan="8" valign="top"></td><td colspan="2" valign="top"><a name="chmbookmark1073"></a>(if COMP_T > 0) </td><td valign="top"></td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top"><a name="chmbookmark1074"></a>.CUT_ST[ATUS]</td><td colspan="2" valign="top"><p><a name="chmbookmark1075"></a>Cut status </p><p>0 = material not cut. </p><p>1 = material cut. </p></td><td valign="top"><a name="chmbookmark1076"></a>I </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.FORM</td><td colspan="2" valign="top"><p><a name="chmbookmark1077"></a>Form </p><p>1 = straight </p><p>2 = bend </p></td><td valign="top"><a name="chmbookmark1078"></a>I </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.TOTAL_A[REA]</td><td colspan="2" valign="top"><a name="chmbookmark1079"></a>Total area (mm2). </td><td valign="top"><a name="chmbookmark1080"></a>r </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.MATERIAL_L[ENGTH]</td><td colspan="2" valign="top"><a name="chmbookmark1081"></a>Material length (mm). </td><td valign="top"><a name="chmbookmark1082"></a>r </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.ROUTE_L[ENGTH]</td><td colspan="2" valign="top"><a name="chmbookmark1083"></a>Route length (mm). </td><td valign="top"><a name="chmbookmark1084"></a>r </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.NCON[NECTION]</td><td colspan="2" valign="top"><a name="chmbookmark1085"></a>Number of connections. </td><td valign="top"><a name="chmbookmark1086"></a>I </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.CON[NECTION](I)			 I=1,NCONN</td><td colspan="2" valign="top"><a name="chmbookmark1087"></a>Connection number. </td><td valign="top"><a name="chmbookmark1088"></a>- </td></tr>
<tr><td colspan="4" valign="top"></td><td colspan="4" valign="top">.POI[NT]</td><td colspan="2" valign="top"><a name="chmbookmark1089"></a>Coordinates for connection. </td><td valign="top"><a name="chmbookmark1090"></a>3r </td></tr>
<tr><td colspan="4" valign="top"></td><td colspan="4" valign="top">.ID[ENTITY]</td><td colspan="2" valign="top"><a name="chmbookmark1091"></a>Identity. </td><td valign="top"><a name="chmbookmark1092"></a>I </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.MAN_PROP[ERTIES]</td><td colspan="2" valign="top"><a name="chmbookmark1093"></a>Management property data. </td><td valign="top"><a name="chmbookmark1094"></a>- </td></tr>
<tr><td colspan="4" valign="top"></td><td colspan="4" valign="top">.PLAN_U[NIT]</td><td colspan="2" valign="top"><a name="chmbookmark1095"></a>Planning unit. </td><td valign="top"><a name="chmbookmark1096"></a>s </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.NUSER_ATT[RIBUTE]</td><td colspan="2" valign="top"><a name="chmbookmark1097"></a>Number of user attributes </td><td valign="top"><a name="chmbookmark1098"></a>i </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.USER_ATT[RIBUTE](i),</td><td colspan="2" valign="top"><a name="chmbookmark1099"></a>See [Information from a Drawing in Chapter Drafting Keywords](#chmbookmark438)</td><td valign="top"></td></tr>
<tr><td colspan="2" valign="top"></td><td colspan="6" valign="top"><a name="chmbookmark1100"></a>.NGRO[UP]</td><td colspan="2" valign="top"><a name="chmbookmark1101"></a>Number of groups. </td><td valign="top"><a name="chmbookmark1102"></a>- </td></tr>
<tr><td colspan="2" valign="top"></td><td colspan="6" valign="top">.GRO[UP](I)				 I=1, NGROUP</td><td colspan="2" valign="top"><a name="chmbookmark1103"></a>Group number. </td><td valign="top"><a name="chmbookmark1104"></a>- </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.NAME</td><td colspan="2" valign="top"><a name="chmbookmark1105"></a>Group name. </td><td valign="top"><a name="chmbookmark1106"></a>s </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.ASS[EMBLY]</td><td colspan="2" valign="top"><a name="chmbookmark1107"></a>Assembly reference name </td><td valign="top"><a name="chmbookmark1108"></a>s </td></tr>
<tr><td colspan="2" valign="top"></td><td colspan="6" valign="top">.TDM_I[NFORMATION]</td><td colspan="2" valign="top"><a name="chmbookmark1109"></a>Data management </td><td valign="top"><a name="chmbookmark1110"></a>- </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.ALIAS1</td><td colspan="2" valign="top"><a name="chmbookmark1111"></a>Alias information </td><td valign="top"><a name="chmbookmark1112"></a>s </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.ALIAS2</td><td colspan="2" valign="top"><a name="chmbookmark1113"></a>Alias information </td><td valign="top"><a name="chmbookmark1114"></a>s </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.ALIAS3</td><td colspan="2" valign="top"><a name="chmbookmark1115"></a>Alias information </td><td valign="top"><a name="chmbookmark1116"></a>s </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.ALIAS4</td><td colspan="2" valign="top"><a name="chmbookmark1117"></a>Alias information </td><td valign="top"><a name="chmbookmark1118"></a>s </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.DESC[RIPTION]</td><td colspan="2" valign="top"><a name="chmbookmark1119"></a>Description </td><td valign="top"><a name="chmbookmark1120"></a>s </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.REM[ARKS]</td><td colspan="2" valign="top"><a name="chmbookmark1121"></a>Remarks </td><td valign="top"><a name="chmbookmark1122"></a>s </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.TYPE1</td><td colspan="2" valign="top"><a name="chmbookmark1123"></a>Type </td><td valign="top"><a name="chmbookmark1124"></a>s </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.TYPE2</td><td colspan="2" valign="top"><a name="chmbookmark1125"></a>Type </td><td valign="top"><a name="chmbookmark1126"></a>s </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.TYPE3</td><td colspan="2" valign="top"><a name="chmbookmark1127"></a>Type </td><td valign="top"><a name="chmbookmark1128"></a>s </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.TYPE4</td><td colspan="2" valign="top"><a name="chmbookmark1129"></a>Type </td><td valign="top"><a name="chmbookmark1130"></a>s </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.PLA[NINGUNIT]</td><td colspan="2" valign="top"><a name="chmbookmark1131"></a>Planning unit </td><td valign="top"><a name="chmbookmark1132"></a>s </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.COST[CODE]</td><td colspan="2" valign="top"><a name="chmbookmark1133"></a>Cost code </td><td valign="top"><a name="chmbookmark1134"></a>s </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.STATUS_D[ESIGN]</td><td colspan="2" valign="top"><a name="chmbookmark1135"></a>Status design </td><td valign="top"><a name="chmbookmark1136"></a>s </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.STATUS_MAT[ERIAL]</td><td colspan="2" valign="top"><a name="chmbookmark1137"></a>Status material </td><td valign="top"><a name="chmbookmark1138"></a>s </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.STATUS_MAN[UFACT]</td><td colspan="2" valign="top"><a name="chmbookmark1139"></a>Status manufact </td><td valign="top"><a name="chmbookmark1140"></a>s </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.STATUS_A[SSEMBLY]</td><td colspan="2" valign="top"><a name="chmbookmark1141"></a>Status material </td><td valign="top"><a name="chmbookmark1142"></a>s </td></tr>
<tr><td colspan="2" valign="top"></td><td colspan="6" valign="top">.TDM_R[EFERENCES]</td><td colspan="2" valign="top"><a name="chmbookmark1143"></a>Drawing references </td><td valign="top"><a name="chmbookmark1144"></a>- </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.NREF[ERENCE]</td><td colspan="2" valign="top"><a name="chmbookmark1145"></a>Number of references </td><td valign="top"><a name="chmbookmark1146"></a>i </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.REF[ERENCE]</td><td colspan="2" valign="top"><a name="chmbookmark1147"></a>Drawing references </td><td valign="top"><a name="chmbookmark1148"></a>s </td></tr>
</table>

<table><tr><th valign="bottom">![Previous Page][ ](#chmtopic39)</th><th valign="bottom">![Next Page][ ](#chmtopic49)</th><th> </th><th> </th><th></th></tr>
<tr><td colspan="5"></td></tr>
<tr><td colspan="4">Copyright © 1993-2005 AVEVA AB </td><td>![HOME - Generated from Data_Extraction_cable.fm in Data_Extraction.book][HOME - Generated from Data_ExtractionFP.fm in Data_Extraction.book][](#chmtopic1)</td></tr>
</table>

<table><tr><th colspan="5" valign="bottom"><a name="chmtopic49"></a>User's Guide Data Extraction </th><th valign="bottom">![Version]</th></tr>
<tr><td colspan="6"></td></tr>
<tr><td valign="top">![Previous Page][ ](#chmtopic48)</td><td valign="top">![Next Page][ ](#chmtopic40)</td><td colspan="4" valign="top"><b>Chapter: [Cable Keywords](#chmtopic11)</b> </td></tr>
</table>

<a name="chmbookmark1149"></a>**7.4 Information from Penetration Object** 

<table><tr><th colspan="8" valign="top"><a name="chmbookmark1150"></a><a name="chmbookmark1151"></a><b>Keyword</b> </th><th valign="top"><a name="chmbookmark1152"></a><b>Explanation</b> </th><th colspan="2" valign="top"><a name="chmbookmark1153"></a><b>Output</b> </th></tr>
<tr><td valign="top"><a name="chmbookmark1154"></a><b>1</b> </td><td valign="top"><a name="chmbookmark1155"></a><b>2</b> </td><td valign="top"><a name="chmbookmark1156"></a><b>3</b> </td><td valign="top"><a name="chmbookmark1157"></a><b>4</b> </td><td valign="top"><a name="chmbookmark1158"></a><b>5</b> </td><td colspan="2" valign="top"><a name="chmbookmark1159"></a><b>6</b> </td><td valign="top"></td><td colspan="2" valign="top"></td><td valign="top"></td></tr>
<tr><td colspan="7" valign="top"><a name="chmbookmark1160"></a><a name="chmbookmark1161"></a><a name="chmbookmark1162"></a>CAB[LE][(s)]</td><td valign="top">` `s=<Project></td><td colspan="2" valign="top"><a name="chmbookmark1163"></a>Cable application. </td><td valign="top"><a name="chmbookmark1164"></a>- </td></tr>
<tr><td valign="top"></td><td colspan="6" valign="top">.PNTR(s)</td><td valign="top">` `s=<Name></td><td colspan="2" valign="top"><a name="chmbookmark1165"></a>Penetration name. </td><td valign="top"><a name="chmbookmark1166"></a>- </td></tr>
<tr><td colspan="2" valign="top"></td><td colspan="6" valign="top">.NAME</td><td colspan="2" valign="top"><a name="chmbookmark1167"></a>Penetration name. </td><td valign="top"><a name="chmbookmark1168"></a>s </td></tr>
<tr><td colspan="2" valign="top"></td><td colspan="6" valign="top">.POI[NT]</td><td colspan="2" valign="top"><a name="chmbookmark1169"></a>Coordinates of penetration. </td><td valign="top"><a name="chmbookmark1170"></a>3r </td></tr>
<tr><td colspan="2" valign="top"></td><td colspan="6" valign="top">.TYPE</td><td colspan="2" valign="top"><p><a name="chmbookmark1171"></a>Type of penetration </p><p>1 = real </p><p>2 = imaginary </p></td><td valign="top"><a name="chmbookmark1172"></a>I </td></tr>
<tr><td colspan="2" valign="top"></td><td colspan="6" valign="top">.TOTAL_A[REA]</td><td colspan="2" valign="top"><a name="chmbookmark1173"></a>Total area (if real) (mm2). </td><td valign="top"><a name="chmbookmark1174"></a>r </td></tr>
<tr><td colspan="2" valign="top"></td><td colspan="6" valign="top">.USED_A[REA]</td><td colspan="2" valign="top"><a name="chmbookmark1175"></a>Used area (if real) (mm2). </td><td valign="top"><a name="chmbookmark1176"></a>r </td></tr>
<tr><td colspan="2" valign="top"></td><td colspan="6" valign="top">.COMP_N[AME]</td><td colspan="2" valign="top"><a name="chmbookmark1177"></a>Component name (if real). </td><td valign="top"><a name="chmbookmark1178"></a>s </td></tr>
<tr><td colspan="2" valign="top"></td><td colspan="6" valign="top">.ASS[EMBLY]</td><td colspan="2" valign="top"><a name="chmbookmark1179"></a>Assembly reference. </td><td valign="top"><a name="chmbookmark1180"></a>s </td></tr>
<tr><td colspan="2" valign="top"></td><td colspan="6" valign="top">.NCWAY</td><td colspan="2" valign="top"><a name="chmbookmark1181"></a>Number of cableways. </td><td valign="top"><a name="chmbookmark1182"></a>I </td></tr>
<tr><td colspan="2" valign="top"></td><td colspan="6" valign="top">.CWAY_L[OC](I),				 I=1,NCWAY</td><td colspan="2" valign="top"><a name="chmbookmark1183"></a>Cableway location number. </td><td valign="top"><a name="chmbookmark1184"></a>- </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.REF[ERENCE]</td><td colspan="2" valign="top"><a name="chmbookmark1185"></a>Reference to cableway. </td><td valign="top"><a name="chmbookmark1186"></a>- </td></tr>
<tr><td colspan="4" valign="top"></td><td colspan="4" valign="top">.PRO[JECT]</td><td colspan="2" valign="top"><a name="chmbookmark1187"></a>Cableway project name. </td><td valign="top"><a name="chmbookmark1188"></a>s </td></tr>
<tr><td colspan="4" valign="top"></td><td colspan="4" valign="top">.CWAY</td><td colspan="2" valign="top"><a name="chmbookmark1189"></a>Cableway name. </td><td valign="top"><a name="chmbookmark1190"></a>s </td></tr>
<tr><td colspan="4" valign="top"></td><td colspan="4" valign="top">.BRAN[CH]</td><td colspan="2" valign="top"><a name="chmbookmark1191"></a>Cableway branch ID. </td><td valign="top"><a name="chmbookmark1192"></a>I </td></tr>
<tr><td colspan="4" valign="top"></td><td colspan="4" valign="top">.PART</td><td colspan="2" valign="top"><a name="chmbookmark1193"></a>Cableway part ID. </td><td valign="top"><a name="chmbookmark1194"></a>I </td></tr>
<tr><td colspan="4" valign="top"></td><td colspan="4" valign="top">.ID[ENTITY]</td><td colspan="2" valign="top"><a name="chmbookmark1195"></a>Cableway point ID. </td><td valign="top"><a name="chmbookmark1196"></a>I </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.NCAB[LE]</td><td colspan="2" valign="top"><a name="chmbookmark1197"></a>Number of cables passing. </td><td valign="top"><a name="chmbookmark1198"></a>I </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.CAB[LENO](I),			 I=1,NCAB</td><td colspan="2" valign="top"><a name="chmbookmark1199"></a>Cable number. </td><td valign="top"><a name="chmbookmark1200"></a>- </td></tr>
<tr><td colspan="4" valign="top"></td><td colspan="4" valign="top">.PRO[JECT]</td><td colspan="2" valign="top"><a name="chmbookmark1201"></a>Cable project. </td><td valign="top"><a name="chmbookmark1202"></a>s </td></tr>
<tr><td colspan="4" valign="top"></td><td colspan="4" valign="top">.NAM[E]</td><td colspan="2" valign="top"><a name="chmbookmark1203"></a>Cable name. </td><td valign="top"><a name="chmbookmark1204"></a>s </td></tr>
<tr><td colspan="4" valign="top"></td><td colspan="4" valign="top">.GLA[ND_COMPONENT]</td><td colspan="2" valign="top"><a name="chmbookmark1205"></a>Gland component name. </td><td valign="top"><a name="chmbookmark1206"></a>s </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.NGLA[ND]</td><td colspan="2" valign="top"><a name="chmbookmark1207"></a>Number of glands in penetration. </td><td valign="top"><a name="chmbookmark1208"></a>I </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.GLA[ND](I), I=1,NGLAND</td><td colspan="2" valign="top"><a name="chmbookmark1209"></a>Gland number. </td><td valign="top"><a name="chmbookmark1210"></a>- </td></tr>
<tr><td colspan="4" valign="top"></td><td colspan="4" valign="top">.GLA[ND_COMPONENT]</td><td colspan="2" valign="top"><a name="chmbookmark1211"></a>Gland component name. </td><td valign="top"><a name="chmbookmark1212"></a>s </td></tr>
<tr><td colspan="4" valign="top"></td><td colspan="4" valign="top">.CABLE_PRO[JECT]</td><td colspan="2" valign="top"><a name="chmbookmark1213"></a>Cable project name. </td><td valign="top"><a name="chmbookmark1214"></a>s </td></tr>
<tr><td colspan="4" valign="top"></td><td colspan="4" valign="top">.CABLE_NAM[E]</td><td colspan="2" valign="top"><a name="chmbookmark1215"></a>Cable name. </td><td valign="top"><a name="chmbookmark1216"></a>s </td></tr>
<tr><td colspan="2" valign="top"></td><td colspan="6" valign="top">.NUSER_ATT[RIBUTE]</td><td colspan="2" valign="top"><a name="chmbookmark1217"></a>Number of user attributes </td><td valign="top"><a name="chmbookmark1218"></a>i </td></tr>
<tr><td colspan="2" valign="top"></td><td colspan="6" valign="top">.USER_ATT[RIBUTE](i),</td><td colspan="2" valign="top"><a name="chmbookmark1219"></a>See [Information from a Drawing in Chapter Drafting Keywords](#chmbookmark438)</td><td valign="top"></td></tr>
</table>

<table><tr><th valign="bottom"><a name="chmbookmark1220"></a>![Previous Page][ ](#chmtopic48)</th><th valign="bottom">![Next Page][ ](#chmtopic40)</th><th> </th><th> </th><th></th></tr>
<tr><td colspan="5"></td></tr>
<tr><td colspan="4">Copyright © 1993-2005 AVEVA AB </td><td>![HOME - Generated from Data_Extraction_cable.fm in Data_Extraction.book][HOME - Generated from Data_ExtractionFP.fm in Data_Extraction.book][](#chmtopic1)</td></tr>
</table>

<table><tr><th colspan="5" valign="bottom"><a name="chmtopic51"></a>User's Guide Data Extraction </th><th valign="bottom">![Version]</th></tr>
<tr><td colspan="6"></td></tr>
<tr><td valign="top">![Previous Page][ ](#chmtopic41)</td><td valign="top">![Next Page][ ](#chmtopic56)</td><td colspan="4" valign="top"><b>Chapter: [Pipe Keywords](#chmtopic13)</b> </td></tr>
</table>

<a name="chmbookmark1221"></a>**9.3 Information from Pipe Line Object** 

<a name="chmbookmark1222"></a>The following is an overview of keywords available for pipe line objects:</sup> 

![Excel document]<a name="chmbookmark1223"></a>  DatExt\_Pipe\_Line.xls

<table><tr><th valign="bottom">![Previous Page][ ](#chmtopic41)</th><th valign="bottom">![Next Page][ ](#chmtopic56)</th><th> </th><th> </th><th></th></tr>
<tr><td colspan="5"></td></tr>
<tr><td colspan="4">Copyright © 1993-2005 AVEVA AB </td><td>![HOME - Generated from Data_Extraction_pipe.FM in Data_Extraction.book][HOME - Generated from Data_ExtractionFP.fm in Data_Extraction.book][](#chmtopic1)</td></tr>
</table>

<table><tr><th colspan="5" valign="bottom"><a name="chmtopic52"></a>User's Guide Data Extraction </th><th valign="bottom">![Version]</th></tr>
<tr><td colspan="6"></td></tr>
<tr><td valign="top">![Previous Page][ ](#chmtopic56)</td><td valign="top">![Next Page][ ](#chmtopic42)</td><td colspan="4" valign="top"><b>Chapter: [Pipe Keywords](#chmtopic13)</b> </td></tr>
</table>

<a name="chmbookmark1224"></a>**9.5 Information from Pipe Component in Pipe Object** 

<table><tr><th colspan="8" valign="top"><a name="chmbookmark1225"></a><b>Keyword</b> </th><th valign="top"><a name="chmbookmark1226"></a><b>Explanation</b> </th><th colspan="2" valign="top"><a name="chmbookmark1227"></a><b>Output</b> </th></tr>
<tr><td valign="top"><a name="chmbookmark1228"></a><b>1</b> </td><td valign="top"><a name="chmbookmark1229"></a><b>2</b> </td><td valign="top"><a name="chmbookmark1230"></a><b>3</b> </td><td valign="top"><a name="chmbookmark1231"></a><b>4</b> </td><td valign="top"><a name="chmbookmark1232"></a><b>5</b> </td><td valign="top"><a name="chmbookmark1233"></a><b>6</b> </td><td colspan="2" valign="top"></td><td colspan="2" valign="top"></td><td valign="top"></td></tr>
<tr><td colspan="6" valign="top"><a name="chmbookmark1234"></a><a name="chmbookmark1235"></a><a name="chmbookmark1236"></a>PIP[E][(s)],</td><td colspan="2" valign="top">s=<project></td><td colspan="2" valign="top"><a name="chmbookmark1237"></a>Pipe application </td><td valign="top"><a name="chmbookmark1238"></a>- </td></tr>
<tr><td valign="top"></td><td colspan="5" valign="top">.PIPO[BJECT](s), </td><td colspan="2" valign="top">s=<module>-<subsystem></td><td colspan="2" valign="top"><a name="chmbookmark1239"></a>Pipe spool </td><td valign="top"><a name="chmbookmark1240"></a>- </td></tr>
<tr><td colspan="2" valign="top"></td><td colspan="6" valign="top">.PIPC[OMP](s), s=<'sketch name'></td><td colspan="2" valign="top"><a name="chmbookmark1241"></a>Pipe component element </td><td valign="top"><a name="chmbookmark1242"></a>- </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.NAM[E]</td><td colspan="2" valign="top"><a name="chmbookmark1243"></a>Name of pipe component </td><td valign="top"><a name="chmbookmark1244"></a>s </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.COMP_N[AME]</td><td colspan="2" valign="top"><a name="chmbookmark1245"></a>Name of component </td><td valign="top"><a name="chmbookmark1246"></a>s </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.COMP_T[YPE]</td><td colspan="2" valign="top"><a name="chmbookmark1247"></a>Type of component </td><td valign="top"><a name="chmbookmark1248"></a>i </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.PSDB_I[D]</td><td colspan="2" valign="top"><a name="chmbookmark1249"></a>Id of the corresponding part on PSDB </td><td valign="top"><a name="chmbookmark1250"></a>i </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.MAT[ERIAL]</td><td colspan="2" valign="top"><a name="chmbookmark1251"></a>Material used </td><td valign="top"><a name="chmbookmark1252"></a>r </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.ROT[ATION]</td><td colspan="2" valign="top"><a name="chmbookmark1253"></a>Rotation angle </td><td valign="top"><a name="chmbookmark1254"></a>r </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.SYS_N[OTE]</td><td colspan="2" valign="top"><a name="chmbookmark1255"></a>System notation </td><td valign="top"><a name="chmbookmark1256"></a>s </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.ASS[EMBLY]</td><td colspan="2" valign="top"><a name="chmbookmark1257"></a>Assembly name </td><td valign="top"><a name="chmbookmark1258"></a>s </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.NNOD[E]</td><td colspan="2" valign="top"><a name="chmbookmark1259"></a>Number of node points </td><td valign="top"><a name="chmbookmark1260"></a>i </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.NODP[OINT](i), i=0,NNODE</td><td colspan="2" valign="top"><a name="chmbookmark1261"></a>Node point, by index </td><td valign="top"><a name="chmbookmark1262"></a>3r </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.NCON[NECTION]</td><td colspan="2" valign="top"><a name="chmbookmark1263"></a>Number of connections </td><td valign="top"><a name="chmbookmark1264"></a>i </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="7" valign="top">.CON[NECTION](i), i=1,NCONNECTION</td><td valign="top"></td></tr>
<tr><td colspan="8" valign="top"></td><td colspan="2" valign="top"><a name="chmbookmark1265"></a><a name="chmbookmark1266"></a>Connection, by index </td><td valign="top"><a name="chmbookmark1267"></a>- </td></tr>
<tr><td colspan="4" valign="top"></td><td colspan="4" valign="top">.VEC[TOR]</td><td colspan="2" valign="top"><a name="chmbookmark1268"></a>Vector </td><td valign="top"><a name="chmbookmark1269"></a>r </td></tr>
<tr><td colspan="4" valign="top"></td><td colspan="4" valign="top">.NOD[E]</td><td colspan="2" valign="top"><a name="chmbookmark1270"></a>Node </td><td valign="top"><a name="chmbookmark1271"></a>i </td></tr>
<tr><td colspan="4" valign="top"></td><td colspan="4" valign="top">.LEN[GTH]</td><td colspan="2" valign="top"><a name="chmbookmark1272"></a>Length </td><td valign="top"><a name="chmbookmark1273"></a>r </td></tr>
<tr><td colspan="4" valign="top"></td><td colspan="4" valign="top">.TYP[E]</td><td colspan="2" valign="top"><a name="chmbookmark1274"></a>Type </td><td valign="top"><a name="chmbookmark1275"></a>i </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.NCONN_D[ESCRIPTION]</td><td colspan="2" valign="top"><a name="chmbookmark1276"></a>Number of connection descriptions </td><td valign="top"><a name="chmbookmark1277"></a>i </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="7" valign="top">.CONN_D[ESCRIPTION](I), i=1,NCONN_DESCRIPTION</td><td valign="top"></td></tr>
<tr><td colspan="8" valign="top"></td><td colspan="2" valign="top"><a name="chmbookmark1278"></a><a name="chmbookmark1279"></a>Connection description, by index </td><td valign="top"><a name="chmbookmark1280"></a>- </td></tr>
<tr><td colspan="4" valign="top"></td><td colspan="4" valign="top">.TOT[AL_LENGTH]</td><td colspan="2" valign="top"><a name="chmbookmark1281"></a>Total length </td><td valign="top"><a name="chmbookmark1282"></a>r </td></tr>
<tr><td colspan="4" valign="top"></td><td colspan="4" valign="top">.OUT[ER_DIAMETER]</td><td colspan="2" valign="top"><a name="chmbookmark1283"></a>Outer diameter </td><td valign="top"><a name="chmbookmark1284"></a>r </td></tr>
<tr><td colspan="4" valign="top"></td><td colspan="4" valign="top">.PIPD[IAMETER]</td><td colspan="2" valign="top"><a name="chmbookmark1285"></a>Pipe diameter </td><td valign="top"><a name="chmbookmark1286"></a>r </td></tr>
<tr><td colspan="4" valign="top"></td><td colspan="4" valign="top">.NBHOLE</td><td colspan="2" valign="top"><a name="chmbookmark1287"></a>Number of bolt holes </td><td valign="top"><a name="chmbookmark1288"></a>i </td></tr>
<tr><td colspan="4" valign="top"></td><td colspan="4" valign="top">.BHOLE_L[ENGTH]</td><td colspan="2" valign="top"><a name="chmbookmark1289"></a>Bolt hole length </td><td valign="top"><a name="chmbookmark1290"></a>r </td></tr>
<tr><td colspan="4" valign="top"></td><td colspan="4" valign="top">.BHOLE_C[IRCLE_DIAMETER]</td><td colspan="2" valign="top"><a name="chmbookmark1291"></a>Bolt hole circle diameter </td><td valign="top"><a name="chmbookmark1292"></a>r </td></tr>
<tr><td colspan="4" valign="top"></td><td colspan="4" valign="top">.BHOLE_D[IAMETER]</td><td colspan="2" valign="top"><a name="chmbookmark1293"></a>Bolt hole diameter </td><td valign="top"><a name="chmbookmark1294"></a>r </td></tr>
<tr><td colspan="4" valign="top"></td><td colspan="4" valign="top">.WALLT[HICKNESS]</td><td colspan="2" valign="top"><a name="chmbookmark1295"></a>Wall thickness </td><td valign="top"><a name="chmbookmark1296"></a>r </td></tr>
<tr><td colspan="4" valign="top"></td><td colspan="4" valign="top">.GAS[KET_DIAMETER]</td><td colspan="2" valign="top"><a name="chmbookmark1297"></a>Gasket diameter </td><td valign="top"><a name="chmbookmark1298"></a>r </td></tr>
<tr><td colspan="4" valign="top"></td><td colspan="4" valign="top">.FLA[NGECODE]</td><td colspan="2" valign="top"><a name="chmbookmark1299"></a>Flange code </td><td valign="top"><a name="chmbookmark1300"></a>i </td></tr>
<tr><td colspan="4" valign="top"></td><td colspan="4" valign="top">.THR[EADCODE]</td><td colspan="2" valign="top"><a name="chmbookmark1301"></a>Thread code </td><td valign="top"><a name="chmbookmark1302"></a>i </td></tr>
<tr><td colspan="4" valign="top"></td><td colspan="4" valign="top">.NOM[INALDIAMETER]</td><td colspan="2" valign="top"><a name="chmbookmark1303"></a>Nominal diameter </td><td valign="top"><a name="chmbookmark1304"></a>i </td></tr>
<tr><td colspan="4" valign="top"></td><td colspan="4" valign="top">.PRES[SURE]</td><td colspan="2" valign="top"><a name="chmbookmark1305"></a>Pressure </td><td valign="top"><a name="chmbookmark1306"></a>i </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.SYM_F[ONT]</td><td colspan="2" valign="top"><a name="chmbookmark1307"></a>Symbol font number </td><td valign="top"><a name="chmbookmark1308"></a>i </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.SYM_N[UMBER]</td><td colspan="2" valign="top"><a name="chmbookmark1309"></a>Symbol number </td><td valign="top"><a name="chmbookmark1310"></a>i </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.SYM_S[CALE]</td><td colspan="2" valign="top"><a name="chmbookmark1311"></a>Scale factor </td><td valign="top"><a name="chmbookmark1312"></a>r </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.NSYM_P[ARAMETER]</td><td colspan="2" valign="top"><a name="chmbookmark1313"></a>Number of symbol parameters </td><td valign="top"><a name="chmbookmark1314"></a>i </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="7" valign="top">.SYM_P[ARAMETER](i), i=1,NSYM_PARAMETER</td><td valign="top"></td></tr>
<tr><td colspan="8" valign="top"></td><td colspan="2" valign="top"><a name="chmbookmark1315"></a><a name="chmbookmark1316"></a>Symbol parameter, by index </td><td valign="top"><a name="chmbookmark1317"></a>i </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.GEN_P[ROPERTY]</td><td colspan="2" valign="top"><a name="chmbookmark1318"></a>General property block </td><td valign="top"><a name="chmbookmark1319"></a>- </td></tr>
<tr><td colspan="4" valign="top"></td><td colspan="4" valign="top">.SUBP[ROJECT]</td><td colspan="2" valign="top"><a name="chmbookmark1320"></a>Subproject </td><td valign="top"><a name="chmbookmark1321"></a>s </td></tr>
<tr><td colspan="4" valign="top"></td><td colspan="4" valign="top">.NOT[E](i), i=1,3</td><td colspan="2" valign="top"><a name="chmbookmark1322"></a>Note </td><td valign="top"><a name="chmbookmark1323"></a>i </td></tr>
<tr><td colspan="4" valign="top"></td><td colspan="4" valign="top">.NOTE_C[ODE]</td><td colspan="2" valign="top"><a name="chmbookmark1324"></a>Note code </td><td valign="top"><a name="chmbookmark1325"></a>i </td></tr>
<tr><td colspan="4" valign="top"></td><td colspan="6" valign="top">.JOI[NT_PREPARATION_CODE]</td><td valign="top"></td></tr>
<tr><td colspan="8" valign="top"></td><td colspan="2" valign="top"><a name="chmbookmark1326"></a><a name="chmbookmark1327"></a>Joint preparation code </td><td valign="top"><a name="chmbookmark1328"></a>i </td></tr>
<tr><td colspan="4" valign="top"></td><td colspan="4" valign="top">.WELDC[ODE]</td><td colspan="2" valign="top"><a name="chmbookmark1329"></a>Weld code </td><td valign="top"><a name="chmbookmark1330"></a>i </td></tr>
<tr><td colspan="4" valign="top"></td><td colspan="4" valign="top">.HEATC[ODE]</td><td colspan="2" valign="top"><a name="chmbookmark1331"></a>Heat treatment code </td><td valign="top"><a name="chmbookmark1332"></a>i </td></tr>
<tr><td colspan="4" valign="top"></td><td colspan="4" valign="top">.TESTPRES[SURE]</td><td colspan="2" valign="top"><a name="chmbookmark1333"></a>Test pressure </td><td valign="top"><a name="chmbookmark1334"></a>r </td></tr>
<tr><td colspan="4" valign="top"></td><td colspan="4" valign="top">.MIN_T[EMPERATURE]</td><td colspan="2" valign="top"><a name="chmbookmark1335"></a>Minimum temperature </td><td valign="top"><a name="chmbookmark1336"></a>r </td></tr>
<tr><td colspan="4" valign="top"></td><td colspan="4" valign="top">.MAX_T[EMPERATURE]</td><td colspan="2" valign="top"><a name="chmbookmark1337"></a>Maximum temperature </td><td valign="top"><a name="chmbookmark1338"></a>r </td></tr>
<tr><td colspan="4" valign="top"></td><td colspan="4" valign="top">.WEI[GHT]</td><td colspan="2" valign="top"><a name="chmbookmark1339"></a>Weight (kg) </td><td valign="top"><a name="chmbookmark1340"></a>r </td></tr>
<tr><td colspan="4" valign="top"></td><td colspan="4" valign="top">.INS[ULATION_CODE]</td><td colspan="2" valign="top"><a name="chmbookmark1341"></a>Insulation code </td><td valign="top"><a name="chmbookmark1342"></a>i </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.MAN_P[ROPERTY]</td><td colspan="2" valign="top"><a name="chmbookmark1343"></a>Management property block </td><td valign="top"><a name="chmbookmark1344"></a>- </td></tr>
<tr><td colspan="4" valign="top"></td><td colspan="4" valign="top">.COMP_A[LIAS]</td><td colspan="2" valign="top"><a name="chmbookmark1345"></a>Component alias </td><td valign="top"><a name="chmbookmark1346"></a>s </td></tr>
<tr><td colspan="4" valign="top"></td><td colspan="4" valign="top">.ACQ[UISITION_CODE]</td><td colspan="2" valign="top"><a name="chmbookmark1347"></a>Acquisition code </td><td valign="top"><a name="chmbookmark1348"></a>s </td></tr>
<tr><td colspan="4" valign="top"></td><td colspan="4" valign="top">.CRE[ATION_DATE]</td><td colspan="2" valign="top"><a name="chmbookmark1349"></a>Date of creation </td><td valign="top"><a name="chmbookmark1350"></a>s </td></tr>
<tr><td colspan="4" valign="top"></td><td colspan="4" valign="top">.MOD[IFICATION_DATE]</td><td colspan="2" valign="top"><a name="chmbookmark1351"></a>Modification date </td><td valign="top"><a name="chmbookmark1352"></a>s </td></tr>
<tr><td colspan="4" valign="top"></td><td colspan="4" valign="top">.APPL_N[AME]</td><td colspan="2" valign="top"><a name="chmbookmark1353"></a>Application dependent name </td><td valign="top"><a name="chmbookmark1354"></a>s </td></tr>
<tr><td colspan="4" valign="top"></td><td colspan="4" valign="top">.APPL_A[LIAS]</td><td colspan="2" valign="top"><a name="chmbookmark1355"></a>Application dependent alias </td><td valign="top"><a name="chmbookmark1356"></a>s </td></tr>
<tr><td colspan="4" valign="top"></td><td colspan="4" valign="top">.PLA[NNING_UNIT]</td><td colspan="2" valign="top"><a name="chmbookmark1357"></a>Planning unit </td><td valign="top"><a name="chmbookmark1358"></a>s </td></tr>
<tr><td colspan="4" valign="top"></td><td colspan="4" valign="top">.PROD[UCTION_CODE]</td><td colspan="2" valign="top"><a name="chmbookmark1359"></a>Production code </td><td valign="top"><a name="chmbookmark1360"></a>i </td></tr>
<tr><td colspan="4" valign="top"></td><td colspan="6" valign="top">.US[ER_IDENTIFICATION_CODE]</td><td valign="top"></td></tr>
<tr><td colspan="8" valign="top"></td><td colspan="2" valign="top"><a name="chmbookmark1361"></a><a name="chmbookmark1362"></a>User identification string </td><td valign="top"><a name="chmbookmark1363"></a>s </td></tr>
<tr><td colspan="4" valign="top"></td><td colspan="4" valign="top">.DRA[WING_NAME]</td><td colspan="2" valign="top"><a name="chmbookmark1364"></a>Name of drawing </td><td valign="top"><a name="chmbookmark1365"></a>s </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.DESCR[IPTION]</td><td colspan="2" valign="top"><a name="chmbookmark1366"></a>Description string </td><td valign="top"></td></tr>
<tr><td colspan="2" valign="top"></td><td colspan="6" valign="top"><a name="chmbookmark1367"></a>.TDM_I[NFORMATION]</td><td colspan="2" valign="top"><a name="chmbookmark1368"></a>Data management </td><td valign="top"><a name="chmbookmark1369"></a>- </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.ALIAS1</td><td colspan="2" valign="top"><a name="chmbookmark1370"></a>Alias information </td><td valign="top"><a name="chmbookmark1371"></a>s </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.ALIAS2</td><td colspan="2" valign="top"><a name="chmbookmark1372"></a>Alias information </td><td valign="top"><a name="chmbookmark1373"></a>s </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.ALIAS3</td><td colspan="2" valign="top"><a name="chmbookmark1374"></a>Alias information </td><td valign="top"><a name="chmbookmark1375"></a>s </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.ALIAS4</td><td colspan="2" valign="top"><a name="chmbookmark1376"></a>Alias information </td><td valign="top"><a name="chmbookmark1377"></a>s </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.DESC[RIPTION]</td><td colspan="2" valign="top"><a name="chmbookmark1378"></a>Description </td><td valign="top"><a name="chmbookmark1379"></a>s </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.REM[ARKS]</td><td colspan="2" valign="top"><a name="chmbookmark1380"></a>Remarks </td><td valign="top"><a name="chmbookmark1381"></a>s </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.TYPE1</td><td colspan="2" valign="top"><a name="chmbookmark1382"></a>Type </td><td valign="top"><a name="chmbookmark1383"></a>s </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.TYPE2</td><td colspan="2" valign="top"><a name="chmbookmark1384"></a>Type </td><td valign="top"><a name="chmbookmark1385"></a>s </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.TYPE3</td><td colspan="2" valign="top"><a name="chmbookmark1386"></a>Type </td><td valign="top"><a name="chmbookmark1387"></a>s </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.TYPE4</td><td colspan="2" valign="top"><a name="chmbookmark1388"></a>Type </td><td valign="top"><a name="chmbookmark1389"></a>s </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.PLA[NINGUNIT]</td><td colspan="2" valign="top"><a name="chmbookmark1390"></a>Planning unit </td><td valign="top"><a name="chmbookmark1391"></a>s </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.COST[CODE]</td><td colspan="2" valign="top"><a name="chmbookmark1392"></a>Cost code </td><td valign="top"><a name="chmbookmark1393"></a>s </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.STATUS_D[ESIGN]</td><td colspan="2" valign="top"><a name="chmbookmark1394"></a>Status design </td><td valign="top"><a name="chmbookmark1395"></a>s </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.STATUS_MAT[ERIAL]</td><td colspan="2" valign="top"><a name="chmbookmark1396"></a>Status material </td><td valign="top"><a name="chmbookmark1397"></a>s </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.STATUS_MAN[UFACT]</td><td colspan="2" valign="top"><a name="chmbookmark1398"></a>Status manufact </td><td valign="top"><a name="chmbookmark1399"></a>s </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.STATUS_A[SSEMBLY]</td><td colspan="2" valign="top"><a name="chmbookmark1400"></a>Status material </td><td valign="top"><a name="chmbookmark1401"></a>s </td></tr>
<tr><td colspan="2" valign="top"></td><td colspan="6" valign="top">.TDM_R[EFERENCES]</td><td colspan="2" valign="top"><a name="chmbookmark1402"></a>Drawing references </td><td valign="top"><a name="chmbookmark1403"></a>- </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.NREF[ERENCE]</td><td colspan="2" valign="top"><a name="chmbookmark1404"></a>Number of references </td><td valign="top"><a name="chmbookmark1405"></a>i </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.REF[ERENCE]</td><td colspan="2" valign="top"><a name="chmbookmark1406"></a>Drawing references </td><td valign="top"><a name="chmbookmark1407"></a>s </td></tr>
</table>

<table><tr><th valign="bottom">![Previous Page][ ](#chmtopic56)</th><th valign="bottom">![Next Page][ ](#chmtopic42)</th><th> </th><th> </th><th></th></tr>
<tr><td colspan="5"></td></tr>
<tr><td colspan="4">Copyright © 1993-2005 AVEVA AB </td><td>![HOME - Generated from Data_Extraction_pipe.FM in Data_Extraction.book][HOME - Generated from Data_ExtractionFP.fm in Data_Extraction.book][](#chmtopic1)</td></tr>
</table>

<table><tr><th colspan="5" valign="bottom"><a name="chmtopic53"></a>User's Guide Data Extraction </th><th valign="bottom">![Version]</th></tr>
<tr><td colspan="6"></td></tr>
<tr><td valign="top">![Previous Page][ ](#chmtopic43)</td><td valign="top"></td><td colspan="4" valign="top"></b> </td></tr>
</table>

<a name="chmbookmark1408"></a>**11 Assembly Keywords** 

<a name="chmbookmark1409"></a>There is no implementation of Data Extraction for assemblies in Tribon M3. Instead the Vitesse Assembly Functions should be used. Reference: *Vitesse / Assembly / Functions*.

<table><tr><th valign="bottom">![Previous Page][ ](#chmtopic43)</th><th valign="bottom"></th><th> </th><th> </th><th></th></tr>
<tr><td colspan="5"></td></tr>
<tr><td colspan="4">Copyright © 1993-2005 AVEVA AB </td><td>![HOME - Generated from Data_Extraction_assembly.FM in Data_Extraction.book][HOME - Generated from Data_ExtractionFP.fm in Data_Extraction.book][](#chmtopic1)</td></tr>
</table>

<table><tr><th colspan="5" valign="bottom"><a name="chmtopic54"></a>User's Guide Data Extraction </th><th valign="bottom">![Version]</th></tr>
<tr><td colspan="6"></td></tr>
<tr><td valign="top">![Previous Page][ ](#chmtopic44)</td><td valign="top">![Next Page][ ](#chmtopic57)</td><td colspan="4" valign="top"><b>Chapter: [Basic Data Extraction](#chmtopic2)</b> </td></tr>
</table>

<a name="chmbookmark1410"></a>**1.2.4 Multiple Lines** 

<a name="chmbookmark1411"></a>When writing a command on the screen that includes more than 80 characters then use the hyphen as a continuation character. 

<table><tr><th colspan="2" valign="top"><a name="chmbookmark1412"></a>For example, the following command: </th></tr>
<tr><td valign="top">  </td><td valign="top">EQUIP('TB').ITEM(A).PIPE.-</td></tr>
<tr><td valign="top"></td><td valign="top">NCONNECTION</td></tr>
<tr><td colspan="2" valign="top"><a name="chmbookmark1413"></a>means exactly the same as </td></tr>
<tr><td valign="top"></td><td valign="top">EQUIP('TB').ITEM(*).PIPE.NCONNECTION</td></tr>
</table>

<table><tr><th valign="bottom">![Previous Page][ ](#chmtopic44)</th><th valign="bottom">![Next Page][ ](#chmtopic57)</th><th> </th><th> </th><th></th></tr>
<tr><td colspan="5"></td></tr>
<tr><td colspan="4">Copyright © 1993-2005 AVEVA AB </td><td>![HOME - Generated from Data_Extraction_basic.fm in Data_Extraction.book][HOME - Generated from Data_ExtractionFP.fm in Data_Extraction.book][](#chmtopic1)</td></tr>
</table>

<table><tr><th colspan="5" valign="bottom"><a name="chmtopic55"></a>User's Guide Data Extraction </th><th valign="bottom">![Version]</th></tr>
<tr><td colspan="6"></td></tr>
<tr><td valign="top">![Previous Page][ ](#chmtopic58)</td><td valign="top">![Next Page][ ](#chmtopic45)</td><td colspan="4" valign="top"><b>Chapter: [Basic Data Extraction](#chmtopic2)</b> </td></tr>
</table>

<a name="chmbookmark1414"></a>**1.5.1 Example files:** 

- <a name="chmbookmark1415"></a>Example 1: 

  ![Text document]  basic\_macro\_dex.txt

- <a name="chmbookmark1416"></a>Example 2: 

  ![Text document]  basic\_macro\_dexj.txt

<table><tr><th valign="bottom">![Previous Page][ ](#chmtopic58)</th><th valign="bottom">![Next Page][ ](#chmtopic45)</th><th> </th><th> </th><th></th></tr>
<tr><td colspan="5"></td></tr>
<tr><td colspan="4">Copyright © 1993-2005 AVEVA AB </td><td>![HOME - Generated from Data_Extraction_basic.fm in Data_Extraction.book][HOME - Generated from Data_ExtractionFP.fm in Data_Extraction.book][](#chmtopic1)</td></tr>
</table>

<table><tr><th colspan="5" valign="bottom"><a name="chmtopic56"></a>User's Guide Data Extraction </th><th valign="bottom">![Version]</th></tr>
<tr><td colspan="6"></td></tr>
<tr><td valign="top">![Previous Page][ ](#chmtopic51)</td><td valign="top">![Next Page][ ](#chmtopic52)</td><td colspan="4" valign="top"><b>Chapter: [Pipe Keywords](#chmtopic13)</b> </td></tr>
</table>

<a name="chmbookmark1417"></a>**9.4 Information from Pipe Spool Object** 

<table><tr><th colspan="8" valign="top"><a name="chmbookmark1418"></a><a name="chmbookmark1419"></a><b>Keyword</b></th><th valign="top"><a name="chmbookmark1420"></a><b>Explanation</b> </th><th colspan="2" valign="top"><a name="chmbookmark1421"></a><b>Output</b> </th></tr>
<tr><td valign="top"><a name="chmbookmark1422"></a><b>1</b> </td><td valign="top"><a name="chmbookmark1423"></a><b>2</b> </td><td valign="top"><a name="chmbookmark1424"></a><b>3</b> </td><td valign="top"><a name="chmbookmark1425"></a><b>4</b> </td><td valign="top"><a name="chmbookmark1426"></a><b>5</b> </td><td colspan="2" valign="top"><a name="chmbookmark1427"></a><b>6</b> </td><td valign="top"></td><td colspan="2" valign="top"></td><td valign="top"></td></tr>
<tr><td colspan="6" valign="top"><a name="chmbookmark1428"></a><a name="chmbookmark1429"></a><a name="chmbookmark1430"></a>PIP[E][(s)]</td><td colspan="2" valign="top">s=<project></td><td colspan="2" valign="top"><a name="chmbookmark1431"></a>Pipe application single </td><td valign="top"><a name="chmbookmark1432"></a>- </td></tr>
<tr><td colspan="8" valign="top"></td><td colspan="2" valign="top"><a name="chmbookmark1433"></a>(only single values in arg.) </td><td valign="top"></td></tr>
<tr><td valign="top"></td><td colspan="9" valign="top"><p><a name="chmbookmark1434"></a>.PIPS[POOL](s,</p><p>s=<module>-<subsystem>-<sketch name></p></td><td valign="top"></td></tr>
<tr><td colspan="8" valign="top"></td><td colspan="2" valign="top"><a name="chmbookmark1435"></a><a name="chmbookmark1436"></a>Pipe spool </td><td valign="top"><a name="chmbookmark1437"></a>- </td></tr>
<tr><td colspan="2" valign="top"></td><td colspan="5" valign="top">.NAM[E]		</td><td valign="top"></td><td colspan="2" valign="top"><a name="chmbookmark1438"></a>Name of pipe sketch object </td><td valign="top"><a name="chmbookmark1439"></a>s </td></tr>
<tr><td colspan="2" valign="top"></td><td colspan="5" valign="top">.PROJ[ECT]</td><td valign="top"></td><td colspan="2" valign="top"><a name="chmbookmark1440"></a>Name of project </td><td valign="top"></td></tr>
<tr><td colspan="2" valign="top"></td><td colspan="5" valign="top"><a name="chmbookmark1441"></a>.MAX[IMUM_ID]			</td><td valign="top"></td><td colspan="2" valign="top"><a name="chmbookmark1442"></a>Maximum identification </td><td valign="top"><a name="chmbookmark1443"></a>i </td></tr>
<tr><td colspan="2" valign="top"></td><td colspan="6" valign="top">.MOD[IFICATION_STRING]</td><td colspan="2" valign="top"><a name="chmbookmark1444"></a>Modification string </td><td valign="top"><a name="chmbookmark1445"></a>s </td></tr>
<tr><td colspan="2" valign="top"></td><td colspan="6" valign="top">.SYS_N[OTE]			</td><td colspan="2" valign="top"><a name="chmbookmark1446"></a>System notation </td><td valign="top"><a name="chmbookmark1447"></a>s </td></tr>
<tr><td colspan="2" valign="top"></td><td colspan="6" valign="top">.ASS[EMBLY]			</td><td colspan="2" valign="top"><a name="chmbookmark1448"></a>Assembly name </td><td valign="top"><a name="chmbookmark1449"></a>s </td></tr>
<tr><td colspan="2" valign="top"></td><td colspan="6" valign="top">.GEN_P[ROPERTY]			</td><td colspan="2" valign="top"><a name="chmbookmark1450"></a>General property block </td><td valign="top"><a name="chmbookmark1451"></a>- </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.SUBP[ROJECT]		</td><td colspan="2" valign="top"><a name="chmbookmark1452"></a>Subproject </td><td valign="top"><a name="chmbookmark1453"></a>s </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.NOT[E](i), i=1,3</td><td colspan="2" valign="top"><a name="chmbookmark1454"></a>Note </td><td valign="top"><a name="chmbookmark1455"></a>s </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.NOTE_C[ODE]		</td><td colspan="2" valign="top"><a name="chmbookmark1456"></a>Note code </td><td valign="top"><a name="chmbookmark1457"></a>i </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.MTRL_C[ODE]		</td><td colspan="2" valign="top"><a name="chmbookmark1458"></a>Material code </td><td valign="top"><a name="chmbookmark1459"></a>i </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="7" valign="top">.JOI[NT_PREPARATION_CODE]</td><td valign="top"></td></tr>
<tr><td colspan="8" valign="top"></td><td colspan="2" valign="top"><a name="chmbookmark1460"></a><a name="chmbookmark1461"></a>Joint preparation code </td><td valign="top"><a name="chmbookmark1462"></a>i </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.WELDC[ODE]</td><td colspan="2" valign="top"><a name="chmbookmark1463"></a>Weld code </td><td valign="top"><a name="chmbookmark1464"></a>i </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.HEATC[ODE]</td><td colspan="2" valign="top"><a name="chmbookmark1465"></a>Heat treatment code </td><td valign="top"><a name="chmbookmark1466"></a>i </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="7" valign="top">.SUR[FACE_TREATMENT_CODE]</td><td valign="top"><a name="chmbookmark1467"></a>- </td></tr>
<tr><td colspan="4" valign="top"></td><td colspan="4" valign="top">.ARE[A]</td><td colspan="2" valign="top"><a name="chmbookmark1468"></a>Area to be treated </td><td valign="top"><a name="chmbookmark1469"></a>i </td></tr>
<tr><td colspan="4" valign="top"></td><td colspan="4" valign="top">.NPRET[REATMENT]</td><td colspan="2" valign="top"><a name="chmbookmark1470"></a>No. of pretreatment lines </td><td valign="top"><a name="chmbookmark1471"></a>i </td></tr>
<tr><td colspan="4" valign="top"></td><td colspan="6" valign="top">.PRET[REATMENT](i), i=1,NPRETREATMENT</td><td valign="top"></td></tr>
<tr><td colspan="8" valign="top"></td><td colspan="2" valign="top"><a name="chmbookmark1472"></a><a name="chmbookmark1473"></a>Pretreatment </td><td valign="top"><a name="chmbookmark1474"></a>s </td></tr>
<tr><td colspan="4" valign="top"></td><td colspan="4" valign="top">.NOUTS[IDE_TREATMENT]</td><td colspan="2" valign="top"><a name="chmbookmark1475"></a>No. of outside treatment files </td><td valign="top"><a name="chmbookmark1476"></a>i </td></tr>
<tr><td colspan="4" valign="top"></td><td colspan="7" valign="top">.OUTS[IDE_TREATMENT](i), i=1,NOUTSIDE_TREATMENT</td></tr>
<tr><td colspan="8" valign="top"></td><td colspan="2" valign="top"><a name="chmbookmark1477"></a>Outside treatment </td><td valign="top"><a name="chmbookmark1478"></a>s </td></tr>
<tr><td colspan="4" valign="top"></td><td colspan="4" valign="top">.NINS[IDE_TREATMENT]</td><td colspan="2" valign="top"><a name="chmbookmark1479"></a>No. of inside treatment files </td><td valign="top"><a name="chmbookmark1480"></a>i </td></tr>
<tr><td colspan="4" valign="top"></td><td colspan="7" valign="top">.INS[IDE_TREATMENT](i), i=1,NINSIDE_TREATMENT</td></tr>
<tr><td colspan="8" valign="top"></td><td colspan="2" valign="top"><a name="chmbookmark1481"></a>Inside treatment </td><td valign="top"><a name="chmbookmark1482"></a>s </td></tr>
<tr><td colspan="4" valign="top"></td><td colspan="4" valign="top">.SHO[RT_CODE](i), i=1,2</td><td colspan="2" valign="top"><a name="chmbookmark1483"></a>Short code used in pipe shop </td><td valign="top"><a name="chmbookmark1484"></a>i </td></tr>
<tr><td colspan="4" valign="top"></td><td colspan="6" valign="top">.SUR[FACE_TREATMENT_CODE](i), i=1,2</td><td valign="top"></td></tr>
<tr><td colspan="8" valign="top"></td><td colspan="2" valign="top"><a name="chmbookmark1485"></a><a name="chmbookmark1486"></a>Surface treatment code </td><td valign="top"><a name="chmbookmark1487"></a>i </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.BEN[DING_RADIUS]</td><td colspan="2" valign="top"><a name="chmbookmark1488"></a>Bending radius </td><td valign="top"><a name="chmbookmark1489"></a>r </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.TESTPRES[SURE]</td><td colspan="2" valign="top"><a name="chmbookmark1490"></a>Test pressure </td><td valign="top"><a name="chmbookmark1491"></a>r </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.MIN_T[EMPERATURE]</td><td colspan="2" valign="top"><a name="chmbookmark1492"></a>Minimum temperature </td><td valign="top"><a name="chmbookmark1493"></a>r </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.MAX_T[EMPERATURE]</td><td colspan="2" valign="top"><a name="chmbookmark1494"></a>Maximum temperature </td><td valign="top"><a name="chmbookmark1495"></a>r </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.WEI[GHT]</td><td colspan="2" valign="top"><a name="chmbookmark1496"></a>Weight (kg) </td><td valign="top"><a name="chmbookmark1497"></a>r </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.INS[ULATION_CODE]</td><td colspan="2" valign="top"><a name="chmbookmark1498"></a>Insulation code </td><td valign="top"><a name="chmbookmark1499"></a>i </td></tr>
<tr><td colspan="2" valign="top"></td><td colspan="6" valign="top">.MAN_P[ROPERTY]</td><td colspan="2" valign="top"><a name="chmbookmark1500"></a>Management property block </td><td valign="top"><a name="chmbookmark1501"></a>- </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.COMP_A[LIAS]</td><td colspan="2" valign="top"><a name="chmbookmark1502"></a>Component alias </td><td valign="top"><a name="chmbookmark1503"></a>s </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.ACQ[UISITION_CODE]</td><td colspan="2" valign="top"><a name="chmbookmark1504"></a>Acquisition code </td><td valign="top"><a name="chmbookmark1505"></a>s </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.CRE[ATION_DATE]</td><td colspan="2" valign="top"><a name="chmbookmark1506"></a>Date of creation </td><td valign="top"><a name="chmbookmark1507"></a>s </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.MOD[IFICATION_DATE]</td><td colspan="2" valign="top"><a name="chmbookmark1508"></a>Modification date </td><td valign="top"><a name="chmbookmark1509"></a>s </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.APPL_D[ATE]</td><td colspan="2" valign="top"><a name="chmbookmark1510"></a>Application dependent date </td><td valign="top"><a name="chmbookmark1511"></a>s </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.STAND_I[D]</td><td colspan="2" valign="top"><a name="chmbookmark1512"></a>Standard id </td><td valign="top"><a name="chmbookmark1513"></a>s </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.APPL_N[AME]</td><td colspan="2" valign="top"><a name="chmbookmark1514"></a>Application dependent name </td><td valign="top"><a name="chmbookmark1515"></a>s </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.APPL_A[LIAS]</td><td colspan="2" valign="top"><a name="chmbookmark1516"></a>Application dependent alias </td><td valign="top"><a name="chmbookmark1517"></a>s </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.PLA[NNING_UNIT]</td><td colspan="2" valign="top"><a name="chmbookmark1518"></a>Planning unit </td><td valign="top"><a name="chmbookmark1519"></a>s </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.PROD[UCTION_CODE</td><td colspan="2" valign="top"><a name="chmbookmark1520"></a>Production code </td><td valign="top"><a name="chmbookmark1521"></a>i </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.US[ER_IDENTIFICATION_CODE]</td><td colspan="2" valign="top"><a name="chmbookmark1522"></a>User identification string </td><td valign="top"><a name="chmbookmark1523"></a>s </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.DRA[WING_NAME]</td><td colspan="2" valign="top"><a name="chmbookmark1524"></a>Name of drawing </td><td valign="top"><a name="chmbookmark1525"></a>s </td></tr>
<tr><td colspan="2" valign="top"></td><td colspan="6" valign="top">.DESCR[IPTION]</td><td colspan="2" valign="top"><a name="chmbookmark1526"></a>Description string </td><td valign="top"><a name="chmbookmark1527"></a>s </td></tr>
<tr><td colspan="2" valign="top"></td><td colspan="6" valign="top">.BRA[NCH](i), i=id 0, 999</td><td colspan="2" valign="top"><a name="chmbookmark1528"></a>Branch, by id </td><td valign="top"><a name="chmbookmark1529"></a>- </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.PSDB_N[AME]</td><td colspan="2" valign="top"><a name="chmbookmark1530"></a>Name of the pipe object on PSDB </td><td valign="top"><a name="chmbookmark1531"></a>s </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.PSDB_P[ROJECT]</td><td colspan="2" valign="top"><a name="chmbookmark1532"></a>Project name of the pipe object on PSDB </td><td valign="top"><a name="chmbookmark1533"></a>s </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.PSDB_I[D]</td><td colspan="2" valign="top"><a name="chmbookmark1534"></a>Id of the corresponding branch on PSDB. </td><td valign="top"><a name="chmbookmark1535"></a>i </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.SPART</td><td colspan="2" valign="top"><a name="chmbookmark1536"></a>PART LIST on Branch with order. </td><td valign="top"><a name="chmbookmark1537"></a>i </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.PART(i), i=id -1000, -32000</td><td colspan="2" valign="top"><a name="chmbookmark1538"></a>Part, by id. </td><td valign="top"><a name="chmbookmark1539"></a>- </td></tr>
<tr><td colspan="4" valign="top"></td><td colspan="4" valign="top">.COMP_N[AME]</td><td colspan="2" valign="top"><a name="chmbookmark1540"></a>Name of component. </td><td valign="top"><a name="chmbookmark1541"></a>s </td></tr>
<tr><td colspan="4" valign="top"></td><td colspan="4" valign="top">.COMP_T[YPE]</td><td colspan="2" valign="top"><a name="chmbookmark1542"></a>Type of component. </td><td valign="top"><a name="chmbookmark1543"></a>i </td></tr>
<tr><td colspan="4" valign="top"></td><td colspan="4" valign="top">.PSDB_I[D]</td><td colspan="2" valign="top"><a name="chmbookmark1544"></a>Id of the corresponding part on PSDB. </td><td valign="top"><a name="chmbookmark1545"></a>i </td></tr>
<tr><td colspan="4" valign="top"></td><td colspan="4" valign="top">.MTRL_REF[ERENCE]</td><td colspan="2" valign="top"><a name="chmbookmark1546"></a>Id of the corresponding material element. </td><td valign="top"><a name="chmbookmark1547"></a>i </td></tr>
<tr><td colspan="4" valign="top"></td><td colspan="4" valign="top">.REF_P[OINT]</td><td colspan="2" valign="top"><a name="chmbookmark1548"></a>Reference point number. </td><td valign="top"><a name="chmbookmark1549"></a>i </td></tr>
<tr><td colspan="4" valign="top"></td><td colspan="4" valign="top">.MTRL_US[AGE]</td><td colspan="2" valign="top"><a name="chmbookmark1550"></a>Portion of used material. </td><td valign="top"><a name="chmbookmark1551"></a>r </td></tr>
<tr><td colspan="4" valign="top"></td><td colspan="4" valign="top">.ROT[ATION]</td><td colspan="2" valign="top"><a name="chmbookmark1552"></a>Rotation angle. </td><td valign="top"><a name="chmbookmark1553"></a>r </td></tr>
<tr><td colspan="4" valign="top"></td><td colspan="4" valign="top">.NNOD[E]</td><td colspan="2" valign="top"><a name="chmbookmark1554"></a>Number of node points. </td><td valign="top"><a name="chmbookmark1555"></a>i </td></tr>
<tr><td colspan="4" valign="top"></td><td colspan="6" valign="top">.NODP[OINT](i), i=0,NNODE</td><td valign="top"></td></tr>
<tr><td colspan="8" valign="top"></td><td colspan="2" valign="top"><a name="chmbookmark1556"></a><a name="chmbookmark1557"></a>Node point, by index. </td><td valign="top"><a name="chmbookmark1558"></a>3r </td></tr>
<tr><td colspan="4" valign="top"></td><td colspan="4" valign="top">.NCON[NECTION]	</td><td colspan="2" valign="top"><a name="chmbookmark1559"></a>Number of connections. </td><td valign="top"><a name="chmbookmark1560"></a>i </td></tr>
<tr><td colspan="4" valign="top"></td><td colspan="6" valign="top">.CON[NECTION](i), i=1,NCONNECTION</td><td valign="top"></td></tr>
<tr><td colspan="8" valign="top"></td><td colspan="2" valign="top"><a name="chmbookmark1561"></a><a name="chmbookmark1562"></a>Connection, by index. </td><td valign="top"><a name="chmbookmark1563"></a>- </td></tr>
<tr><td colspan="5" valign="top"></td><td colspan="3" valign="top">.VEC[TOR]</td><td colspan="2" valign="top"><a name="chmbookmark1564"></a>Vector. </td><td valign="top"><a name="chmbookmark1565"></a>r </td></tr>
<tr><td colspan="5" valign="top"></td><td colspan="3" valign="top">.NOD[E]</td><td colspan="2" valign="top"><a name="chmbookmark1566"></a>Node. </td><td valign="top"><a name="chmbookmark1567"></a>i </td></tr>
<tr><td colspan="5" valign="top"></td><td colspan="3" valign="top">.LEN[GTH]</td><td colspan="2" valign="top"><a name="chmbookmark1568"></a>Length. </td><td valign="top"><a name="chmbookmark1569"></a>r </td></tr>
<tr><td colspan="5" valign="top"></td><td colspan="3" valign="top">.TYP[E]</td><td colspan="2" valign="top"><a name="chmbookmark1570"></a>Type. </td><td valign="top"><a name="chmbookmark1571"></a>i </td></tr>
<tr><td colspan="4" valign="top"></td><td colspan="4" valign="top">.GEN_P[ROPERTY]</td><td colspan="2" valign="top"><a name="chmbookmark1572"></a>General property block See:PIPE.PIPSPOOL.GEN _PROPERTY. </td><td valign="top"><a name="chmbookmark1573"></a>- </td></tr>
<tr><td colspan="4" valign="top"></td><td colspan="4" valign="top">.MAN_P[ROPERTY]</td><td colspan="2" valign="top"><a name="chmbookmark1574"></a>Management property block See:PIPE.PIPSPOOL.MA_PROPERTY. </td><td valign="top"><a name="chmbookmark1575"></a>- </td></tr>
<tr><td colspan="4" valign="top"></td><td colspan="4" valign="top">.DESCR[IPTION]</td><td colspan="2" valign="top"><a name="chmbookmark1576"></a>Description string. </td><td valign="top"><a name="chmbookmark1577"></a>s </td></tr>
<tr><td colspan="2" valign="top"></td><td colspan="6" valign="top">.MTRL(i), i=<mtrl.ref.></td><td colspan="2" valign="top"><a name="chmbookmark1578"></a>Material information. </td><td valign="top"><a name="chmbookmark1579"></a>- </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.COMP_N[AME]</td><td colspan="2" valign="top"><a name="chmbookmark1580"></a>Name of component. </td><td valign="top"><a name="chmbookmark1581"></a>s </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.COMP_T[YPE]</td><td colspan="2" valign="top"><a name="chmbookmark1582"></a>Type of component. </td><td valign="top"><a name="chmbookmark1583"></a>I </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.BRA[NCH_ID]</td><td colspan="2" valign="top"><a name="chmbookmark1584"></a>Id of branch. </td><td valign="top"><a name="chmbookmark1585"></a>i </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.NPART</td><td colspan="2" valign="top"><a name="chmbookmark1586"></a>Number of parts. </td><td valign="top"><a name="chmbookmark1587"></a>i </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.PART_I[D](i) i=1,NPART</td><td colspan="2" valign="top"><a name="chmbookmark1588"></a>Part, by index. </td><td valign="top"><a name="chmbookmark1589"></a>i </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.UN[IT_CODE]</td><td colspan="2" valign="top"><a name="chmbookmark1590"></a>Unit code. </td><td valign="top"><a name="chmbookmark1591"></a>i </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.QU[ANTITY]</td><td colspan="2" valign="top"><a name="chmbookmark1592"></a>Quantity. </td><td valign="top"></td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top"><a name="chmbookmark1593"></a>.BUILDING_L[ENGTH]</td><td colspan="2" valign="top"><a name="chmbookmark1594"></a>Building length. </td><td valign="top"><a name="chmbookmark1595"></a>r </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.GEN_P[ROPERTY]</td><td colspan="2" valign="top"><a name="chmbookmark1596"></a>General property block See: PIPE.PIPSPOOL.GEN _PROPERTY </td><td valign="top"></td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top"><a name="chmbookmark1597"></a>.MAN_P[ROPERTY]</td><td colspan="2" valign="top"><a name="chmbookmark1598"></a>Management property block See: PIPE.PIPSPOOL.MAN _PROPERTY </td><td valign="top"><a name="chmbookmark1599"></a>- </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.DESCR[IPTION]</td><td colspan="2" valign="top"><a name="chmbookmark1600"></a>Description string </td><td valign="top"><a name="chmbookmark1601"></a>s </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.BEND[ING]</td><td colspan="2" valign="top"><a name="chmbookmark1602"></a>Bending information </td><td valign="top"><a name="chmbookmark1603"></a>- </td></tr>
<tr><td colspan="4" valign="top"></td><td colspan="4" valign="top">.MAC[HINE]</td><td colspan="2" valign="top"><a name="chmbookmark1604"></a>Machine number </td><td valign="top"><a name="chmbookmark1605"></a>i </td></tr>
<tr><td colspan="4" valign="top"></td><td colspan="4" valign="top">.DIR[ECTION]</td><td colspan="2" valign="top"><a name="chmbookmark1606"></a>Operation direction </td><td valign="top"><a name="chmbookmark1607"></a>i </td></tr>
<tr><td colspan="4" valign="top"></td><td colspan="4" valign="top">.NAC[TIVITY]</td><td colspan="2" valign="top"><a name="chmbookmark1608"></a>Number of activities </td><td valign="top"><a name="chmbookmark1609"></a>i </td></tr>
<tr><td colspan="4" valign="top"></td><td colspan="6" valign="top">.ACT[IVITY_CODE](i), i=1,NACTIVITY</td><td valign="top"></td></tr>
<tr><td colspan="8" valign="top"></td><td colspan="2" valign="top"><p><a name="chmbookmark1610"></a><a name="chmbookmark1611"></a>Activity code, by index </p><p>=1 feed, unit mm </p><p>=2 turn, unit rad </p><p>=3 bend, unit rad </p></td><td valign="top"><a name="chmbookmark1612"></a>i </td></tr>
<tr><td colspan="4" valign="top"></td><td colspan="6" valign="top">.OPE[RATING_VALUE](i), i=1,NACTIVITY</td><td valign="top"></td></tr>
<tr><td colspan="8" valign="top"></td><td colspan="2" valign="top"><a name="chmbookmark1613"></a><a name="chmbookmark1614"></a>Operating value, by index </td><td valign="top"><a name="chmbookmark1615"></a>r </td></tr>
<tr><td colspan="8" valign="top"></td><td colspan="2" valign="top"><a name="chmbookmark1616"></a>E.g. bending and turning angle </td><td valign="top"></td></tr>
<tr><td colspan="4" valign="top"></td><td colspan="4" valign="top"><a name="chmbookmark1617"></a>.RAD[IUS]</td><td colspan="2" valign="top"><a name="chmbookmark1618"></a>Bending radius </td><td valign="top"><a name="chmbookmark1619"></a>r </td></tr>
<tr><td colspan="4" valign="top"></td><td colspan="4" valign="top">.CHE[CKSUM]</td><td colspan="2" valign="top"><a name="chmbookmark1620"></a>Check sum for machine </td><td valign="top"><a name="chmbookmark1621"></a>i </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.AUT[OWELDING]</td><td colspan="2" valign="top"><a name="chmbookmark1622"></a>Automatic welding info </td><td valign="top"><a name="chmbookmark1623"></a>- </td></tr>
<tr><td colspan="4" valign="top"></td><td colspan="6" valign="top"><p>.COD[E](i), i=<conn> 1,2</p><p>Welding code</p></td><td valign="top"><a name="chmbookmark1624"></a>i </td></tr>
<tr><td colspan="8" valign="top"></td><td colspan="2" valign="top"><p>=0 no auto welding </p><p>=1 auto welding </p></td><td valign="top"></td></tr>
<tr><td colspan="4" valign="top"></td><td colspan="4" valign="top"><a name="chmbookmark1625"></a>.ID(i),		 i=<flange>1,2</td><td colspan="2" valign="top"><p><a name="chmbookmark1626"></a>Material id </p><p>= -1 not auto welded </p><p>> 0 will be auto welded </p></td><td valign="top"><a name="chmbookmark1627"></a>i </td></tr>
<tr><td colspan="4" valign="top"></td><td colspan="4" valign="top">.ANG[LE]</td><td colspan="2" valign="top"><a name="chmbookmark1628"></a>Rotation angle between two flanges </td><td valign="top"><a name="chmbookmark1629"></a>r </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.REF_NO</td><td colspan="2" valign="top"><p><a name="chmbookmark1630"></a>Reference of pipe end going to machine </p><p>> 0 mtrl.number of connected part </p><p>< 0 reference point number </p></td><td valign="top"><a name="chmbookmark1631"></a>i </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.NJOI[NT]</td><td colspan="2" valign="top"><a name="chmbookmark1632"></a>Number of joints </td><td valign="top"><a name="chmbookmark1633"></a>i </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.JOI[NT](i),			 i=1,NJOINT</td><td colspan="2" valign="top"><a name="chmbookmark1634"></a>Joint, by index </td><td valign="top"><a name="chmbookmark1635"></a>- </td></tr>
<tr><td colspan="4" valign="top"></td><td colspan="4" valign="top">.INT_C[ODE]</td><td colspan="2" valign="top"><p><a name="chmbookmark1636"></a>Internal connection code </p><p><a name="chmbookmark1637"></a>How to treat this connection: </p><p>0 none </p><p>1-99 boss joint </p><p>1=USER_EXTRUDE </p><p>2=USER_INSERT </p><p>3=USER_SADDLE </p><p>4=USER_SURFACE </p><p>90=EXTRUDE_MANUAL </p><p>91=EXTRUDE </p><p>92=SURFACE </p><p>93=SADDLE </p><p>94=DRILL </p><p>95=WELD </p><p>96=SOLDER </p><p>97=SADDLE_NS </p><p>98=BURN </p><p>99=CENTRAL </p><p>101-104 joint excess </p><p>101=insert </p><p>201=mitre </p><p>300=thread </p><p>401=form </p></td><td valign="top"><a name="chmbookmark1638"></a>i </td></tr>
<tr><td colspan="4" valign="top"></td><td colspan="4" valign="top">.INT_E[XCESS]</td><td colspan="2" valign="top"><a name="chmbookmark1639"></a>Internal excess </td><td valign="top"><a name="chmbookmark1640"></a>r </td></tr>
<tr><td colspan="4" valign="top"></td><td colspan="4" valign="top">.EXT_C[ODE]</td><td colspan="2" valign="top"><p><a name="chmbookmark1641"></a>External connection code </p><p><a name="chmbookmark1642"></a>When to cut external excess: </p><p>0 = no excess </p><p>1 = in work shop </p><p>2 = on board </p></td><td valign="top"><a name="chmbookmark1643"></a>i </td></tr>
<tr><td colspan="4" valign="top"></td><td colspan="4" valign="top">.EXT_E[XCESS]</td><td colspan="2" valign="top"><a name="chmbookmark1644"></a>External excess </td><td valign="top"><a name="chmbookmark1645"></a>r </td></tr>
<tr><td colspan="4" valign="top"></td><td colspan="4" valign="top">.CONN_REF[ERENCE]</td><td colspan="2" valign="top"><p><a name="chmbookmark1646"></a>Connection reference </p><p>< 0 reference point number </p><p>= 0 no reference </p><p>> 0 mtrl.id </p></td><td valign="top"><a name="chmbookmark1647"></a>i </td></tr>
<tr><td colspan="4" valign="top"></td><td colspan="4" valign="top">.ROT[ATION]	</td><td colspan="2" valign="top"><a name="chmbookmark1648"></a>Rotation info </td><td valign="top"><a name="chmbookmark1649"></a>- </td></tr>
<tr><td colspan="5" valign="top"></td><td colspan="3" valign="top">.VEC[TOR]</td><td colspan="2" valign="top"><a name="chmbookmark1650"></a>Vector </td><td valign="top"><a name="chmbookmark1651"></a>3r </td></tr>
<tr><td colspan="5" valign="top"></td><td colspan="3" valign="top">.ANG[LE]</td><td colspan="2" valign="top"><a name="chmbookmark1652"></a>Rotation angle </td><td valign="top"><a name="chmbookmark1653"></a>r </td></tr>
<tr><td colspan="5" valign="top"></td><td colspan="3" valign="top">.REF[ERENCE]</td><td colspan="2" valign="top"><a name="chmbookmark1654"></a>Reference </td><td valign="top"><a name="chmbookmark1655"></a>- </td></tr>
<tr><td colspan="6" valign="top"></td><td colspan="2" valign="top">.PLANE_C[ODE]</td><td colspan="2" valign="top"><p><a name="chmbookmark1656"></a>Plane type code </p><p>= -1 undefined </p><p>=10 pipe bend, with no comp </p><p>=11 pipe bend, with comp. </p><p>=20 eccentric comp. </p><p>=25 mitre </p><p>=30 boss conn., with comp. </p><p>=40 boss conn., with no comp. </p><p>=45 comp. has more than two connections </p><p>=50 flange (with rot. def.) </p></td><td valign="top"><a name="chmbookmark1657"></a>i </td></tr>
<tr><td colspan="6" valign="top"></td><td colspan="2" valign="top">.PLANE_V[ECTOR] </td><td colspan="2" valign="top"><a name="chmbookmark1658"></a>Vector </td><td valign="top"><a name="chmbookmark1659"></a>3r </td></tr>
<tr><td colspan="6" valign="top"></td><td colspan="4" valign="top"><a name="chmbookmark1660"></a>.PLANE_P[ART](i), i=1,2</td><td valign="top"></td></tr>
<tr><td colspan="8" valign="top"></td><td colspan="2" valign="top"><a name="chmbookmark1661"></a><a name="chmbookmark1662"></a><a name="chmbookmark1663"></a>Part id </td><td valign="top"><a name="chmbookmark1664"></a>i </td></tr>
<tr><td colspan="4" valign="top"></td><td colspan="4" valign="top">.INC[LINATION]	</td><td colspan="2" valign="top"><a name="chmbookmark1665"></a>Inclination info </td><td valign="top"><a name="chmbookmark1666"></a>- </td></tr>
<tr><td colspan="5" valign="top"></td><td colspan="3" valign="top">.VEC[TOR]</td><td colspan="2" valign="top"><a name="chmbookmark1667"></a>Vector </td><td valign="top"><a name="chmbookmark1668"></a>3r </td></tr>
<tr><td colspan="5" valign="top"></td><td colspan="3" valign="top">.ANG[LE]</td><td colspan="2" valign="top"><a name="chmbookmark1669"></a>Turning angle </td><td valign="top"><a name="chmbookmark1670"></a>r </td></tr>
<tr><td colspan="5" valign="top"></td><td colspan="3" valign="top">.REF[ERENCE]</td><td colspan="2" valign="top"><a name="chmbookmark1671"></a>Reference </td><td valign="top"><a name="chmbookmark1672"></a>- </td></tr>
<tr><td colspan="6" valign="top"></td><td colspan="2" valign="top">.PLANE_V[ECTOR] </td><td colspan="2" valign="top"><a name="chmbookmark1673"></a>Vector </td><td valign="top"><a name="chmbookmark1674"></a>3r </td></tr>
<tr><td colspan="4" valign="top"></td><td colspan="4" valign="top">.TURN[ING]</td><td colspan="2" valign="top"><a name="chmbookmark1675"></a>Turning info </td><td valign="top"><a name="chmbookmark1676"></a>- </td></tr>
<tr><td colspan="5" valign="top"></td><td colspan="3" valign="top">.VEC[TOR]</td><td colspan="2" valign="top"><a name="chmbookmark1677"></a>Vector </td><td valign="top"><a name="chmbookmark1678"></a>3r </td></tr>
<tr><td colspan="5" valign="top"></td><td colspan="3" valign="top">.ANG[LE]</td><td colspan="2" valign="top"><a name="chmbookmark1679"></a>Turning angle </td><td valign="top"><a name="chmbookmark1680"></a>r </td></tr>
<tr><td colspan="5" valign="top"></td><td colspan="3" valign="top">.REF[ERENCE]</td><td colspan="2" valign="top"><a name="chmbookmark1681"></a>Reference </td><td valign="top"><a name="chmbookmark1682"></a>- </td></tr>
<tr><td colspan="6" valign="top"></td><td colspan="2" valign="top">.PLANE_C[ODE]</td><td colspan="2" valign="top"><a name="chmbookmark1683"></a>Plane type code </td><td valign="top"><a name="chmbookmark1684"></a>i </td></tr>
<tr><td colspan="8" valign="top"></td><td colspan="2" valign="top"><a name="chmbookmark1685"></a>See ROTATION.REFERENCE .PLANE_CODE </td><td valign="top"></td></tr>
<tr><td colspan="6" valign="top"></td><td colspan="2" valign="top"><a name="chmbookmark1686"></a>.PLANE_V[ECTOR] </td><td colspan="2" valign="top"><a name="chmbookmark1687"></a>Vector </td><td valign="top"><a name="chmbookmark1688"></a>3r </td></tr>
<tr><td colspan="6" valign="top"></td><td colspan="4" valign="top"><a name="chmbookmark1689"></a>.PLANE_P[ART](i), i=1,2</td><td valign="top"></td></tr>
<tr><td colspan="8" valign="top"></td><td colspan="2" valign="top"><a name="chmbookmark1690"></a><a name="chmbookmark1691"></a><a name="chmbookmark1692"></a>Part id </td><td valign="top"><a name="chmbookmark1693"></a>i </td></tr>
<tr><td colspan="4" valign="top"></td><td colspan="4" valign="top">.MIT[RE](i), i=<conn>1,2</td><td colspan="2" valign="top"><a name="chmbookmark1694"></a>Mitre info </td><td valign="top"><a name="chmbookmark1695"></a>- </td></tr>
<tr><td colspan="5" valign="top"></td><td colspan="3" valign="top">.VEC[TOR]</td><td colspan="2" valign="top"><a name="chmbookmark1696"></a>Vector </td><td valign="top"><a name="chmbookmark1697"></a>3r </td></tr>
<tr><td colspan="5" valign="top"></td><td colspan="3" valign="top">.TURN[ING_ANGLE]</td><td colspan="2" valign="top"><a name="chmbookmark1698"></a>Turning angle </td><td valign="top"><a name="chmbookmark1699"></a>r </td></tr>
<tr><td colspan="5" valign="top"></td><td colspan="3" valign="top">.INC[LINATION_ANGLE]</td><td colspan="2" valign="top"><a name="chmbookmark1700"></a>Inclination angle </td><td valign="top"><a name="chmbookmark1701"></a>r </td></tr>
<tr><td colspan="5" valign="top"></td><td colspan="3" valign="top">.REF[ERENCE]</td><td colspan="2" valign="top"><a name="chmbookmark1702"></a>Reference </td><td valign="top"><a name="chmbookmark1703"></a>- </td></tr>
<tr><td colspan="6" valign="top"></td><td colspan="2" valign="top">.PLANE_C[ODE]</td><td colspan="2" valign="top"><a name="chmbookmark1704"></a>Plane type code </td><td valign="top"><a name="chmbookmark1705"></a>i </td></tr>
<tr><td colspan="8" valign="top"></td><td colspan="2" valign="top"><a name="chmbookmark1706"></a>See ROTATION.REFERENCE .PLANE_CODE </td><td valign="top"></td></tr>
<tr><td colspan="6" valign="top"></td><td colspan="2" valign="top"><a name="chmbookmark1707"></a>.PLANE_V[ECTOR] </td><td colspan="2" valign="top"><a name="chmbookmark1708"></a>Vector </td><td valign="top"><a name="chmbookmark1709"></a>3r </td></tr>
<tr><td colspan="6" valign="top"></td><td colspan="4" valign="top"><a name="chmbookmark1710"></a>.PLANE_P[ART](i), i=1,2</td><td valign="top"></td></tr>
<tr><td colspan="8" valign="top"></td><td colspan="2" valign="top"><a name="chmbookmark1711"></a><a name="chmbookmark1712"></a>Part id </td><td valign="top"><a name="chmbookmark1713"></a>i </td></tr>
<tr><td colspan="6" valign="top"></td><td colspan="4" valign="top">.INCL_A[NGLE](i), i=1,3</td><td valign="top"></td></tr>
<tr><td colspan="8" valign="top"></td><td colspan="2" valign="top"><a name="chmbookmark1714"></a><a name="chmbookmark1715"></a>Inclination angle </td><td valign="top"><a name="chmbookmark1716"></a>r </td></tr>
<tr><td colspan="6" valign="top"></td><td colspan="2" valign="top">.MIT[RE_END]</td><td colspan="2" valign="top"><p><a name="chmbookmark1717"></a>Reference to mitre end </p><p><0 reference point number </p><p>=0 no reference </p><p>>0 material id </p></td><td valign="top"><a name="chmbookmark1718"></a>i </td></tr>
<tr><td colspan="6" valign="top"></td><td colspan="2" valign="top">.POI[NT_NO]</td><td colspan="2" valign="top"><a name="chmbookmark1719"></a>Point number if pipe bend is ref. plane </td><td valign="top"><a name="chmbookmark1720"></a>i </td></tr>
<tr><td colspan="4" valign="top"></td><td colspan="4" valign="top">.REF[ERENCE]</td><td colspan="2" valign="top"><p><a name="chmbookmark1721"></a>Used reference </p><p><0 reference point number </p><p>=0 no reference </p><p>>0 material id </p></td><td valign="top"><a name="chmbookmark1722"></a>i </td></tr>
<tr><td colspan="4" valign="top"></td><td colspan="4" valign="top">.NFE[ED]</td><td colspan="2" valign="top"><a name="chmbookmark1723"></a>Number of feed excess points </td><td valign="top"><a name="chmbookmark1724"></a>i </td></tr>
<tr><td colspan="4" valign="top"></td><td colspan="4" valign="top">.FE[ED](i), i=1,NFEED</td><td colspan="2" valign="top"><a name="chmbookmark1725"></a>Feed excess, by index </td><td valign="top"><a name="chmbookmark1726"></a>- </td></tr>
<tr><td colspan="5" valign="top"></td><td colspan="3" valign="top">.PART</td><td colspan="2" valign="top"><a name="chmbookmark1727"></a>Part with the feed excess </td><td valign="top"><a name="chmbookmark1728"></a>i </td></tr>
<tr><td colspan="5" valign="top"></td><td colspan="3" valign="top">.POI[NT]</td><td colspan="2" valign="top"><a name="chmbookmark1729"></a>Point with the feed excess </td><td valign="top"><a name="chmbookmark1730"></a>3r </td></tr>
<tr><td colspan="5" valign="top"></td><td colspan="3" valign="top">.EXC[ESS]</td><td colspan="2" valign="top"><a name="chmbookmark1731"></a>Feed excess </td><td valign="top"><a name="chmbookmark1732"></a>r </td></tr>
<tr><td colspan="5" valign="top"></td><td colspan="3" valign="top">.REF_NO</td><td colspan="2" valign="top"><a name="chmbookmark1733"></a>Reference point number </td><td valign="top"><a name="chmbookmark1734"></a>i </td></tr>
<tr><td colspan="2" valign="top"></td><td colspan="6" valign="top">.NBRA[NCH]</td><td colspan="2" valign="top"><a name="chmbookmark1735"></a>Number of branches </td><td valign="top"><a name="chmbookmark1736"></a>i </td></tr>
<tr><td colspan="2" valign="top"></td><td colspan="6" valign="top">.BRA_NO(i), i=0,NBRANCH -1</td><td colspan="2" valign="top"><a name="chmbookmark1737"></a>Branch, by index </td><td valign="top"><a name="chmbookmark1738"></a>- </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.BRA_ID</td><td colspan="2" valign="top"><a name="chmbookmark1739"></a>Branch id </td><td valign="top"><a name="chmbookmark1740"></a>i </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.NCLPART</td><td colspan="2" valign="top"><a name="chmbookmark1741"></a>Number of centre line parts </td><td valign="top"><a name="chmbookmark1742"></a>i </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="7" valign="top">.CLPART_NO(i), i=1,NCLPART</td><td valign="top"></td></tr>
<tr><td colspan="8" valign="top"></td><td colspan="2" valign="top"><a name="chmbookmark1743"></a><a name="chmbookmark1744"></a>Centre line part, by index </td><td valign="top"><a name="chmbookmark1745"></a>- </td></tr>
<tr><td colspan="4" valign="top"></td><td colspan="4" valign="top">.CLPART_ID</td><td colspan="2" valign="top"><a name="chmbookmark1746"></a>Centre line id </td><td valign="top"><a name="chmbookmark1747"></a>i </td></tr>
<tr><td colspan="4" valign="top"></td><td colspan="4" valign="top">.NPERPART</td><td colspan="2" valign="top"><a name="chmbookmark1748"></a>Number of peripheral parts </td><td valign="top"><a name="chmbookmark1749"></a>i </td></tr>
<tr><td colspan="4" valign="top"></td><td colspan="6" valign="top">.PERPART_NO(i), i=1,NPERPART</td><td valign="top"></td></tr>
<tr><td colspan="8" valign="top"></td><td colspan="2" valign="top"><a name="chmbookmark1750"></a><a name="chmbookmark1751"></a>Peripheral part, by index </td><td valign="top"><a name="chmbookmark1752"></a>- </td></tr>
<tr><td colspan="5" valign="top"></td><td colspan="3" valign="top">.PERPART_ID</td><td colspan="2" valign="top"><a name="chmbookmark1753"></a>Peripheral part id </td><td valign="top"><a name="chmbookmark1754"></a>i </td></tr>
<tr><td colspan="2" valign="top"></td><td colspan="6" valign="top">.NUSER_ATT[RIBUTE]</td><td colspan="2" valign="top"><a name="chmbookmark1755"></a>Number of user attributes </td><td valign="top"><a name="chmbookmark1756"></a>i </td></tr>
<tr><td colspan="2" valign="top"></td><td colspan="6" valign="top">.USER_ATT[RIBUTE](i),</td><td colspan="2" valign="top"><a name="chmbookmark1757"></a>See [Information from a Drawing in Chapter Drafting Keywords](#chmbookmark438)</td><td valign="top"></td></tr>
<tr><td colspan="2" valign="top"></td><td colspan="6" valign="top"><a name="chmbookmark1758"></a>.TDM_I[NFORMATION]</td><td colspan="2" valign="top"><a name="chmbookmark1759"></a>Data management </td><td valign="top"><a name="chmbookmark1760"></a>- </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.ALIAS1</td><td colspan="2" valign="top"><a name="chmbookmark1761"></a>Alias information </td><td valign="top"><a name="chmbookmark1762"></a>s </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.ALIAS2</td><td colspan="2" valign="top"><a name="chmbookmark1763"></a>Alias information </td><td valign="top"><a name="chmbookmark1764"></a>s </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.ALIAS3</td><td colspan="2" valign="top"><a name="chmbookmark1765"></a>Alias information </td><td valign="top"><a name="chmbookmark1766"></a>s </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.ALIAS4</td><td colspan="2" valign="top"><a name="chmbookmark1767"></a>Alias information </td><td valign="top"><a name="chmbookmark1768"></a>s </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.DESC[RIPTION]</td><td colspan="2" valign="top"><a name="chmbookmark1769"></a>Description </td><td valign="top"><a name="chmbookmark1770"></a>s </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.REM[ARKS]</td><td colspan="2" valign="top"><a name="chmbookmark1771"></a>Remarks </td><td valign="top"><a name="chmbookmark1772"></a>s </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.TYPE1</td><td colspan="2" valign="top"><a name="chmbookmark1773"></a>Type </td><td valign="top"><a name="chmbookmark1774"></a>s </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.TYPE2</td><td colspan="2" valign="top"><a name="chmbookmark1775"></a>Type </td><td valign="top"><a name="chmbookmark1776"></a>s </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.TYPE3</td><td colspan="2" valign="top"><a name="chmbookmark1777"></a>Type </td><td valign="top"><a name="chmbookmark1778"></a>s </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.TYPE4</td><td colspan="2" valign="top"><a name="chmbookmark1779"></a>Type </td><td valign="top"><a name="chmbookmark1780"></a>s </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.PLA[NINGUNIT]</td><td colspan="2" valign="top"><a name="chmbookmark1781"></a>Planning unit </td><td valign="top"><a name="chmbookmark1782"></a>s </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.COST[CODE]</td><td colspan="2" valign="top"><a name="chmbookmark1783"></a>Cost code </td><td valign="top"><a name="chmbookmark1784"></a>s </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.STATUS_D[ESIGN]</td><td colspan="2" valign="top"><a name="chmbookmark1785"></a>Status design </td><td valign="top"><a name="chmbookmark1786"></a>s </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.STATUS_MAT[ERIAL]</td><td colspan="2" valign="top"><a name="chmbookmark1787"></a>Status material </td><td valign="top"><a name="chmbookmark1788"></a>s </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.STATUS_MAN[UFACT]</td><td colspan="2" valign="top"><a name="chmbookmark1789"></a>Status manufact </td><td valign="top"><a name="chmbookmark1790"></a>s </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.STATUS_A[SSEMBLY]</td><td colspan="2" valign="top"><a name="chmbookmark1791"></a>Status material </td><td valign="top"><a name="chmbookmark1792"></a>s </td></tr>
<tr><td colspan="2" valign="top"></td><td colspan="6" valign="top">.TDM_R[EFERENCES]</td><td colspan="2" valign="top"><a name="chmbookmark1793"></a>Drawing references </td><td valign="top"><a name="chmbookmark1794"></a>- </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.NREF[ERENCE]</td><td colspan="2" valign="top"><a name="chmbookmark1795"></a>Number of references </td><td valign="top"><a name="chmbookmark1796"></a>i </td></tr>
<tr><td colspan="3" valign="top"></td><td colspan="5" valign="top">.REF[ERENCE]</td><td colspan="2" valign="top"><a name="chmbookmark1797"></a>Drawing references </td><td valign="top"><a name="chmbookmark1798"></a>s </td></tr>
</table>

<table><tr><th valign="bottom">![Previous Page][ ](#chmtopic51)</th><th valign="bottom">![Next Page][ ](#chmtopic52)</th><th> </th><th> </th><th></th></tr>
<tr><td colspan="5"></td></tr>
<tr><td colspan="4">Copyright © 1993-2005 AVEVA AB </td><td>![HOME - Generated from Data_Extraction_pipe.FM in Data_Extraction.book][HOME - Generated from Data_ExtractionFP.fm in Data_Extraction.book][](#chmtopic1)</td></tr>
</table>

<table><tr><th colspan="5" valign="bottom"><a name="chmtopic57"></a>User's Guide Data Extraction </th><th valign="bottom">![Version]</th></tr>
<tr><td colspan="6"></td></tr>
<tr><td valign="top">![Previous Page][ ](#chmtopic54)</td><td valign="top">![Next Page][ ](#chmtopic59)</td><td colspan="4" valign="top"><b>Chapter: [Basic Data Extraction](#chmtopic2)</b> </td></tr>
</table>

<a name="chmbookmark1799"></a>**1.2.5 Resulting Data Types** 

<a name="chmbookmark1800"></a>The result of a Data Extraction command is returned as one or several values, which can be of different data types. Below follows a list of the most common data types. 

|<a name="chmbookmark1801"></a>String (s) |<a name="chmbookmark1802"></a>A character varying string |
| :- | :- |
|<a name="chmbookmark1803"></a>Integer (i) |<a name="chmbookmark1804"></a>An integer value (double integer) |
|<a name="chmbookmark1805"></a>Real (r) |<a name="chmbookmark1806"></a>A real value (double precision) |
|<a name="chmbookmark1807"></a>Real vector(3r) |<a name="chmbookmark1808"></a>A vector with three real double precision values. |

<a name="chmbookmark1809"></a>A complete list of the data types may be found in the chapter *PROGRAMMING WITH DATA EXTRACTION*. 

<table><tr><th valign="bottom">![Previous Page][ ](#chmtopic54)</th><th valign="bottom">![Next Page][ ](#chmtopic59)</th><th> </th><th> </th><th></th></tr>
<tr><td colspan="5"></td></tr>
<tr><td colspan="4">Copyright © 1993-2005 AVEVA AB </td><td>![HOME - Generated from Data_Extraction_basic.fm in Data_Extraction.book][HOME - Generated from Data_ExtractionFP.fm in Data_Extraction.book][](#chmtopic1)</td></tr>
</table>

<table><tr><th colspan="5" valign="bottom"><a name="chmtopic58"></a>User's Guide Data Extraction </th><th valign="bottom">![Version]</th></tr>
<tr><td colspan="6"></td></tr>
<tr><td valign="top">![Previous Page][ ](#chmtopic60)</td><td valign="top">![Next Page][ ](#chmtopic55)</td><td colspan="4" valign="top"><b>Chapter: [Basic Data Extraction](#chmtopic2)</b> </td></tr>
</table>

<a name="chmbookmark1810"></a>**1.5 Data Extraction in Geometry Macro Language** 

<a name="chmbookmark1811"></a>In the Geometry Macro language it is possible to retrieve data with the Data Extraction syntax. 

<a name="chmbookmark1812"></a>With the EXTRACTION statement the wanted data is retrieved from the Tribon data base and put in the Data Extraction result structure. 

<a name="chmbookmark1813"></a>With the statement GET/EXTRACT or possibly GET/RANGE the extracted information is obtained and assigned to ordinary variables in the Geometry Macro language. 

<table><tr><th valign="bottom">![Previous Page][ ](#chmtopic60)</th><th valign="bottom">![Next Page][ ](#chmtopic55)</th><th> </th><th> </th><th></th></tr>
<tr><td colspan="5"></td></tr>
<tr><td colspan="4">Copyright © 1993-2005 AVEVA AB </td><td>![HOME - Generated from Data_Extraction_basic.fm in Data_Extraction.book][HOME - Generated from Data_ExtractionFP.fm in Data_Extraction.book][](#chmtopic1)</td></tr>
</table>

<table><tr><th colspan="5" valign="bottom"><a name="chmtopic59"></a>User's Guide Data Extraction </th><th valign="bottom">![Version]</th></tr>
<tr><td colspan="6"></td></tr>
<tr><td valign="top">![Previous Page][ ](#chmtopic57)</td><td valign="top">![Next Page][ ](#chmtopic61)</td><td colspan="4" valign="top"><b>Chapter: [Basic Data Extraction](#chmtopic2)</b> </td></tr>
</table>

<a name="chmbookmark1814"></a>**1.3 Environment** 

<a name="chmbookmark1815"></a>To be able to use the Data Extraction facilities, some files and logical names must be defined. 

<table><tr><th valign="bottom">![Previous Page][ ](#chmtopic57)</th><th valign="bottom">![Next Page][ ](#chmtopic61)</th><th> </th><th> </th><th></th></tr>
<tr><td colspan="5"></td></tr>
<tr><td colspan="4">Copyright © 1993-2005 AVEVA AB </td><td>![HOME - Generated from Data_Extraction_basic.fm in Data_Extraction.book][HOME - Generated from Data_ExtractionFP.fm in Data_Extraction.book][](#chmtopic1)</td></tr>
</table>

<table><tr><th colspan="5" valign="bottom"><a name="chmtopic60"></a>User's Guide Data Extraction </th><th valign="bottom">![Version]</th></tr>
<tr><td colspan="6"></td></tr>
<tr><td valign="top">![Previous Page][ ](#chmtopic62)</td><td valign="top">![Next Page][ ](#chmtopic58)</td><td colspan="4" valign="top"><b>Chapter: [Basic Data Extraction](#chmtopic2)</b> </td></tr>
</table>

<a name="chmbookmark1816"></a>**C++ Example** 

<a name="chmbookmark1817"></a>An example file can be found here: 

![Text document]  basic\_cplus\_example.txt

<a name="chmbookmark1818"></a>A sample MFC application that illustrates the usage of the Data Extraction component from C++ can be downloaded: 

![Zip file](ahh2b1y4.010.png)  Sx711.zip

. 

<table><tr><th valign="bottom">![Previous Page][ ](#chmtopic62)</th><th valign="bottom">![Next Page][ ](#chmtopic58)</th><th> </th><th> </th><th></th></tr>
<tr><td colspan="5"></td></tr>
<tr><td colspan="4">Copyright © 1993-2005 AVEVA AB </td><td>![HOME - Generated from Data_Extraction_basic.fm in Data_Extraction.book][HOME - Generated from Data_ExtractionFP.fm in Data_Extraction.book][](#chmtopic1)</td></tr>
</table>

<table><tr><th colspan="5" valign="bottom"><a name="chmtopic61"></a>User's Guide Data Extraction </th><th valign="bottom">![Version]</th></tr>
<tr><td colspan="6"></td></tr>
<tr><td valign="top">![Previous Page][ ](#chmtopic59)</td><td valign="top">![Next Page][ ](#chmtopic63)</td><td colspan="4" valign="top"><b>Chapter: [Basic Data Extraction](#chmtopic2)</b> </td></tr>
</table>

<a name="chmbookmark1819"></a>**1.3.1 Tribon Data Banks** 

<a name="chmbookmark1820"></a>Only the data banks needed for the specific data extraction command have to be assigned. 

<table><tr><th valign="bottom">![Previous Page][ ](#chmtopic59)</th><th valign="bottom">![Next Page][ ](#chmtopic63)</th><th> </th><th> </th><th></th></tr>
<tr><td colspan="5"></td></tr>
<tr><td colspan="4">Copyright © 1993-2005 AVEVA AB </td><td>![HOME - Generated from Data_Extraction_basic.fm in Data_Extraction.book][HOME - Generated from Data_ExtractionFP.fm in Data_Extraction.book][](#chmtopic1)</td></tr>
</table>

<table><tr><th colspan="5" valign="bottom"><a name="chmtopic62"></a>User's Guide Data Extraction </th><th valign="bottom">![Version]</th></tr>
<tr><td colspan="6"></td></tr>
<tr><td valign="top">![Previous Page][ ](#chmtopic64)</td><td valign="top">![Next Page][ ](#chmtopic60)</td><td colspan="4" valign="top"><b>Chapter: [Basic Data Extraction](#chmtopic2)</b> </td></tr>
</table>

<a name="chmbookmark1821"></a>**VBA Example** 

<a name="chmbookmark1822"></a>An example file can be found here: 

![Text document]  basic\_vba\_example.txt

<table><tr><th valign="bottom">![Previous Page][ ](#chmtopic64)</th><th valign="bottom">![Next Page][ ](#chmtopic60)</th><th> </th><th> </th><th></th></tr>
<tr><td colspan="5"></td></tr>
<tr><td colspan="4">Copyright © 1993-2005 AVEVA AB </td><td>![HOME - Generated from Data_Extraction_basic.fm in Data_Extraction.book][HOME - Generated from Data_ExtractionFP.fm in Data_Extraction.book][](#chmtopic1)</td></tr>
</table>

<table><tr><th colspan="5" valign="bottom"><a name="chmtopic63"></a>User's Guide Data Extraction </th><th valign="bottom">![Version]</th></tr>
<tr><td colspan="6"></td></tr>
<tr><td valign="top">![Previous Page][ ](#chmtopic61)</td><td valign="top">![Next Page][ ](#chmtopic65)</td><td colspan="4" valign="top"><b>Chapter: [Basic Data Extraction](#chmtopic2)</b> </td></tr>
</table>

<a name="chmbookmark1823"></a>**General Design** 

|<a name="chmbookmark1824"></a>SB\_PDB |<a name="chmbookmark1825"></a>Drawing Data Bank |
| :- | :- |
|<a name="chmbookmark1826"></a>SBD\_VOLUME |<a name="chmbookmark1827"></a>Volume Data Bank |
|<a name="chmbookmark1828"></a>SBE\_GENCMPDB |<a name="chmbookmark1829"></a>General Component Data Bank |
|<a name="chmbookmark1830"></a>SBE\_GENEQPDB |<a name="chmbookmark1831"></a>General Equipment Data Bank |

<table><tr><th valign="bottom">![Previous Page][ ](#chmtopic61)</th><th valign="bottom">![Next Page][ ](#chmtopic65)</th><th> </th><th> </th><th></th></tr>
<tr><td colspan="5"></td></tr>
<tr><td colspan="4">Copyright © 1993-2005 AVEVA AB </td><td>![HOME - Generated from Data_Extraction_basic.fm in Data_Extraction.book][HOME - Generated from Data_ExtractionFP.fm in Data_Extraction.book][](#chmtopic1)</td></tr>
</table>

<table><tr><th colspan="5" valign="bottom"><a name="chmtopic64"></a>User's Guide Data Extraction </th><th valign="bottom">![Version]</th></tr>
<tr><td colspan="6"></td></tr>
<tr><td valign="top">![Previous Page][ ](#chmtopic66)</td><td valign="top">![Next Page][ ](#chmtopic62)</td><td colspan="4" valign="top"><b>Chapter: [Basic Data Extraction](#chmtopic2)</b> </td></tr>
</table>

<a name="chmbookmark1832"></a>**ConvertToImperial Method** 

<a name="chmbookmark1833"></a>The ConvertToImperial method makes it possible to display result values in Imperial Units. Conversion to imperial units will be made if the Tribon environment variable for the unit is set to IMP (see *chapter 5 in the Basic Users Guide*).

ConvertToImperial(

`   `VARIANT value,

`   `short   quantity,

`   `short   nDecimals,

`   `short   notation,

`   `short   syntax,

`   `VARIANT \*pVal);

<a name="chmbookmark1834"></a>**Parameter description:** 

<table><tr><th colspan="3" valign="top">value</th><th colspan="2" valign="top"><a name="chmbookmark1835"></a>- VARIANT containing the value to convert </th></tr>
<tr><td colspan="3" valign="top">quantity</td><td colspan="2" valign="top"><a name="chmbookmark1836"></a>- The quantity type: </td></tr>
<tr><td valign="top">     </td><td valign="top">1</td><td colspan="2" valign="top">(XD969_LIN_MEAS)</td><td valign="top"><a name="chmbookmark1837"></a>Linear </td></tr>
<tr><td valign="top"></td><td valign="top">2</td><td colspan="2" valign="top">(XD969_WEIGHT)</td><td valign="top"><a name="chmbookmark1838"></a>Weight </td></tr>
<tr><td valign="top"></td><td valign="top">3</td><td colspan="2" valign="top">(XD969_DENSITY)</td><td valign="top"><a name="chmbookmark1839"></a>Density </td></tr>
<tr><td valign="top"></td><td valign="top">4</td><td colspan="2" valign="top">(XD969_AREA)</td><td valign="top"><a name="chmbookmark1840"></a>Area </td></tr>
<tr><td valign="top"></td><td valign="top">5</td><td colspan="2" valign="top">(XD969_VOLUME)</td><td valign="top"><a name="chmbookmark1841"></a>Volume </td></tr>
<tr><td valign="top"></td><td valign="top">6</td><td colspan="2" valign="top">(XD969_VELOCITY)</td><td valign="top"><a name="chmbookmark1842"></a>Velocity </td></tr>
<tr><td valign="top"></td><td valign="top">7</td><td colspan="2" valign="top">(XD969_FLOW)</td><td valign="top"><a name="chmbookmark1843"></a>Flow </td></tr>
<tr><td valign="top"></td><td valign="top">8</td><td colspan="2" valign="top">(XD969_PRE_DROP)</td><td valign="top"><a name="chmbookmark1844"></a>Pressure drop </td></tr>
<tr><td valign="top"></td><td valign="top">9</td><td colspan="2" valign="top">(XD969_TEMP)</td><td valign="top"><a name="chmbookmark1845"></a>Temperature </td></tr>
<tr><td valign="top"></td><td valign="top">10</td><td colspan="2" valign="top">(XD969_IMPEDANCE)</td><td valign="top"><a name="chmbookmark1846"></a>Impedance </td></tr>
<tr><td valign="top"></td><td valign="top">11</td><td colspan="2" valign="top">(XD969_LUMINANCE)</td><td valign="top"><a name="chmbookmark1847"></a>Luminance </td></tr>
<tr><td valign="top"></td><td valign="top">12</td><td colspan="2" valign="top">(XD969_INDUCTANCE)</td><td valign="top"><a name="chmbookmark1848"></a>Inductance </td></tr>
<tr><td valign="top"></td><td valign="top">13</td><td colspan="2" valign="top">(XD969_COORD)</td><td valign="top"><a name="chmbookmark1849"></a>Coordinate </td></tr>
<tr><td colspan="3" valign="top">nDecimals</td><td colspan="2" valign="top"><a name="chmbookmark1850"></a>- Maximum number of decimals to use </td></tr>
<tr><td colspan="3" valign="top">notation</td><td valign="top"><a name="chmbookmark1851"></a>- The wanted notation: </td><td valign="top"></td></tr>
<tr><td valign="top"><a name="chmbookmark1852"></a>     </td><td valign="top">1</td><td colspan="2" valign="top">(WY268_FIXED)</td><td valign="top"><a name="chmbookmark1853"></a>Syntax with fixed point notation </td></tr>
<tr><td valign="top"></td><td valign="top">2</td><td colspan="2" valign="top">(WY268_SCIENT)</td><td valign="top"><a name="chmbookmark1854"></a>Syntax with scientific notation </td></tr>
<tr><td colspan="3" valign="top">syntax</td><td colspan="2" valign="top"><a name="chmbookmark1855"></a>The wanted presentation syntax: </td></tr>
<tr><td valign="top"></td><td valign="top">1</td><td colspan="2" valign="top">(WY268_OUTPUT)</td><td valign="top"><a name="chmbookmark1856"></a>Output syntax </td></tr>
<tr><td valign="top"></td><td valign="top">2</td><td colspan="2" valign="top">(WY268_INPUT)</td><td valign="top"><a name="chmbookmark1857"></a>Input syntax </td></tr>
<tr><td valign="top"></td><td colspan="4" valign="top"><a name="chmbookmark1858"></a>Only relevant for imperial coordinates and linear measures, where:<br>'OUTPUT' means delimiters ' and ", and 'INPUT' means delimiters F and I. </td></tr>
<tr><td colspan="3" valign="top">` `pVal</td><td colspan="2" valign="top"><a name="chmbookmark1859"></a>- return value </td></tr>
</table>

<table><tr><th valign="bottom">![Previous Page][ ](#chmtopic66)</th><th valign="bottom">![Next Page][ ](#chmtopic62)</th><th> </th><th> </th><th></th></tr>
<tr><td colspan="5"></td></tr>
<tr><td colspan="4">Copyright © 1993-2005 AVEVA AB </td><td>![HOME - Generated from Data_Extraction_basic.fm in Data_Extraction.book][HOME - Generated from Data_ExtractionFP.fm in Data_Extraction.book][](#chmtopic1)</td></tr>
</table>

<table><tr><th colspan="5" valign="bottom"><a name="chmtopic65"></a>User's Guide Data Extraction </th><th valign="bottom">![Version]</th></tr>
<tr><td colspan="6"></td></tr>
<tr><td valign="top">![Previous Page][ ](#chmtopic63)</td><td valign="top">![Next Page][ ](#chmtopic67)</td><td colspan="4" valign="top"><b>Chapter: [Basic Data Extraction](#chmtopic2)</b> </td></tr>
</table>

<a name="chmbookmark1860"></a>**Hull** 

|<a name="chmbookmark1861"></a>SB\_OGDB |<a name="chmbookmark1862"></a>Hull Data Bank |
| :- | :- |
|<a name="chmbookmark1863"></a>SB\_PLDB |<a name="chmbookmark1864"></a>Hull Plate Data Bank |

<table><tr><th valign="bottom">![Previous Page][ ](#chmtopic63)</th><th valign="bottom">![Next Page][ ](#chmtopic67)</th><th> </th><th> </th><th></th></tr>
<tr><td colspan="5"></td></tr>
<tr><td colspan="4">Copyright © 1993-2005 AVEVA AB </td><td>![HOME - Generated from Data_Extraction_basic.fm in Data_Extraction.book][HOME - Generated from Data_ExtractionFP.fm in Data_Extraction.book][](#chmtopic1)</td></tr>
</table>

<table><tr><th colspan="5" valign="bottom"><a name="chmtopic66"></a>User's Guide Data Extraction </th><th valign="bottom">![Version]</th></tr>
<tr><td colspan="6"></td></tr>
<tr><td valign="top">![Previous Page][ ](#chmtopic68)</td><td valign="top">![Next Page][ ](#chmtopic64)</td><td colspan="4" valign="top"><b>Chapter: [Basic Data Extraction](#chmtopic2)</b> </td></tr>
</table>

<a name="chmbookmark1865"></a>**GetResTree Method** 

<a name="chmbookmark1866"></a>The GetResTree method returns the result string of the current value in the result structure and is implemented as a function. The string is returned as an array ordered in pairs with statements in even positions and arguments in odd positions. In the example of 1.2.1 the 0 element would hold the string 'HULL' the 1 element would hold an empty string, the 2 element would hold 'PANEL' and the 3 element would hold the panel name of the currently extracted object etc. 

<a name="chmbookmark1867"></a>**Note:**   As this method does not move the result structure pointer it can only be used together with GetValue method. 

<a name="chmbookmark1868"></a>**Example of use (VBA):** 

|<a name="chmbookmark1869"></a>**Example:** |
| :- |
|ResultTree = dextr.GetResTree|
|<a name="chmbookmark1870"></a>Resulting array of data extraction input string from 1.2.1 could look like this example. |
|<p>ResultTree(0) = "HULL"</p><p>ResultTree(1) = ""</p><p>ResultTree(2) = "PANEL"</p><p>ResultTree(3) = "ES123-621"</p><p>ResultTree(4) = "NBRACKET"</p><p>ResultTree(5) = ""</p>|

<table><tr><th valign="bottom">![Previous Page][ ](#chmtopic68)</th><th valign="bottom">![Next Page][ ](#chmtopic64)</th><th> </th><th> </th><th></th></tr>
<tr><td colspan="5"></td></tr>
<tr><td colspan="4">Copyright © 1993-2005 AVEVA AB </td><td>![HOME - Generated from Data_Extraction_basic.fm in Data_Extraction.book][HOME - Generated from Data_ExtractionFP.fm in Data_Extraction.book][](#chmtopic1)</td></tr>
</table>

<table><tr><th colspan="5" valign="bottom"><a name="chmtopic67"></a>User's Guide Data Extraction </th><th valign="bottom">![Version]</th></tr>
<tr><td colspan="6"></td></tr>
<tr><td valign="top">![Previous Page][ ](#chmtopic65)</td><td valign="top">![Next Page][ ](#chmtopic69)</td><td colspan="4" valign="top"><b>Chapter: [Basic Data Extraction](#chmtopic2)</b> </td></tr>
</table>

<a name="chmbookmark1871"></a>**Pipe** 

|<a name="chmbookmark1872"></a>SB\_PSDB |<a name="chmbookmark1873"></a>Pipe Structure Data Bank |
| :- | :- |
|<a name="chmbookmark1874"></a>SB\_PPDB |<a name="chmbookmark1875"></a>Pipe Production Data Bank |
|<a name="chmbookmark1876"></a>SB\_SURT |<a name="chmbookmark1877"></a>Surface Treatment file |

<table><tr><th valign="bottom">![Previous Page][ ](#chmtopic65)</th><th valign="bottom">![Next Page][ ](#chmtopic69)</th><th> </th><th> </th><th></th></tr>
<tr><td colspan="5"></td></tr>
<tr><td colspan="4">Copyright © 1993-2005 AVEVA AB </td><td>![HOME - Generated from Data_Extraction_basic.fm in Data_Extraction.book][HOME - Generated from Data_ExtractionFP.fm in Data_Extraction.book][](#chmtopic1)</td></tr>
</table>

<table><tr><th colspan="5" valign="bottom"><a name="chmtopic68"></a>User's Guide Data Extraction </th><th valign="bottom">![Version]</th></tr>
<tr><td colspan="6"></td></tr>
<tr><td valign="top">![Previous Page][ ](#chmtopic70)</td><td valign="top">![Next Page][ ](#chmtopic66)</td><td colspan="4" valign="top"><b>Chapter: [Basic Data Extraction](#chmtopic2)</b> </td></tr>
</table>

<a name="chmbookmark1878"></a>**GetValue Method** 

<a name="chmbookmark1879"></a>The GetValue method gets the next value in the Data extraction result structure and is implemented as a function, which means that it will return the value. This method starts by moving the pointer to the next result value in the result structure and ends by returning the value found. If the last value in the structure is current when calling GetValue the result will be a Boolean variable set to the value FALSE. 

<a name="chmbookmark1880"></a>**Example of the GetValue method** 

Value = dextr.GetValue

<table><tr><th valign="bottom">![Previous Page][ ](#chmtopic70)</th><th valign="bottom">![Next Page][ ](#chmtopic66)</th><th> </th><th> </th><th></th></tr>
<tr><td colspan="5"></td></tr>
<tr><td colspan="4">Copyright © 1993-2005 AVEVA AB </td><td>![HOME - Generated from Data_Extraction_basic.fm in Data_Extraction.book][HOME - Generated from Data_ExtractionFP.fm in Data_Extraction.book][](#chmtopic1)</td></tr>
</table>

<table><tr><th colspan="5" valign="bottom"><a name="chmtopic69"></a>User's Guide Data Extraction </th><th valign="bottom">![Version]</th></tr>
<tr><td colspan="6"></td></tr>
<tr><td valign="top">![Previous Page][ ](#chmtopic67)</td><td valign="top">![Next Page][ ](#chmtopic71)</td><td colspan="4" valign="top"><b>Chapter: [Basic Data Extraction](#chmtopic2)</b> </td></tr>
</table>

<a name="chmbookmark1881"></a>**Structure** 

|<a name="chmbookmark1882"></a>SBF\_DB\_FSTRU |<a name="chmbookmark1883"></a>Structure model data bank |
| :- | :- |

<table><tr><th valign="bottom">![Previous Page][ ](#chmtopic67)</th><th valign="bottom">![Next Page][ ](#chmtopic71)</th><th> </th><th> </th><th></th></tr>
<tr><td colspan="5"></td></tr>
<tr><td colspan="4">Copyright © 1993-2005 AVEVA AB </td><td>![HOME - Generated from Data_Extraction_basic.fm in Data_Extraction.book][HOME - Generated from Data_ExtractionFP.fm in Data_Extraction.book][](#chmtopic1)</td></tr>
</table>

<table><tr><th colspan="5" valign="bottom"><a name="chmtopic70"></a>User's Guide Data Extraction </th><th valign="bottom">![Version]</th></tr>
<tr><td colspan="6"></td></tr>
<tr><td valign="top">![Previous Page][ ](#chmtopic72)</td><td valign="top">![Next Page][ ](#chmtopic68)</td><td colspan="4" valign="top"><b>Chapter: [Basic Data Extraction](#chmtopic2)</b> </td></tr>
</table>

<a name="chmbookmark1884"></a>**DoDataExtraction Method** 

<a name="chmbookmark1885"></a>The DoDataExtraction method takes a string as parameter and the string to input is the string that the Data Extraction program would take to extract the desired data. 

<a name="chmbookmark1886"></a>**Example of how to call (VBA), there the object name is defined as dextr.** 

dextr.DoDataExtraction "HULL.PANEL('ES123'\*).NBRA"

<table><tr><th valign="bottom">![Previous Page][ ](#chmtopic72)</th><th valign="bottom">![Next Page][ ](#chmtopic68)</th><th> </th><th> </th><th></th></tr>
<tr><td colspan="5"></td></tr>
<tr><td colspan="4">Copyright © 1993-2005 AVEVA AB </td><td>![HOME - Generated from Data_Extraction_basic.fm in Data_Extraction.book][HOME - Generated from Data_ExtractionFP.fm in Data_Extraction.book][](#chmtopic1)</td></tr>
</table>

<table><tr><th colspan="5" valign="bottom"><a name="chmtopic71"></a>User's Guide Data Extraction </th><th valign="bottom">![Version]</th></tr>
<tr><td colspan="6"></td></tr>
<tr><td valign="top">![Previous Page][ ](#chmtopic69)</td><td valign="top">![Next Page][ ](#chmtopic73)</td><td colspan="4" valign="top"><b>Chapter: [Basic Data Extraction](#chmtopic2)</b> </td></tr>
</table>

<a name="chmbookmark1887"></a>**Cable** 

|<a name="chmbookmark1888"></a>SBC\_CABSTRDB |<a name="chmbookmark1889"></a>Cable model DB |
| :- | :- |
|<a name="chmbookmark1890"></a>SBC\_CAWSTRDB |<a name="chmbookmark1891"></a>Cableway model DB |

<table><tr><th valign="bottom">![Previous Page][ ](#chmtopic69)</th><th valign="bottom">![Next Page][ ](#chmtopic73)</th><th> </th><th> </th><th></th></tr>
<tr><td colspan="5"></td></tr>
<tr><td colspan="4">Copyright © 1993-2005 AVEVA AB </td><td>![HOME - Generated from Data_Extraction_basic.fm in Data_Extraction.book][HOME - Generated from Data_ExtractionFP.fm in Data_Extraction.book][](#chmtopic1)</td></tr>
</table>

<table><tr><th colspan="5" valign="bottom"><a name="chmtopic72"></a>User's Guide Data Extraction </th><th valign="bottom">![Version]</th></tr>
<tr><td colspan="6"></td></tr>
<tr><td valign="top">![Previous Page][ ](#chmtopic74)</td><td valign="top">![Next Page][ ](#chmtopic70)</td><td colspan="4" valign="top"><b>Chapter: [Basic Data Extraction](#chmtopic2)</b> </td></tr>
</table>

<a name="chmbookmark1892"></a>**1.4.2 Using the COM-object** 

<table><tr><th valign="bottom">![Previous Page][ ](#chmtopic74)</th><th valign="bottom">![Next Page][ ](#chmtopic70)</th><th> </th><th> </th><th></th></tr>
<tr><td colspan="5"></td></tr>
<tr><td colspan="4">Copyright © 1993-2005 AVEVA AB </td><td>![HOME - Generated from Data_Extraction_basic.fm in Data_Extraction.book][HOME - Generated from Data_ExtractionFP.fm in Data_Extraction.book][](#chmtopic1)</td></tr>
</table>

<table><tr><th colspan="5" valign="bottom"><a name="chmtopic73"></a>User's Guide Data Extraction </th><th valign="bottom">![Version]</th></tr>
<tr><td colspan="6"></td></tr>
<tr><td valign="top">![Previous Page][ ](#chmtopic71)</td><td valign="top">![Next Page][ ](#chmtopic75)</td><td colspan="4" valign="top"><b>Chapter: [Basic Data Extraction](#chmtopic2)</b> </td></tr>
</table>

<a name="chmbookmark1893"></a>**1.3.2 Tribon Environment Variables** 

|<a name="chmbookmark1894"></a>SB\_PROJECT |<a name="chmbookmark1895"></a>Default Project Name. |
| :- | :- |
||<a name="chmbookmark1896"></a><a name="chmbookmark1897"></a>If default project is not defined, then the project must be given in the extraction command. |
|<a name="chmbookmark1898"></a>SB\_CUSTOMER |<a name="chmbookmark1899"></a>Default Customer |
|<a name="chmbookmark1900"></a>SB\_LANGUAGE |<a name="chmbookmark1901"></a>Default Language. |
||<a name="chmbookmark1902"></a><a name="chmbookmark1903"></a>The language parameter is used only for components and equipment. If not defined, then the parameter = 1. |
|<a name="chmbookmark1904"></a>SBD\_ALIAS |<a name="chmbookmark1905"></a>Alias file. |
||<a name="chmbookmark1906"></a><a name="chmbookmark1907"></a>Used only for drawings. |
|<a name="chmbookmark1908"></a>SB\_SREF |<a name="chmbookmark1909"></a>Hull Structure Reference Object. |
||<a name="chmbookmark1910"></a><a name="chmbookmark1911"></a>Used only for hull extraction. |

<table><tr><th valign="bottom">![Previous Page][ ](#chmtopic71)</th><th valign="bottom">![Next Page][ ](#chmtopic75)</th><th> </th><th> </th><th></th></tr>
<tr><td colspan="5"></td></tr>
<tr><td colspan="4">Copyright © 1993-2005 AVEVA AB </td><td>![HOME - Generated from Data_Extraction_basic.fm in Data_Extraction.book][HOME - Generated from Data_ExtractionFP.fm in Data_Extraction.book][](#chmtopic1)</td></tr>
</table>

<table><tr><th colspan="5" valign="bottom"><a name="chmtopic74"></a>User's Guide Data Extraction </th><th valign="bottom">![Version]</th></tr>
<tr><td colspan="6"></td></tr>
<tr><td valign="top">![Previous Page][ ](#chmtopic76)</td><td valign="top">![Next Page][ ](#chmtopic72)</td><td colspan="4" valign="top"><b>Chapter: [Basic Data Extraction](#chmtopic2)</b> </td></tr>
</table>

<a name="chmbookmark1912"></a>**1.4.1 General** 

<a name="chmbookmark1913"></a>This COM-object includes the Data Extraction program but extends the use to handle over more of the integration and implementation of the Data Extraction program to the user. It allows integration in own developed programs using the programming language that best suits the application, include it in VBA (Visual Basic for Applications)-scripts for Microsoft products such as Word, Excel etc. All connections between the COM-object and the application are done through an interface. 

<a name="chmbookmark1914"></a>COM is short for Component Object Model. 

<a name="chmbookmark1915"></a>For further information on using data extraction COM-object, see the customised examples in your Tribon installation. 

<table><tr><th valign="bottom">![Previous Page][ ](#chmtopic76)</th><th valign="bottom">![Next Page][ ](#chmtopic72)</th><th> </th><th> </th><th></th></tr>
<tr><td colspan="5"></td></tr>
<tr><td colspan="4">Copyright © 1993-2005 AVEVA AB </td><td>![HOME - Generated from Data_Extraction_basic.fm in Data_Extraction.book][HOME - Generated from Data_ExtractionFP.fm in Data_Extraction.book][](#chmtopic1)</td></tr>
</table>

<table><tr><th colspan="5" valign="bottom"><a name="chmtopic75"></a>User's Guide Data Extraction </th><th valign="bottom">![Version]</th></tr>
<tr><td colspan="6"></td></tr>
<tr><td valign="top">![Previous Page][ ](#chmtopic73)</td><td valign="top">![Next Page][ ](#chmtopic76)</td><td colspan="4" valign="top"><b>Chapter: [Basic Data Extraction](#chmtopic2)</b> </td></tr>
</table>

<a name="chmbookmark1916"></a>**1.3.3 Miscellaneous Files** 

|<a name="chmbookmark1917"></a>D0300069.SBM |<a name="chmbookmark1918"></a>Message file |
| :- | :- |
|<a name="chmbookmark1919"></a>D0300093.SBM |<a name="chmbookmark1920"></a>Message file |

<table><tr><th valign="bottom">![Previous Page][ ](#chmtopic73)</th><th valign="bottom">![Next Page][ ](#chmtopic76)</th><th> </th><th> </th><th></th></tr>
<tr><td colspan="5"></td></tr>
<tr><td colspan="4">Copyright © 1993-2005 AVEVA AB </td><td>![HOME - Generated from Data_Extraction_basic.fm in Data_Extraction.book][HOME - Generated from Data_ExtractionFP.fm in Data_Extraction.book][](#chmtopic1)</td></tr>
</table>

<table><tr><th colspan="5" valign="bottom"><a name="chmtopic76"></a>User's Guide Data Extraction </th><th valign="bottom">![Version]</th></tr>
<tr><td colspan="6"></td></tr>
<tr><td valign="top">![Previous Page][ ](#chmtopic75)</td><td valign="top">![Next Page][ ](#chmtopic74)</td><td colspan="4" valign="top"><b>Chapter: [Basic Data Extraction](#chmtopic2)</b> </td></tr>
</table>

<a name="chmbookmark1921"></a>**1.4 Data Extraction COM-object** 

<table><tr><th valign="bottom">![Previous Page][ ](#chmtopic75)</th><th valign="bottom">![Next Page][ ](#chmtopic74)</th><th> </th><th> </th><th></th></tr>
<tr><td colspan="5"></td></tr>
<tr><td colspan="4">Copyright © 1993-2005 AVEVA AB </td><td>![HOME - Generated from Data_Extraction_basic.fm in Data_Extraction.book][HOME - Generated from Data_ExtractionFP.fm in Data_Extraction.book][](#chmtopic1)</td></tr>
</table>

[Next Page]: ahh2b1y4.002.png
[HOME - Generated from Data_ExtractionFP.fm in Data_Extraction.book]: ahh2b1y4.003.png
[Version]: ahh2b1y4.004.png
[Previous Page]: ahh2b1y4.005.png
[Excel document]: ahh2b1y4.006.png
[Text document]: ahh2b1y4.009.png
