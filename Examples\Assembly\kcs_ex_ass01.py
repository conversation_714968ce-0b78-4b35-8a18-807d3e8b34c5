import string
import kcs_ui
import kcs_util
import kcs_assembly
import Kcs<PERSON>tringlist
import KcsBox
import KcsDate
import KcsModel
import KcsAssembly
import KcsAssemblyKeyInItem
import KcsPoint2D
import KcsPoint3D
import KcsVector3D
import KcsStat_point3D_req

OK = kcs_util.ok()
CANCEL  = kcs_util.cancel()


# types for objects in list returned by GetAssemblyTree function
ASSEMBLY    = 0
MODEL_PART  = 1
KEYIN_PART  = 2
KEYIN_COMP  = 3


ITEMTYPE = 0
# valid for assembly tree node
ASS_USERNAME = 1
ASS_INTNAME  = 2
ASS_OFFSET   = 3

# valid for model tree node
MOD_OBJECT     = 1
MOD_PARENTASS  = 2
MOD_OFFSET     = 3

# valid for keyin component and keyin part tree node
KEYIN_OBJECT   = 1
KEYIN_PARENTASS= 2
KEYIN_OFFSET   = 3

#---------------------------------------------------------------------------------
# this function builds assembly tree and returns it as list of tupples:
#
#      [ (itemtype, ...), (itemtype, ...), (itemtype, ...) ]
#
# contents of tuple depends on itemtype:
#
#      itemtype = ASSEMBLY       (ASSEMBLY, username, internal_name, tree_level)
#      itemtype = MODEL_PART     (MODEL_PART, Model, parent_assembly_intname, tree_level)
#      itemtype = KEYIN_PART     (KEYIN_PART, AssemblyKeyInItem, parent_assembly_intname, tree_level)
#      itemtype = KEYIN_COMP     (KEYIN_COMP, AssemblyKeyInItem, parent_assembly_intname, tree_level)
#
#---------------------------------------------------------------------------------
def GetAssemblyTree(bWithParts):
   resultList = []
   try:
      rootass = kcs_assembly.assembly_sub_get()
      for assembly in rootass:
         resultList.append((ASSEMBLY, assembly[0], assembly[1], 0))
         ListSubAssemblies(resultList, 1, assembly[1], bWithParts)
   except:
      print sys.exc_info()[1]
      print kcs_assembly.error
   return resultList

#---------------------------------------------------------------------------------
def ListSubAssemblies(resultList, offset, name, bWithParts):
   try:
      subAssemblies = kcs_assembly.assembly_sub_get(name)

      # create subassemblies subtree
      for assembly in subAssemblies:
         resultList.append((ASSEMBLY, assembly[0], assembly[1], offset))
         ListSubAssemblies(resultList, offset+1, assembly[1], bWithParts)

      # create model info
      if bWithParts:
         try:
            models = kcs_assembly.assembly_model_ref_get(name)
            for model in models:
               resultList.append((MODEL_PART, model, name, offset))
         except:
            print 'exception in assembly_model_ref_get:', kcs_assembly.error
         try:
            keyins = kcs_assembly.assembly_keyin_ref_get(name)
            for keyin in keyins:
               print '----------'
               print keyin
               if keyin.GetItemType() == 'key-in part':
                  resultList.append((KEYIN_PART, keyin, name, offset))
               else:
                  resultList.append((KEYIN_COMP, keyin, name, offset))
         except:
            print 'exception in assembly_keyin_ref_get:', kcs_assembly.error

   except:
      pass

#---------------------------------------------------------------------------------
def SelectAssembly():
   resultList = GetAssemblyTree(0)

   strlist = KcsStringlist.Stringlist('')
   strlist.StrList = []

   for item in resultList:
      strlist.StrList.append( '     ' * item[ASS_OFFSET] + (string.split(item[ASS_USERNAME], '-'))[-1] + ' ('+item[ASS_INTNAME]+')')

   if len(strlist.StrList) == 0:
      kcs_ui.message_confirm('No assemblies definitions!')
      return 0, '', ''

   res, selected = kcs_ui.string_select('Assemblies', '', '', strlist)
   if res == OK:
      return 1, resultList[selected-1]
   return 0, '', ''

#---------------------------------------------------------------------------------
def SelectAssemblyElement(elements, prompt=''):

   if (MODEL_PART in elements) or (KEYIN_PART in elements) or (KEYIN_COMP in elements):
      resultList = GetAssemblyTree(1)
   else:
      resultList = GetAssemblyTree(0)

   strlist = KcsStringlist.Stringlist('')
   strlist.StrList = []

   for item in resultList:
      if item[ITEMTYPE] == ASSEMBLY:
         strlist.StrList.append( '     ' * item[ASS_OFFSET] + (string.split(item[ASS_USERNAME], '-'))[-1] + ' ('+item[ASS_INTNAME]+')')
      elif item[ITEMTYPE] == MODEL_PART:
         strlist.StrList.append( '     ' * item[MOD_OFFSET] + 'model: ' + item[MOD_OBJECT].GetName())
      elif item[ITEMTYPE] == KEYIN_PART:
         strlist.StrList.append( '     ' * item[KEYIN_OFFSET] + 'keyin part: ' + item[KEYIN_OBJECT].GetName())
      elif item[ITEMTYPE] == KEYIN_COMP:
         strlist.StrList.append( '     ' * item[KEYIN_OFFSET] + 'keyin comp: ' + item[KEYIN_OBJECT].GetName())

   if len(strlist.StrList) == 0:
      kcs_ui.message_confirm('No assemblies definitions!')
      return 0, None

   while(1):
      res, selected = kcs_ui.string_select('Assemblies', 'Double click to get assembly or part info', '', strlist)
      if res == OK:
         if (resultList[selected-1])[ITEMTYPE] in elements:
            return 1, resultList[selected-1]
      else:
         return 0, None

#---------------------------------------------------------------------------------
def ShowElementProperties(element):
   if element[ITEMTYPE] == ASSEMBLY:
      try:
         kcs_assembly.assembly_activate(element[ASS_INTNAME])
         object = kcs_assembly.assembly_properties_get()
         kcs_assembly.assembly_cancel()
      except:
         print sys.exc_info()[1]
         object = 'not able to get properties of: ' + element[ASS_USERNAME] + '\n'
         object = object + 'because of: ' + kcs_assembly.error
         kcs_assembly.assembly_cancel()
   elif element[ITEMTYPE] == MODEL_PART:
      object = element[MOD_OBJECT]

      strRepresent = str(object)
      strings = string.split(strRepresent, '\n')

      stringlist = KcsStringlist.Stringlist(strings[0])
      stringlist.AddString(strings[1])
      stringlist.AddString(strings[2])
      stringlist.AddString(strings[4])
      stringlist.AddString(strings[7])
      kcs_ui.string_select('Item', '', '', stringlist)
      return

   elif element[ITEMTYPE] == KEYIN_PART or element[ITEMTYPE] == KEYIN_COMP:
      object = element[KEYIN_OBJECT]
   else:
      object = None

   strRepresent = str(object)
   strings = string.split(strRepresent, '\n')

   if len(strings)>0:
      stringlist = KcsStringlist.Stringlist(strings[0])
      stringlist.StrList = []
      for item in strings:
         stringlist.AddString(item)
      kcs_ui.string_select('Item', 'Model reference properties', '', stringlist)
   else:
      kcs_ui.message_confirm('No string representation of given object')

#---------------------------------------------------------------------------------
# Decollect model reference
#---------------------------------------------------------------------------------
def DecollectModelReference():
   while 1:
      res, element = SelectAssemblyElement(((KEYIN_PART, KEYIN_COMP, MODEL_PART)))
      if res:
         if element[ITEMTYPE]==MODEL_PART:
            object = element[MOD_OBJECT]
            parent = element[MOD_PARENTASS]
         elif element[ITEMTYPE]==KEYIN_PART or element[ITEMTYPE]==KEYIN_COMP:
            object = element[KEYIN_OBJECT]
            parent = element[KEYIN_PARENTASS]
         else:
            raise ValueError, 'Wrong element selected!'

         try:
            kcs_assembly.assembly_activate(parent)
            kcs_assembly.model_decollect(object)
         finally:
            try:
               kcs_assembly.assembly_save()
            except:
               kcs_assembly.assembly_cancel()
               pass
      else:
         break


#---------------------------------------------------------------------------------
# Create path name
#---------------------------------------------------------------------------------
def CreatePathName():
   try:
      # receive name of assembly
      assemblyParent = kcs_ui.string_req("Key in name of parent name assembly :")
      assembly = kcs_ui.string_req("Key in name of assembly :")
      if assembly[0] == OK:
         try:
            name = kcs_assembly.assembly_path_name_create(assemblyParent[1], assembly[1])
            kcs_ui.message_confirm("Assembly " + name + " exist !" )
         except:
            kcs_ui.message_confirm("Path name of assembly can not created !" )
            print kcs_assembly.error
   except:
      kcs_ui.message_noconfirm("Invalid input parameter !" )
      print kcs_ui.error

#---------------------------------------------------------------------------------
# Change path in internal name
#---------------------------------------------------------------------------------
def Path2Internal():
   try:
      # receive name of assembly
      assembly = kcs_ui.string_req("Key in name of path name :")
      if assembly[0] == OK:
         try:
            name = kcs_assembly.assembly_internal_name_get(assembly[1])
            kcs_ui.message_confirm("Assembly " + name + " exist !" )
         except:
            kcs_ui.message_confirm("Assembly does not exist." )
            print kcs_assembly.error
   except:
      kcs_ui.message_noconfirm("Invalid input parameter !" )
      print kcs_ui.error

#---------------------------------------------------------------------------------
# Change internal in path name
#---------------------------------------------------------------------------------
def Internal2Path():
   try:
      # receive name of assembly
      assembly = kcs_ui.string_req("Key in internal name for assembly: ")
      if assembly[0] == OK:
         try:
            name = kcs_assembly.assembly_path_name_get(assembly[1])
            kcs_ui.message_confirm("Assembly " + name + " exist !" )
         except:
            kcs_ui.message_confirm("Assembly does not exist." )
            print kcs_assembly.error
   except:
      kcs_ui.message_noconfirm("Invalid input parameter !" )
      print kcs_ui.error


#---------------------------------------------------------------------------------
# Model Draw
#---------------------------------------------------------------------------------
def ModelDraw():
   # check licence
   try:
      import kcs_draft
   except:
       kcs_ui.message_confirm("You have not licence of drafting !!!" )


   try:
      res, model.Type = kcs_ui.string_req("Input model type:", model.Type)
      if res == CANCEL:
         return
      res, model.Name = kcs_ui.string_req("Input model name:", model.Name)
      if res == CANCEL:
         return
      res, model.PartId = kcs_ui.int_req("Input part id:", model.PartId)
      if res == CANCEL:
               return
   except:
      print sys.exc_info()[1]
      print kcs_ui.error

   point = KcsPoint2D.Point2D()
   prompt = 'Indicate view, OC to exit'
   resp = kcs_ui.point2D_req(prompt, point)    # request user for point
   handle = KcsElementHandle.ElementHandle()
   try:
      handle = kcs_draft.view_identify(point)      # view
      kcs_ui.message_noconfirm('Indicated view: ' + str(handle))
   except:
      kcs_ui.message_noconfirm('View not found')

   try:
      kcs_draft.model_draw(model)
   except:
       kcs_draft.error


#---------------------------------------------------------------------------------
# List subassemblies
#---------------------------------------------------------------------------------
def ListSubassemblies():
   try:
      # receive name of assembly
      print 'list subassemblies'
      assembly = kcs_ui.string_req("Key in path name for assembly: ")
      try:
         list = kcs_assembly.assembly_sub_get()
         strtable = []
         for model in list:
           strtable.append(model[0] + ' -- ' + model[1])
         tab = KcsStringlist.Stringlist(strtable[0])
         for name in strtable[1:]:
              tab.AddString(name)

         res = kcs_ui.string_select('Information about subassemblies','Subassemblies','',tab)
         if res[0]==OK:
           index=res[1]
           return tab.StrList[index-1]
         else:
           return '-1'
      except:
         kcs_ui.message_confirm("Assembly have not child !." )
         print kcs_assembly.error
   except:
      kcs_ui.message_noconfirm("Invalid input parameter !" )
      print kcs_ui.error

#---------------------------------------------------------------------------------
# Get Parent
#---------------------------------------------------------------------------------
def GetParent():
   try:
      # receive name of assembly
      assembly = kcs_ui.string_req("Key in name of path name :")
      if assembly[0] == OK:
         try:
            name, internal = parent = kcs_assembly.assembly_parent_get(assembly[1])
            kcs_ui.message_confirm("Parent of assembly is " + name + " or (" + internal + ")" )
         except:
            kcs_ui.message_confirm("Assembly have not parent." )
            print kcs_assembly.error
   except:
      kcs_ui.message_noconfirm("Invalid input parameter !" )
      print kcs_ui.error

#---------------------------------------------------------------------------------
#  Prepare table properties
#---------------------------------------------------------------------------------
def GetPropertiesStringTable():

    table = []
    table.append('Name                               :')   #01
    table.append('Description                       :')    #02
    table.append('BuildStrategy 1                 :'  )    #03
    table.append('BuildStrategy 2                 :'  )    #04
    table.append('BuildStrategy 3                 :'  )    #05
    table.append('BuildStrategy 4                 :'  )    #06
    table.append('BuildStrategy 5                 :'  )    #07
    table.append('BuildStrategy 6                 :'  )    #08
    table.append('PlanningUnit                     :')     #09
    table.append('Type                                 :') #10
    table.append('WorkingLocation              :')         #11
    table.append('Destination                        :')   #12
    table.append('DesignStatus                     :'  )   #13
    table.append('ProductionStatus              :'   )     #14
    table.append('Orientation                        :')   #15
    table.append('SpecificPanel                    :')     #16
    table.append('Level Id                             :') #17
    table.append('Long name of level            :')        #18
    table.append('Short name of level            :')       #19
    table.append('PlannedStartDate              :'  )      #20
    table.append('PlannedEndDate                :'  )      #21
    table.append('ActualStartDate                 :')      #22
    table.append('ActualEndDate                   :')      #23
    table.append('EstimatedWeight                :' )      #24
    table.append('EstimatedCOG     X            :'  )      #25
    table.append('EstimatedCOG     Y            :'  )      #26
    table.append('EstimatedCOG     Z            :'  )      #27

    return table

#---------------------------------------------------------------------------------
#  Prepare table properties
#---------------------------------------------------------------------------------
def GetPropertiesValueTable(ass):

#   kcs_assembly.assembly_properties_set(ass)
    box1 = ass.GetGlobalCoordBox()
    box2 = ass.GetAssemblyCoordBox()
    date1 = str(ass.GetPlannedStartDate())
    if (len(date1)>4):
       date1 = date1[6:len(date1)]
    date2 = str(ass.GetPlannedEndDate())
    if (len(date2)>4):
       date2 = date2[6:len(date2)]
    date3 = str(ass.GetActualStartDate())
    if (len(date3)>4):
       date3 = date3[6:len(date3)]
    date4 = str(ass.GetActualEndDate())
    if (len(date4)>4):
       date4 = date4[6:len(date4)]

    pECOG = ass.GetEstimatedCOG()
    level = ass.GetLevel()

    table = []
    table.append(ass.GetName()                )  #01
    table.append(ass.GetDescription()         )  #02
    table.append(ass.GetBuildingStrategy(0)   )  #03
    table.append(ass.GetBuildingStrategy(1)   )  #04
    table.append(ass.GetBuildingStrategy(2)   )  #05
    table.append(ass.GetBuildingStrategy(3)   )  #06
    table.append(ass.GetBuildingStrategy(4)   )  #07
    table.append(ass.GetBuildingStrategy(5)   )  #08

    table.append(ass.GetPlanningUnit()        )  #09
    table.append(ass.GetType()                )  #10
    table.append(ass.GetWorkingLocation()     )  #11
    table.append(ass.GetDestination()         )  #12
    table.append(ass.GetDesignStatus()        )  #13
    table.append(ass.GetProductionStatus()    )  #14
    table.append(ass.GetOrientation()         )  #15
    table.append(ass.GetSpecificPanel()       )  #16
    table.append(str(level[0])                )  #17
    table.append(level[1]                     )  #18
    table.append(level[2]                     )  #19
    table.append(date1)                          #20
    table.append(date2)                          #21
    table.append(date3)                          #22
    table.append(date4)                          #23
    table.append(str(ass.GetEstimatedWeight()))  #24
    table.append(str(pECOG.X))                   #25
    table.append(str(pECOG.Y))                   #26
    table.append(str(pECOG.Z))                   #27
    return table
#---------------------------------------------------------------------------------
#  Prepare table properties
#---------------------------------------------------------------------------------
def SetPropertiesValueTable(tab, indx, value):
    ass = KcsAssembly.Assembly()

    i = 0
    strtable = []
    for name in tab:
        if( i == indx):
           strtable.append(value)
        else:
           strtable.append(name)
        i = i + 1

    ass.SetName(strtable[0])                     #01
    ass.SetDescription(strtable[1])              #02
    ass.SetBuildingStrategy(strtable[2],0)         #03
    ass.SetBuildingStrategy(strtable[3],1)         #04
    ass.SetBuildingStrategy(strtable[4],2)         #05
    ass.SetBuildingStrategy(strtable[5],3)         #06
    ass.SetBuildingStrategy(strtable[6],4)         #07
    ass.SetBuildingStrategy(strtable[7],5)         #08
    ass.SetPlanningUnit(strtable[8])             #09
    ass.SetType(strtable[9])                     #10
    ass.SetWorkingLocation(strtable[10])         #11
    ass.SetDestination(strtable[11])             #12
    ass.SetDesignStatus(strtable[12])            #13
    ass.SetProductionStatus(strtable[13])        #14
    ass.SetOrientation(strtable[14])             #15
    ass.SetSpecificPanel(strtable[15])           #16
    ass.SetLevel(int(strtable[16]),
                 strtable[17],
                 strtable[18])                   #19
    if (len(strtable[19])>7):
       day, month, year = Translation(strtable[19])
       ass.SetPlannedStartDate(year, month, day)    #20
    if (len(strtable[20])>7):
       day, month, year = Translation(strtable[20])
       ass.SetPlannedEndDate(year, month, day)      #21
    if (len(strtable[21])>7):
       day, month, year = Translation(strtable[21])
       ass.SetActualStartDate(year, month, day)     #22
    if (len(strtable[22])>7):
       day, month, year = Translation(strtable[22])
       ass.SetActualEndDate(year, month, day)       #23
    ass.SetEstimatedWeight(float(strtable[23]))  #24
    ass.SetEstimatedCOG(float(strtable[24]),
                        float(strtable[25]),
                        float(strtable[26]))     #27
    print ass
    return ass

def Translation(str):
    year = str[0:4]
    test = str[5:len(str)]
    pos = test.count("-")
    month = test[0:pos]
    day = test[pos+1:len(test)]
    return  (int(day), int(month), int(year))

#---------------------------------------------------------------------------------
# SelectPropertiesValue
#---------------------------------------------------------------------------------
def SelectPropertiesValue(tab1, tab2):
    strtable = []
    for name in tab1:
        ind = tab1.index(name)
        strtable.append(name + tab2[ind])
    list = KcsStringlist.Stringlist(strtable[0])
    for name in strtable[1:]:
        list.AddString(name)
    res = kcs_ui.string_select('Attributes of Assembly', 'List of properties','', list)


    if res[0] == OK:
        return (tab1[res[1]-1], tab2[res[1]-1], res[1]-1) ;
    else:
        return ('', '', -1)

#---------------------------------------------------------------------------------
# Update Assembly
#---------------------------------------------------------------------------------
def UpdateAssembly():
   try:
      assembly = kcs_ui.string_req("Name of assembly :")
      if assembly[0] == OK:
         try:
            kcs_assembly.assembly_activate(assembly[1])
            kcs_ui.message_noconfirm("Assembly " + assembly[1] + " activate !" )
         except:
            print 'Error activating assembly', kcs_assembly.error
            return

      elif assembly[0] == CANCEL:
        return
   except:
      kcs_ui.message_noconfirm("Invalid input parameter !" )
      print kcs_assembly.error

   ass = kcs_assembly.assembly_properties_get()
   print ass
   tab1 = GetPropertiesStringTable()
   tab2 = GetPropertiesValueTable(ass)

   loop = 1
   while loop:
      name, value, index = SelectPropertiesValue(tab1, tab2)
      if name != '':
         res = kcs_ui.string_req("Key in name of statement:  " + name, value)
         if res[0] == OK:
            ass  = SetPropertiesValueTable(tab2, index, res[1])
            tab2 = GetPropertiesValueTable(ass)
      elif name ==OK:
         kcs_ui.message_confirm("Not ready" )
      else:
         loop = 0

   try:
      res= kcs_assembly.assembly_properties_set(ass)
   except:
      kcs_assembly.error

   oper = KcsStringlist.Stringlist('Calculation WCOG')
   oper.AddString('Save change')
   #   oper.AddString('Calculation Boundix Box')
   next = 1
   save = 0
   while next:
      (status, index) = kcs_ui.choice_select('Operation on assembly name','Change',oper)
      if status == OK:
       if index == 1:
          kcs_assembly.assembly_wcog_calc()
       elif index == 2:
          kcs_assembly.assembly_save()
          save = 1
#       elif index == 3:
#          next = 0
      else:
         if (save==0):
            try:
               kcs_assembly.assembly_cancel()
            except:
               pass
         next = 0

#---------------------------------------------------------------------------------
def OrientationMatrix():
   try:
      assembly = kcs_ui.string_req("Name of assembly :")
      if assembly[0] == OK:
        state = kcs_assembly.assembly_exist(assembly[1])
        print state
        if (state==0):
           kcs_ui.message_confirm("Assembly " + string.upper(assembly[1]) + " does not exist ! " )

        try:
           kcs_assembly.assembly_activate(assembly[1])
        except:
           print 'Error activating assembly', kcs_assembly.error

        ass = kcs_assembly.assembly_properties_get()
        mat = ass.GetOrientationMatrix()

        table = []
        table.append('matrix11 : ' + str(mat.matrix11))
        table.append('matrix12 : ' + str(mat.matrix12))
        table.append('matrix13 : ' + str(mat.matrix13))
        table.append('matrix14 : ' + str(mat.matrix24))
        table.append('matrix21 : ' + str(mat.matrix21))
        table.append('matrix22 : ' + str(mat.matrix22))
        table.append('matrix23 : ' + str(mat.matrix23))
        table.append('matrix24 : ' + str(mat.matrix24))
        table.append('matrix31 : ' + str(mat.matrix31))
        table.append('matrix32 : ' + str(mat.matrix32))
        table.append('matrix33 : ' + str(mat.matrix33))
        table.append('matrix34 : ' + str(mat.matrix34))
        table.append('matrix41 : ' + str(mat.matrix41))
        table.append('matrix42 : ' + str(mat.matrix42))
        table.append('matrix43 : ' + str(mat.matrix43))
        table.append('matrix44 : ' + str(mat.matrix44))
        print table

        list = KcsStringlist.Stringlist(table[0])
        for name in table[1:]:
           list.AddString(name)
        res = kcs_ui.string_select('Matrix of Orientation','Elements of matrix',
                                   'Type of transformation matrix : ' + str(mat.type), list)

        kcs_ui.message_noconfirm("Assembly " + assembly[1] + " activate !" )
      elif assembly[0] == CANCEL:
        return
   except:
      kcs_ui.message_noconfirm("Invalid input parameter !" )
      print kcs_assembly.error

#---------------------------------------------------------------------------------
# List Assembly structure without model refs
#---------------------------------------------------------------------------------
def Tools():
   nameass = KcsStringlist.Stringlist('Get name of parent assembly')
   nameass.AddString('Create assembly path name')
   nameass.AddString('Trans Inter name --> Path name')
   nameass.AddString('Trans Path name --> Inter name')
   nameass.AddString('Draw assembly in drawing')
   nameass.AddString('Orientation Matrix')

   while 1:
      (status, index) = kcs_ui.choice_select('Operation on assembly name','Tools menu',nameass)
      if status == OK:
         if index == 1:
            GetParent()
         elif index == 2:
            CreatePathName()
         elif index == 3:
            Internal2Path()
         elif index == 4:
            Path2Internal()
         elif index == 5:
            ModelDraw()
         elif index == 6:
            OrientationMatrix()
      else:
         break;

#---------------------------------------------------------------------------------
# Exist Assembly
#---------------------------------------------------------------------------------
def ExistAssembly():
   try:
    # receive name of assembly
    assembly = kcs_ui.string_req("Key in name of assembly :")
    if assembly[0] == OK:
      try:
        state = kcs_assembly.assembly_exist(assembly[1])
        if (state==1):
           kcs_ui.message_confirm("Assembly " + string.upper(assembly[1]) + " exist !" )
        else:
           kcs_ui.message_confirm("Assembly " + string.upper(assembly[1]) + " does not exist ! " )
      except:
        kcs_ui.message_noconfirm("Assembly does not exist." )
        print kcs_assembly.error
    elif assembly[0] == CANCEL:
      return
   except:
       kcs_ui.message_noconfirm("Invalid input parameter !" )
       print kcs_ui.error

#---------------------------------------------------------------------------------
# New Assembly
#---------------------------------------------------------------------------------
def NewAssembly():
   try:
      #  receive name of assembly
      assemblyParent = kcs_ui.string_req("Key in name of parent assembly :")
      assembly       = kcs_ui.string_req("Key in name of assembly :")
      if assembly[0] == OK:
         kcs_assembly.assembly_new(assemblyParent[1],assembly[1])
         kcs_ui.message_noconfirm("Assembly " + string.upper(assemblyParent[1]) + "-" + string.upper(assembly[1]) + " created !" )
   except:
      kcs_ui.message_noconfirm("Invalid input parameter !" )
      print kcs_ui.error

#---------------------------------------------------------------------------------
# Delete Assembly
#---------------------------------------------------------------------------------
def DeleteAssembly():
   try:
      #  receive name of assembly
      assembly = kcs_ui.string_req("Key in name of assembly :")
      if assembly[0] == OK:
         try:
            kcs_assembly.assembly_delete(assembly[1])
            kcs_ui.message_noconfirm("Assembly " + assembly[1] + " deleted !" )
         except:
            kcs_ui.message_noconfirm("Assembly " + assembly[1] + " not exist." )
            print kcs_assembly.error
   except:
       kcs_ui.message_noconfirm("Invalid input parameter !" )
       print kcs_ui.error

#---------------------------------------------------------------------------------
# Move Assembly
#---------------------------------------------------------------------------------
def MoveAssembly():
   res, assname = kcs_ui.string_req('Assembly name to move:', '')
   if res == OK:
      state = kcs_assembly.assembly_exist(assname)
      print state
      if (state==1):
        try:
            kcs_assembly.assembly_activate(assname)
        except:
            print 'Error activating assembly', kcs_assembly.error
            return
      else:
        kcs_ui.message_confirm("Assembly " + string.upper(assname) + " does not exist ! " )
        return
   elif res == CANCEL:
      return


   try:
      # receive name of assembly
      assembly = kcs_ui.string_req("New parent name:")
      if assembly[0] == OK:
        try:
           kcs_assembly.assembly_move(assembly[1])
        except:
           kcs_assembly.assembly_cancel()
           print kcs_assembly.error
   except:
       kcs_ui.message_noconfirm("Invalid input parameter !" )
       print kcs_ui.error

#---------------------------------------------------------------------------------
# Collect model reference
#---------------------------------------------------------------------------------
def CollectModelReference():
   assembly = kcs_ui.string_req("Key in name of assembly :")
   if assembly[0] == OK:
      try:
         kcs_assembly.assembly_activate(assembly[1])
      except:
         print 'Error activating assembly', kcs_assembly.error
         return

   elif assembly[0] == CANCEL:
      return

   list = KcsStringlist.Stringlist('Indicate model part')
   list.AddString('Key in model part')
   list.AddString('KeyIn part reference')
   list.AddString('Component part reference')
   list.AddString('Save')

   next = 1
   while next:
      (status, index) = kcs_ui.choice_select('Part reference menu','Collect model reference',list)
      if status == OK:
          if index == 1:
             IndicateModelObject()
          elif index == 2:
             WriteModelObject()
          elif index == 3:
             KeyInPart()
          elif index == 4:
             ComponentPart()
          elif index == 5:
             Save()
             return
      else:
         next = 0
         print "User interrupted!"


#---------------------------------------------------------------------------------
# Indicate Model Object
#---------------------------------------------------------------------------------
def IndicateModelObject():
   # check licence
   try:
       import kcs_draft
   except:
       kcs_ui.message_confirm("You have not licence of drafting !!!" )
       return

   pt = KcsPoint2D.Point2D()
   kcs_ui.point2D_req("Indicate part", pt)
   model = KcsModel.Model()

   try:
       kcs_draft.model_identify(pt, model)
       strPartId = str( model.GetPartId() )
       strReflCode = str( model.GetReflCode() )
       res = kcs_ui.message_confirm('Collected part: \n'  +
                               'Type:              '+model.GetType()+'\n'+
                               'Name:              '+model.GetName()+'\n'+
                               'Part ID:           '+strPartId+'\n'+
                               'Reflection code:  '+strReflCode+'\n')

       kcs_assembly.model_collect(model)

   except:
       kcs_ui.message_noconfirm("Invalid model object !" )
       print kcs_ui.error

#---------------------------------------------------------------------------------
# Indicate Model Object
#---------------------------------------------------------------------------------
def WriteModelObject():
   model = KcsModel.Model()
   type = GetModelType();
   if type==-1:
      return
   else:
      model.SetType(type)

   parttype = GetPartType();
   if parttype==-1:
      return
   else:
      model.SetPartType(parttype)

   name = kcs_ui.string_req("Key in name of model part:  ")
   if name[0] == OK:
      model.SetName(name[1])
   elif name[0] == CANCEL:
      return

   id = kcs_ui.int_req("Key in Id of model part:  ")
   if id[0] == OK:
      model.SetPartId(id[1])
   elif id[0] == CANCEL:
      return

   try:
      kcs_assembly.model_collect(model)
   except:
      kcs_assembly.error


#---------------------------------------------------------------------------------
#  Prepare table properties for KeyIn part
#---------------------------------------------------------------------------------
def GetKeyInString():
    table = []
    table.append('Part Name               :')#01
    table.append('Quantity                :')#02
    table.append('Position number         :')#03
    table.append('Quality                 :')#04
    table.append('Weight                  :')#05
    table.append('COG-X                   :')#06
    table.append('COG-Y                   :')#07
    table.append('COG-Z                   :')#08
    table.append('Description             :')#09
    table.append('Comment                 :')#10
    return table

#---------------------------------------------------------------------------------
#  Prepare table KeyIn
#---------------------------------------------------------------------------------
def GetKeyInValue(asskeyin):
    pCOG = asskeyin.GetCOG()
    table = []
    table.append(asskeyin.GetName()            )
    table.append(str(asskeyin.GetQuantity())   )
    table.append(asskeyin.GetPositionNumber()  )
    table.append(asskeyin.GetQuality()         )
    table.append(str(asskeyin.GetWeight())     )
    table.append(str(pCOG.X)                   )
    table.append(str(pCOG.Y)                   )
    table.append(str(pCOG.Z)                   )
    table.append(asskeyin.GetComment()         )
    table.append(asskeyin.GetDescription()     )
    return table
#---------------------------------------------------------------------------------
#  Prepare table properties
#---------------------------------------------------------------------------------
def SetKeyInValue(tab, indx, value):
    asskeyin = KcsAssemblyKeyInItem.AssemblyKeyInItem()

    i = 0
    strtable = []
    for name in tab:
        if( i == indx):
           strtable.append(value)
        else:
           strtable.append(name)
        i = i + 1

    asskeyin.SetName(strtable[0])
    asskeyin.SetQuantity(int(strtable[1]))
    asskeyin.SetPositionNumber(strtable[2])
    asskeyin.SetQuality(strtable[3])
    asskeyin.SetWeight(float(strtable[4]))
    asskeyin.SetCOG(float(strtable[5]),
                    float(strtable[6]),
                    float(strtable[7]))
    asskeyin.SetComment(strtable[8])
    asskeyin.SetDescription(strtable[9])
    return asskeyin

#---------------------------------------------------------------------------------
# SelectKeyInValue
#---------------------------------------------------------------------------------
def SelectKeyInValue(tab1, tab2):
    strtable = []
    for name in tab1:
        ind = tab1.index(name)
        strtable.append(name + str(tab2[ind]))
    list = KcsStringlist.Stringlist(strtable[0])
    for name in strtable[1:]:
        list.AddString(name)
    res = kcs_ui.string_select('Attributes of part', 'List of properties','', list)
    if res[0] == OK:
        return (tab1[res[1]-1], tab2[res[1]-1], res[1]-1) ;
    else:
        return ('', '', -1)

#---------------------------------------------------------------------------------
# KeyIn Part reference
#---------------------------------------------------------------------------------
def KeyInPart():
   asskeyin = KcsAssemblyKeyInItem.AssemblyKeyInItem()

   tab1 = GetKeyInString()
   tab2 = GetKeyInValue(asskeyin)
   loop = 1
   while loop:
      name, value, index = SelectKeyInValue(tab1, tab2)
      if name != '':
         res = kcs_ui.string_req("Key in name of statement:  " + name, value)
         if res[0] == OK:
            asskeyin  = SetKeyInValue(tab2, index, res[1])
            tab2 = GetKeyInValue(asskeyin)
      else:
         loop = 0

   try:
      kcs_assembly.model_collect(asskeyin)
   except:
      kcs_assembly.error
#---------------------------------------------------------------------------------
# Component Part reference
#---------------------------------------------------------------------------------
def ComponentPart():
   asskeyin = KcsAssemblyKeyInItem.AssemblyKeyInItem()

   tab1 = GetKeyInString()
   tab2 = GetKeyInValue(asskeyin)
   loop = 1
   while loop:
      name, value, index = SelectKeyInValue(tab1, tab2)
      if name != '':
         res = kcs_ui.string_req("Key in name of statement:  " + name, value)
         if res[0] == OK:
            asskeyin  = SetKeyInValue(tab2, index, res[1])
            tab2 = GetKeyInValue(asskeyin)
      else:
         loop = 0
   try:
      asskeyin.SetItemType('key-in component')
      kcs_assembly.model_collect(asskeyin)
   except:
      kcs_assembly.error

#---------------------------------------------------------------------------------
# Save
#---------------------------------------------------------------------------------
def Save():
   try:
      kcs_assembly.assembly_save()
   except:
      print 'Error saving assembly', kcs_assembly.error

#---------------------------------------------------------------------------------
#  Prepare model type
#---------------------------------------------------------------------------------
def GetModelType():

    tab = KcsStringlist.Stringlist('plane panel')    #01
    tab.AddString('hull curve')     #02
    tab.AddString('pipe')           #03
    tab.AddString('pipe spool')     #04
    tab.AddString('equipment')      #05
    tab.AddString('cable way')      #06
    tab.AddString('cable')          #07
    tab.AddString('penetration')    #08
    tab.AddString('struct')         #09
    tab.AddString('placed volume')  #10
    tab.AddString('longitudinal')   #11
    tab.AddString('transversal')    #12
    tab.AddString('ventilation')    #13
    tab.AddString('subsurface')     #14
    tab.AddString('lines fairing curve')  #15
    tab.AddString('accomodation')   #16
    tab.AddString('curved panel')   #17
    tab.AddString('assembly')       #18

    res = kcs_ui.string_select('Information about a Model', 'Select model type', '', tab)
    if res[0]==OK:
       index=res[1]
       return tab.StrList[index-1]
    else:
       return '-1'

#---------------------------------------------------------------------------------
#  Prepare model type
#---------------------------------------------------------------------------------
def GetPartType():
    tab = KcsStringlist.Stringlist('panel')    #01
    tab.AddString('boundary')  #02
    tab.AddString('hole')      #03
    tab.AddString('bracket')   #04
    tab.AddString('plate')     #05
    tab.AddString('notch')     #06
    tab.AddString('seam')      #07
    tab.AddString('stiffener') #08
    tab.AddString('flange')    #09
    tab.AddString('pillar')    #10
    tab.AddString('bead')      #11
    tab.AddString('cutout')    #12
    tab.AddString('excess')    #13
    tab.AddString('hole/notch7cutout')    #14
    tab.AddString('point')     #15
    tab.AddString('curve')     #16
    tab.AddString('unknown')   #17

    res = kcs_ui.string_select('Information about a Model','Select model subtype','',tab)
    if res[0]==OK:
       index=res[1]
       return tab.StrList[index-1]
    else:
       return '-1'

#----------------------------------------------------------------------------------
# Create main menu
#----------------------------------------------------------------------------------
main = KcsStringlist.Stringlist('View assembly structure')
main.AddString('View assembly structure part refs')
main.AddString('Check if assembly exists')
main.AddString('Create new assembly')
main.AddString('Delete assembly')
main.AddString('Update assembly attributes')
main.AddString('Move assembly')
main.AddString('Collect model references')
main.AddString('Decollect model references')
main.AddString('Tools')

next = 1
while next:
   (status, index) = kcs_ui.choice_select('VITESSE ASSEMBLY','Operations on the assembly',main)
   if status == OK:
      if index == 1:
         while 1:
            res, element = SelectAssemblyElement((ASSEMBLY,))
            if res:
               ShowElementProperties(element)
            else:
               break;
      elif index == 2:
         while 1:
            res, element = SelectAssemblyElement(((ASSEMBLY, KEYIN_PART, KEYIN_COMP, MODEL_PART)))
            if res:
               ShowElementProperties(element)
            else:
               break;
      elif index == 3:
         ExistAssembly()
      elif index == 4:
         NewAssembly()
      elif index == 5:
         DeleteAssembly()
      elif index == 6:
         UpdateAssembly()
      elif index == 7:
         MoveAssembly()
      elif index == 8:
         CollectModelReference()
      elif index == 9:
         DecollectModelReference()
      else:
         Tools()
   else:
      try:
         kcs_assembly.assembly_cancel()
      except:
         pass

      print "User interrupted!"
      break;


#----------------------------------------------------------------------------------
