#----------------------------------------------------------------------------------
#
#      NAME:
#
#          kcs_ex_ass02.py
#
#      PURPOSE:
#
#               This program presents how to use document reference handling for assembly objects
#
#
#
#----------------------------------------------------------------------------------
import string
import kcs_ui
import kcs_util
import kcs_draft
import kcs_db
import kcs_assembly
import KcsStringlist
import CommonSample
import KcsDocumentReference
import KcsObjectCriteria

#----------------------------------------------------------------------------------
# Allow user to select drawing name stored in SB_ASSINSTR or SB_PDB
#----------------------------------------------------------------------------------
def GetDrawingName():
   database = 'SB_PDB'
   try:
      dbname = kcs_util.TB_environment_get('SB_ASSINSTR')
      if dbname:
         database = 'SB_ASSINSTR'
   except:
      pass

   list = []
   crit = KcsObjectCriteria.ObjectCriteria()
   try:
      list = []
      kcs_db.object_list_get(crit, database, list)
   except:
      CommonSample.ReportTribonError(kcs_db)
      return None, None

   if len(list) == 0:
      kcs_ui.message_confirm('There are no drawings in %s databank!' % (database,))
      return None, None

   strlist = []
   for item in list:
      strlist.append(item.GetName())

   (status, index) = kcs_ui.string_select('Drawings', 'Select drawing:', '', strlist)
   if status == kcs_util.ok():
      if database == 'SB_PDB':
         database = kcs_draft.kcsDWGTYPE_GEN
      else:
         database = kcs_draft.kcsDWGTYPE_ASS_INSTR

      return strlist[index-1], database
   else:
      return None

#----------------------------------------------------------------------------------
# Allows user to edit and add document reference to assembly given by name
#----------------------------------------------------------------------------------
def AddAssemblyDocumentReference(assname):
   try:
      reference = KcsDocumentReference.DocumentReference()

      if EditAssemblyDocumentReference(reference):

         kcs_assembly.assembly_activate(assname)

         try:
            kcs_assembly.document_reference_add(reference)
         except:
            CommonSample.ReportTribonError(kcs_assembly)

         kcs_assembly.assembly_save()

   except:
      CommonSample.ReportTribonError(kcs_assembly)

#----------------------------------------------------------------------------------
#  Displays all document references attached to given assembly name
#  and allow user to select one
#----------------------------------------------------------------------------------
def GetAssemblyDocumentReference(assname, prompt1=''):
   list = []
   try:
      kcs_assembly.assembly_activate(assname)
      try:
         list = kcs_assembly.document_reference_get()
      except:
         CommonSample.ReportTribonError(kcs_assembly)

      kcs_assembly.assembly_cancel()

   except:
      CommonSample.ReportTribonError(kcs_assembly)

   strlist = []
   for item in list:
      itemstr = str(item)
      strlist = strlist + string.split(itemstr, '\n')
      strlist.append('')

   nItems = len(strlist)
   if nItems == 0:
      strlist = ['no document references']
   resp, index = kcs_ui.string_select('Document references', prompt1, '', strlist)
   if resp==kcs_util.ok() and nItems:
      linesPerItem = len(string.split(str(KcsDocumentReference.DocumentReference()), '\n')) + 1
      return list[(index-1) / linesPerItem]
   else:
      return None

#----------------------------------------------------------------------------------
#  That function shows memebers of document reference and allow user to edit them
#----------------------------------------------------------------------------------
def EditAssemblyDocumentReference(reference):
   try:
      while 1:
         actions = []
         actions.append('Type: ' + reference.GetType())
         actions.append('Document: ' + reference.GetDocument())
         actions.append('Description: ' + reference.GetDescription())
         actions.append('Return')

         (status, index) = kcs_ui.choice_select('Document reference','Define members', actions)
         if status == kcs_util.ok():
            if index == 1:
               actions = KcsDocumentReference.DocumentReference.ReferenceTypes.keys()
               (status, index) = kcs_ui.choice_select('Reference type', 'Choose type', actions)
               if status == kcs_util.ok():
                  reference.SetType(actions[index-1])
            elif index == 2:
               if reference.GetType() == 'drawing':
                  strRef, database = GetDrawingName()
                  if strRef != None:
                     reference.SetDocument(strRef)
                     reference.SetPurpose(database)
               else:
                  resp, strRef = kcs_ui.string_req('Document:', reference.GetDocument())
                  if resp == kcs_util.ok():
                     reference.SetDocument(strRef)
            elif index == 3:
               resp, strDesc = kcs_ui.string_req('Description:', reference.GetDescription())
               if resp == kcs_util.ok():
                  reference.SetDescription(strDesc)
            else:
               return 1
         else:
            return 0
   except:
      print sys.exc_info()[1]

#----------------------------------------------------------------------------------
#  Removes given document reference from assembly given by name
#----------------------------------------------------------------------------------
def RemoveAssemblyDocumentReference(assname, reference):
   try:
      kcs_assembly.assembly_activate(assname)
      try:
         kcs_assembly.document_reference_remove(reference)
      except:
         CommonSample.ReportTribonError(kcs_assembly)

      kcs_assembly.assembly_save()

   except:
      CommonSample.ReportTribonError(kcs_assembly)

#----------------------------------------------------------------------------------
#  Displays all document references of 'drawing' type and opens selected drawing
#----------------------------------------------------------------------------------
def OpenReferencedDrawing(assname):
   list = []
   try:
      kcs_assembly.assembly_activate(assname)
      try:
         list = kcs_assembly.document_reference_get()
      except:
         CommonSample.ReportTribonError(kcs_assembly)
         return

      kcs_assembly.assembly_cancel()

   except:
      CommonSample.ReportTribonError(kcs_assembly)
      return

   strlist = []
   for item in list:
      if item.GetType() == 'drawing':
         strlist.append(item.GetDocument())
   if len(strlist) == 0:
      kcs_ui.message_confirm('No drawing type document references!')
   else:
      (status, index) = kcs_ui.string_select('Drawings', 'Select drawing name:', '', strlist)
      if status == kcs_util.ok():
         try:
            try:
               kcs_draft.dwg_close()
            except:
               pass
            kcs_draft.dwg_open(strlist[index-1])
         except:
            CommonSample.ReportTribonError(kcs_draft)


#----------------------------------------------------------------------------------
# Create main menu
#----------------------------------------------------------------------------------
assname = ''

while 1:
   actions = (
      'Assembly: '+assname,
      'Get document references',
      'Add document reference',
      'Remove document reference',
      'Open referenced drawing'
      )

   (status, index) = kcs_ui.choice_select('Assembly documents','Operations on the documents', actions)

   if status == kcs_util.ok():
      if index == 1:
         resp, name = kcs_ui.string_req('Keyin assembly name:', assname)
         if resp == kcs_util.ok():
            assname = name
      elif index == 2:
         GetAssemblyDocumentReference(assname)
      elif index == 3:
         AddAssemblyDocumentReference(assname)
      elif index == 4:
         reference = GetAssemblyDocumentReference(assname, 'Select document reference to delete')
         if reference != None:
            RemoveAssemblyDocumentReference(assname, reference)
      elif index == 5:
         OpenReferencedDrawing(assname)
   else:
      print "User interrupted!"
      break;


#----------------------------------------------------------------------------------
