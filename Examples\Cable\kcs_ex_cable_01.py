#
#      NAME:
#
#          kcs_ex_cable_01.py
#
#      PURPOSE:
#
#          This program serve to create a new cable
#           and connect to equipment then save the changes.
#

import kcs_util
import kcs_ui
import kcs_cable

import wx.grid


OK      = kcs_util.ok()

try:
  # receive system-name and name of the cable
  System = kcs_ui.string_req("Key in system-name of the cable :")
  if System[0] == OK:
    Name = kcs_ui.string_req("Key in name of cable :")
    if Name[0] == OK:
      try:
        # create cable object
        kcs_cable.cable_new(System[1], Name[1])
        kcs_ui.message_noconfirm("New cable " + System[1] + Name[1] + " is created." )
        # activate cable object
        kcs_cable.cable_activate(System[1], Name[1])
        kcs_ui.message_noconfirm("Cable " + System[1] + Name[1] + " is active !" )
        try:
          Component = kcs_ui.string_req("Component name","")
          if Component[0] == OK:
            try:
              # set the component reference to the cable component
              kcs_cable.cable_component_set(Component[1])
              kcs_ui.message_noconfirm("Component " + Component[1] + " found ")
              try:
                # receive name of equipment 1
                Equip1 = kcs_ui.string_req("Equipment1","")
                if Equip1[0] == OK:
                  try:
                    Connect1 = kcs_ui.int_req("Connection")
                    if Connect1[0] == OK:
                      try:
                        # connect the cable in 'connection' to the equipment1
                        kcs_cable.cable_equipment_connect(Equip1[1], Connect1[1])
                        kcs_ui.message_noconfirm("Connection " + str(Connect1[1]) + " is realized !")
                      except:
                        kcs_ui.message_noconfirm("Connection " + str(Connect1[1])+ " not realized !")
                        print kcs_ui.error
                  except:
                    kcs_ui.message_noconfirm("Invalid input equipment !" )
                    print kcs_ui.error
                # receive name of equipment 2
                Equip2 = kcs_ui.string_req("Equipment2")
                if Equip2[0] == OK:
                  try:
                    Connect2 = kcs_ui.int_req("Connection")
                    if Connect2[0] == OK:
                      try:
                        # connect the cable in 'connection' to the equipment2
                        kcs_cable.cable_equipment_connect(Equip2[1], Connect2[1])
                        kcs_ui.message_noconfirm("Connection " + str(Connect2[1]) + " is realized !")
                      except:
                        kcs_ui.message_noconfirm("Connection " + str(Connect2[1]) + " not realized !")
                        print kcs_ui.error
                  except:
                    kcs_ui.message_noconfirm("Invalid input equipment !")
                    print kcs_ui.error
                # save changes
                kcs_cable.cable_save()
                kcs_ui.message_noconfirm("Save the changes !")
              except:
                kcs_ui.message_noconfirm("Invalid input equipment !")
                print kcs_ui.error
            except:
              kcs_ui.message_noconfirm("Component " + name + " not found ")
              print kcs_ui.error
        except:
          kcs_ui.message_noconfirm("Invalid name of component !")
          print kcs_ui.error
      except:
        kcs_ui.message_noconfirm("New cable can not created." )
        print kcs_ui.error
except:
  kcs_ui.message_noconfirm("Invalid input cable !" )
  print kcs_ui.error
