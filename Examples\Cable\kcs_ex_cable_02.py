#
#      NAME:
#
#          kcs_ex_cable_02.py
#
#      PURPOSE:
#
#          This program serve to handle existing cable
#
import kcs_util
import kcs_ui
import kcs_cable


OK      = kcs_util.ok()
CANCEL  = kcs_util.cancel()
YES     = kcs_util.yes()

try:
  System = kcs_ui.string_req("Key in system-name of the cable :")
  if System[0] == OK:
    Name = kcs_ui.string_req("Key in name of cable :")
    if Name[0] == OK:
      try:
        # check whether cable yet exist
        kcs_cable.cable_exist(System[1], Name[1])
        # activate cable
        kcs_cable.cable_activate(System[1], Name[1])
        # display name and system
        kcs_ui.message_noconfirm("Cable " + System[1] + Name[1] + " exist and it is activated !" )
        print "Cable " + System[1] + Name[1] + " exist and it is activated !"
        status = kcs_ui.answer_req("Question"," Do you want delete ?")
        if status == YES:
          status = CANCEL
          try:
            # disconnect the cable
            kcs_cable.cable_equipment_disconnect(1)
            kcs_ui.message_noconfirm("Disconnect cable !" )
            kcs_cable.cable_equipment_disconnect(2)
            kcs_ui.message_noconfirm("Disconnect cable !" )
            # save the changes
            kcs_cable.cable_save()
            # delete the cable
            kcs_cable.cable_delete(System[1], Name[1])
            kcs_ui.message_noconfirm("Cable is deleted !" )
          except:
            kcs_ui.message_noconfirm("Can not deleted cable or disconnected.")
            print kcs_ui.error
        else:
          try:
            # cancel cable
            kcs_cable.cable_cancel()
            kcs_ui.message_noconfirm("Cancel the changes !")
          except:
            kcs_ui.message_noconfirm("Error ! It is possible operation cancel !")
            print kcs_ui.error
      except:
        kcs_ui.message_noconfirm("Cable " + System[1] + Name[1] + " not exist or not be activated !")
        print kcs_ui.error
except:
  kcs_ui.message_noconfirm("Invalid input of cable ! ")
  print kcs_ui.error
