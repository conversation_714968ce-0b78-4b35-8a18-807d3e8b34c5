#
#      NAME:
#
#          kcs_ex_cableway_01.py
#
#      PURPOSE:
#
#          This program serve to create a new cableway
#

import kcs_util
import kcs_ui
import kcs_cable
import KcsColour

OK = kcs_util.ok()
loop =1
while loop:
  try:
    # receive name, module and colour of cableway
    Name = kcs_ui.string_req("Key in name of cableway :")
    if Name[0] == OK:
      Module = kcs_ui.string_req("Key in module of cableway :")
      if Module[0] == OK:
        col = KcsColour.Colour()
        res = kcs_ui.colour_select("Choose a colour", col)
        if res [0] == OK:
          color = col.Name()
        try:
          # create cableway object
          kcs_cable.cway_new(Name[1], Module[1], color )
          kcs_ui.message_noconfirm("New cableway " + Name[1] + " is created." )
          try:
            # activate cableway object
            kcs_cable.cway_activate(Name[1])
            kcs_ui.message_noconfirm("Cableway " + Name[1] + " is active !" )
            # save cableway object
            kcs_cable.cway_save()
            kcs_ui.message_noconfirm("Cableway " + Name[1] + " is save !" )
          except:
            kcs_ui.message_noconfirm("New cableway can not be activate !." )
            print kcs_ui.error
        except:
          kcs_ui.message_noconfirm("New cableway can not created." )
          print kcs_ui.error
    else:
      loop = 0
  except:
    loop = 0
    kcs_ui.message_noconfirm("Invalid input cableway !" )
    print kcs_ui.error
