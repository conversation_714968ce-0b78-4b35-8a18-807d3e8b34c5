#
#      NAME:
#
#          kcs_ex_cway_02.py
#
#      PURPOSE:
#
#          This program serve to handle existing cableway
#
import kcs_util
import kcs_ui
import kcs_cable


OK      = kcs_util.ok()
CANCEL  = kcs_util.cancel()
YES     = kcs_util.yes()
loop = 1
while loop:
  try:
    Name = kcs_ui.string_req("Key in name of cableway :")
    if Name[0] == OK:
      try:
        # check whether cableway yet exist
        kcs_cable.cway_exist(Name[1])
        kcs_ui.message_noconfirm("Cableway " + Name[1] + " exist." )
        print "Cableway " + Name[1] + " exist."
        try:
          # activate cableway
          kcs_cable.cway_activate(Name[1])
          kcs_ui.message_noconfirm("Cableway " + Name[1] + " is active !" )
          kcs_cable.cway_save()
          kcs_ui.message_noconfirm("Cableway " + Name[1] + " is saved !" )
          status = kcs_ui.answer_req("Question"," Do you want delete ?")
          if status == YES:
            status = CANCEL
            try:
              kcs_cable.cway_delete(Name[1])
              kcs_ui.message_noconfirm("Cableway " + Name[1] + " is deleted !" )
            except:
              kcs_ui.message_noconfirm("Error !!!")
              print kcs_ui.error
          else:
            try:
              kcs_cable.cway_cancel()
              kcs_ui.message_noconfirm("Cancel the changes !" )
            except:
              kcs_ui.message_noconfirm("Error !!!")
              print kcs_ui.error
        except:
          kcs_ui.message_noconfirm("Cableway " + Name[1] + " could not be activate !")
          print kcs_ui.error
      except:
        kcs_ui.message_noconfirm("Cableway " + Name[1] + " not exist !")
        print kcs_ui.error
    else:
      loop = 0
  except:
    loop = 0
    kcs_ui.message_noconfirm("Invalid input of cableway ! ")
    print kcs_ui.error
