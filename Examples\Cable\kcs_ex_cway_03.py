#
#      NAME:
#
#          kcs_ex_cway_03.py
#
#      PURPOSE:
#
#          The function updates a default value by a given default statement.
#
#
#

import kcs_ui
import kcs_cable
import kcs_util
import KcsStringlist

OK = kcs_util.ok()
CANCEL  = kcs_util.cancel()

#------------------------------------------------------------------------------
def GetDefaultsTable():
    table = []
    table.append('USER')
    table.append('SYMBFONT')
    table.append('PROJECT')
    table.append('FILL_LEV_PEN')
    table.append('FILL_LEV_CAW')
    table.append('CABLE_EXCESS')
    table.append('VOLTAGE_DROP')
    table.append('PUT_GLAND')
    table.append('CAB_TYPE')
    table.append('CW_ANGLE_CUT')
    table.append('CONN_EQ_POINT')
    table.append('CONN_EQ_ALIAS')
    table.append('CAWAREA_CALC')
    table.append('CABLEN_CALC')
    table.append('CW_AUTO_CONN')
    table.append('CAB_GRP_COLOUR')
    table.append('CABLE_ADD_DATA')
    table.append('PDI_TRANS_CABLEWAY')
    table.append('PDI_TRANS_PENETRATION')
    table.append('PDI_TRANS_CABLE')
    table.append('PDI_CABLEWAY_VRML')
    table.append('PDI_PENETRATION_VRML')
    table.append('CHECK_CWAY_POSNO')
    table.append('CHECK_CWAY_PLAUN')
    table.append('CHECK_CWAY_CWAY_ASS')
    table.append('CHECK_CWAY_PEN_ASS')
    table.append('CHECK_CAB_ROUTE')
    table.append('CHECK_CAB_CWAY_READY')
    table.append('CHECK_CAB_CWAY_PDI')
    table.append('CHECK_CAB_CONN')
    table.append('CHECK_CAB_ASS')
    table.append('PDI_TRANS_CABLE_ROUTE')
    return table

#------------------------------------------------------------------------------
def SelectDefaultValue(table):
    strtable = []
    for name in table:
        strtable.append(name + ':' + kcs_cable.default_value_get(name))

    list = KcsStringlist.Stringlist(strtable[0])
    for name in strtable[1:]:
        list.AddString(name)
    res = kcs_ui.string_select('Default value', '','', list)
    if res[0] == OK:
        return table[res[1]-1];
    else:
        return ''
#------------------------------------------------------------------------------

table = GetDefaultsTable()
loop = 1

while loop:
    name = SelectDefaultValue(table)
    if name != '':
        value = kcs_cable.default_value_get(name)
        res = kcs_ui.string_req("Key in name of default statement:  " + name, value)
        if res[0] == OK:
            kcs_cable.default_value_set(name + '=' + res[1])
    else:
        loop = 0
#------------------------------------------------------------------------------
