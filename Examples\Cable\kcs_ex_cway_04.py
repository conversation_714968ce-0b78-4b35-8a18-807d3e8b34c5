#
#      NAME:
#
#          kcs_ex_cway_04.py
#
#      PURPOSE:
#
#               This program serve to make a copy of another cableway
#
#
#

import kcs_ui
import kcs_cable
import kcs_util
import KcsStringlist
import kcs_dex
import KcsTransformation3D
import KcsPoint3D
import KcsStat_point3D_req
import KcsVector3D
import KcsColour


OK = kcs_util.ok()
CANCEL  = kcs_util.cancel()

drawing = kcs_ui.string_req("Drawing name")
#-------------------------------------------------------------------------
# Create table cableway
def GetCablewayTable():

    table = []

    st = "DRA('" + drawing[1] + "').VIEW(*).NCWA"
    if kcs_dex.extract(st) == 0:
       if kcs_dex.next_result()== 1:
           a = kcs_dex.get_int()
           b = range(a+1)
           b.remove(0)
    for number in b:
       st1 = "DRA('" + drawing[1] +"').VIEW(*).CWAY("+str(number)+").NAM"
       if kcs_dex.extract(st1) == 0:
           if kcs_dex.next_result()== 3:
               name = kcs_dex.get_string()
               name = SetName(name)
               table.append(name)
    return table
#---------------------------------------------------------------------------
def SetName(fullname):
    x = fullname.index('=')
    size = range(x+1)
    return fullname[len(size):]

#---------------------------------------------------------------------------

table = GetCablewayTable()
for name in table:
    kcs_cable.cway_cwenv_incl(name)

kcs_cable.cway_cwenv_clear()
print sys.exc_info()[1]

for name in table:
    kcs_cable.cway_cwenv_incl(name)
#---------------------------------------------------------------------------

loop = 1
while loop:
    try:
      # receive name, module and colour of cableway
      Name = kcs_ui.string_req("Key in name of new cableway :")
      if Name[0] == OK:
        Module = kcs_ui.string_req("Key in module of new cableway :")
        if Module[0] == OK:
          col = KcsColour.Colour()
          res = kcs_ui.colour_select("Choose a colour", col)
          if res [0] == OK:
            color = col.Name()
          try:
            # create cableway object
            kcs_cable.cway_new(Name[1], Module[1], color )
            kcs_ui.message_noconfirm("New cableway " + Name[1] + Module[1] + " is created." )
            try:
                kcs_cable.cway_activate(Name[1])
                # Key in name of cableway to copy from
                try:
                    cway = kcs_ui.string_req("Key in name of cableway to copy from (in storing format): ")
                    if cway[0] == OK:
                        # check whether cableway yet exist
                        name = SetName(cway[1])
                        print name
                        kcs_cable.cway_exist(name)
                        kcs_ui.message_noconfirm("Cableway " + cway[1]+ " exist." )
                        print "Cableway " + cway[1] + " exist."
                        # duplicate
                        try:
                            kcs_cable.cway_duplicate(cway[1])
                        except:
                            print kcs_cable.error
                        # transform
                        Transformation()
                    else:
                        loop = 0
                except:
                    kcs_ui.message_noconfirm("Cableway not exist or not activate." )
                    print kcs_ui.error
            except:
                kcs_cable.cway_cancel()
                kcs_ui.message_noconfirm("Cableway can not be activated." )
                print kcs_ui.error
          except:
            kcs_ui.message_noconfirm("New cableway can not created." )
            print kcs_ui.error
      else:
          loop = 0
    except:
      loop = 0
      kcs_ui.message_noconfirm("Invalid input cableway !" )
      print kcs_ui.error

#---------------------------------------------------------------------------------
def Transformation():
    point = KcsPoint3D.Point3D()
    stat = KcsStat_point3D_req.Stat_point3D_req()
    res = kcs_ui.point3D_req("Indicate", stat, point)
    normal = KcsVector3D.Vector3D(0.0, 1.0, 0.0)
    #res = kcs_ui.vector3D_req()
    angle = kcs_ui.int_req("The angle, in radians, of rotation.")
    try:
      trans = KcsTransformation3D.Transformation3D()
      trans.Rotate(point, normal, angle[1])
      kcs_cable.cway_transform(trans)
      kcs_ui.message_noconfirm("OK !")
    except:
      kcs_ui.message_noconfirm("Error !")
      print kcs_ui.error
