#
#      NAME:
#
#          kcs_ex_cway_05.py
#
#      PURPOSE:
#
#          This program serve to route
#
#
#

import kcs_ui
import kcs_cable
import kcs_util
import KcsStringlist
import kcs_dex
import KcsPoint3D
import KcsStat_point3D_req



OK = kcs_util.ok()
CANCEL  = kcs_util.cancel()

drawing = kcs_ui.string_req("Drawing name")
#-------------------------------------------------------------------------
# Create table cableway
def GetCablewayTable():
    table = []
    st = "DRA('" + drawing[1] + "').VIEW(*).NCWA"
    if kcs_dex.extract(st) == 0:
       if kcs_dex.next_result()== 1:
           a = kcs_dex.get_int()
           b = range(a+1)
           b.remove(0)
    for number in b:
       st1 = "DRA('" + drawing[1] +"').VIEW(*).CWAY("+str(number)+").NAM"
       if kcs_dex.extract(st1) == 0:
           if kcs_dex.next_result()== 3:
               name = kcs_dex.get_string()
               name = SetName(name)
               table.append(name)
    return table
#---------------------------------------------------------------------------
def SetName(fullname):
    x = fullname.index('=')
    size = range(x+1)
    return fullname[len(size):]

#---------------------------------------------------------------------------
# include all cableways

table = GetCablewayTable()
for name in table:
    kcs_cable.cway_cwenv_incl(name)
kcs_cable.cway_cwenv_clear()
for name in table:
    kcs_cable.cway_cwenv_incl(name)
#---------------------------------------------------------------------------

loop = 1
while loop:
  try:
    # receive name, module and colour of cableway
    Name = kcs_ui.string_req("Key in name of cableway :")
    if Name[0] == OK:
      try:
        kcs_cable.cway_activate(Name[1])
        kcs_ui.message_noconfirm("Cableway " + Name[1] + " is active !" )
        # route or delete
        list = KcsStringlist.Stringlist('Route')
        list.AddString('Delete')
        res = kcs_ui.choice_select('Route or delete ', '', list)
        if res[0]==OK:
          if res[1] == 1:
            RouteParts()
          if res[1] == 2:
            DeleteParts()
        kcs_cable.cway_save()
        kcs_ui.message_noconfirm("Save the changes." )
      except:
        kcs_cable.cway_cancel()
        kcs_ui.message_noconfirm("Could not be activate." )
        print kcs_ui.error
    else:
      loop = 0
  except:
    loop = 0
    kcs_ui.message_noconfirm("Invalid input cableway !" )
    print kcs_ui.error

#--------------------------------------------------------------------------------------
def DeleteParts():
    # if delete
    print 'Delete'
    res = kcs_cable.cway_check_cables()
    kcs_ui.message_noconfirm("The number of cables routed on cableway is "  + str(res) )
    print "The number of cables routed on cableway is "  + str(res)
    status = kcs_ui.answer_req("Question"," Do you want delete ?")
    if status == YES:
       status = CANCEL
       # delete parts
       try:
         start_id = kcs_ui.int_req("Start id: ")
         if start_id[0] == OK:
           end_id = kcs_ui.int_req("End id:")
           if end_id[0] == OK:
             try:
               kcs_cable.cway_route_delete(start_id[1], end_id[1])
               kcs_ui.message_noconfirm("Delete parts from " + start_id[1]+ " to " + end_id[1] )
             except:
               kcs_ui.message_noconfirm("Could not be delete this part !" )
               print kcs_ui.error
       except:
         kcs_ui.message_noconfirm("Invalid input parameter !" )
         print kcs_ui.error

#---------------------------------------------------------------------------------------
def RouteParts():
    # if route
    print 'Route'
    stat = KcsStat_point3D_req.Stat_point3D_req()
    point = KcsPoint3D.Point3D()
    res = kcs_ui.point3D_req("Indicate start point" ,stat , point)
    try:
      kcs_cable.cway_route_start_point(point)
      kcs_ui.message_noconfirm("OK !")
      # second point
      stat1 = KcsStat_point3D_req.Stat_point3D_req()
      point1 = KcsPoint3D.Point3D()
      res = kcs_ui.point3D_req("Indicate second (end) point" ,stat ,point1)
      try:
        kcs_cable.cway_route_end_point(point1)
        kcs_ui.message_noconfirm("OK !")
        str = kcs_ui.string_req("Straight material :")
        bnd = kcs_ui.string_req("Bend component :")
        st_dist = kcs_ui.int_req("Start distance: ", 0)
        it_dist = kcs_ui.int_req("Intermediate distance: ", 0)
        en_dist = kcs_ui.int_req("End distance: ", 0)
        try:
          kcs_cable.cway_material_set(1, 1, str[1], bnd[1], st_dist[1], it_dist[1], en_dist[1])
          kcs_ui.message_noconfirm("OK !")
          Rotate()
        except:
          kcs_ui.message_noconfirm("Error !" )
          print kcs_ui.error
      except:
        kcs_ui.message_noconfirm("Wrong point !")
        print kcs_ui.error
    except:
      kcs_ui.message_noconfirm("Wrong point !")
      print kcs_ui.error

#---------------------------------------------------------------------------------
def Rotate():
    print 'Rotate'
    angle = kcs_ui.real_req("Give angle rotation:")
    try:
      kcs_cable.cway_material_rotate(1,  angle[1])
      kcs_ui.message_noconfirm("OK !")
    except:
      kcs_ui.message_noconfirm("Error !" )
      print kcs_ui.error


