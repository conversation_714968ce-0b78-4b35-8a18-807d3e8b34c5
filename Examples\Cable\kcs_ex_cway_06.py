#
#      NAME:
#
#          kcs_ex_cway_06.py
#
#      PURPOSE:
#
#          Program to handle existing cableway
#

import kcs_ui
import kcs_cable
import kcs_util
import KcsPoint3D
import KcsStat_point3D_req

OK = kcs_util.ok()

try:
  # receive name, module and colour of cableway
  Name = kcs_ui.string_req("Key in name of cableway :")
  if Name[0] == OK:
      # activate cableway object
      kcs_cable.cway_activate(Name[1])
      kcs_ui.message_noconfirm("Cableway " + Name[1] + " is active !" )
      # ----- Planning unit ---------------------
      plu = kcs_ui.string_req("Planning unit :")
      kcs_cable.cway_planning_unit_set(plu[1])
      #------------------------------------------
      # ----- Exclude class
      Name = kcs_ui.string_req("Key in name of the interference class :")
      if Name[0] == OK:
        try:
          kcs_cable.cway_interference_exclude(Name[1])
          kcs_ui.message_noconfirm("Exclude " + Name[1] + " ." )
        except:
          kcs_ui.message_noconfirm("Error ....." )
          print kcs_ui.error
      #--------------------------------------------
      # ----- Permit class ------------------------
      Name = kcs_ui.string_req("Key in name of the interference class :")
      if Name[0] == OK:
        try:
          kcs_cable.cway_interference_permit(Name[1])
          kcs_ui.message_noconfirm("Permit " + Name[1] + " ." )
        except:
          kcs_ui.message_noconfirm("Error ....." )
          print kcs_ui.error
      #--------------------------------------------
      # ---- Position number ----------------------
      Posno()
      kcs_cable.cway_save()
except:
  kcs_ui.message_noconfirm("Invalid input cableway !" )
  print kcs_ui.error

#-------------------------------------------------------------------------------------------
def Posno():
    try:
      posno = kcs_ui.string_req("Position number:")
      id = kcs_ui.int_req("Id of part to put position")
      if posno[0] == OK:
        try:
          kcs_cable.cway_part_posno_set(posno[1],id[1])
          kcs_ui.message_noconfirm("Set position "+ posno[1] + " for " + id[1] + " number." )
        except:
          kcs_ui.message_noconfirm("Not set position "+ posno[1] + " for " + id[1] + " number." )
          print kcs_ui.error
    except:
      kcs_ui.message_noconfirm("Invalid input data !" )
      print kcs_ui.error

