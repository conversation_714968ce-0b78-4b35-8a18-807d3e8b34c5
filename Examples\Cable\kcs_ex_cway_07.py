#---------------------------------------------------------------------------------
#
#      NAME:
#
#          kcs_ex_cway_07.py
#
#      PURPOSE:
#
#          This program serve to create real, imaginary or delete penetration.
#
#
#
#---------------------------------------------------------------------------------

import kcs_ui
import kcs_cable
import kcs_util
import KcsStringlist
import KcsPoint3D
import KcsVector3D
import KcsStat_point3D_req
import KcsTransformation3D

OK       = kcs_util.ok()
CANCEL   = kcs_util.cancel()

#---------------------------------------------------------------------------------
# Real penetration will be created
#---------------------------------------------------------------------------------
def RealPenetration():
   try:
    # receive name of penetration
    Name = kcs_ui.string_req("Key in name of penetration:")
    if Name[0] == OK:
        CwayName = kcs_ui.string_req("Key in name of cableway:")
        if CwayName[0] == OK:
            kcs_ui.message_noconfirm(CwayName[1] + " OK !")
            stat = KcsStat_point3D_req.Stat_point3D_req()
            point = KcsPoint3D.Point3D()
            res = kcs_ui.point3D_req("Indicate start point", stat, point)
            # Key in name of component
            component = kcs_ui.string_req("Key in name of component:")
            if ( len(component[1])> 0 ):
               try:
                  kcs_cable.cpen_real_new(Name[1], CwayName[1], point, component[1])
               except:
                  print kcs_ui.error
            else:
               height = kcs_ui.int_req("Height")
               width = kcs_ui.int_req("Width")
               try:
                  kcs_cable.cpen_real_new(Name[1], CwayName[1], point, height[1], width[1])
               except:
                  print kcs_ui.error
   except:
      kcs_ui.message_noconfirm("Invalid input cableway !" )
      print kcs_ui.error

#---------------------------------------------------------------------------------
# Imaginary penetration will be created
#---------------------------------------------------------------------------------
def ImaginaryPenetration():
   try:
    # receive name of penetration
    Name = kcs_ui.string_req("Key in name of penetration:")
    if Name[0] == OK:
        CwayName = kcs_ui.string_req("Key in name of cableway:")
        if CwayName[0] == OK:
            kcs_ui.message_noconfirm(CwayName[1] + " OK !")
            stat = KcsStat_point3D_req.Stat_point3D_req()
            point = KcsPoint3D.Point3D()
            res = kcs_ui.point3D_req("Indicate start point", stat, point)
            try:
               kcs_cable.cpen_imag_new(Name[1], CwayName[1], point)
            except:
               print kcs_ui.error
   except:
      kcs_ui.message_noconfirm("Invalid input cableway !" )
      print kcs_ui.error

#---------------------------------------------------------------------------------
# Any kind of penetration will be deleted
#---------------------------------------------------------------------------------
def DeletePenetration():
   try:
    # receive name of penetration
    Name = kcs_ui.string_req("Key in name of penetration:")
    if Name[0] == OK:
       kcs_cable.cpen_delete(Name[1])
   except:
      kcs_ui.message_noconfirm("Invalid input cableway !" )
      print kcs_ui.error

#---------------------------------------------------------------------------------
# Transform a real penatration
#---------------------------------------------------------------------------------
def RealTransformation():
   try:
      # receive name of penetration
      Name = kcs_ui.string_req("Key in name of penetration:")
      if Name[0] == OK:
         point   = KcsPoint3D.Point3D()
         stat    = KcsStat_point3D_req.Stat_point3D_req()
         res     = kcs_ui.point3D_req("Indicate", stat, point)
         normal  = KcsVector3D.Vector3D(0.0, 1.0, 0.0)
         angle   = kcs_ui.int_req("The angle in radians of rotation.")
         try:
            trans = KcsTransformation3D.Transformation3D()
            trans.Rotate(point, normal, angle[1])
            kcs_cable.cpen_real_transform(Name[1], trans)
         except:
            print kcs_ui.error
   except:
      kcs_ui.message_noconfirm("Invalid input cableway !" )
      print kcs_ui.error

#---------------------------------------------------------------------------------
# Create main menu
#---------------------------------------------------------------------------------
list = KcsStringlist.Stringlist('New real penetration')
list.AddString('New imaginary penetration')
list.AddString('Delete penetration')
list.AddString('Transform a real penetration')
next = 1
while next:
   (status, index) = kcs_ui.choice_select('Main menu','Operation on the penetrations',list)
   if status == OK:
      status == CANCEL
      if index == 1:
         RealPenetration()
      elif index == 2:
         ImaginaryPenetration()
      elif index == 3:
         DeletePenetration()
      elif index == 4:
         RealTransformation()
   else:
      next = 0
      print kcs_ui.error

#---------------------------------------------------------------------------------
