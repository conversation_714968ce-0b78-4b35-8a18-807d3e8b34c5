#--------------------------------------------------------------------------------------
#
#      NAME:
#
#          kcs_ex_cway_08.py
#
#      PURPOSE:
#
#          This program serve to handling structure reference
#
#
#
#--------------------------------------------------------------------------------------

import kcs_ui
import kcs_cable
import kcs_struct
import kcs_util
import kcs_draft
import KcsStringlist
import kcs_dex
import KcsPoint2D
import KcsPoint3D
import KcsVector3D
import KcsStat_point3D_req
import KcsModel


OK = kcs_util.ok()
CANCEL  = kcs_util.cancel()

#------------------------------------------------------------------------------------
#   Select cableway part
#------------------------------------------------------------------------------------
def SelectPart():
   pt = KcsPoint2D.Point2D()
   kcs_ui.point2D_req("Indicate cableway part", pt)
   model = KcsModel.Model()
   try:
      kcs_draft.model_identify(pt, model)
      if model.Type == "cable way":
         print model.Name, model.PartId
         return model.PartId
      else:
         print "Selected model is not cable way"
         raise 0
   except:
      print kcs_ui.error
      print sys.exc_info()[1]

#--------------------------------------------------------------------------------------
# Connection structure to cableway will be created
#--------------------------------------------------------------------------------------

def ConnectStructure():
   try:
      # receive name of cableway and structure
      cwayName = kcs_ui.string_req("Key in name of cableway:")
      if cwayName[0] == OK:
         kcs_cable.cway_exist(cwayName[1])
         strName = kcs_ui.string_req("Key in name of structure:")
         if strName[0] == OK:
            print "1"
            id = SelectPart()
            stat = KcsStat_point3D_req.Stat_point3D_req()
            point = KcsPoint3D.Point3D()
            res = kcs_ui.point3D_req("Indicate start point", stat, point)
            try:
               kcs_struct.struct_cway_connect(cwayName[1], id, point, strName[1])
            except:
               print kcs_ui.error
               print sys.exc_info()[1]
   except:
      print kcs_ui.error
      print sys.exc_info()[1]


#--------------------------------------------------------------------------------------
# Disconnect structure and cableway will be created
#--------------------------------------------------------------------------------------
def DisconnectStructure():
   try:
    # receive name of cableway and structure
    cwayName = kcs_ui.string_req("Key in name of cableway:")
    if cwayName[0] == OK:
        strName = kcs_ui.string_req("Key in name of structure:")
        if strName[0] == OK:
           try:
              kcs_struct.struct_cway_disconnect(cwayName[1], strName[1])
           except:
              print kcs_ui.error
   except:
      kcs_ui.message_noconfirm("Invalid input !" )
      print kcs_ui.error

#--------------------------------------------------------------------------------------
# Update parameters of structure
#--------------------------------------------------------------------------------------
def Update():
   try:
      # coordinate for node point for cableway material
      stat    = KcsStat_point3D_req.Stat_point3D_req()
      point   = KcsPoint3D.Point3D()
      res     = kcs_ui.point3D_req("Indicate start point", stat, point)
      # vector route
      route   = KcsVector3D.Vector3D(0.0, 1.0, 0.0)
      # vector rotation
      rotat   = KcsVector3D.Vector3D(0.0, 0.0, 1.0)
      # length
      length  = kcs_ui.int_req("Key in length")
      # width
      width  = kcs_ui.int_req("Key in width")
      # height
      height  = kcs_ui.int_req("Key in height")
      # call structure function
      kcs_struct.struct_cway_data(point, route, rotat, length[1], width[1], height[1])
   except:
      kcs_ui.message_noconfirm("Invalid input !" )
      print kcs_ui.error


#--------------------------------------------------------------------------------------
# Main loop
#--------------------------------------------------------------------------------------

main = KcsStringlist.Stringlist('Connect structure to cableway')
main.AddString('Disconnect structure from cableway')
main.AddString('Update data cableway')
next = 1
while next:
   (status, index) = kcs_ui.choice_select('Main menu','Operation on the structure',main)
   if status == OK:
      status == CANCEL
      if index == 1:
         ConnectStructure()
      elif index == 2:
         DisconnectStructure()
      elif index == 3:
         Update()
   else:
      next = 0
      print 'Error'
