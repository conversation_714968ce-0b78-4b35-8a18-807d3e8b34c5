#
#      NAME:
#
#          kcs_ex_cway_09_compcheck.py
#
#      PURPOSE:
#
#          Vitesse program for checking names of components in cable ways.
#          Name rules are defined in a file, see kcs_ex_cway_09_rulesfile.py.
#
#          This sample is supposed to be run from some triggers,
#          see kcs_ex_trig_cable_cway*
#

import kcs_ui
import kcs_util
import kcs_cable
import kcs_ex_cway_09_rulesfile
import sys


#--------------------------------------------------------------------------------------------
def Comp<PERSON>heck(compName):

#
#  Fetch name of active cableway
#
    try:
       cwayName = kcs_cable.cway_name_get()
    except:
       kcs_ui.message_confirm("No active cableway!")
       return -1

#
#  Check component name against rules
#
    if kcs_ex_cway_09_rulesfile.CheckCwayCompName(cwayName, compName) != 0:
       kcs_ui.message_confirm("Given component " +
                              compName +
                              " does not match the rules in the rules file!")
       return -1

#
#  Component was OK
#
    kcs_ui.message_noconfirm("Component " + compName + " verified OK against rules file.")
    return 0
