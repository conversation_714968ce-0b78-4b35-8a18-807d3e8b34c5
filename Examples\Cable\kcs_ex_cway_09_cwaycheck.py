#
#      NAME:
#
#          kcs_ex_cway_09_cwaycheck.py
#
#      PURPOSE:
#
#          Vitesse program for checking names of cable ways.
#          Name rules are defined in a file, see kcs_ex_cway_09_rulesfile.py
#
#          This sample is supposed to be run from some triggers,
#          see kcs_ex_trig_cable_cway*
#

import kcs_ui
import kcs_ex_cway_09_rulesfile


#----------------------------------------------------------------------------------------
def Cway<PERSON>heck(cwayName):

    matchList = kcs_ex_cway_09_rulesfile.CheckCwayName(cwayName)
    if len(matchList) == 0:
       kcs_ui.message_confirm("Cableway name " + cwayName +
                              " does not match the rules in the rules file!")
       return -1

    kcs_ui.message_noconfirm("Cableway name checked OK against rules file.")
    return 0
