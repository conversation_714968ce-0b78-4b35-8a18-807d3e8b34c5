#
#      NAME:
#
#          kcs_ex_cway_09_rulesfile.py
#
#      PURPOSE:
#
#          Read cableway name / component name restriction file
#          and check gives names against this.
#
#          This sample is supposed to be run from some triggers,
#          see kcs_ex_trig_cable_cway*
#

import kcs_dex
import kcs_ui
import re
import string
import kcs_ex_cway_09_preferences


#----------------------------------------------------------------
#  Rule Statement Cache
#----------------------------------------------------------------
ruleStatements = []


#----------------------------------------------------------------
#  ReadRulesFile
#
#  Purpose:
#      Opens and reads the rules file into a static structured list
#
#  Returns:  0 = OK
#           -1 = Error
#----------------------------------------------------------------
def ReadRulesFile():

#
#  If file is already cached, then don't read again
#
    if len(ruleStatements) > 0:
       return 0

#
#  Open & read rules file
#
    fileName = kcs_ex_cway_09_preferences.GetRulesFileName()
    try:
       file = open(fileName)
       try:
	  temp = file.readlines()
	  for str in temp:
	     ruleStatements.append(str)
       except:
          kcs_ui.message_confirm("ReadRulesFile: Rules file could not be read: " + fileName)
          return -1
    except:
       kcs_ui.message_confirm("ReadRulesFile: Rules file could not be opened: " + fileName)
       return -1

#
#  Strip comments from the rules file statements. If there is nothing else but a comment,
#  remove the whole line.
#
    i = 0
    noOfStatements = len(ruleStatements)
    while i < noOfStatements:
       ruleStatements[i] = string.strip(ruleStatements[i])
       temp = re.split("#", ruleStatements[i])
       if (temp[0] == ""):
          del ruleStatements[i]
          noOfStatements = noOfStatements - 1
       else:
          ruleStatements[i] = temp[0]
          i = i + 1

#
#  Split each line into keyword and value
#
    i = 0
    for statement in ruleStatements:
       statement = re.split("=", statement)
       j = 0
       for eachWord in statement:
          statement[j] = string.strip(eachWord)
          j = j + 1
       ruleStatements[i] = statement
       i = i + 1

#
#  Split keywords into "ruletype" and "instance", e.g.
#  "CWAY_NAME_RESTRICTIONS,1" is split up into:
#     Ruletype: "CWAY_NAME_RESTRICTION"
#     Instance: "1"
#  Resulting list for each statement should be:
#     <Ruletype>, <instance>, <value>
    try:
       i = 0
       for statement in ruleStatements:
          tempList = re.split(",", statement[0])
          tempList[0] = string.strip(tempList[0])
          tempList[1] = string.strip(tempList[1])
          tempList.append(statement[1])
          ruleStatements[i] = tempList
          i = i + 1
    except:
       kcs_ui.message_confirm("ReadRulesFile: Cableway name rule file has wrong format!")
       return -1

#
#  Convert wildcard expressions  to regular expressions so that we can use
#  this for matching ("*" to ".*")
#
    i = 0
    for statement in ruleStatements:
       statement[2] = string.replace(statement[2], "*", ".*")
       ruleStatements[i] = statement
       i = i + 1

#
#  Check if we got any rules in the final list
#
    if (len(ruleStatements) == 0):
       kcs_ui.message_confirm("ReadRulesFile: No rules in the rules file!!")
       return -1

    return 0



#----------------------------------------------------------------
#  CompIsStructure
#
#  Purpose:
#      Checks if a given component name is a structure component
#
#  Returns:  0  = True
#            1  = False
#            -1 = Error, Component type code could not be fetched
#----------------------------------------------------------------
def CompIsStructure(compName):

#
#  Extract type code for component by DEX
#
    dexString = "COMP('" + compName + "').TYPE"
    if kcs_dex.extract(dexString) != 0:
       kcs_ui.message_confirm("DEX STATEMENT EXECUTION FAILED!")
       return -1
    type = kcs_dex.next_result()
    if type == 1:
       compTypeCode = kcs_dex.get_int()
    else:
       return -1

#
#  Check type code against preferences
#
    charPattern = kcs_ex_cway_09_preferences.GetStructureCompPattern()
    patternLength = len(charPattern)
    numPattern = string.atoi(charPattern)
    compTypeShortened = compTypeCode / (10**(7-patternLength))
    if abs(compTypeShortened - numPattern) < 1:
       return 0
    else:
       return 1




#----------------------------------------------------------------
#  CheckCwayName
#
#  Purpose:
#      Checks if a given name matches any of the rules from the rules file
#
#  Returns:  list of matching rule numbers, if list is empty, no match was found
#----------------------------------------------------------------
def CheckCwayName(cwayName):

#
#  Read rules file
#
    ReadRulesFile()

#
#  Check name and return list of matching rule numbers
#
    matchList = []
    for statement in ruleStatements:
       if statement[0] == kcs_ex_cway_09_preferences.CWAY_RULE_NAME:
          match = re.match(statement[2], cwayName)
          if match:
             matchList.append(statement[1])
    return matchList




#----------------------------------------------------------------
#  CheckCwayCompName
#
#  Purpose:
#      Checks if a given component name matches any of the rules from the rules file.
#      The found component name rule must also be valid for any of the cableway name rules
#      that matches the current cableway name.
#
#  Returns:  0 = OK, match is found
#           -1 = No match is found
#----------------------------------------------------------------
def CheckCwayCompName(cwayName, compName):

#
#  Read rules file
#
    ReadRulesFile()

#
#  Get a list of cableway name rules that match the current name
#
    matchingCwayRules = CheckCwayName(cwayName)
    if len(matchingCwayRules) == 0:
       return -1

#
#  Check if it is a structure component or a "normal" component.
#  Select the the rule name to look for according to this.
#
    if CompIsStructure(compName) == 0:
       ruleName = kcs_ex_cway_09_preferences.STR_RULE_NAME
    else:
       ruleName = kcs_ex_cway_09_preferences.COMP_RULE_NAME

#
#  Check component name against component name rules in the rules file
#
    for statement in ruleStatements:
       if statement[0] == ruleName:
          match = re.match(statement[2], compName)
          if match:
#
#  Found matching component name rule, check that this rule
#  is valid for the current cableway name. This means that the rule number
#  for the component must be one of the ones found by CheckCwayName method
#
             for rule in matchingCwayRules:
                if statement[1] == rule:
                   return 0
#
#  We have gone through the whole list and not found any match,
#  so component name is not valid according to rules file
#
    return -1



