#
#      NAME:
#
#          kcs_ex_att2.py
#
#      PURPOSE:
#
#          This program shows how the user-defined attribute functions
#          can be used.
#
#          Please observe that this script has the following
#          requirements:
#
#             - There must be an attribute template category called
#               'pipe' in the Tribon Toolkit Preferences.
#             - There must be an attribute template called 'status'
#               in this category.
#             - There must be at least three integer data in this
#               template.
#             - The value of SB_CUSTOMER is yard.
#             - The imported module 'kcs_att_pipe' will be created
#               by the Toolkit Preferences if the above attribute
#               template is created.
#
import kcs_att
import kcs_att_pipe
import kcs_draft
import kcs_ui
import kcs_util
import KcsModel
import KcsPoint2D
#
# Ask the user to indicate a model.
#
coord = KcsPoint2D.Point2D()
try:
#
#  Ask the user to indicate a model.
#
   res = kcs_ui.point2D_req('Indicate',coord)
   if res[0] == kcs_util.ok():
      model = KcsModel.Model('pipe')
      try:
#
#        Find the model.
#
         ident = kcs_draft.model_identify(coord,model)
         try:
#
#           Find the status attribute.
#
            found = 0
            att = kcs_att.attribute_first_get(model)
            while att and not found:
               found = kcs_att.attribute_is(att,kcs_att_pipe.yard_pipe_status())
               if not found:
                  att = kcs_att.attribute_next_get()
#
#           Print all values.
#
            if found:
               i = 0
               while i < kcs_att.integer_count_get(att):
                  print kcs_att.integer_title_get(att,i),': ',kcs_att.integer_get(att,i)
                  i += 1
         except:
            print kcs_att.error
      except:
         print kcs_draft.error
except:
   print kcs_ui.error
