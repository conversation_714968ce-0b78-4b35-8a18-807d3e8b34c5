#
#      NAME:
#
#          kcs_ex_att3.py
#
#      PURPOSE:
#
#          This program shows how the user-defined attribute functions
#          can be used.
#
#          The script will examine all Models given as argument
#          and return a list of all Models that have a user-defined
#          attribute attached.
#
#          Please note that this Vitesse trigger script must be named
#          trig_draft_insert_model_filter.py and be stored in the
#          SBB_TRIGDIR directory to work properly.
#
import kcs_att
import kcs_util
import KcsModel

def pre(*args):
#
# Create a list for model classes.
#
   list = []
#
# Loop through all Model classes in the argument list.
#
   for i in range(len(args[0])):
      att = kcs_att.attribute_first_get(args[0][i])
      if att != None:
         list.append(args[0][i])
#
# Create the return list.
#
   result = []
#
# First, append the proper return code.
#
   result.append(kcs_util.trigger_ok())
#
# Then, append the list of Model classes.
#
   result.append(list)
   return result
