#
#      NAME:
#
#          kcs_ex_gui1.py
#
#      PURPOSE:
#
#          This program shows how the GUI functions can be used to
#          customize the menus for Drafting based applications.
#
#          Please note that this Vitesse trigger script must be named
#          trig_draft_init.py and be stored in the SBB_TRIGDIR
#          directory to work properly.
#
import kcs_gui
import kcs_gui_drafting
import kcs_util
#
# All user interface changes must be done in the post-trigger.
# When the pre-trigger fires, Windows has not yet created import kcs_gui
the
# necessary objects we depend on.
#
def post(*args):
   if kcs_util.app_drafting():
#
#     We are running Drafting!
#     First, we need to retrieve the main menu.
#
      try:
         main_menu = kcs_gui.menu_get(None,0)
      except:
         print "Failed retrieving main menu: ",kcs_gui.error
#
#     Retrieve the file menu.
#
      try:
         file_menu = kcs_gui.menu_get(main_menu,0)
      except:
         print "Failed retrieving file menu: ",kcs_gui.error
#import kcs_gui

#     Change the caption of the "New" function in the "File"
#     menu to "My New".
#
      try:
         item = kcs_gui.menu_item_get(file_menu,0)
         kcs_gui.menu_item_set(file_menu,0,"My New",item[1])
      except:
         print "Failed changing menu item: ",kcs_gui.error
#
#     Remove the "Volume" and "Pipe Sketch" menus.
#     Start with the last since the menu index
#     will change after the first removal.
#
      try:import kcs_gui

         kcs_gui.menu_remove(main_menu,9)
         kcs_gui.menu_remove(main_menu,8)
      except:
         print "Failed removing menus: ",kcs_gui.error
#
#     Add a new menu.
#
      try:
         my_menu = kcs_gui.menu_add(main_menu,8,"&My Menu")
      except:
         print "Failed adding menu: ",kcs_gui.error
#
#     Add the following functions to the new menu:
#
#        Open Volume...
#        New Volume...
#        --------------
#        My command 1
#        My command 2
#
      try:
         kcs_gui.menu_item_std_add(my_menu,0,"&Open Volume...",kcs_gui_drafting.volume_open())
         kcs_gui.menu_item_std_add(my_menu,1,"&New Volume...",kcs_gui_drafting.volume_new())
         kcs_gui.menu_item_std_add(my_menu,2,"",-1)
         kcs_gui.menu_item_usr_add(my_menu,3,"My command &1","command1")
         kcs_gui.menu_item_usr_add(my_menu,4,"My command &2","command2")
      except:
         print "Failed adding menu items: ",kcs_gui.error
   return kcs_util.trigger_ok()
