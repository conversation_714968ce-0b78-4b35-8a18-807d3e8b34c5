#
#      NAME:
#
#          kcs_ex_gui2.py
#
#      PURPOSE:
#
#          This program shows how the GUI functions can be used to
#          customize toolbars for Drafting based applications.
#
#          Please note that this Vitesse trigger script must be named
#          trig_draft_init.py and be stored in the SBB_TRIGDIR
#          directory to work properly.
#
import kcs_gui
import kcs_gui_drafting
import kcs_util
#
# All user interface changes must be done in the post-trigger.
# When the pre-trigger fires, Windows has not yet created the
# necessary objects we depend on.
#
def post(*args):
   if kcs_util.app_drafting():
#
#     We are running Drafting!
#     First, we create a new toolbar.
#
      try:
         my_toolbar = kcs_gui.toolbar_add("My ToolBar")
      except:
         print "Failed creating toolbar: ",kcs_gui.error
#
#     Add the following buttons to the toolbar:
#
#        Open Volume...
#        New Volume...
#        --------------
#        My command 1
#        My command 2
#
      try:
         kcs_gui.toolbar_button_std_add(my_toolbar,0,kcs_gui_drafting.volume_open())
         kcs_gui.toolbar_button_std_add(my_toolbar,1,kcs_gui_drafting.volume_new())
         kcs_gui.toolbar_button_std_add(my_toolbar,2,-1)
         kcs_gui.toolbar_button_usr_add(my_toolbar,3,"command1","c:\\bitmaps\\command1.bmp")
         kcs_gui.toolbar_button_usr_add(my_toolbar,3,"command2","c:\\bitmaps\\command2.bmp")
      except:
         print "Failed adding toolbar buttons: ",kcs_gui.error
#
#     Remove the "New drawing" button from the standard toolbar.
#
      try:
         std_toolbar = kcs_gui.toolbar_get(kcs_gui_drafting.toolbar_standard())
         kcs_gui.toolbar_button_remove(std_toolbar,0)
      except:
         print "Failed removing toolbar button: ",kcs_gui.error
   return kcs_util.trigger_ok()
