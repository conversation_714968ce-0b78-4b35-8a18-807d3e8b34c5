#
#      NAME:
#
#          kcs_ex_gui3.py
#
#      PURPOSE:
#
#          This program shows how the GUI functions can be used to
#          customize the frame title for Drafting based applications.
#
#          Please note that this Vitesse trigger script must be named
#          trig_draft_init.py and be stored in the SBB_TRIGDIR
#          directory to work properly.
#
import kcs_gui
import kcs_util
#
# All user interface changes must be done in the post-trigger.
# When the pre-trigger fires, Windows has not yet created the
# necessary objects we depend on.
#
def post(*args):
   if kcs_util.app_drafting():
#
#     We are running Drafting!
#     Change the frame title.
#
      try:
         kcs_gui.frame_title_set("My Drafting","%p [%o]")
      except:
         print "Failed changing the frame title: ",kcs_gui.error
   return kcs_util.trigger_ok()
