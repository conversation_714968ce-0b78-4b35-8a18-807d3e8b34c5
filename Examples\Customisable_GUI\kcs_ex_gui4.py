#
#      NAME:
#
#          kcs_ex_gui4.py
#
#      PURPOSE:
#
#          This program shows how the GUI functions can be used to
#          customize accelerators for Drafting based applications.
#
#          Please note that this Vitesse trigger script must be named
#          trig_draft_init.py and be stored in the SBB_TRIGDIR
#          directory to work properly.
#
import kcs_gui
import kcs_gui_drafting
import kcs_util
#
# All user interface changes must be done in the post-trigger.
# When the pre-trigger fires, Windows has not yet created the
# necessary objects we depend on.
#
def post(*args):
   if kcs_util.app_drafting():
#
#     We are running Drafting!
#     Add some accelerators.
#
#
#     Retrieve the main menu.
#
      try:
         main_menu = kcs_gui.menu_get(None,0)
      except:
         print "Failed retrieving main menu: ",kcs_gui.error
#
#     Retrieve the file menu.
#
      try:
         file_menu = kcs_gui.menu_get(main_menu,0)
      except:
         print "Failed retrieving file menu: ",kcs_gui.error
      try:
#
#        F5 - Open Volume
#
         kcs_gui.accelerator_add("F5",0,kcs_gui_drafting.volume_open())
#
#        F6 - New Volume
#
         kcs_gui.accelerator_add("F6",0,kcs_gui_drafting.volume_new())
#
#        Add a new menu function and set accelerator to ALT+CTRL-NUMPAD0.
#
         id = kcs_gui.menu_item_usr_add(file_menu,0,"&My command","command")
         kcs_gui.accelerator_add("NUMPAD0",3,id))
      except:
         print "Failed adding accelerators: ",kcs_gui.error
   return kcs_util.trigger_ok()
