#
#      NAME:
#
#          kcs_ex_equip01.py
#
#      PURPOSE:
#
#          This program creates a new equipment item, sets some attributes, and stores
#          the item. User is asked for input through a series of dialogs.
#
import kcs_equip
import kcs_ui
import kcs_util

#
#  Get required data, name & module
#
name = kcs_ui.string_req("Enter name of equipment to create")
if (name[0] == kcs_util.ok()):
   module = kcs_ui.string_req("Enter module of equipment")
   if (module[0] == kcs_util.ok()):
#
#  Create new equipment
#
      try:
         kcs_equip.equip_new(name[1],module[1])
#
#  Set equipment attributes
#
         try:
            component = kcs_ui.string_req("Enter component for equipment")
            if (component[0] == kcs_util.ok()):
               kcs_equip.equip_component_set(component[1])
            room = kcs_ui.string_req("Enter room for equipment")
            if (room[0] == kcs_util.ok()):
               kcs_equip.equip_room_set(room[1])
            alias = kcs_ui.string_req("Enter alias name for equipment")
            if (alias[0] == kcs_util.ok()):
               kcs_equip.equip_alias_set(alias[1])
            description = kcs_ui.string_req("Enter description for equipment")
            if (description[0] == kcs_util.ok()):
               kcs_equip.equip_description_set(description[1])
#
#  Save equipment item
#
            try:
               kcs_equip.equip_save()
               kcs_ui.message_confirm("New equipment successfully created and saved")
            except:
               kcs_ui.message_confirm("Could not save equipment: " + kcs_equip.error)
         except:
            kcs_ui.message_confirm("Could not set attributes for new equipment: " + kcs_equip.error)
      except:
         kcs_ui.message_confirm("Could not create new equipment: " + kcs_equip.error);

