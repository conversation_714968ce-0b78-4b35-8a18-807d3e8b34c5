#
#      NAME:
#
#          kcs_ex_equip02.py
#
#      PURPOSE:
#
#          This program finds an existing equipment item, updates description, and stores
#          the item. User is asked for input through a series of dialogs.
#
import kcs_equip
import kcs_ui
import kcs_util

#
#  Get name of equipment to update
#
name = kcs_ui.string_req("Enter name of equipment to update")
if (name[0] == kcs_util.ok()):
#
#  Check if equipment exists
#
   try:
      exist = kcs_equip.equip_exist(name[1])
      if (exist == 1):
#
#  Activate equipment
#
         try:
            kcs_equip.equip_activate(name[1])
#
#  Set description for equipment
#
            try:
               description = kcs_ui.string_req("Enter description for equipment")
               if (description[0] == kcs_util.ok()):
                  kcs_equip.equip_description_set(description[1])
#
#  Save equipment item
#
                  try:
                     kcs_equip.equip_save()
                     kcs_ui.message_confirm("Equipment " + name[1] + " successfully updated!")
                  except:
                     kcs_ui.message_confirm("Could not save equipment: " + kcs_equip.error)
            except:
               kcs_ui.message_confirm("Could not set attributes for new equipment: " + kcs_equip.error)
         except:
            kcs_ui.message_confirm("Could not activate equipment: " + kcs_equip.error)
      else:
         kcs_ui.message_confirm("Equipment item " + name[1] + "does not exist.");
   except:
      kcs_ui.message_confirm("Could not check if equipment exists: " + kcs_equip.error);

