#
#      NAME:
#
#          kcs_ex_equip03.py
#
#      PURPOSE:
#
#          This program deletes an existing equipment item.
#          User is asked for input through a series of dialogs.
#
import kcs_equip
import kcs_ui
import kcs_util

#
#  Get name of equipment to delete
#
name = kcs_ui.string_req("Enter name of equipment to delete")
if (name[0] == kcs_util.ok()):
#
#  Check if equipment exists
#
   try:
      exists = kcs_equip.equip_exist(name[1])
      if (exists == 1):
         confirm = kcs_ui.answer_req("Confirm", "Are you sure that you want to delete equipment item " + name[1] + "?")
         if (confirm == kcs_util.yes()):
#
#  Delete equipment
#
            try:
               kcs_equip.equip_delete(name[1])
               kcs_ui.message_confirm("Equipment " + name[1] + " successfully deleted!")
            except:
               kcs_ui.message_confirm("Could not delete equipment: " + kcs_equip.error)
         else:
            kcs_ui.message_confirm("Equipment NOT deleted.")
      else:
         kcs_ui.message_confirm("Equipment item " + name[1] + "does not exist.");
   except:
      kcs_ui.message_confirm("Could not check if equipment exists: " + kcs_equip.error);

