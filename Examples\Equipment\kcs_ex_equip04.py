#
#      NAME:
#
#          kcs_ex_equip04.py
#
#      PURPOSE:
#
#          This program places or transforms an existing equipment item.
#          User is asked for input through a series of dialogs.
#
import kcs_equip
import kcs_ui
import kcs_util
import KcsStringlist
import KcsPoint3D
import KcsVector3D
import KcsTransformation3D

#
#  Get name of equipment to place/transform/make ready.
#  Loop until no more equipments should be handled
#  (user will press cancel when asked for equipment name)
#
name = kcs_ui.string_req("Enter name of equipment to handle")
while (name[0] == kcs_util.ok()):
#
#  Check if equipment exists
#
   try:
      exists = kcs_equip.equip_exist(name[1])
      if (exists == 1):
#
#  Activate equipment and ask user for action
#
         try:
            kcs_equip.equip_activate(name[1])
            alternatives = KcsStringlist.Stringlist("Place")
            alternatives.AddString("Transform - Translate")
            alternatives.AddString("Transform - Rotate")
            alternatives.AddString("Ready")
            alternatives.AddString("Save")
            dirtyFlag = 0
            action = kcs_ui.choice_select("Actions", "Select action for equipment: " + name[1], alternatives)
            if (action[0] == kcs_util.ok()):
               quit = 0
            else:
               quit = 1
#
#  Loop until the user does not want to perform any more actions
#  on current equipment
#
            while (quit == 0):
#
#  "Place" action has been chosen by user
#
               if (action[1] == 1):
                  xResult = kcs_ui.real_req("Please give X coordinate", 0.0)
                  if (xResult[0] == kcs_util.ok()):
                     yResult = kcs_ui.real_req("Please give Y coordinate", 0.0)
                     if (yResult[0] == kcs_util.ok()):
                        zResult = kcs_ui.real_req("Please give Z coordinate", 0.0)
                        print "Before Before point"
                        if (zResult[0] == kcs_util.ok()):
                           print "Before point"
                           point = KcsPoint3D.Point3D(xResult[1], yResult[1], zResult[1])
                           print "After point"
                           kcs_ui.message_confirm("Placement vectors will be 1,0,0 and 0,1,0")
                           vector1 = KcsVector3D.Vector3D(1.0, 0.0, 0.0)
                           vector2 = KcsVector3D.Vector3D(0.0, 1.0, 0.0)
                           try:
                              kcs_equip.equip_place(point, vector1, vector2)
                              dirtyFlag = 1
                              kcs_ui.message_confirm("Equipment: " + name[1] + " has been placed OK.")
                           except:
                              kcs_ui.message_confirm("Could not place equipment: " + kcs_equip.error)
#
#  "Transform - Translate" action has been chosen by user
#
               elif (action[1] == 2):
                  xResult = kcs_ui.real_req("Please give translation vector X coordinate", 0.0)
                  if (xResult[0] == kcs_util.ok()):
                     yResult = kcs_ui.real_req("Please give translation vector Y coordinate", 0.0)
                     if (yResult[0] == kcs_util.ok()):
                        zResult = kcs_ui.real_req("Please give translation vector Z coordinate", 0.0)
                        if (zResult[0] == kcs_util.ok()):
                           transVector = KcsVector3D.Vector3D(xResult[1], yResult[1], zResult[1])
                           transformation = KcsTransformation3D.Transformation3D()
                           transformation.Translate(transVector)
                           try:
                              kcs_equip.equip_transform(transformation)
                              dirtyFlag = 1
                              kcs_ui.message_confirm("Equipment: " + name[1] + " has been transformed OK.")
                           except:
                              kcs_ui.message_confirm("Could not transform equipment: " + kcs_equip.error)
#
#  "Transform - Rotate" action has been chosen by user
#
               elif (action[1] == 3):
                  xResult = kcs_ui.real_req("Please give rotation vector X coordinate", 0.0)
                  if (xResult[0] == kcs_util.ok()):
                     yResult = kcs_ui.real_req("Please give rotation vector Y coordinate", 0.0)
                     if (yResult[0] == kcs_util.ok()):
                        zResult = kcs_ui.real_req("Please give rotation vector Z coordinate", 0.0)
                        if (zResult[0] == kcs_util.ok()):
                           angleResult = kcs_ui.real_req("Please give rotation angle", 0.0)
                           if (angleResult[0] == kcs_util.ok()):
                              rotVector = KcsVector3D.Vector3D(xResult[1], yResult[1], zResult[1])
                              point = KcsPoint3D.Point3D(0.0, 0.0, 0.0)
                              transformation = KcsTransformation3D.Transformation3D()
                              transformation.Rotate(point, rotVector, angleResult[1])
                              try:
                                 kcs_equip.equip_transform(transformation)
                                 dirtyFlag = 1
                                 kcs_ui.message_confirm("Equipment: " + name[1] + " has been transformed OK.")
                              except:
                                 kcs_ui.message_confirm("Could not transform equipment: " + kcs_equip.error)
#
#  "Ready" action has been chosen by user
#
               elif (action[1] == 4):
                  try:
                     result = kcs_equip.equip_ready()
                     if (result == 0):
                        dirtyFlag = 1
                        kcs_ui.message_confirm("Equipment " + name[1] + " has been made Ready OK!")
                     else:
                        message = "Equipment " + name[1] + " could NOT be made ready because:\n"
                        if (result == 1):
                           message = message + "room is not set"
                        elif (result == 2):
                           message = message + "subproject is not set"
                        elif (result == 3):
                           message = message + "planning unit is not set"
                        elif (result == 4):
                           message = message + "description is not set"
                        elif (result == 5):
                           message = message + "equipment is not placed"
                        else:
                           message = message + "unknown reason (" + result + ")"
                        kcs_ui.message_confirm(message)
                  except:
                     kcs_ui.message_confirm("Could not make equipment ready: " + kcs_equip.error)
#
#  "Save" action has been chosen by user
#
               elif (action[1] == 5):
                  try:
                     kcs_equip.equip_save()
                     dirtyFlag = 0
                     kcs_ui.message_confirm("Equipment " + name[1] + " saved OK!")
                     quit = 1
                  except:
                     kcs_ui.message_confirm("Could not save equipment: " + kcs_equip.error)
#
#  Prompt for another action for current equipment and loop back.
#  Also check for non-saved modifications and notify user.
#
               if (quit == 0):
                  action = kcs_ui.choice_select("Actions", "Select action for equipment: " + name[1], alternatives)
                  if (action[0] != kcs_util.ok()):
                     quit = 1
                     if (dirtyFlag == 1):
                        question = "Equipment " + name[1] + " has been modified. OK to save?: "
                        save = kcs_ui.answer_req("Question", question)
                        if (save == kcs_util.yes()):
                           try:
                              kcs_equip.equip_save()
                              kcs_ui.message_confirm("Equipment " + name[1] + " saved OK!")
                           except:
                              kcs_ui.message_confirm("Could not save equipment: " + kcs_equip.error)
                        elif (save != kcs_util.no()):
                           quit = 0
            # End action loop
#
#  Equipment does not exist, could not be checked for existence or could not be activated
#
         except:
	    kcs_ui.message_confirm("Equipment item " + name[1] + " could not be activated:" + kcs_equip.error)
      else:
         kcs_ui.message_confirm("Equipment item " + name[1] + " does not exist.")
   except:
      kcs_ui.message_confirm("Could not check if equipment exists: " + kcs_equip.error)
#
#  Prompt for another equipment and loop back
#
   try:
      kcs_equip.equip_cancel()
   except:
      a = 1  # Exception can occur if no equipment is current, we ignore this
   name = kcs_ui.string_req("Enter name of equipment to handle")
   # End equipment loop
