#
#      NAME:
#
#          kcs_ex_model05.py
#
#      PURPOSE:
#
#          This example shows how to use model_interference_check.
#          Outfitting models touched by gievn panel in given range are delivered.
#
import kcs_model
import kcs_ui
import kcs_util
import KcsModel
import CommonSample
import math

Equipments = ['equipment', 'struct', 'pipe', 'ventilation', 'cable']

model = None
outfitting = Equipments[0]
distance = 0.5

def BuildChoiceSelectList():
    list = []
    if model == None:
        list.append('Model: None')
    else:
        list.append('Model: ' + model.Name + " (" + model.Type + ")")
    list.append('Outfit type: ' + outfitting)
    list.append('Distance: ' + str(distance))
    list.append('Check interference')
    return list

while 1:
    res, index = kcs_ui.choice_select('model_interference_check test', 'Action:', BuildChoiceSelectList())
    if res == kcs_util.ok():
        if index==1:
            model = CommonSample.SelectModel()
        elif index==2:
            res, index = kcs_ui.choice_select('Outfitting types', 'Select type:', Equipments)
            if res == kcs_util.ok():
                outfitting = Equipments[index-1]
        elif index==3:
            res, value = kcs_ui.real_req('Distance value:', distance)
            if res == kcs_util.ok():
                distance = math.fabs(value)
        elif index==4:
            try:
                models = kcs_model.model_interference_check(model, outfitting, distance)
                results = []
                if models != None:
                    for model in models:
                        results.append(model.Name + "(" + model.Type + ")")
                else:
                    results.append('No interferences found!')
                kcs_ui.string_select('Interference models', 'Results', 'Press cancel', results)
            except:
                pass
    else:
        break

