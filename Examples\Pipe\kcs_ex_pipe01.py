#
#      NAME:
#
#          kcs_ex_pipe01.py
#
#      PURPOSE:
#
#          Program to create pipe
#

import kcs_ui
import kcs_util
import KcsColour
import kcs_pipe
import KcsPipeName


#------------------------------------------------------------------------------------
# Input functions
#------------------------------------------------------------------------------------

def InputComponentName():
    (status, ComponentName) = kcs_ui.string_req("Component Name (optional)", "")
    if (status != kcs_util.ok()) :
        ComponentName = "";

    return  ComponentName

#------------------------------------------------------------------------------------
# Ask user to input pipe name
#------------------------------------------------------------------------------------

def InputPipeName():
    try:
        (status, name) = kcs_ui.string_req("Creating new pipe, key in pipe name", "")
        if (status != kcs_util.ok()) :
            return 0, None

        PipeName = KcsPipeName.PipeName( name )
        return (1, PipeName)

    except:
        print 'Error: Invalid pipe name'
        return 0, None


#------------------------------------------------------------------------------------
# Pipe Creation
#------------------------------------------------------------------------------------

try:
    print "Creating new pipe"

    status, PipeName = InputPipeName()

    if (status == 1):
        (ComponentName) = InputComponentName()

    PipeColor = ""
    UserID    = ""

    if (status == 1):
       try:
            kcs_pipe.pipe_new(PipeName, PipeColor, UserID, ComponentName)

            print "Getting pipe name"
            try:
                    name = kcs_pipe.pipe_name_get()
                    print name
            except:
                    print 'Error while trying to get current pipe name: ', kcs_pipe.error

            print "Saving pipe"
            try:
                    kcs_pipe.pipe_save()
            except:
                    print 'Error while trying to save pipe: ', kcs_pipe.error

            print "Activating pipe"
            try:
                   kcs_pipe.pipe_activate(PipeName)
            except:
                   print 'Error while trying to activate pipe: ', kcs_pipe.error

            print "Canceling pipe"
            try:
                   kcs_pipe.pipe_cancel()
            except:
                   print 'Error canceling pipe: ', kcs_pipe.error

            kcs_ui.message_confirm('Pipe %s created !' % str(PipeName))

       except:
            print 'Error while trying to create new pipe: ', kcs_pipe.error
except:
    print 'Error: ', kcs_pipe.error
