#
#      NAME:
#
#          kcs_ex_pipe02.py
#
#      PURPOSE:
#
#          Program to delete pipe
#

import kcs_ui
import kcs_util
import kcs_pipe
import KcsPipeName


#------------------------------------------------------------------------------------
# Input Pipe Name function
#------------------------------------------------------------------------------------

def InputPipeName():
    try:
        (status, ModuleName) = kcs_ui.string_req("Module Name", "")
        if (status != kcs_util.ok()) :
            return 0, None

        (status, SystemName) = kcs_ui.string_req("System Name", "")
        if (status != kcs_util.ok()) :
            return 0, None

        (status, LineNo) = kcs_ui.int_req("Line Number")
        if (status != kcs_util.ok()) :
            return 0, None

        PipeName = KcsPipeName.PipeName(ModuleName, SystemName, LineNo)
        return (1, PipeName)

    except:
        return 0, None



#------------------------------------------------------------------------------------

try:
        print "Testing pipe exist"

        status, PipeName = InputPipeName()

        if (status == 1) :

           (result) = kcs_pipe.pipe_exist(PipeName)

           if (result != 1):
               print "No"
           else:
               print "Yes"
               print "Deleting pipe"

               res = kcs_ui.answer_req('Delete pipe','Do you want to delete pipe ?')

               if res == kcs_util.yes():
                 try:
                       kcs_pipe.pipe_delete(PipeName)
                 except:
                       print ' Error when trying to delete'
                       print kcs_pipe.error

               (result) = kcs_pipe.pipe_exist(PipeName)

               print "Testing pipe exist"

               if (result == 1):
                   print "Yes"
               else:
                   print "No"


except:
    print kcs_pipe.error
