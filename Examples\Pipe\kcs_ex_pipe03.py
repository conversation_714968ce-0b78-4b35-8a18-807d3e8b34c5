#
#      NAME:
#
#          kcs_ex_pipe03.py
#
#      PURPOSE:
#
#          This program :
#			1. Automatic update of position names for all spools without position names in current pipe
#           2. Gets/Sets bending machine object Id
#

import KcsStringlist
import kcs_ui
import kcs_util
import kcs_pipe

#------------------------------------------------------------------------------------
#   Automatic update of current pipe position names
#------------------------------------------------------------------------------------

def AutoSpoolName():
    try:
        print "Update pipe spool position names"
        kcs_pipe.pipe_auto_spool_name_set()
    except:
        print kcs_pipe.error

#------------------------------------------------------------------------------------
#   Automatic deleteion of current pipe position names
#------------------------------------------------------------------------------------

def AutoSpoolNameDelete():
    try:
        print "Delete pipe spool position names"
        kcs_pipe.pipe_auto_spool_name_delete()
    except:
        print kcs_pipe.error

#------------------------------------------------------------------------------------
#   Get Bend Object ID
#------------------------------------------------------------------------------------

def PipeBendObjIdGet():
    try:
        print "Get bending machine"
        BendObjId = kcs_pipe.default_bendobj_id_get()
        print "Id : ", BendObjId
    except:
        print kcs_pipe.error

#------------------------------------------------------------------------------------
#   Set Bend Object ID
#------------------------------------------------------------------------------------

def PipeBendObjIdSet():
    try:
        print "Set bending machine"
        BendObjId = 1
        kcs_pipe.default_bendobj_id_set(BendObjId)
    except:
        print kcs_pipe.error

#------------------------------------------------------------------------------------
#   Function selection
#------------------------------------------------------------------------------------

actions = KcsStringlist.Stringlist('Update spool position names')
actions.AddString('Remove spool position names')
actions.AddString('Get bending machine')
actions.AddString('Set bending machine')
try:
    (status, option) = kcs_ui.choice_select('Spool name', 'Select option', actions)
    if status == kcs_util.ok() :
        if option == 1 :
            AutoSpoolName()
        elif option == 2 :
            AutoSpoolNameDelete()
        elif option == 3 :
            PipeBendObjIdGet()
        elif option == 4 :
            PipeBendObjIdSet()
    else:
        print "User interrupted!"
except:
    print kcs_ui.error
