#
#      NAME:
#
#          kcs_ex_pipe04.py
#
#      PURPOSE:
#
#          This program :
#				1. Traverses through all pipe parts
#				2. Traverses through all pipe spools
#				3. Displays part branch and connection coordinates
#

import KcsStringlist
import kcs_ui
import kcs_util
import kcs_pipe
import KcsPoint3D
import KcsStat_point3D_req
import kcs_draft
import KcsModel
import KcsPoint2D


#------------------------------------------------------------------------------------
#   Select pipe part
#------------------------------------------------------------------------------------

def SelectPart():
        pt = KcsPoint2D.Point2D()
        kcs_ui.point2D_req("Indicate part", pt)
        ModelInfo = KcsModel.Model()
        try:
                kcs_draft.model_identify(pt, ModelInfo)
                if ModelInfo.Type in ["pipe", "pipe spool"]:
                    print "Selected part : ", ModelInfo.Name, ModelInfo.PartId
                    return ModelInfo
                else:
                    print "Selected model is not a pipe"
                    return None
        except:
            print kcs_draft.error

#------------------------------------------------------------------------------------
#   Select point 3D
#------------------------------------------------------------------------------------

def SelectPoint(InitialType = 3, Message = "Indicate point"):
        status = KcsStat_point3D_req.Stat_point3D_req()
        point = KcsPoint3D.Point3D()
        status.Initial3D = InitialType
        res = kcs_ui.point3D_req(Message, status, point)
        if res[0] == kcs_util.ok():
            return point
        else:
            print "User interrupted!"
            return None

#------------------------------------------------------------------------------------
#   Select part connection
#------------------------------------------------------------------------------------

def SelectConnection(partId):
        point = SelectPoint(3, "Indicate connection")
        return kcs_pipe.part_conn_find(partId, point)

#------------------------------------------------------------------------------------
#   Find part function
#------------------------------------------------------------------------------------

def FindPart():
    try:
        print "Iterating through pipe parts"
        (result, partId, inConn) = kcs_pipe.pipe_part_find(1)
        while (result != 0):
            print "Part Id :", partId, "Input conn :", inConn
            (result, partId, inConn) = kcs_pipe.pipe_part_find(2, partId)

    except:
        print kcs_pipe.error

#------------------------------------------------------------------------------------
#   Find spool part function
#------------------------------------------------------------------------------------

def FindSpoolPart():
    try:
        print "Iterating through pipe spools"
        (result, spoolId) = kcs_pipe.pipe_spool_find(1);

        while (result != 0):
            spoolName = kcs_pipe.part_spool_get(spoolId)
            print "Spool name :", spoolName, "Spool Id :", spoolId

            (result, partId) = kcs_pipe.pipe_spool_part_find(1, spoolId)

            while (result != 0):
                print "Part Id :", partId
                (result, partId) = kcs_pipe.pipe_spool_part_find(2, partId)

            (result, spoolId) = kcs_pipe.pipe_spool_find(2, spoolId)

    except:
        print kcs_pipe.error

#------------------------------------------------------------------------------------
#   Get branch Id function
#------------------------------------------------------------------------------------

def GetPartBranch():
    try:
        print "Part branch"
        ModelInfo = SelectPart()
        branchId = kcs_pipe.part_branch_get(ModelInfo.PartId)
        print "Branch Id :", branchId

    except:
        print kcs_pipe.error


#------------------------------------------------------------------------------------
#   Get part connection coordinates
#------------------------------------------------------------------------------------

def PipePartConnCoordGet():
    try:
        print "Get connection coordinates"
        coord = KcsPoint3D.Point3D()
        ModelInfo = SelectPart()
        conn      = SelectConnection(ModelInfo.PartId)
        kcs_pipe.part_conn_coord_get(ModelInfo.PartId, conn, coord)
        print "Coordinates :", coord

    except:
        print kcs_pipe.error

#------------------------------------------------------------------------------------
#   Function selection
#------------------------------------------------------------------------------------

actions = KcsStringlist.Stringlist('Find part')
actions.AddString('Find spool part')
actions.AddString('Get part branch')
actions.AddString('Connection coordinates')

try:
    (status, option) = kcs_ui.choice_select('Pipe part functions', 'Select option', actions)
    if status == kcs_util.ok() :
        if option == 1 :
            FindPart()
        elif option == 2:
            FindSpoolPart()
        elif option == 3 :
            GetPartBranch()
        elif option == 4:
            PipePartConnCoordGet()

    else:
        print "User interrupted!"
except:
    print kcs_ui.error
