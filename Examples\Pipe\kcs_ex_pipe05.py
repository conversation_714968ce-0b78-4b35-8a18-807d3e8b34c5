#
#      NAME:
#
#          kcs_ex_pipe05.py
#
#      PURPOSE:
#
#          This program shows example usage of:
#                  pipe production check functions
#                  pipe ready function
#

import sys
import kcs_pipe
import KcsStringlist
import kcs_ui
import kcs_util
import KcsPipeCheckSettings
import KcsPipeCheck

#------------------------------------------------------------------------------------
#   Display result
#------------------------------------------------------------------------------------

def ShowCheckResut(result):
    try:
        status      = result[0]
        numMessages = result[1]

        if status == 1:
            print "Ok"
        elif status == 2:
            print "OK with warnings"
        elif status == 3:
            print "not OK, due to controls"
        else:
            print "not OK, due to other reasons"


        for index in range(numMessages):
            print result[index+2]
    except:
        print sys.exc_info()[0]

#------------------------------------------------------------------------------------
#   Check pipe
#------------------------------------------------------------------------------------

def CheckPipe():
    try:
        print "Pipe production checks"

        check = KcsPipeCheck.PipeCheck()

        check.SetPosNoCheck()
        check.SetExcessCheck()
        check.SetBendingCheck()
        check.SetExtrusionCheck()
        check.SetFeedCheck()
        check.SetFrameCheck()
        check.SetLengthCheck()
        check.SetLooseCheck()
        check.SetRotationCheck()
        check.SetJointCheck()
        check.SetNonConnCheck()
        check.SetWeldGapsCheck()

        result = kcs_pipe.pipe_check( check )
        ShowCheckResut(result);

    except:
        print "Error when checking pipe"
        print kcs_pipe.error

#------------------------------------------------------------------------------------
#   Make Pipe Ready
#------------------------------------------------------------------------------------

def PipeReady():
    try:
        print "Making pipe ready"
        result = kcs_pipe.pipe_ready()
        print "Result :"
        ShowCheckResut(result);

    except:
        print "Error when trying to make pipe ready"
        print kcs_pipe.error

#------------------------------------------------------------------------------------
#   Enable check functions
#------------------------------------------------------------------------------------

def SetCheckOn():
    try:
        print "Pipe checking control"

        settings = KcsPipeCheckSettings.PipeCheckSettings()

        print "Enable position name check"
        settings.SetPosNoCheck()
        kcs_pipe.pipe_check_settings( settings )

        print "Enable bending check"
        print "Enable extrusion check"

        settings.SetBendingCheck()
        settings.SetExtrusionCheck()

        #iterate through all parts
        result = kcs_pipe.pipe_part_find(1)
        while (result[0] != 0):
            partId = result[1]
            kcs_pipe.pipe_check_settings( settings, partId )

            result = kcs_pipe.pipe_part_find(2, partId)

    except:
        print "Error when calling function"
        print kcs_pipe.error

#------------------------------------------------------------------------------------
#   Disable check functions
#------------------------------------------------------------------------------------

def SetCheckOff():
    try:
        print "Pipe checking control"

        settings = KcsPipeCheckSettings.PipeCheckSettings()

        print "Disable position name check"
        settings.SetPosNoCheck(0)
        kcs_pipe.pipe_check_settings( settings )

        print "Disable bending check"
        print "Disable extrusion check"

        settings.SetBendingCheck(0)
        settings.SetExtrusionCheck(0)

        #iterate through all parts
        result = kcs_pipe.pipe_part_find(1)
        while (result[0] != 0):
            partId = result[1]
            kcs_pipe.pipe_check_settings( settings, partId )

            result = kcs_pipe.pipe_part_find(2, partId)


    except:
        print "Error when calling function"
        print kcs_pipe.error


#------------------------------------------------------------------------------------
#   Function selection
#------------------------------------------------------------------------------------

actions = KcsStringlist.Stringlist('Check pipe')
actions.AddString('Pipe ready')
actions.AddString('Enable pipe checking')
actions.AddString('Disable pipe checking')

try:
    (status, option) = kcs_ui.choice_select('Pipe part functions', 'Select option', actions)
    if status == kcs_util.ok() :
        if option == 1 :
            CheckPipe()
        elif option == 2 :
            PipeReady()
        elif option == 3 :
            SetCheckOn()
        elif option == 4 :
            SetCheckOff()

    else:
        print "User interrupted!"
except:
    print kcs_ui.error
