#
#      NAME:
#
#          kcs_ex_pipe06.py
#
#      PURPOSE:
#
#          This program shows example usage of pipe material functions
#

import KcsStringlist
import kcs_ui
import kcs_util
import kcs_pipe
import kcs_draft
import KcsModel
import KcsPoint2D
import KcsPipeMaterial

#------------------------------------------------------------------------------------
#   Get current pipe model
#------------------------------------------------------------------------------------

def GetCurrentPipeModel():
        CurrentModel = KcsModel.Model()
        CurrentModel.SetType("pipe")	

	try:
	   ProjectName = kcs_util.TB_environment_get('SB_PROJ')
	   PipeName    = kcs_pipe.pipe_name_get()	
	   CurrentModel.SetName(ProjectName + '-' + str(PipeName))
	except:
	   print 'Error getting model: ', kcs_pipe.error
		
	return CurrentModel

#------------------------------------------------------------------------------------
#   Select pipe part
#------------------------------------------------------------------------------------

def SelectPart():
        pt = KcsPoint2D.Point2D()
        kcs_ui.point2D_req("Indicate part", pt)
        ModelInfo = KcsModel.Model()
        try:
                kcs_draft.model_identify(pt, ModelInfo)
                if ModelInfo.Type in ["pipe", "pipe spool"]:
                    print "Selected part : ", ModelInfo.Name, ModelInfo.PartId
                    return ModelInfo.PartId
                else:
                    print "Selected model is not a pipe"
                    return None
        except:
            print kcs_draft.error

#------------------------------------------------------------------------------------
#   Set pipe material
#------------------------------------------------------------------------------------

def PipeMaterialSet():
    try:
        print "Set pipe material"

        material = KcsPipeMaterial.PipeMaterial()
        material.SetStraightMaterial("88.9-10-1330")
        material.SetBendMaterial("88.9-10-1330")

        kcs_pipe.pipe_material_set(material)

    except:
        print kcs_pipe.error

#------------------------------------------------------------------------------------
#   Set material straight
#------------------------------------------------------------------------------------

def PipeMaterialStraightSet():
    try:
        print "Set straight material"
        PartId = SelectPart()

        material = KcsPipeMaterial.PipeMaterial()
        material.SetType('straight')
        material.SetStraightMaterial("88.9-10-1330")

        kcs_pipe.part_material_set( PartId, material)
    except:
        print kcs_pipe.error

#------------------------------------------------------------------------------------
#   Set material bend
#------------------------------------------------------------------------------------

def PipeMaterialBendSet():
    try:
        print "Set bend material"
        PartId = SelectPart()
        PartId2 = SelectPart()

        material = KcsPipeMaterial.PipeMaterial()
        material.SetType('bend')
        material.SetBendMaterial("88.9-10-1330")

        kcs_pipe.part_material_set( PartId, PartId2, material)
    except:
        print kcs_pipe.error

#------------------------------------------------------------------------------------
#   Set material mitre
#------------------------------------------------------------------------------------

def PipeMaterialMitreSet():
    try:
        print "Set mitre material"
        PartId = SelectPart()
        PartId2 = SelectPart()

        material = KcsPipeMaterial.PipeMaterial()
        material.SetType('mitre')

        kcs_pipe.part_material_set( PartId, PartId2, material)
    except:
        print kcs_pipe.error

#------------------------------------------------------------------------------------
#  Remove part material
#------------------------------------------------------------------------------------

def PipePartMaterialRemove():
    try:
        print "Remove part material"
        PartId = SelectPart()
        kcs_pipe.part_material_remove( PartId )
    except:
        print kcs_pipe.error
#------------------------------------------------------------------------------------
#   Remove pipe material
#------------------------------------------------------------------------------------

def PipeMaterialRemove():
    try:
        print "Remove pipe material"
        kcs_pipe.pipe_material_remove()
    except:
        print kcs_pipe.error

#------------------------------------------------------------------------------------
#   Function selection
#------------------------------------------------------------------------------------

actions = KcsStringlist.Stringlist('Set material')
actions.AddString('Set straight material')
actions.AddString('Set bend material')
actions.AddString('Set mitre material')
actions.AddString('Remove part material')
actions.AddString('Remove pipe material')

try:
    (status, option) = kcs_ui.choice_select('Pipe part functions', 'Select option', actions)
    if status == kcs_util.ok() :
        if option == 1 :
            PipeMaterialSet()
        elif option == 2 :
            PipeMaterialStraightSet()
        elif option == 3 :
            PipeMaterialBendSet()
        elif option == 4 :
            PipeMaterialMitreSet()
        elif option == 5 :
            PipePartMaterialRemove()
        elif option == 6 :
            PipeMaterialRemove()

    else:
        print "User interrupted!"

    try:
        kcs_draft.model_draw(GetCurrentPipeModel())
    except:
        print "Redraw error", kcs_draft.error

except:
    print kcs_ui.error
