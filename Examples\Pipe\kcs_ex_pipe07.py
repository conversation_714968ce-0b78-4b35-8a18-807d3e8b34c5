#
#      NAME:
#
#          kcs_ex_pipe07.py
#
#      PURPOSE:
#
#          This program shows example usage of pipe routing functions
#

import KcsStringlist
import kcs_ui
import kcs_util
import kcs_pipe
import KcsPoint3D
import KcsStat_point3D_req
import kcs_draft
import KcsModel
import KcsPoint2D
import KcsPipeRoute

#------------------------------------------------------------------------------------
#   Get current pipe model
#------------------------------------------------------------------------------------

def GetCurrentPipeModel():
        CurrentModel = KcsModel.Model()
        CurrentModel.SetType("pipe")	

	try:
	   ProjectName = kcs_util.TB_environment_get('SB_PROJ')
	   PipeName    = kcs_pipe.pipe_name_get()	
	   CurrentModel.SetName(ProjectName + '-' + str(PipeName))
	except:
	   print 'Error getting model: ', kcs_pipe.error
		
	return CurrentModel

#------------------------------------------------------------------------------------
#   Select pipe part
#------------------------------------------------------------------------------------

def SelectPart():
        pt = KcsPoint2D.Point2D()
        kcs_ui.point2D_req("Indicate part", pt)
        ModelInfo = KcsModel.Model()
        try:
                kcs_draft.model_identify(pt, ModelInfo)
                if ModelInfo.Type in ["pipe", "pipe spool"]:
                    print "Selected part : ", ModelInfo.Name, ModelInfo.PartId
                    return ModelInfo
                else:
                    print "Selected model is not a pipe"
                    return None
        except:
            print kcs_draft.error

#------------------------------------------------------------------------------------
#   Select point 3D
#------------------------------------------------------------------------------------

def SelectPoint(InitialType = 3, Message = "Indicate point"):
        status = KcsStat_point3D_req.Stat_point3D_req()
        point = KcsPoint3D.Point3D()
        status.Initial3D = InitialType
        res = kcs_ui.point3D_req(Message, status, point)
        if res[0] == kcs_util.ok():
            return point
        else:
            print "User interrupted!"
            return None

#------------------------------------------------------------------------------------
#   Select part connection
#------------------------------------------------------------------------------------

def SelectConnection(partId):
        point = SelectPoint(3, "Indicate connection")
        return kcs_pipe.part_conn_find(partId, point)

#------------------------------------------------------------------------------------
#   Start route from point
#------------------------------------------------------------------------------------

def PipeRoutePointStart():
    try:
        print "Start route from point"
        point = SelectPoint(1)

        criteria = KcsPipeRoute.PipeRoute()
        criteria.SetRouteType('point')
        criteria.SetFreePoint(point)

        kcs_pipe.pipe_route_start(criteria)
    except:
        print kcs_pipe.error

#------------------------------------------------------------------------------------
#   Start route from part
#------------------------------------------------------------------------------------

def PipeRoutePartStart():
    try:
        print "Start route from part"
        Model = SelectPart()
        Conn  = SelectConnection(Model.PartId);

        criteria = KcsPipeRoute.PipeRoute()
        criteria.SetRouteType('part')
        criteria.SetPartId(Model.PartId)
        criteria.SetConnection(Conn)

        kcs_pipe.pipe_route_start(criteria)

    except:
        print kcs_pipe.error

#------------------------------------------------------------------------------------
#   Start route from external part
#------------------------------------------------------------------------------------

def PipeRoutePartExtStart():
    try:
        print "Start route from external part"
        Model = SelectPart()
        Conn  = 1

        criteria = KcsPipeRoute.PipeRoute()
        criteria.SetRouteType('part')
        criteria.SetPartId(Model.PartId)
        criteria.SetConnection(Conn)
        criteria.SetExternalPipe( Model.Name )

        kcs_pipe.pipe_route_start(criteria)
    except:
        print kcs_pipe.error

#------------------------------------------------------------------------------------
#   Start route from part surface
#------------------------------------------------------------------------------------

def PipeRouteSurfaceStart():
    try:
        print "Start route from surface"
        Model = SelectPart()
        point = SelectPoint(1, "Indicate point on surface")

        criteria = KcsPipeRoute.PipeRoute()
        criteria.SetRouteType('surface')
        criteria.SetPartId(Model.PartId)
        criteria.SetSurfPoint(point)

        kcs_pipe.pipe_route_start(criteria)

    except:
        print kcs_pipe.error

#------------------------------------------------------------------------------------
#   Start route from external part surface
#------------------------------------------------------------------------------------

def PipeRouteSurfaceExtStart():
    try:
        print "Start route from external surface"
        Model = SelectPart()
        point = SelectPoint(1, "Indicate point on surface")

        criteria = KcsPipeRoute.PipeRoute()
        criteria.SetRouteType('surface')
        criteria.SetPartId(Model.PartId)
        criteria.SetSurfPoint(point)
        criteria.SetExternalPipe( Model.Name )

        kcs_pipe.pipe_route_start(criteria)

    except:
        print kcs_pipe.error

#------------------------------------------------------------------------------------
#   Add route point
#------------------------------------------------------------------------------------

def PipeRoutePointAdd():
    try:
        print "Add route point"
        point = SelectPoint(1)

        criteria = KcsPipeRoute.PipeRoute()
        criteria.SetRouteType('point')
        criteria.SetFreePoint(point)

        kcs_pipe.pipe_route_point(criteria)
    except:
        print kcs_pipe.error

#------------------------------------------------------------------------------------
#   End route at point
#------------------------------------------------------------------------------------

def PipeRoutePointEnd():
    try:
        print "End route at point"
        point = SelectPoint(1)

        criteria = KcsPipeRoute.PipeRoute()
        criteria.SetRouteType('point')
        criteria.SetFreePoint(point)

        kcs_pipe.pipe_route_end(criteria)

    except:
        print kcs_pipe.error

#------------------------------------------------------------------------------------
#   End route at part
#------------------------------------------------------------------------------------

def PipeRoutePartEnd():
    try:
        print "End route at part"
        Model = SelectPart()
        Conn  = SelectConnection(Model.PartId)

        criteria = KcsPipeRoute.PipeRoute()
        criteria.SetRouteType('part')
        criteria.SetPartId(Model.PartId)
        criteria.SetConnection(Conn)

        kcs_pipe.pipe_route_end(criteria)

    except:
        print kcs_pipe.error

#------------------------------------------------------------------------------------
#   End route at external part
#------------------------------------------------------------------------------------

def PipeRoutePartExtEnd():
    try:
        print "End route at external part"
        Model = SelectPart()
        Conn  = 1

        criteria = KcsPipeRoute.PipeRoute()
        criteria.SetRouteType('part')
        criteria.SetPartId(Model.PartId)
        criteria.SetConnection(Conn)
        criteria.SetExternalPipe( Model.Name )

        kcs_pipe.pipe_route_end(criteria)

    except:
        print kcs_pipe.error

#------------------------------------------------------------------------------------
#   End route on part surface
#------------------------------------------------------------------------------------

def PipeRouteSurfaceEnd():
    try:
        print "End route on surface"
        Model = SelectPart()
        point = SelectPoint(1, "Indicate point on surface")

        criteria = KcsPipeRoute.PipeRoute()
        criteria.SetRouteType('surface')
        criteria.SetPartId(Model.PartId)
        criteria.SetSurfPoint(point)

        kcs_pipe.pipe_route_end(criteria)

    except:
        print kcs_pipe.error

#------------------------------------------------------------------------------------
#   End route on external part surface
#------------------------------------------------------------------------------------

def PipeRouteSurfaceExtEnd():
    try:
        print "End route on external surface"
        Model = SelectPart()
        point = SelectPoint(1, "Indicate point on surface")

        criteria = KcsPipeRoute.PipeRoute()
        criteria.SetRouteType('surface')
        criteria.SetPartId(Model.PartId)
        criteria.SetSurfPoint(point)
        criteria.SetExternalPipe( Model.Name )

        kcs_pipe.pipe_route_end(criteria)

    except:
        print kcs_pipe.error

#------------------------------------------------------------------------------------
#   Function selection
#------------------------------------------------------------------------------------

actions = KcsStringlist.Stringlist('Start route from point')
actions.AddString('Start route from part')
actions.AddString('Start route from external part')
actions.AddString('Start route form surface')
actions.AddString('Start route form external surface')
actions.AddString('Add route point')
actions.AddString('End route at point')
actions.AddString('End route at part')
actions.AddString('End route at external part')
actions.AddString('End route on surface')
actions.AddString('End route on external surface')

try:
    status = kcs_util.ok()
    while status == kcs_util.ok() :
        (status, option) = kcs_ui.choice_select('Pipe route functions', 'Select option', actions)
        if option == 1 :
            PipeRoutePointStart()
        elif option == 2 :
            PipeRoutePartStart()
        elif option == 3 :
            PipeRoutePartExtStart()
        elif option == 4 :
            PipeRouteSurfaceStart()
        elif option == 5 :
            PipeRouteSurfaceExtStart()
        elif option == 6 :
            PipeRoutePointAdd()
        elif option == 7 :
            PipeRoutePointEnd()
        elif option == 8 :
            PipeRoutePartEnd()
        elif option == 9 :
            PipeRoutePartExtEnd()
        elif option == 10 :
            PipeRouteSurfaceEnd()
        elif option == 11 :
            PipeRouteSurfaceExtEnd()

    try:
        kcs_draft.model_draw(GetCurrentPipeModel())
    except:
        print "Redraw error", kcs_draft.error

    else:
        print "User interrupted!"
except:
    print kcs_ui.error
