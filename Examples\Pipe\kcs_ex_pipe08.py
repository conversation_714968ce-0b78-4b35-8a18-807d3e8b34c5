#
#      NAME:
#
#          kcs_ex_pipe08.py
#
#      PURPOSE:
#
#          This program shows example usage of pipe part functions
#

import KcsStringlist
import kcs_ui
import kcs_util
import kcs_pipe
import kcs_draft
import KcsPoint2D
import KcsPoint3D
import KcsVector3D
import KcsStat_point3D_req
import KcsModel
import KcsPipeName
import KcsPipePartAddCriteria

#------------------------------------------------------------------------------------
#   Get current pipe model
#------------------------------------------------------------------------------------

def GetCurrentPipeModel():
        CurrentModel = KcsModel.Model()
        CurrentModel.SetType("pipe")	

	try:
	   ProjectName = kcs_util.TB_environment_get('SB_PROJ')
	   PipeName    = kcs_pipe.pipe_name_get()	
	   CurrentModel.SetName(ProjectName + '-' + str(PipeName))
	except:
	   print 'Error getting model: ', kcs_pipe.error
		
	return CurrentModel

#------------------------------------------------------------------------------------
#   Select pipe
#------------------------------------------------------------------------------------

def SelectPart():
        pt = KcsPoint2D.Point2D()
        kcs_ui.point2D_req("Indicate part", pt)
        ModelInfo = KcsModel.Model()
        try:
                kcs_draft.model_identify(pt, ModelInfo)
                if ModelInfo.Type in ["pipe", "pipe spool"]:
                    print "Selected part : ", ModelInfo.Name, ModelInfo.PartId
                    return ModelInfo
                else:
                    print "Selected model is not a pipe"
                    return None
        except:
            print kcs_draft.error

#------------------------------------------------------------------------------------
#  Select point 3D
#------------------------------------------------------------------------------------

def SelectPoint(InitialType = 3, Message = "Indicate point"):
        status = KcsStat_point3D_req.Stat_point3D_req()
        point = KcsPoint3D.Point3D()
        status.Initial3D = InitialType
        status.Initial2D = 6
        res = kcs_ui.point3D_req(Message, status, point)
        if res[0] == kcs_util.ok():
            return point
        else:
            print "User interrupted!"
            return None

#------------------------------------------------------------------------------------
#  Select vector
#------------------------------------------------------------------------------------

def SelectVector(InitialType = 3, Message = "Indicate point"):
        Point = SelectPoint(InitialType, Message)
        if isinstance(Point, KcsPoint3D.Point3D):
                return KcsVector3D.Vector3D(Point.X, Point.Y, Point.Z)

        return None

#------------------------------------------------------------------------------------
#   Select part connection
#------------------------------------------------------------------------------------

def SelectConnection(partId):
        point = SelectPoint(3, "Indicate connection")
        return kcs_pipe.part_conn_find(partId, point)

#------------------------------------------------------------------------------------
#   Add part
#------------------------------------------------------------------------------------

def PipePartAdd():
    try:
        print "Add part"
        Model = SelectPart()
        Conn  = SelectConnection(Model.PartId)

        criteria = KcsPipePartAddCriteria.PipePartAddCriteria()
        criteria.SetConnType( "part")
        criteria.SetComponent("88.9-10-1330")
        criteria.SetLength(200)

        PartNewId = kcs_pipe.part_add( Model.PartId, Conn, criteria)
        print PartNewId

    except:
        print kcs_pipe.error

#------------------------------------------------------------------------------------
#   Add part
#------------------------------------------------------------------------------------

def PipePartExtAdd():
    try:
        print "Add exteranl part"
        Model = SelectPart()
        Conn  = SelectConnection(Model.PartId)

        criteria = KcsPipePartAddCriteria.PipePartAddCriteria()
        criteria.SetConnType( "part")
        criteria.SetComponent("88.9-10-1330")
        criteria.SetLength(200)
        criteria.SetExternalPipe(Model.Name)

        PartNewId = kcs_pipe.part_add(Model.PartId, Conn, criteria)
        print PartNewId
    except:
        print kcs_pipe.error
#------------------------------------------------------------------------------------
#   Add angled part
#------------------------------------------------------------------------------------

def PipePartAngledAdd():
    try:
        print "Add angled part"
        Model     = SelectPart()
        Conn      = SelectConnection(Model.PartId)
        Direction = SelectVector(1, "Indicate direction")

        criteria = KcsPipePartAddCriteria.PipePartAddCriteria()
        criteria.SetConnType( "part")
        criteria.SetComponent("B26-6")
        criteria.SetDirection( Direction )

        PartNewId = kcs_pipe.part_add(Model.PartId, Conn, criteria)
        print PartNewId
    except:
        print kcs_pipe.error
#------------------------------------------------------------------------------------
#   Add external angled part
#------------------------------------------------------------------------------------

def PipePartAngledExtAdd():
    try:
        print "Add external angled part"
        Model     = SelectPart()
        Conn      = SelectConnection(Model.PartId)
        Direction = SelectVector(1, "Indicate direction")
        criteria = KcsPipePartAddCriteria.PipePartAddCriteria()
        criteria.SetConnType( "part")
        criteria.SetComponent("B26-6")
        criteria.SetDirection( Direction )
        criteria.SetExternalPipe(Model.Name)

        PartNewId = kcs_pipe.part_add(Model.PartId, Conn, criteria)
        print PartNewId
    except:
        print kcs_pipe.error

#------------------------------------------------------------------------------------
#   Add surface part
#------------------------------------------------------------------------------------

def PipePartSurfaceAdd():
    try:
        print "Add surface part"
        Model     = SelectPart()
        Point     = SelectPoint(1, "Indicate point on surface")
        Direction = SelectVector(1, "Indicate direction")

        criteria = KcsPipePartAddCriteria.PipePartAddCriteria()
        criteria.SetConnType( "surface")
        criteria.SetComponent("88.9-10-1330")
        criteria.SetDirection( Direction )
        criteria.SetSurfPoint( Point )
        criteria.SetLength( 200.0 )

        PartNewId = kcs_pipe.part_add(Model.PartId, criteria)

        print PartNewId
    except:
        print kcs_pipe.error

#------------------------------------------------------------------------------------
#   Add surface external part
#------------------------------------------------------------------------------------

def PipePartSurfaceExtAdd():
    try:
        print "Add surface external part"
        Model     = SelectPart()
        Point     = SelectPoint(1, "Indicate point on surface")
        Direction = SelectVector(1, "Indicate direction")

        criteria = KcsPipePartAddCriteria.PipePartAddCriteria()
        criteria.SetConnType( "surface")
        criteria.SetComponent("88.9-10-1330")
        criteria.SetDirection( Direction )
        criteria.SetSurfPoint( Point )
        criteria.SetLength( 200.0 )
        criteria.SetExternalPipe(Model.Name)

        PartNewId = kcs_pipe.part_add(Model.PartId, criteria)
        print PartNewId
    except:
        print kcs_pipe.error

#------------------------------------------------------------------------------------
#   Add surface part
#------------------------------------------------------------------------------------

def PipePartExccetricSurfaceAdd():
    try:
        print "Add surface eccentric part"
        Model       = SelectPart()
        Point       = SelectPoint(1, "Indicate point on surface")
        Direction   = SelectVector(1, "Indicate direction")
        Orientation = SelectVector(1, "Indicate orientation")

        criteria = KcsPipePartAddCriteria.PipePartAddCriteria()
        criteria.SetConnType( "surface")
        criteria.SetComponent( "K90-3")
        criteria.SetDirection( Direction )
        criteria.SetOrientation( Orientation )
        criteria.SetSurfPoint( Point )

        PartNewId = kcs_pipe.part_add(Model.PartId, criteria)

        print PartNewId
    except:
        print kcs_pipe.error

#------------------------------------------------------------------------------------
#   Add surface external part
#------------------------------------------------------------------------------------

def PipePartEccentricSurfaceExtAdd():
    try:
        print "Add surface external part"
        Model       = SelectPart()
        Point       = SelectPoint(1, "Indicate point on surface")
        Direction   = SelectVector(1, "Indicate direction")
        Orientation = SelectVector(1, "Indicate orientation")

        criteria = KcsPipePartAddCriteria.PipePartAddCriteria()
        criteria.SetConnType( "surface")
        criteria.SetComponent( "K90-3")
        criteria.SetDirection( Direction )
        criteria.SetOrientation( Orientation )
        criteria.SetSurfPoint( Point )
        criteria.SetExternalPipe(Model.Name)

        PartNewId = kcs_pipe.part_add(Model.PartId, criteria)
        print PartNewId
    except:
        print kcs_pipe.error


#------------------------------------------------------------------------------------
#   Add eccentric part
#------------------------------------------------------------------------------------

def PipePartEccentricAdd():
    try:
        print "Add eccentric part"
        Model       = SelectPart()
        Conn        = SelectConnection(Model.PartId)
        Orientation = SelectVector(1, "Indicate orietation")

        criteria = KcsPipePartAddCriteria.PipePartAddCriteria()
        criteria.SetConnType( "part")
        criteria.SetComponent("K90-3")
        criteria.SetOrientation( Orientation )

        PartNewId = kcs_pipe.part_add(Model.PartId, Conn, criteria)
        print PartNewId
    except:
        print kcs_pipe.error

#------------------------------------------------------------------------------------
#   Add eccentric external part
#------------------------------------------------------------------------------------

def PipePartEccentricExtAdd():
    try:
        print "Add eccentric external part"
        Model       = SelectPart()
        Conn        = SelectConnection(Model.PartId)
        Orientation = SelectVector(1, "Indicate orietation")

        criteria = KcsPipePartAddCriteria.PipePartAddCriteria()
        criteria.SetConnType( "part")
        criteria.SetComponent("K90-3")
        criteria.SetOrientation( Orientation )
        criteria.SetExternalPipe(Model.Name)

        PartNewId = kcs_pipe.part_add(Model.PartId, Conn, criteria)

        print PartNewId
    except:
        print kcs_pipe.error

#------------------------------------------------------------------------------------
#   Add angled part and cut angle
#------------------------------------------------------------------------------------

def PipePartAngledCutAdd():
    try:
        print "Add cut angled part"
        Model       = SelectPart()
        Conn        = SelectConnection(Model.PartId)
        Direction = SelectVector(1, "Indicate direction")


        criteria = KcsPipePartAddCriteria.PipePartAddCriteria()
        criteria.SetConnType( "part")
        criteria.SetComponent("B26-6")
        criteria.SetDirection( Direction )
        criteria.SetAngle(0.785398)

        PartNewId = kcs_pipe.part_add(Model.PartId, Conn, criteria)

        print PartNewId
    except:
        print kcs_pipe.error

#------------------------------------------------------------------------------------
#   Add part and cut building length
#------------------------------------------------------------------------------------

def PipePartCutBlenAdd():
    try:
        print "Add part and cut off building length"
        Model       = SelectPart()
        Conn        = SelectConnection(Model.PartId)

        criteria = KcsPipePartAddCriteria.PipePartAddCriteria()
        criteria.SetConnType( "part")
        criteria.SetComponent("F18-9")
        criteria.SetCutBlenType( 1 )

        PartNewId = kcs_pipe.part_add(Model.PartId, Conn, criteria)

        print PartNewId
    except:
        print kcs_pipe.error


#------------------------------------------------------------------------------------
#   Function selection
#------------------------------------------------------------------------------------

actions = KcsStringlist.Stringlist('Add part')
actions.AddString('Add external part')
actions.AddString('Add part angled')
actions.AddString('Add part external angled')
actions.AddString('Add surface part')
actions.AddString('Add surface external part')
actions.AddString('Add surface eccentric part')
actions.AddString('Add surface external eccentric')
actions.AddString('Add eccentric part')
actions.AddString('Add eccentric external part')
actions.AddString('Add angled part and cut angle')
actions.AddString('Add part and cut off building length')

try:
    (status, option) = kcs_ui.choice_select('Pipe part functions', 'Select option', actions)
    if status == kcs_util.ok() :
        if option == 1 :
            PipePartAdd()
        elif option == 2 :
            PipePartExtAdd()
        elif option == 3 :
            PipePartAngledAdd()
        elif option == 4 :
            PipePartAngledExtAdd()
        elif option == 5 :
            PipePartSurfaceAdd()
        elif option == 6 :
            PipePartSurfaceExtAdd()
        elif option == 7 :
            PipePartExccetricSurfaceAdd()
        elif option == 8 :
            PipePartEccentricSurfaceExtAdd()
        elif option == 9 :
            PipePartEccentricAdd()
        elif option == 10 :
            PipePartEccentricExtAdd()
        elif option == 11 :
            PipePartAngledCutAdd()
        elif option == 12 :
            PipePartCutBlenAdd()
        try:
            kcs_draft.model_draw(GetCurrentPipeModel())
        except:
            print "Redraw error", kcs_draft.error

    else:
        print "User interrupted!"
except:
    print kcs_ui.error
