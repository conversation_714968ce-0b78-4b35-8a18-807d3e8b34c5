#
#      NAME:
#
#          kcs_ex_pipe_09.py
#
#      PURPOSE:
#
#          This program is example usage of pipe production functions
#

import KcsStringlist
import kcs_ui
import kcs_util
import kcs_pipe
import KcsPoint3D
import KcsStat_point3D_req
import kcs_draft
import KcsModel
import KcsPoint2D
import KcsPipeProp
import KcsPipeSpoolProp
import KcsPipePartProp

#------------------------------------------------------------------------------------
#   Select pipe part
#------------------------------------------------------------------------------------

def SelectPart():
        pt = KcsPoint2D.Point2D()
        kcs_ui.point2D_req("Indicate part", pt)
        ModelInfo = KcsModel.Model()
        try:
                kcs_draft.model_identify(pt, ModelInfo)
                if ModelInfo.Type in ["pipe", "pipe spool"]:
                    print ModelInfo.Name, ModelInfo.PartId
                    return ModelInfo.PartId
                else:
                    print "Selected model is not a pipe"
                    return None
        except:
            print kcs_draft.error


#------------------------------------------------------------------------------------
#   Set pipe production data
#------------------------------------------------------------------------------------

def PipeProductionDataSet():
    try:
        print "Set pipe production information"

        prop = KcsPipeProp.PipeProp()
        prop.SetJointCode(2)
        prop.SetWeldCode(3)
        prop.SetHeatCode(4)
        prop.SetSurfaceTreatmentCode(5)
        prop.SetTestPressure(6.7)
        prop.SetSketchNote("NOTE")
        prop.SetPlanningUnit("PU1")
        prop.SetBendRadius(2.7)
        prop.SetColor("Green")

        kcs_pipe.pipe_properties_set(prop)

    except:
        print kcs_pipe.error

#------------------------------------------------------------------------------------
#   Set spool production data
#------------------------------------------------------------------------------------

def SpoolProductionDataSet():
    try:
        print "Set spool production information"
        PartId = SelectPart()

        prop = KcsPipeSpoolProp.PipeSpoolProp()
        prop.SetPosNo("POSNO")
        prop.SetHeatCode(1)
        prop.SetSurfaceTreatmentCode(2)
        prop.SetTestPressure(6.7)
        prop.SetSketchNote("NOTE")
        prop.SetSketchType("TEMP")
        prop.SetPlanningUnit("PU1")
        prop.SetBMNote("BM NOTE")

        kcs_pipe.spool_properties_set( PartId, prop )

    except:
        print kcs_pipe.error

#------------------------------------------------------------------------------------
#   Set part production data
#------------------------------------------------------------------------------------

def PartProductionDataSet():
    try:
        print "Set part production information"
        PartId = SelectPart()

        prop = KcsPipePartProp.PipePartProp()
        prop.SetMaterialNote("MN")
        prop.SetAcquisitionCode(1)
        prop.SetJointCode(2)
        prop.SetWeldCode(3)
        prop.SetPlacingCode(0)
        prop.SetLooseCode(0)
        prop.SetInsulation("Component")

        kcs_pipe.part_properties_set(PartId, prop)

        print "Set spool limit"
        Conn    = 1
        kcs_pipe.part_spool_limit_set(PartId, Conn)
        print "Set feed excess"
        kcs_pipe.part_feed_excess_set(PartId)
        print "Set part end excess"
        kcs_pipe.part_end_excess_set(PartId, Conn, 10.0)
        print "Set part feed min"
        kcs_pipe.part_feed_min_set(PartId, 10.0)

    except:
        print kcs_pipe.error

#------------------------------------------------------------------------------------
#   Function selection
#------------------------------------------------------------------------------------

actions = KcsStringlist.Stringlist('Set pipe production data')
actions.AddString('Set spool production data')
actions.AddString('Set part production data')
try:
    (status, option) = kcs_ui.choice_select('Pipe functions', 'Select option', actions)
    if status == kcs_util.ok() :
        if option == 1 :
            PipeProductionDataSet()
        elif option == 2 :
            SpoolProductionDataSet()
        elif option == 3 :
            PartProductionDataSet()
    else:
        print "User interrupted!"
except:
    print kcs_ui.error
