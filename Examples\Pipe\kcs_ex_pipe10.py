#
#      NAME:
#
#          kcs_ex_pipe10.py
#
#      PURPOSE:
#
#          This program shows example usage of pipe  joint functions
#

import KcsStringlist
import kcs_ui
import kcs_util
import kcs_pipe
import kcs_draft
import KcsPoint2D
import KcsPoint3D
import KcsVector3D
import KcsStat_point3D_req
import KcsModel
import Kcs<PERSON>ipeJointAddCriteria
import KcsPipeName

#------------------------------------------------------------------------------------
#   Get current pipe model
#------------------------------------------------------------------------------------

def GetCurrentPipeModel():

        CurrentModel = KcsModel.Model()
        CurrentModel.SetType("pipe")	

        try:
           ProjectName = kcs_util.TB_environment_get('SB_PROJ')
           PipeName    = kcs_pipe.pipe_name_get()	
           CurrentModel.SetName(ProjectName + '-' + str(PipeName))
        except:
           print 'Error getting model: ', kcs_pipe.error
		
        return CurrentModel


#------------------------------------------------------------------------------------
#   Select pipe part
#------------------------------------------------------------------------------------

def SelectPart():
        pt = KcsPoint2D.Point2D()
        kcs_ui.point2D_req("Indicate part", pt)
        ModelInfo = KcsModel.Model()
        try:
                kcs_draft.model_identify(pt, ModelInfo)
                if ModelInfo.Type in ["pipe", "pipe spool"]:
                    print "Selected part : ", ModelInfo.Name, ModelInfo.PartId
                    return ModelInfo
                else:
                    print "Selected model is not a pipe"
                    return None
        except:
            print kcs_draft.error

#------------------------------------------------------------------------------------
#  Select point 3D
#------------------------------------------------------------------------------------

def SelectPoint(InitialType = 3, Message = "Indicate point"):
        status = KcsStat_point3D_req.Stat_point3D_req()
        point = KcsPoint3D.Point3D()
        status.Initial3D = InitialType
        status.Initial2D = 6
        res = kcs_ui.point3D_req(Message, status, point)
        if res[0] == kcs_util.ok():
            return point
        else:
            print "User interrupted!"
            return None

#------------------------------------------------------------------------------------
#   Select part connection
#------------------------------------------------------------------------------------

def SelectConnection(partId):
        point = SelectPoint(3, "Indicate connection")
        return kcs_pipe.part_conn_find(partId, point)

#------------------------------------------------------------------------------------
#  Select joint type
#------------------------------------------------------------------------------------

def SelectJointType():
        actions = KcsStringlist.Stringlist("")
        actions.StrList = []

        for joint in KcsPipeJointAddCriteria.PipeJointAddCriteria.JointTypes:
               actions.AddString( joint )

        (status, option) = kcs_ui.choice_select('Add joint', 'Select type', actions)
        if status == kcs_util.ok() :
                return KcsPipeJointAddCriteria.PipeJointAddCriteria.JointTypes[option-1]
        else:
                return None



#------------------------------------------------------------------------------------
#   Add part joint
#------------------------------------------------------------------------------------

def PipePartJointAdd():
    try:
        print "Add joint"

        Model     = SelectPart()
        Conn      = SelectConnection(Model.PartId)
        Joint     = SelectJointType()
        Direction = None

        if Joint == "mitre" :
                pt = SelectPoint(1, "Indicate direction")
                Direction = KcsVector3D.Vector3D(pt.X, pt.Y, pt.Z)
        print Direction
        Criteria  = KcsPipeJointAddCriteria.PipeJointAddCriteria()
        Criteria.SetJointType( Joint )
        Criteria.SetDirection( Direction )

        PartNewId = kcs_pipe.joint_add(Model.PartId, Conn, Criteria)
        print PartNewId
    except:
        print kcs_pipe.error

#------------------------------------------------------------------------------------
#   Add part external joint
#------------------------------------------------------------------------------------

def PipePartJointExtAdd():
    try:
        print "Add external joint"
        Model     = SelectPart()
        Conn      = SelectConnection(Model.PartId)
        Joint     = SelectJointType()
        Direction = None

        if Joint == "mitre" :
                pt = SelectPoint(1, "Indicate direction")
                Direction = KcsVector3D.Vector3D(pt.X, pt.Y, pt.Z)

        Criteria  = KcsPipeJointAddCriteria.PipeJointAddCriteria()
        Criteria.SetJointType( Joint )
        Criteria.SetDirection( Direction )
        Criteria.SetExternalPipe( Model.Name )

        PartNewId = kcs_pipe.joint_add(Model.PartId, Conn, Criteria)
        print PartNewId

    except:
        print kcs_pipe.error


#------------------------------------------------------------------------------------
#   Insert joint
#------------------------------------------------------------------------------------

def PipeJointInsert():
    try:
        print "Insert joint"
        Model     = SelectPart()
        Conn      = SelectConnection(Model.PartId)
        Joint     = SelectJointType()

        Criteria  = KcsPipeJointAddCriteria.PipeJointAddCriteria()
        Criteria.SetJointType( Joint )
        Criteria.SetDistance( 200 )

        newPartId = kcs_pipe.joint_insert(Model.PartId, Conn, Criteria)
        print "New part Id", newPartId

    except:
        print kcs_pipe.error


#------------------------------------------------------------------------------------
#   Function selection
#------------------------------------------------------------------------------------

actions = KcsStringlist.Stringlist('Add joint')
actions.AddString('Add external joint')
actions.AddString('Insert joint')

try:
    (status, option) = kcs_ui.choice_select('Pipe part functions', 'Select option', actions)
    if status == kcs_util.ok() :
        if option == 1 :
            PipePartJointAdd()
        elif option == 2 :
            PipePartJointExtAdd()
        elif option == 3 :
            PipeJointInsert()

        try:
            kcs_draft.model_draw(GetCurrentPipeModel())
        except:
            print "Redraw error", kcs_draft.error


    else:
        print "User interrupted!"
except:
    print kcs_ui.error
