#
#      NAME:
#
#          kcs_ex_pipe11py
#
#      PURPOSE:
#
#          This program shows example usage of pipe default value functions
#               pipe default value functions
#               pipe mode / ventilation mode
#

import KcsStringlist
import kcs_ui
import kcs_util
import kcs_pipe

#------------------------------------------------------------------------------------
#   Pipe Default Value
#------------------------------------------------------------------------------------

def PipeDefaultValue():
    try:
        print "Pipe default value"
        value = kcs_pipe.default_value_get("DEFAULT_FLANGE_NAME")
        print "DEFAULT_FLANGE_NAME = ", value

        print "Set new value"
        kcs_pipe.default_value_set("DEFAULT_FLANGE_NAME=MY_NAME")

        value = kcs_pipe.default_value_get("DEFAULT_FLANGE_NAME")
        print "DEFAULT_FLANGE_NAME = ", value

    except:
        print kcs_pipe.error

#------------------------------------------------------------------------------------
#   Pipe mode set
#------------------------------------------------------------------------------------

def PipeModeSet():
    try:
        print "Set pipe mode"
        kcs_pipe.pipe_mode_activate()
    except:
        print kcs_pipe.error

#------------------------------------------------------------------------------------
#   Vent mode set
#------------------------------------------------------------------------------------

def VentModeSet():
    try:
        print "Set vent mode"
        kcs_pipe.vent_mode_activate()
    except:
        print kcs_pipe.error


#------------------------------------------------------------------------------------
#   Function selection
#------------------------------------------------------------------------------------

actions = KcsStringlist.Stringlist('Default value')
actions.AddString('Set pipe mode')
actions.AddString('Set ventilation mode')

try:
    (status, option) = kcs_ui.choice_select('Pipe part functions', 'Select option', actions)
    if status == kcs_util.ok() :
        if option == 1 :
            PipeDefaultValue()
        if option == 2 :
            PipeModeSet()
        if option == 3 :
            VentModeSet()

    else:
        print "User interrupted!"
except:
    print kcs_ui.error
