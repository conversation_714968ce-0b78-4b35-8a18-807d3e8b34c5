#
#      NAME:
#
#          kcs_ex_pipe12py
#
#      PURPOSE:
#
#          This program shows example usage of pipe structure functions
#

import KcsStringlist
import kcs_ui
import kcs_util
import kcs_pipe
import kcs_draft
import KcsModel
import KcsPoint2D

#------------------------------------------------------------------------------------
#   Select Model
#------------------------------------------------------------------------------------

def SelectModel( Message = "Select Model"):
        pt = KcsPoint2D.Point2D()
        kcs_ui.point2D_req(Message, pt)
        ModelInfo = KcsModel.Model()
        try:
            kcs_draft.model_identify(pt, ModelInfo)
            print "Selected model : ", ModelInfo.Name
            return ModelInfo
        except:
            print kcs_draft.error


#------------------------------------------------------------------------------------
#   Connect structure to pipe part
#------------------------------------------------------------------------------------

def StructureConnect():
    try:
        print "Connect structure"
        ModelPipe   = SelectModel("Select pipe part")

        if (ModelPipe.Type == "pipe"):
            ModelStruct = SelectModel("Select structure")
            if ( ModelStruct.Type == "struct"):
                kcs_pipe.part_structure_connect( ModelPipe.PartId, ModelStruct.Name, "Alias" );
            else:
                print "Selected model is not structure"
        else:
            print "Selected model is not pipe"

    except:
        print kcs_pipe.error

#------------------------------------------------------------------------------------
#   Disconnect structure from pipe part
#------------------------------------------------------------------------------------

def StructureDisconnect():
    try:
        print "Disconnect structure"
        ModelPipe   = SelectModel("Select pipe part")

        if (ModelPipe.Type == "pipe"):
            ModelStruct = SelectModel("Select structure")
            if ( ModelStruct.Type == "struct"):
                kcs_pipe.part_structure_disconnect( ModelPipe.PartId, ModelStruct.Name);
            else:
                print "Selected model is not structure"
        else:
            print "Selected model is not pipe"

    except:
        print kcs_pipe.error

#------------------------------------------------------------------------------------
#   Get structures connected to pipe part
#------------------------------------------------------------------------------------

def StructureGet():
    try:
        print "Get structures"
        ModelPipe   = SelectModel("Select pipe part")

        if (ModelPipe.Type == "pipe"):
            result = kcs_pipe.part_structure_get( ModelPipe.PartId);
            print result;

        else:
            print "Selected model is not pipe"

    except:
        print kcs_pipe.error

#------------------------------------------------------------------------------------
#   Function selection
#------------------------------------------------------------------------------------

actions = KcsStringlist.Stringlist('Connect structure')
actions.AddString('Disconnect structure')
actions.AddString('Get structures')

try:
    (status, option) = kcs_ui.choice_select('Pipe part functions', 'Select option', actions)
    if status == kcs_util.ok() :
        if option == 1 :
            StructureConnect()
        if option == 2 :
            StructureDisconnect()
        if option == 3 :
            StructureGet()

    else:
        print "User interrupted!"
except:
    print kcs_ui.error
