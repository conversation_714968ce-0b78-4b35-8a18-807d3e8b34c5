#
#      NAME:
#
#          kcs_ex_pipe13.py
#
#      PURPOSE:
#
#          This program shows example usage of pipe weldgap functions
#

import KcsStringlist
import kcs_ui
import kcs_util
import kcs_pipe
import kcs_draft
import KcsPoint2D
import KcsPoint3D
import KcsVector3D
import KcsStat_point3D_req
import KcsModel

#------------------------------------------------------------------------------------
#   Get current pipe model
#------------------------------------------------------------------------------------

def GetCurrentPipeModel():

        CurrentModel = KcsModel.Model()
        CurrentModel.SetType("pipe")	

        try:
           ProjectName = kcs_util.TB_environment_get('SB_PROJ')
           PipeName    = kcs_pipe.pipe_name_get()	
           CurrentModel.SetName(ProjectName + '-' + str(PipeName))
        except:
           print 'Error getting model: ', kcs_pipe.error
		
        return CurrentModel

#------------------------------------------------------------------------------------
#   Select pipe part
#------------------------------------------------------------------------------------

def SelectPart():
        pt = KcsPoint2D.Point2D()
        kcs_ui.point2D_req("Indicate part", pt)
        ModelInfo = KcsModel.Model()
        try:
                kcs_draft.model_identify(pt, ModelInfo)
                if ModelInfo.Type in ["pipe", "pipe spool"]:
                    print "Selected part : ", ModelInfo.Name, ModelInfo.PartId
                    return ModelInfo
                else:
                    print "Selected model is not a pipe"
                    return None
        except:
            print kcs_draft.error

#------------------------------------------------------------------------------------
#  Select point 3D
#------------------------------------------------------------------------------------

def SelectPoint(InitialType = 3, Message = "Indicate point"):
        status = KcsStat_point3D_req.Stat_point3D_req()
        point = KcsPoint3D.Point3D()
        status.Initial3D = InitialType
        status.Initial2D = 6
        res = kcs_ui.point3D_req(Message, status, point)
        if res[0] == kcs_util.ok():
            return point
        else:
            print "User interrupted!"
            return None

#------------------------------------------------------------------------------------
#   Select part connection
#------------------------------------------------------------------------------------

def SelectConnection(partId):
        point = SelectPoint(3, "Indicate connection")
        return kcs_pipe.part_conn_find(partId, point)

#------------------------------------------------------------------------------------
#   Add part weld gap
#------------------------------------------------------------------------------------

def PartWeldGapSet():
    try:
        print "Add part weldgap"
        Model     = SelectPart()
        Conn      = SelectConnection(Model.PartId)
        Length    = 10

        kcs_pipe.part_weldgap_set(Model.PartId, Conn, Length)

    except:
        print kcs_pipe.error


#------------------------------------------------------------------------------------
#   Edit part weld gap
#------------------------------------------------------------------------------------

def PartWeldGapEdit():
    try:
        print "Edit part weldgap"
        Model     = SelectPart()
        Conn      = SelectConnection(Model.PartId)
        Length    = 15

        PartId = kcs_pipe.part_conn_part_get(Model.PartId, Conn)
        kcs_pipe.part_weldgap_edit(PartId, Length)


    except:
        print kcs_pipe.error


#------------------------------------------------------------------------------------
#   Delete part weld gap
#------------------------------------------------------------------------------------

def PartWeldGapDelete():
    try:
        print "Delete part weldgap"
        Model     = SelectPart()
        Conn      = SelectConnection(Model.PartId)

        PartId = kcs_pipe.part_conn_part_get(Model.PartId, Conn)
        kcs_pipe.part_weldgap_delete(PartId)


    except:
        print kcs_pipe.error


#------------------------------------------------------------------------------------
#   Add spool weld gap
#------------------------------------------------------------------------------------

def SpoolWeldGapSet():
    try:
        print "Add spool weldgaps"
        Model     = SelectPart()
        Length    = 10

        kcs_pipe.spool_weldgap_set(Model.PartId, Length)

    except:
        print kcs_pipe.error


#------------------------------------------------------------------------------------
#   Edit spool weld gap
#------------------------------------------------------------------------------------

def SpoolWeldGapEdit():
    try:
        print "Edit spool weldgap"
        Model     = SelectPart()
        Length    = 15

        kcs_pipe.spool_weldgap_edit(Model.PartId, Length)

    except:
        print kcs_pipe.error

#------------------------------------------------------------------------------------
#   Delete spool weld gap
#------------------------------------------------------------------------------------

def SpoolWeldGapDelete():
    try:
        print "Delete spool weldgap"
        Model     = SelectPart()

        kcs_pipe.spool_weldgap_delete(Model.PartId)

    except:
        print kcs_pipe.error


#------------------------------------------------------------------------------------
#   Add pipe weld gap
#------------------------------------------------------------------------------------

def PipeWeldGapSet():
    try:
        print "Add pipe weldgaps"

        Length = 10
        kcs_pipe.pipe_weldgap_set( Length)

    except:
        print kcs_pipe.error

#------------------------------------------------------------------------------------
#   Function selection
#------------------------------------------------------------------------------------

actions = KcsStringlist.Stringlist('Set pipe weldgaps')
actions.AddString('Set spool weldgaps')
actions.AddString('Edit spool weldgaps')
actions.AddString('Delete spool weldgaps')
actions.AddString('Set part weldgaps')
actions.AddString('Edit part weldgaps')
actions.AddString('Delete part weldgaps')

try:
    (status, option) = kcs_ui.choice_select('Pipe part functions', 'Select option', actions)
    if status == kcs_util.ok() :
        if option == 1 :
            PipeWeldGapSet()
        elif option == 2 :
            SpoolWeldGapSet()
        elif option == 3 :
            SpoolWeldGapEdit()
        elif option == 4 :
            SpoolWeldGapDelete()
        elif option == 5 :
            PartWeldGapSet()
        elif option == 6 :
            PartWeldGapEdit()
        elif option == 7 :
            PartWeldGapDelete()

        try:
            kcs_draft.model_draw(GetCurrentPipeModel())
        except:
            print "Redraw error", kcs_draft.error

    else:
        print "User interrupted!"
except:
    print kcs_ui.error
