#
#      NAME:
#
#          kcs_ex_pipe14.py
#
#      PURPOSE:
#
#          This program shows example usage of pipe functions
#

import KcsStringlist
import kcs_ui
import kcs_util
import kcs_pipe
import KcsPoint2D
import KcsModel
import KcsPipeName
import kcs_draft
import sys
#------------------------------------------------------------------------------------
#   Select pipe part
#------------------------------------------------------------------------------------

def SelectPart():
        pt = KcsPoint2D.Point2D()
        kcs_ui.point2D_req("Indicate pipe", pt)
        ModelInfo = KcsModel.Model()
        try:
                kcs_draft.model_identify(pt, ModelInfo)
                if ModelInfo.Type in ["pipe", "pipe spool"]:
                    print "Selected part : ", ModelInfo.Name, ModelInfo.PartId
                    return ModelInfo
                else:
                    print "Selected model is not a pipe"
                    return None
        except:
            print kcs_draft.error

#------------------------------------------------------------------------------------
#   List pipe to file
#------------------------------------------------------------------------------------

def PipeList():
    try:
        print "List pipe"
        kcs_pipe.pipe_list()
    except:
        print kcs_pipe.error

#------------------------------------------------------------------------------------
#   Regenerate
#------------------------------------------------------------------------------------

def PipeRegenerate():
    try:
        print "Regenerate pipe"
        kcs_pipe.pipe_regenerate()
    except:
        print kcs_pipe.error

#------------------------------------------------------------------------------------
#   Split pipe
#------------------------------------------------------------------------------------

def PipeSplit():
    try:
        print "Split pipe"
        Model = SelectPart()
        PipeName = KcsPipeName.PipeName(Model.Name)
        PipeName = KcsPipeName.PipeName( PipeName.SplitName() )

        kcs_pipe.pipe_split(PipeName)
    except:
        print sys.exc_info()
        print kcs_pipe.error

#------------------------------------------------------------------------------------
#   Function selection
#------------------------------------------------------------------------------------

actions = KcsStringlist.Stringlist('List pipe')
actions.AddString('Regenerate pipe')
actions.AddString('Split pipe')

try:
    (status, option) = kcs_ui.choice_select('Pipe part functions', 'Select option', actions)
    if status == kcs_util.ok() :
        if option == 1 :
            PipeList()
        elif option == 2 :
            PipeRegenerate()
        elif option == 3 :
            PipeSplit()

    else:
        print "User interrupted!"
except:
    print kcs_ui.error
