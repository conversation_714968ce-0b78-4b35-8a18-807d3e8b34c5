#
#      NAME:
#
#          kcs_ex_pipe16.py
#
#      PURPOSE:
#
#          This program shows example usage of pipe part functions
#

import KcsStringlist
import kcs_ui
import kcs_util
import kcs_pipe
import kcs_draft
import KcsPoint2D
import KcsPoint3D
import KcsVector3D
import KcsStat_point3D_req
import KcsModel
import KcsPipeName
import KcsPipePartAddCriteria
import KcsPipeJointAddCriteria

#------------------------------------------------------------------------------------
#   Get current pipe model
#------------------------------------------------------------------------------------

def GetCurrentPipeModel():

        CurrentModel = KcsModel.Model()
        CurrentModel.SetType("pipe")	

        try:
           ProjectName = kcs_util.TB_environment_get('SB_PROJ')
           PipeName    = kcs_pipe.pipe_name_get()	
           CurrentModel.SetName(ProjectName + '-' + str(PipeName))
        except:
           print 'Error getting model: ', kcs_pipe.error
		
        return CurrentModel

#------------------------------------------------------------------------------------
#   Select pipe
#------------------------------------------------------------------------------------

def SelectPart():
        pt = KcsPoint2D.Point2D()
        kcs_ui.point2D_req("Indicate part", pt)
        ModelInfo = KcsModel.Model()
        try:
                kcs_draft.model_identify(pt, ModelInfo)
                if ModelInfo.Type in ["pipe", "pipe spool"]:
                    print "Selected part : ", ModelInfo.Name, ModelInfo.PartId
                    return ModelInfo
                else:
                    print "Selected model is not a pipe"
                    return None
        except:
            print kcs_draft.error

#------------------------------------------------------------------------------------
#  Select point 3D
#------------------------------------------------------------------------------------

def SelectPoint(InitialType = 3, Message = "Indicate point"):
        status = KcsStat_point3D_req.Stat_point3D_req()
        point = KcsPoint3D.Point3D()
        status.Initial3D = InitialType
        status.Initial2D = 6
        res = kcs_ui.point3D_req(Message, status, point)
        if res[0] == kcs_util.ok():
            return point
        else:
            print "User interrupted!"
            return None

#------------------------------------------------------------------------------------
#   Select part connection
#------------------------------------------------------------------------------------

def SelectConnection(partId):
        point = SelectPoint(3, "Indicate connection")
        return kcs_pipe.part_conn_find(partId, point)

#------------------------------------------------------------------------------------
#  Select boss connection type
#------------------------------------------------------------------------------------

def SelectBossType():
        actions = KcsStringlist.Stringlist('on surface')
        actions.AddString('insert')
        actions.AddString('extrude')
        actions.AddString('saddle')
        actions.AddString('none')

        (status, option) = kcs_ui.choice_select('Boss connection', 'Select type', actions)
        if status == kcs_util.ok() :
                return option
        else:
                return None

#------------------------------------------------------------------------------------
#   Set boss connection
#------------------------------------------------------------------------------------

def BossConnectionSet():
    try:
        print "Set boss connection"
        Model  = SelectPart()
        Conn   = SelectConnection( Model.PartId )
        Code   = SelectBossType()

        kcs_pipe.part_boss_conn_type_set( Model.PartId, Conn, Code)
    except:
        print kcs_pipe.error


#------------------------------------------------------------------------------------
#   Insert part
#------------------------------------------------------------------------------------

def PipePartInsert():
    try:
        print "Insert part"
        Model     = SelectPart()
        Conn      = SelectConnection(Model.PartId)

        criteria = KcsPipePartAddCriteria.PipePartAddCriteria()
        criteria.SetComponent("H7-4")
        criteria.SetDistance(200)

        newPartId = kcs_pipe.part_insert(Model.PartId, Conn, criteria)
        print "New part Id", newPartId

    except:
        print kcs_pipe.error


#------------------------------------------------------------------------------------
#   Disconnect part
#------------------------------------------------------------------------------------

def PipePartDisconnect():
    try:
        print "Disconnect part"
        Model     = SelectPart()
        Conn      = SelectConnection(Model.PartId)

        kcs_pipe.part_disconnect(Model.PartId, Conn)
    except:
        print kcs_pipe.error


#------------------------------------------------------------------------------------
#   Connect part
#------------------------------------------------------------------------------------

def PipePartConnect():
    try:
        print "Connect parts"
        Model1     = SelectPart()
        Conn1      = SelectConnection(Model1.PartId)
        Model2     = SelectPart()
        Conn2      = SelectConnection(Model2.PartId)
        ExtName    = KcsPipeName.PipeName( Model2.Name )

        kcs_pipe.part_connect(Model1.PartId, Conn1, ExtName, Model2.PartId, Conn2)
    except:
        print kcs_pipe.error


#------------------------------------------------------------------------------------
#   Flip pipe part
#------------------------------------------------------------------------------------

def PipePartFlip():
    try:
        print "Flip part"
        ModelInfo = SelectPart()

        kcs_pipe.part_flip(ModelInfo.PartId)
    except:
        print kcs_pipe.error


#------------------------------------------------------------------------------------
#   Rotate pipe part
#------------------------------------------------------------------------------------

def PipePartRotate():
    try:
        print "Rotate part"
        ModelInfo = SelectPart()
        Conn      = SelectConnection(ModelInfo.PartId)

        kcs_pipe.part_rotate(ModelInfo.PartId, Conn, 30.0)

    except:
        print kcs_pipe.error


#------------------------------------------------------------------------------------
#   Turn pipe part
#------------------------------------------------------------------------------------

def PipePartTurn():
    try:
        print "Turn part"
        ModelInfo = SelectPart()
        Conn      = SelectConnection(ModelInfo.PartId)

        kcs_pipe.part_turn(ModelInfo.PartId, Conn, 30.0)

    except:
        print kcs_pipe.error


#------------------------------------------------------------------------------------
#   Delete pipe part
#------------------------------------------------------------------------------------

def DeletePart():
    try:
        print "Delete part"
        ModelInfo = SelectPart()
        kcs_pipe.part_delete(ModelInfo.PartId)
        print "Part deleted"

    except:
        print kcs_pipe.error


#------------------------------------------------------------------------------------
#   Function selection
#------------------------------------------------------------------------------------

actions = KcsStringlist.Stringlist('Insert part')
actions.AddString('Connect part')
actions.AddString('Disconnect part')
actions.AddString('Set boss connection')
actions.AddString('Flip part')
actions.AddString('Rotate part')
actions.AddString('Turn part')
actions.AddString('Delete part')

try:
    (status, option) = kcs_ui.choice_select('Pipe part functions', 'Select option', actions)
    if status == kcs_util.ok() :
        if option == 1 :
            PipePartInsert()
        elif option == 2 :
            PipePartConnect()
        elif option == 3 :
            PipePartDisconnect()
        elif option == 4 :
            BossConnectionSet()
        elif option == 5 :
            PipePartFlip()
        elif option == 6 :
            PipePartRotate()
        elif option == 7 :
            PipePartTurn()
        elif option == 8 :
            DeletePart()

    else:
        print "User interrupted!"

    try:
        kcs_draft.model_draw(GetCurrentPipeModel())
    except:
        print "Redraw error", kcs_draft.error

except:
    print kcs_ui.error
