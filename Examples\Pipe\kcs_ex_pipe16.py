#----------------------------------------------------------------------------------
#
#      NAME:
#
#          kcs_ex_pip16.py
#
#      PURPOSE:
#
#               This program presents how to use document reference handling for pipe objects
#
#
#
#----------------------------------------------------------------------------------
import string
import kcs_ui
import kcs_util
import kcs_draft
import kcs_db
import kcs_pipe
import KcsStringlist
import KcsPipeName
import CommonSample
import KcsDocumentReference
import KcsObjectCriteria

#----------------------------------------------------------------------------------
# Allow user to select drawing name stored in SB_PDB
#----------------------------------------------------------------------------------
def GetDrawingName():
   list = []
   crit = KcsObjectCriteria.ObjectCriteria()
   try:
      list = []
      kcs_db.object_list_get(crit, 'SB_PDB', list)
   except:
      CommonSample.ReportTribonError(kcs_db)
      return None

   if len(list) == 0:
      kcs_ui.message_confirm('There are no drawings in SB_PDB databank!')
      return None

   strlist = []
   for item in list:
      strlist.append(item.GetName())

   (status, index) = kcs_ui.string_select('Drawings', 'Select drawing:', '', strlist)
   if status == kcs_util.ok():
      return strlist[index-1]
   else:
      return None


#----------------------------------------------------------------------------------
# Allows user to edit and add document reference to pipe given by name
#----------------------------------------------------------------------------------
def AddPipeDocumentReference(pipename):
   try:
      reference = KcsDocumentReference.DocumentReference()

      if EditPipeDocumentReference(reference):

         kcs_pipe.pipe_activate(KcsPipeName.PipeName(pipename))

         try:
            kcs_pipe.document_reference_add(reference)
         except:
            CommonSample.ReportTribonError(kcs_pipe)

         kcs_pipe.pipe_save()

   except:
      CommonSample.ReportTribonError(kcs_pipe)

#----------------------------------------------------------------------------------
#  That function shows memebers of document reference and allow user to edit them
#----------------------------------------------------------------------------------
def EditPipeDocumentReference(reference):
   try:
      while 1:
         actions = (
            'Type: ' + reference.GetType(),
            'Document: ' + reference.GetDocument(),
            'Description: ' + reference.GetDescription(),
            'Return'
            )
         (status, index) = kcs_ui.choice_select('Document reference','Define members', actions)
         if status == kcs_util.ok():
            if index == 1:
               actions = KcsDocumentReference.DocumentReference.ReferenceTypes.keys()
               (status, index) = kcs_ui.choice_select('Reference type', 'Choose type', actions)
               if status == kcs_util.ok():
                  reference.SetType(actions[index-1])
            elif index == 2:
               if reference.GetType() == 'drawing':
                  strRef = GetDrawingName()
                  if strRef != None:
                     reference.SetDocument(strRef)
               else:
                  resp, strRef = kcs_ui.string_req('Document:', reference.GetDocument())
                  if resp == kcs_util.ok():
                     reference.SetDocument(strRef)
            elif index == 3:
               resp, strDesc = kcs_ui.string_req('Description:', reference.GetDescription())
               if resp == kcs_util.ok():
                  reference.SetDescription(strDesc)
            else:
               return 1
         else:
            return 0
   except:
      print sys.exc_info()[1]

#----------------------------------------------------------------------------------
#  Displays all document references attached to pipe given by name
#  and allow user to select one
#----------------------------------------------------------------------------------
def GetPipeDocumentReference(pipename, prompt1=''):
   list = []
   try:
      kcs_pipe.pipe_activate(KcsPipeName.PipeName(pipename))
      try:
         list = kcs_pipe.document_reference_get()
      except:
         CommonSample.ReportTribonError(kcs_pipe)

      kcs_pipe.pipe_cancel()

   except:
      CommonSample.ReportTribonError(kcs_pipe)

   strlist = []
   for item in list:
      itemstr = str(item)
      strlist = strlist + string.split(itemstr, '\n')
      strlist.append('')

   nItems = len(strlist)
   if nItems == 0:
      strlist = ['no document references']
   resp, index = kcs_ui.string_select('Document references', prompt1, '', strlist)
   if resp==kcs_util.ok() and nItems:
      linesPerItem = len(string.split(str(KcsDocumentReference.DocumentReference()), '\n')) + 1
      return list[(index-1) / linesPerItem]
   else:
      return None

#----------------------------------------------------------------------------------
#  Removes given document reference from pipe given by name
#----------------------------------------------------------------------------------
def RemovePipeDocumentReference(pipename, reference):
   try:
      kcs_pipe.pipe_activate(KcsPipeName.PipeName(pipename))
      try:
         kcs_pipe.document_reference_remove(reference)
      except:
         CommonSample.ReportTribonError(kcs_pipe)

      kcs_pipe.pipe_save()

   except:
      CommonSample.ReportTribonError(kcs_pipe)


#----------------------------------------------------------------------------------
#  Displays all document references of 'drawing' type and opens selected drawing
#----------------------------------------------------------------------------------
def OpenReferencedDrawing(pipename):
   list = []
   try:
      kcs_pipe.pipe_activate(KcsPipeName.PipeName(pipename))
      try:
         list = kcs_pipe.document_reference_get()
      except:
         CommonSample.ReportTribonError(kcs_pipe)
         return

      kcs_pipe.pipe_cancel()

   except:
      CommonSample.ReportTribonError(kcs_pipe)
      return

   strlist = []
   for item in list:
      if item.GetType() == 'drawing':
         strlist.append(item.GetDocument())
   if len(strlist) == 0:
      kcs_ui.message_confirm('No drawing type document references!')
   else:
      (status, index) = kcs_ui.string_select('Drawings', 'Select drawing name:', '', strlist)
      if status == kcs_util.ok():
         try:
            try:
               kcs_draft.dwg_close()
            except:
               pass
            kcs_draft.dwg_open(strlist[index-1])
         except:
            CommonSample.ReportTribonError(kcs_draft)

#----------------------------------------------------------------------------------
# Create main menu
#----------------------------------------------------------------------------------
pipename = ''

while 1:
   actions = (
      'Pipe: '+pipename,
      'Get document references',
      'Add document reference',
      'Remove document reference',
      'Open referenced drawing'
      )

   (status, index) = kcs_ui.choice_select('Pipe documents','Operations on the documents', actions)

   if status == kcs_util.ok():
      if index == 1:
         resp, name = kcs_ui.string_req('Keyin pipe name:', pipename)
         if resp == kcs_util.ok():
            pipename = name
      elif index == 2:
         GetPipeDocumentReference(pipename)
      elif index == 3:
         AddPipeDocumentReference(pipename)
      elif index == 4:
         reference = GetPipeDocumentReference(pipename, 'Select document reference to delete')
         if reference != None:
            RemovePipeDocumentReference(pipename, reference)
      elif index == 5:
         OpenReferencedDrawing(pipename)
   else:
      print "User interrupted!"
      break;


#----------------------------------------------------------------------------------
