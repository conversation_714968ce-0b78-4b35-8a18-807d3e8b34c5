#
#      NAME:
#
#          kcs_ex_pipe17.py
#
#      PURPOSE:
#
#          This program shows example usage of pipe transform function
#

import KcsStringlist
import kcs_ui
import kcs_util
import kcs_pipe
import kcs_draft
import KcsPoint3D
import KcsVector3D
import KcsStat_point3D_req
import KcsModel
import KcsTransformation3D

#------------------------------------------------------------------------------------
#   Get current pipe model
#------------------------------------------------------------------------------------

def GetCurrentPipeModel():

        CurrentModel = KcsModel.Model()
        CurrentModel.SetType("pipe")	

        try:
           ProjectName = kcs_util.TB_environment_get('SB_PROJ')
           PipeName    = kcs_pipe.pipe_name_get()	
           CurrentModel.SetName(ProjectName + '-' + str(PipeName))
        except:
           print 'Error getting model: ', kcs_pipe.error
		
        return CurrentModel

#------------------------------------------------------------------------------------
#  Select point 3D
#------------------------------------------------------------------------------------

def SelectPoint(InitialType = 3, Message = "Indicate point"):
        status = KcsStat_point3D_req.Stat_point3D_req()
        point = KcsPoint3D.Point3D()
        status.Initial3D = InitialType
        status.Initial2D = 6
        res = kcs_ui.point3D_req(Message, status, point)
        if res[0] == kcs_util.ok():
            return point
        else:
            print "User interrupted!"
            return None

#------------------------------------------------------------------------------------
#   Construct translation matrix
#------------------------------------------------------------------------------------

def GetTranslation():
    try:
        print "Translation"
        basePoint   = SelectPoint(3, "Indicate base point")

        if basePoint != None :
           offsetPoint = SelectPoint(1, "Indicate offset point")

        if basePoint == None or offsetPoint == None :
           return None

        translation =  KcsVector3D.Vector3D(
                offsetPoint.X - basePoint.X,
                offsetPoint.Y - basePoint.Y,
                offsetPoint.Z - basePoint.Z)

        trans = KcsTransformation3D.Transformation3D()
        trans.Translate(translation)

        return trans

    except:
        return None

#------------------------------------------------------------------------------------
#   Construct rotation matrix
#------------------------------------------------------------------------------------

def GetRotation():
    try:
        print "Rotation"

        point = SelectPoint(3, "Indicate origin point")

        if point != None :
           res, angle = kcs_ui.req_real("Angle in degrees")

        if point == None or res != kcs_util.success():
           return None

        normal = KcsVector3D.Vector3D(0.0, 1.0, 0.0)
        trans = KcsTransformation3D.Transformation3D()
        trans.Rotate(point, normal, angle*3.14/180.0)

        return trans

    except:
        return None



#------------------------------------------------------------------------------------
#   Function selection
#------------------------------------------------------------------------------------

actions = KcsStringlist.Stringlist('Translation')
actions.AddString('Rotation')

try:
    (status, option) = kcs_ui.choice_select('Transform pipe', 'Select option', actions)
    if status == kcs_util.ok() :
        try:
                if option == 1 :
                    kcs_pipe.pipe_transform( GetTranslation() )
                elif option == 2 :
                    kcs_pipe.pipe_transform( GetRotation() )
        except:
                print kcs_pipe.error

    else:
        print "User interrupted!"

    try:
        kcs_draft.model_draw(GetCurrentPipeModel())
    except:
        print "Redraw error", kcs_draft.error

except:
    print kcs_ui.error
