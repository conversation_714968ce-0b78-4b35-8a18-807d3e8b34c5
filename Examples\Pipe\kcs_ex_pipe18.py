#
#      NAME:
#
#          kcs_ex_pipe18.py
#
#      PURPOSE:
#
#          This program shows example usage of pipe part selection criteria functions
#

import KcsStringlist
import kcs_ui
import kcs_util
import kcs_pipe
import kcs_draft
import KcsModel
import KcsPoint2D
import KcsPipeMaterial

#------------------------------------------------------------------------------------
#   Get current pipe model
#------------------------------------------------------------------------------------

def GetCurrentPipeModel():
        CurrentModel = KcsModel.Model()
        CurrentModel.SetType("pipe")	

	try:
	   ProjectName = kcs_util.TB_environment_get('SB_PROJ')
	   PipeName    = kcs_pipe.pipe_name_get()	
	   CurrentModel.SetName(ProjectName + '-' + str(PipeName))
	except:
	   print 'Error getting model: ', kcs_pipe.error
		
	return CurrentModel

#------------------------------------------------------------------------------------
#   Select pipe part
#------------------------------------------------------------------------------------

def SelectPart():
        pt = KcsPoint2D.Point2D()
        kcs_ui.point2D_req("Indicate part", pt)
        ModelInfo = KcsModel.Model()
        try:
                kcs_draft.model_identify(pt, ModelInfo)
                if ModelInfo.Type in ["pipe", "pipe spool"]:
                    print "Selected part: ", ModelInfo.Name, ModelInfo.PartId
                    return ModelInfo.PartId
                else:
                    print "Selected model is not a pipe"
                    return None
        except:
            print kcs_draft.error

#------------------------------------------------------------------------------------
#   Function selection
#------------------------------------------------------------------------------------

actions = KcsStringlist.Stringlist('Get selection criteria')
actions.AddString('Set selection criteria')

try:
    (status, option) = kcs_ui.choice_select('Pipe part selection criteria functions', 'Select option', actions)
    if status == kcs_util.ok() :
        if option == 1 :
            PipeSelCriteriaGet()
        elif option == 2 :
            PipeSelCriteriaSet()

    else:
        print "User interrupted!"

    try:
        kcs_draft.model_draw(GetCurrentPipeModel())
    except:
        print "Redraw error", kcs_draft.error

except:
    print kcs_ui.error
#------------------------------------------------------------------------------------
#   Get spec data
#------------------------------------------------------------------------------------

def PipeSelCriteriaGet():
    try:
        print "PipeSelCriteriaGet"
        PartId = SelectPart()
        SelCriteria = kcs_pipe.part_selection_criteria_get( PartId)
        print "Selection Criteria"
        print "		Project  : ", SelCriteria.SCProject
        print "		SpecName : ", SelCriteria.SCSpec
        print "		Function : ", SelCriteria.SCFunction
        print "		DN       : ", SelCriteria.SCNomDia
        print "		Flow     : ", SelCriteria.SCFlow
        print "		Pr Class : ", SelCriteria.SCPressClass

    except:
        print kcs_pipe.error
        print "PipeSelCriteriaGet error"

#------------------------------------------------------------------------------------
#   Set spec data
#------------------------------------------------------------------------------------

def PipeSelCriteriaSet():
    try:
        print "PipeSelCriteriaSet"
        PartId = SelectPart()
        SelCriteria = kcs_pipe.part_selection_criteria_get( PartId)
        print "Selection Criteria"
        print "		Project  : ", SelCriteria.SCProject
        print "		SpecName : ", SelCriteria.SCSpec
        print "		Function : ", SelCriteria.SCFunction
        print "		DN       : ", SelCriteria.SCNomDia
        print "		Flow     : ", SelCriteria.SCFlow
        print "		Pr Class : ", SelCriteria.SCPressClass

	SelCriteria.SCProject    = ""
	SelCriteria.SCSpec       = "C3XC"
	SelCriteria.SCFunction   = "PIPE"
	SelCriteria.SCNomDia     = 65
	SelCriteria.SCFlow       = -1.0
	SelCriteria.SCPressClass = ""

        kcs_pipe.part_selection_criteria_set( PartId, SelCriteria)
        print "	New DN       : ", SelCriteria.SCNomDia
    except:
        print kcs_pipe.error
        print "PipeSelCriteriaGet error"

