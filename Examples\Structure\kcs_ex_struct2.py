
import kcs_ui
import kcs_util
import KcsStringlist
import kcs_struct
import KcsContour2D
import kcs_draft
import KcsModel
import KcsPoint2D
import KcsPoint3D
import KcsVector3D
import KcsStat_point3D_req


OK = kcs_util.ok()
CANCEL  = kcs_util.cancel()

#------------------------------------------------------------------------------------
#   Select structure part
#------------------------------------------------------------------------------------
def SelectPart():
   pt = KcsPoint2D.Point2D()
   kcs_ui.point2D_req("Indicate cableway part", pt)
   model = KcsModel.Model()
   try:
      kcs_draft.model_identify(pt, model)
      if model.Type == "struct":
         print model.Name, model.PartId
         return model.PartId
      else:
         print "Selected model is not struct"
         raise 0
   except:
      print kcs_ui.error
      print sys.exc_info()[1]

#------------------------------------------------------------------------------------
#   Create contour object
#------------------------------------------------------------------------------------
def CreateContour():
   point = KcsPoint2D.Point2D(0.0, 0.0)
   cont  = KcsContour2D.Contour2D(point)
   point.X = 1000.0
   cont.AddLine(point)
   point.Y = 1000.0
   cont.AddLine(point)
   point.X = 0.0
   cont.AddArc(point, 250)
   point.Y = 0.0
   cont.AddLine(point)

   return cont

#---------------------------------------------------------------------------------
# Input structure
#---------------------------------------------------------------------------------
def Input():
   try:
      print "Standard Structure input"
      stdname     = kcs_ui.string_req("Standard Structure Name", "STD1")
      modname     = kcs_ui.string_req("Module Name", "M902")
      strname     = kcs_ui.string_req("New Structure Name", "STR2")
      (status)    = kcs_struct.standard_input(stdname[1], modname[1], strname[1])
   except:
      print kcs_struct.error


#---------------------------------------------------------------------------------
# Save structure as a standard structure
#---------------------------------------------------------------------------------
def Output():
   try:
      # receive name of structure and standard structure
      name = kcs_ui.string_req("Key in name of structure:")
      if name[0] == OK:
         stdname = kcs_ui.string_req("Key in name of standard structure")
         if stdname[0] == OK:
            try:
               kcs_struct.standard_output(name[1], stdname[1])
               kcs_ui.message_noconfirm(" OK !" )
            except:
               kcs_ui.message_noconfirm(" Error ... " )
               print kcs_ui.error
   except:
       kcs_ui.message_noconfirm("Invalid input structure !" )
       print kcs_ui.error

#---------------------------------------------------------------------------------
# Standard structure will be replace with new standard structure
#---------------------------------------------------------------------------------
def Replace():
   try:
    # receive name of structure and standard structure
    name = kcs_ui.string_req("Key in name of structure:")
    if name[0] == OK:
       stdname = kcs_ui.string_req("Key in name of standard structure")
       if stdname[0] == OK:
          try:
             kcs_struct.standard_replace(name[1], stdname[1])
             kcs_ui.message_noconfirm(" OK !" )
          except:
             kcs_ui.message_noconfirm(" Error ... " )
             print kcs_ui.error
   except:
       kcs_ui.message_noconfirm("Invalid input structure !" )
       print kcs_ui.error

#---------------------------------------------------------------------------------
# Create general hole in plate part defined by Contour2D
#---------------------------------------------------------------------------------
def Hole1():
   try:
      # origin point of hole
      stat    = KcsStat_point3D_req.Stat_point3D_req()
      point3d   = KcsPoint3D.Point3D()
      res     = kcs_ui.point3D_req("Indicate start point", stat, point3d)
      # vector route
      vector  = KcsVector3D.Vector3D(0.0, 1.0, 0.0)
      # create contour object
      cont = CreateContour()
      # id of structure plate or profile
      id  = SelectPart()
      try:
         # call structure function
         id_hole = kcs_struct.hole_new(cont, id, point3d, vector)
         print id_hole
      except:
         kcs_ui.message_noconfirm(" Error ... " )
         print kcs_ui.error
   except:
      kcs_ui.message_noconfirm("Invalid input structure !" )
      print kcs_ui.error

#---------------------------------------------------------------------------------
# Create general hole in profile part defined by Contour2D
#---------------------------------------------------------------------------------
def Hole2():
   try:
      # create contour object
      cont = CreateContour()
      # origin point of hole
      stat    = KcsStat_point3D_req.Stat_point3D_req()
      point   = KcsPoint3D.Point3D()
      res     = kcs_ui.point3D_req("Indicate start point", stat, point)
      # vector route
      vector  = KcsVector3D.Vector3D(0.0, 1.0, 0.0)
      # parametr indicating Flange or Web
      side    =  kcs_ui.int_req("Key in side")
      # id of structure plate or profile
      id  = SelectPart()
      try:
         # call structure function
         id_hole = kcs_struct.hole_new(cont, id, point, vector, side[1])
         print id_hole
      except:
         kcs_ui.message_noconfirm(" Error ... " )
         print kcs_ui.error
   except:
       kcs_ui.message_noconfirm("Invalid input structure !" )
       print kcs_ui.error

#---------------------------------------------------------------------------------
# Create standard hole in profile part
#---------------------------------------------------------------------------------
def Hole3():
   try:
      # component name
      component = kcs_ui.string_req("Key in name of component")
      # origin point of hole
      stat    = KcsStat_point3D_req.Stat_point3D_req()
      point   = KcsPoint3D.Point3D()
      res     = kcs_ui.point3D_req("Indicate start point", stat, point)
      # vector route
      vector  = KcsVector3D.Vector3D(0.0, 1.0, 0.0)
      # parametr indicating Flange or Web
      side    =  kcs_ui.int_req("Key in side")
      # id of structure plate or profile
      id  = SelectPart()
      try:
         # call structure function
         id_hole = kcs_struct.hole_new(component[1], id, point, vector, side[1])
         print id_hole
      except:
         kcs_ui.message_noconfirm(" Error ... " )
         print kcs_ui.error
   except:
       kcs_ui.message_noconfirm("Invalid input structure !" )
       print kcs_ui.error


#**************************************************************
# Create standard hole in plate/profile part
#**************************************************************
def Hole4():
   try :
      # check structure activate status
      active = -1
      try :
         active_struct = kcs_struct.struct_name_get()
         kcs_ui.message_noconfirm("One structure " + active_struct + " is active state")
         active = 1
      except :
         kcs_ui.message_noconfirm("No active structure.")
         active = -1

      if active < 0 :
         return

      # make a plate
      ComponentName = "P#20"
      origo3D   = KcsPoint3D.Point3D(0.0, 0.0, 0.0)
      Normal    = KcsVector3D.Vector3D(0.0, 0.0, 1.0)
      RotVector = KcsVector3D.Vector3D(1.0, 0.0,0.0)
      Contour2D = CreateContour()
      try :
          platePart = kcs_struct.plate_new_contour2D(ComponentName, origo3D, Normal, RotVector, Contour2D)
      except :
          kcs_ui.message_noconfirm(kcs_struct.error)
          kcs_struct.struct_cancel()
          return

      # Make a Hole. pseudoHole = "H#200*4*1"
      pseudoHole = "H#1000*500*100"
      HoleCenter  = KcsPoint3D.Point3D(5000.0, 2500.0, 0.0)
      HoleRotV    = KcsVector3D.Vector3D(1.0, 0.0, 0.0)
      try :
         holeid = kcs_struct.hole_new(pseudoHole, platePart, HoleCenter, HoleRotV)
      except :
         print kcs_struct.error
   except :
      print kcs_ui.error

#-------------------------------------------------------------------------
# Create main menu
#-------------------------------------------------------------------------

main = KcsStringlist.Stringlist('Standard - Input')
main.AddString('Standard - Output')
main.AddString('Standard - Replace')
main.AddString('General hole in plate part')
main.AddString('General hole in profile part')
main.AddString('Standard hole in profile part')
main.AddString('Standard hole in plate part')
next = 1

while next:
   (status, index) = kcs_ui.choice_select('Main menu','Operation on structure', main)
   if status == OK:
       if index == 1:
          Input()
       elif index == 2:
          Output()
       elif index == 3:
          Replace()
       elif index == 4:
          Hole1()
       elif index == 5:
          Hole2()
       elif index == 6:
          Hole3()
       elif index == 7:
          Hole4()
   else:
      next = 0
      print "User interrupted!"

#-------------------------------------------------------------------------
