#--------------------------------------------------------------------------------------
#
#      NAME:
#
#          kcs_ex_struct3.py
#
#      PURPOSE:
#
#          This program serve to handling structure reference
#
#
#
#--------------------------------------------------------------------------------------

import kcs_ui
import kcs_cable
import kcs_struct
import kcs_util
import kcs_draft
import KcsStringlist
import KcsPoint3D
import KcsPoint2D
import KcsStat_point3D_req
import KcsModel


OK = kcs_util.ok()
CANCEL  = kcs_util.cancel()

#------------------------------------------------------------------------------------
#   Select structure part
#------------------------------------------------------------------------------------
def SelectPart():
   pt = KcsPoint2D.Point2D()
   kcs_ui.point2D_req("Indicate part", pt)
   model = KcsModel.Model()
   try:
      kcs_draft.model_identify(pt, model)
      if model.Type == "struct":
         print model.Name, model.PartId
         return model.PartId
      else:
         print "Selected model is not a struct"
         raise 0
   except:
      print kcs_struct.error

#------------------------------------------------------------------------------------
#   Select point 3D
#------------------------------------------------------------------------------------
def SelectPoint(InitialType = 3):
   status = KcsStat_point3D_req.Stat_point3D_req()
   point = KcsPoint3D.Point3D()
   status.Initial3D = InitialType
   res = kcs_ui.point3D_req("Indicate point", status, point)
   if res[0] == kcs_util.ok():
      return point
   else:
      print "User interrupted!"
      raise 0

#--------------------------------------------------------------------------------------
# Change component reference for specified part
#--------------------------------------------------------------------------------------
def PartComponent():
   try:
      PartId = SelectPart()
      compName = kcs_ui.string_req("Key in name of component:")
      if compName[0] == OK:
         try:
            kcs_struct.part_component(PartId, compName[1])
         except:
            print kcs_struct.error
   except:
      kcs_ui.message_noconfirm("Invalid input cableway !" )
      print kcs_struct.error


#--------------------------------------------------------------------------------------
# Set end points for specified profile part
#--------------------------------------------------------------------------------------
def ProfileEndPoints():
   try:
      PartId = SelectPart()
      pointP1 = SelectPoint(1)
      pointP2 = SelectPoint(1)
      kcs_struct.profile_endpoints(PartId, pointP1, pointP2)
   except:
      kcs_ui.message_noconfirm("Invalid input !" )
      print kcs_struct.error
#--------------------------------------------------------------------------------------
# Main loop
#--------------------------------------------------------------------------------------

main = KcsStringlist.Stringlist('Part component')
main.AddString('Profile end points')

next = 1
while next:
   (status, index) = kcs_ui.choice_select('Modify part','Operation on profile and plate',main)
   if status == OK:
      status == CANCEL
      if index == 1:
         PartComponent()
      elif index == 2:
         ProfileEndPoints()
   else:
      next = 0
      print 'Error'
