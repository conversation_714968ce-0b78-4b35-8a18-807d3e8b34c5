#--------------------------------------------------------------------------------------
#
#      NAME:
#
#          kcs_ex_struct4.py
#
#      PURPOSE:
#
#          This program serve to handling structure reference
#
#
#
#--------------------------------------------------------------------------------------
import kcs_ui
import kcs_util
import KcsStringlist
import kcs_struct
import KcsContour2D
import kcs_draft
import KcsModel
import KcsPoint2D
import KcsPoint3D
import KcsVector3D
import KcsStat_point3D_req

OK = kcs_util.ok()
CANCEL  = kcs_util.cancel()
#------------------------------------------------------------------------------------
#   Select structure part
#------------------------------------------------------------------------------------
def SelectPart():
   pt = KcsPoint2D.Point2D()
   kcs_ui.point2D_req("Indicate cableway part", pt)
   model = KcsModel.Model()
   try:
      kcs_draft.model_identify(pt, model)
      if model.Type == "struct":
         print model.Name, model.PartId
         return model.PartId
      else:
         print "Selected model is not struct"
         raise 0
   except:
      print kcs_ui.error
      print sys.exc_info()[1]

#---------------------------------------------------------------------------------
#  Plate Bent
#---------------------------------------------------------------------------------
def BentNew():
   try:
      id1 = SelectPart()
      #id1     = kcs_ui.int_req("ID of first plate part")
      #print id1
      stat    = KcsStat_point3D_req.Stat_point3D_req()
      point3d1   = KcsPoint3D.Point3D()
      res     = kcs_ui.point3D_req("Indicate start point", stat , point3d1)
      print point3d1

      id2 = SelectPart()
      #id2     = kcs_ui.int_req("ID of second plate part")
      #print id2
      stat    = KcsStat_point3D_req.Stat_point3D_req()
      point3d2   = KcsPoint3D.Point3D()
      res     = kcs_ui.point3D_req("Indicate start point", stat, point3d2)
      print point3d2

      rad     = kcs_ui.real_req("Radius of bent part")
      # id of structure plate or profile
      try:
         # call structure function
         id_bent = kcs_struct.plate_bent_new(id1, point3d1, id2, point3d2, rad[1])
         print id_bent
      except:
         kcs_ui.message_noconfirm(" Error ... " )
         print kcs_struct.error
   except:
      kcs_ui.message_noconfirm("Invalid input structure !" )
      print kcs_struct.error

#---------------------------------------------------------------------------------
#  Profile Contour2D
#---------------------------------------------------------------------------------
def ProfileContour():
   try :
      # make a profile
      profileComp = "L#100*120*8*10"
      oriPoint  = KcsPoint3D.Point3D(1000.0, 1000.0, 1000.0)
      normal    = KcsVector3D.Vector3D(-1.0, 0.0, 0.0)
      rotation  = KcsVector3D.Vector3D(0.0, 0.0, 1.0)
      point     = KcsPoint2D.Point2D(0.0,0.0)
      cont      = KcsContour2D.Contour2D(point)
      point.X   = 1000.0
      cont.AddLine(point)
      point.Y   = 1000.0
      cont.AddLine(point)
      point.X   = 0.0
      cont.AddArc(point,250)
      point.Y   = 0.0
      cont.AddLine(point)
      try:
         profilePart = kcs_struct.profile_new_contour2d(profileComp, cont, oriPoint, normal, rotation)
      except :
         kcs_ui.message_noconfirm(kcs_struct.error)
         kcs_struct.struct_cancel()
         return
   except :
      print kcs_struct.error

#-------------------------------------------------------------------------
# Create main menu
#-------------------------------------------------------------------------
main = KcsStringlist.Stringlist('Plate bent')
main.AddString('Profile contour2d')
next = 1

while next:
   (status, index) = kcs_ui.choice_select('Main menu','Operation on structure', main)
   if status == OK:
       if index == 1:
          BentNew()
       elif index == 2:
          ProfileContour()
   else:
      next = 0
      print "User interrupted!"
#-------------------------------------------------------------------------
