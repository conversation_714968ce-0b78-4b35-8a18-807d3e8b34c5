import kcs_ui
import kcs_util
import kcs_draft

##创建一个视图

handle = kcs_draft.view_new('ton')

# 获得第一个子图subview
viewhandle = kcs_draft.element_child_first_get()

##获得视图名称
name = kcs_draft.subpicture_name_get(viewhandle)
viewhandle = kcs_draft.element_sibling_next_get(viewhandle)
name = kcs_draft.subpicture_name_get(viewhandle)
kcs_draft.subpicture_current_set(viewhandle)
handle = kcs_draft.subview_new('subton')
kcs_draft.subpicture_current_set(handle)
handle = kcs_draft.component_new('E3-LJD')
