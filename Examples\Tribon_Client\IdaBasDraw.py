#-*- encoding:mbcs -*-
# import sys   #reload()之前必须要引入模
# reload(sys)
# sys.setdefaultencoding("mbcs")


import wingdbstub


import KcsStringlist
import KcsContour2D
import KcsModel
import KcsPoint2D
import KcsRline2D
import sys
from KcsPoint2D               import Point2D
from KcsCursorType            import CursorType, CursorTypes
from KcsStat_point2D_req      import Stat_point2D_req, Point2dDefModes
from KcsButtonState           import ButtonState
import KcsVector3D, KcsColour, kcs_att, kcs_draft, kcs_struct, kcs_util, kcs_ui, kcs_model
from IdaExtStruct import get_structure_info

from IdaUtlDraw import get_colour_by_enum, ColourEnum, getColourByName

kcs_ui.message_noconfirm("Start Debug" )

import time

OK = kcs_util.ok()
CANCEL  = kcs_util.cancel()



#-------------------------------------------------------------------------
# 基础方法
#-------------------------------------------------------------------------
# 获取坐标附近的钢结构件
def get_near_by_struct(pt, model):
    model.SetType("struct")
    kcs_draft.model_identify(pt, model)

#-------------------------------------------------------------------------
# 获取坐标附件的钢结构件
#-------------------------------------------------------------------------
def getNearByStructureByPoint(pt, model):
    model.SetType("struct")
    kcs_draft.model_identify(pt, model)    

# 创建一个结构件的基础方法
def create_structure(nodeId, module, nodeComponent, startPoint, endPoint, colour):
    """
    放置一个用于标识节点的模型, 参数为放置节点标识的3维坐标点
    注意：
    1. nodeId如果超过了22个字符会被截取
    参数：
    nodeId: 节点的ID
    module: 节点所属的模块
    nodeComponent: 节点的结构件类型
    startPoint: 节点的起始坐标
    endPoint: 节点的结束坐标
    colour: 节点的颜色, 枚举
    """
    try:
        # 判断nodeId是否为空，为空则调用接口获取
        # if nodeId is None:
        #     nodeId = get_new_node_id()

        # 检查nodeId长度，截取前22个字符
        # nodeId = nodeId[:22]
        # 生成字符串类型的时间戳作为nodeId
        nodeId = str(int(time.time()))
        # 判断是有由未save得struct，有则cancel
        try:
            active_struct = kcs_struct.struct_name_get()
            kcs_ui.message_noconfirm("One structure " + active_struct + " is active state")
            kcs_struct.struct_cancel()
        except:
            kcs_ui.message_noconfirm("No active structure.")
        # 如果colour为None, 则使用默认颜色
        if colour is None:
            colour = get_colour_by_enum(ColourEnum.BLUE)
        else:
            colour = getColourByName(colour)
        kcs_struct.struct_new(nodeId, module, colour)
        rotation    = KcsVector3D.Vector3D(0.0, 1.0, 0.0)
        profilePart = kcs_struct.profile_new_2point3D(nodeComponent, startPoint, endPoint, rotation)
        kcs_util.coord_to_pos(1, startPoint.GetX())        
        save = kcs_struct.struct_save()
        # 显示以上创建的model
        model = KcsModel.Model("struct", nodeId)
        kcs_att.model_save(model)  
        kcs_util.coord_to_pos(1, startPoint.GetX())              
        kcs_draft.model_draw(model)
    except Exception, e:        
        kcs_ui.message_noconfirm(str(e))
        kcs_struct.struct_cancel()
   

# 删除一个结构件的基础方法
def delete_structure(nodeId):
    """
    删除一个结构件
    参数：
    nodeId: 节点的ID
    """
    try:
        kcs_struct.struct_delete(nodeId)
    except Exception, e:
        kcs_ui.message_noconfirm(str(e))

# 显示或隐藏一个结构件的基础方法
def show_or_hide_structure_by_id(nodeId, isShow):
    """
    显示或隐藏一个结构件
    参数：
    nodeId: 节点的ID
    isShow: 是否显示
    """
    try:
        model = KcsModel.Model("struct", nodeId)
        handle = kcs_draft.element_identify(nodeId)
        if isShow:                    
            kcs_draft.element_visibility_set(handle, 1)    
        else:
            handle = kcs_draft.element_identify(nodeId, 0)
    except Exception, e:
        kcs_ui.message_noconfirm(str(e))        

def placeRoute(routeId, module, routeComponent, startPoint, endPoint):
    """
    放置一个用于通道连接的模型, 参数为连接的起始和结束坐标
    该方法会创建通道
    注意：
    1. routeId如果超过了22个字符会被截取
    """
    try:
        # 检查routeId长度，截取前22个字符
        routeId = routeId[:22]
        # kcs_struct.struct_cancel()
        colour = KcsColour.Colour('BLUE')
        kcs_struct.struct_new(routeId, module, colour)
        rotation    = KcsVector3D.Vector3D(0.0, 1.0, 0.0)
        profilePart = kcs_struct.profile_new_2point3D(routeComponent, startPoint, endPoint, rotation)
        kcs_util.coord_to_pos(1, startPoint.GetX())        
        save = kcs_struct.struct_save()
        # 显示以上创建的model
        model = KcsModel.Model("struct", routeId)
        kcs_att.model_save(model)  
        kcs_util.coord_to_pos(1, startPoint.GetX())              
        kcs_draft.model_draw(model)
    except Exception, e:
        kcs_struct.struct_cancel()
        kcs_ui.message_noconfirm(str(e))



# 创建一条连线的基础方法


