#-*- encoding:mbcs -*-
"""
绘图工具模块
包含结构件创建、删除、显示隐藏等基础绘图功能
"""

import wingdbstub
import kcs_ui
import kcs_util
import kcs_draft
import kcs_struct
import kcs_att
import KcsModel
import KcsVector3D
import KcsColour
from KcsPoint2D import Point2D
from KcsPoint3D import Point3D
from KcsCursorType import CursorType
from KcsStat_point2D_req import Stat_point2D_req
from KcsButtonState import ButtonState
import KcsPoint2D
import time
import random
from IdaUtlCommon import output_exception

OK = kcs_util.ok()
CANCEL = kcs_util.cancel()

# 定义一个用于选择颜色的枚举
class ColourEnum:
    RED = "RED"
    GREEN = "GREEN"
    BLUE = "BLUE"
    YELLOW = "YELLOW"
    PURPLE = "PURPLE"
    CYAN = "CYAN"
    ORANGE = "ORANGE"
    PINK = "PINK"
    BROWN = "BROWN"
    GRAY = "GRAY"
    BLACK = "BLACK"
    WHITE = "WHITE"

class ComponentCodeEnum:
    XFlag = "O#1"

def point2D_reqGetStatus():
    status = Stat_point2D_req()
    status.SetDefMode('ModeCursor')
    curType = CursorType()
    curType.SetCrossHair()
    status.SetCursorType(curType)
    helpPoint = KcsPoint2D.Point2D(0.0, 0.0)
    status.SetHelpPoint(helpPoint)
    return status

def point2D_reqGetButton():
    buttons = ButtonState()
    buttons.LockEnabled = 0
    buttons.OptionsEnabled = 1
    buttons.LockChecked = None
    return buttons

# 根据一个中心点，返回其X形状的四个顶点
def generate_x_vertices(center_point, size=1.0):
    cx, cy = center_point.GetX(), center_point.GetY()
    offset = size * 0.70710678118
    top_left = (cx - offset, cy + offset)
    top_right = (cx + offset, cy + offset)
    bottom_left = (cx - offset, cy - offset)
    bottom_right = (cx + offset, cy - offset)
    return (top_left, top_right, bottom_left, bottom_right)

# 根据一个中心点，返回其X形状的四个顶点(Point3D)
def generate_x_vertices_3d(center_point, size=50.0):
    cx, cy, cz = center_point.GetX(), center_point.GetY(), center_point.GetZ()
    offset = size * 0.70710678118
    top_left = (cx - offset/2, cy + offset , cz - offset/2)
    top_right = (cx + offset/2, cy + offset , cz + offset/2)
    bottom_left = (cx - offset/2, cy - offset , cz - offset/2)
    bottom_right = (cx + offset/2, cy - offset , cz + offset/2)
    return (top_left, top_right, bottom_left, bottom_right)

def get_colour_by_enum(colour_enum):
    return KcsColour.Colour(colour_enum)

def getColourByName(colour):
    return KcsColour.Colour(colour)

def validate_component_code(component_code):
    """
    验证组件代码是否有效
    参数：
    component_code: 组件代码字符串
    返回：
    有效的组件代码字符串
    """
    # 常用的有效组件代码列表
    valid_codes = [
        "F#10*10",      # 扁钢 10x10
        "F#20*10",      # 扁钢 20x10
        "L#100*120*8*10", # 角钢
        "O#1",          # 圆钢
    ]
    
    if component_code in valid_codes:
        return component_code
    else:
        # 如果不在列表中，尝试使用 pseudoname_profile 生成
        try:
            return kcs_struct.pseudoname_profile('F', 10, 10)  # 默认使用扁钢 10x10
        except:
            return "F#10*10"  # 最后的备选方案

# 获取坐标附近的钢结构件
def get_near_by_struct(pt, model):
    model.SetType("struct")
    kcs_draft.model_identify(pt, model)

# 获取坐标附件的钢结构件
def getNearByStructureByPoint(pt, model):
    model.SetType("struct")
    kcs_draft.model_identify(pt, model)    

# 创建一个结构件的基础方法
def create_structure(nodeId, module, nodeComponent, startPoint, endPoint, colour, rotation=None):
    """
    放置一个用于标识节点的模型, 参数为放置节点标识的3维坐标点
    注意：
    1. nodeId如果超过了22个字符会被截取
    参数：
    nodeId: 节点的ID
    module: 节点所属的模块
    nodeComponent: 节点的结构件类型
    startPoint: 节点的起始坐标
    endPoint: 节点的结束坐标
    colour: 节点的颜色, 枚举
    rotation: 旋转向量，可选
    """
    try:
        # 生成字符串类型的时间戳作为nodeId，再加一个3位的随机数
        nodeId = str(int(time.time())) + str(random.randint(100,999))
        # 判断是否有未save得struct，有则cancel
        try:
            active_struct = kcs_struct.struct_name_get()
            kcs_ui.message_noconfirm("One structure " + active_struct + " is active state")
            kcs_struct.struct_cancel()
        except:
            kcs_ui.message_noconfirm("No active structure.")
        # 如果colour为None, 则使用默认颜色
        if colour is None:
            colour = get_colour_by_enum(ColourEnum.BLUE)
        else:
            colour = getColourByName(colour)
        kcs_struct.struct_new(nodeId, module, colour)
        if rotation is None:
            rotation = KcsVector3D.Vector3D(0.0, 1.0, 0.0)

        # 验证并获取有效的组件代码
        # valid_component = validate_component_code(nodeComponent)
        profilePart = kcs_struct.profile_new_2point3D(nodeComponent, startPoint, endPoint, rotation)
        kcs_util.coord_to_pos(1, startPoint.GetX())
        save = kcs_struct.struct_save()
        # 显示以上创建的model
        model = KcsModel.Model("struct", nodeId)
        kcs_att.model_save(model)
        kcs_util.coord_to_pos(1, startPoint.GetX())
        kcs_draft.model_draw(model)
        # 获取模型句柄列表
        model_handles = kcs_draft.model_handle_get(model)
        handle_list = []
        for p in model_handles:
            handle_list.append(p._ElementHandle__handle)
        return nodeId, handle_list
    except Exception, e:        
        output_exception(e)
        raise

# 删除一个结构件的基础方法
def delete_structure(nodeId, handles):
    """
    删除一个结构件
    参数：
    nodeId: 节点的ID
    handles: 句柄列表
    """
    try:
        #删除handles
        if handles:
            for handle in handles:
                kcs_draft.element_delete(handle)
        #从databank中删除
        if nodeId:
            kcs_struct.struct_delete(nodeId)
        #刷新绘图
        kcs_draft.dwg_repaint()
    except Exception, e:
        output_exception(e)

def delete_structures(nodeIds, handlesList):
    """
    批量删除结构件
    参数：
    nodeIds: 节点ID列表
    handlesList: 句柄列表的列表
    """
    try:
        if nodeIds and handlesList:
            for i, nodeId in enumerate(nodeIds):
                if i < len(handlesList):
                    delete_structure(nodeId, handlesList[i])
                else:
                    delete_structure(nodeId, None)
    except Exception, e:
        output_exception(e)

# 显示或隐藏一个结构件的基础方法
def show_or_hide_structure_by_id(nodeId, isShow):
    """
    显示或隐藏一个结构件
    参数：
    nodeId: 节点的ID
    isShow: 是否显示
    """
    try:
        model = KcsModel.Model("struct", nodeId)
        handle = kcs_draft.element_identify(nodeId)
        if isShow:                    
            kcs_draft.element_visibility_set(handle, 1)    
        else:
            handle = kcs_draft.element_identify(nodeId, 0)
    except Exception, e:
        output_exception(e)

# 创建一个路线图路线
def placeRoute(routeId, module, routeComponent, startPoint, endPoint, rotation=None):
    """
    放置一个用于通道连接的模型, 参数为连接的起始和结束坐标
    该方法会创建通道
    注意：
    1. routeId如果超过了22个字符会被截取
    """
    try:
        # 检查routeId长度，截取前22个字符
        routeId = routeId[:22]
        colour = KcsColour.Colour('BLUE')
        kcs_struct.struct_new(routeId, module, colour)
        if rotation is None:
            rotation = KcsVector3D.Vector3D(0.0, 1.0, 0.0)
        profilePart = kcs_struct.profile_new_2point3D(routeComponent, startPoint, endPoint, rotation)
        kcs_util.coord_to_pos(1, startPoint.GetX())        
        save = kcs_struct.struct_save()
        # 显示以上创建的model
        model = KcsModel.Model("struct", routeId)
        kcs_att.model_save(model)  
        kcs_util.coord_to_pos(1, startPoint.GetX())              
        kcs_draft.model_draw(model)
    except Exception, e:
        kcs_struct.struct_cancel()
        output_exception(e)
