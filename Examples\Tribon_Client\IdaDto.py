#-*- encoding:mbcs -*-
"""
数据传输对象模块
包含路径节点和路径的数据结构定义
"""

import wingdbstub
import kcs_ui
import kcs_draft
from KcsPoint2D import Point2D
from KcsPoint3D import Point3D
from IdaUtlCommon import output_exception
from IdaExtStruct import get_structure_info

class IdaDtoRouteNode(object):
    '''
    封装路径节点的方法：
    包含路径节点所在结构件代码和两个标识结构件的代码及句柄
    '''

    def __init__(self, baseOnStructCode=None, flagX1StructCode=None, flagX1Handles=None, flagX2StructCode=None, flagX2Handles=None):
        self._baseOnStructCode = baseOnStructCode
        self._flagX1StructCode = flagX1StructCode
        self._flagX1Handles = flagX1Handles
        self._flagX2StructCode = flagX2StructCode
        self._flagX2Handles = flagX2Handles

    def to_dict(self):
        return {
            "baseOnStructCode": self._baseOnStructCode,
            "flagX1StructCode": self._flagX1StructCode,
            "flagX1Handles": self._flagX1Handles,
            "flagX2StructCode": self._flagX2StructCode,
            "flagX2Handles": self._flagX2Handles
        }
        
    def get_baseOnStructCode(self):
        return self._baseOnStructCode
    def set_baseOnStructCode(self, value):
        self._baseOnStructCode = value
    def get_flagX1StructCode(self):
        return self._flagX1StructCode
    def set_flagX1StructCode(self, value):
        self._flagX1StructCode = value
    def get_flagX1Handles(self):
        return self._flagX1Handles
    def set_flagX1Handles(self, value):
        self._flagX1Handles = value
    def get_flagX2StructCode(self):
        return self._flagX2StructCode
    def set_flagX2StructCode(self, value):
        self._flagX2StructCode = value
    def get_flagX2Handles(self):
        return self._flagX2Handles
    def set_flagX2Handles(self, value):
        self._flagX2Handles = value
        
    # 使用 property 定义属性
    baseOnStructCode = property(get_baseOnStructCode, set_baseOnStructCode, doc="路径节点所在结构件code")
    flagX1StructCode = property(get_flagX1StructCode, set_flagX1StructCode, doc="路径节点标识1的结构件code")
    flagX1Handles = property(get_flagX1Handles, set_flagX1Handles, doc="路径节点标识1的结构件句柄列表")
    flagX2StructCode = property(get_flagX2StructCode, set_flagX2StructCode, doc="路径节点标识2的结构件code")
    flagX2Handles = property(get_flagX2Handles, set_flagX2Handles, doc="路径节点标识2的结构件句柄列表")

    def __str__(self):
        return "baseOnStructCode: " + str(self._baseOnStructCode) + ", flagX1StructCode: " + str(self._flagX1StructCode) + ", flagX2StructCode: " + str(self._flagX2StructCode)

    def getCog(self):
        '''获取结构件重心坐标'''
        try:
            structure_info = get_structure_info(self._baseOnStructCode)
            cog = structure_info['cog']
            return Point3D(cog[0], cog[1], cog[2])
        except Exception, e:
            output_exception(e)
            return Point3D(0, 0, 0)

    def createAndDrawRouteNodeFlag(self, cog):
        '''
        创建一个路径节点，并在绘图中标识
        参数：
        cog: 三维重心坐标(KcsPoint3D.Point3D对象)
        '''
        try:
            from IdaDrawUtil import generate_x_vertices_3d, create_structure, ComponentCodeEnum, ColourEnum
            
            # 获取绘图中第一个视图的句柄
            handler = kcs_draft.element_child_first_get()
            # 设置当前视图
            kcs_draft.subpicture_current_set(handler)
            # 获取以重心为中心的X形状四个顶点，传入的参数为三维重心点
            verticesX = generate_x_vertices_3d(cog)
            # 创建 Point3D 对象
            top_left = Point3D(verticesX[0][0], verticesX[0][1], verticesX[0][2])
            top_right = Point3D(verticesX[1][0], verticesX[1][1], verticesX[1][2])
            bottom_left = Point3D(verticesX[2][0], verticesX[2][1], verticesX[2][2])
            bottom_right = Point3D(verticesX[3][0], verticesX[3][1], verticesX[3][2])
            # 创建两个Structure，使用属性设置器
            self._flagX1StructCode, self._flagX1Handles = create_structure(None, "ECA" , ComponentCodeEnum.XFlag, top_left, bottom_right, ColourEnum.RED )
            self._flagX2StructCode, self._flagX2Handles = create_structure(None, "ECA" , ComponentCodeEnum.XFlag, top_right, bottom_left, ColourEnum.RED )
            
        except Exception, e:        
            output_exception(e)
            raise
        
    def deleteAndUndrawRouteNodeFlag(self):
        # 删除两个Structure
        try:
            from IdaDrawUtil import delete_structure
            if self._flagX1StructCode and self._flagX1Handles:
                delete_structure(self._flagX1StructCode, self._flagX1Handles)
            if self._flagX2StructCode and self._flagX2Handles:
                delete_structure(self._flagX2StructCode, self._flagX2Handles)
        except Exception, e:
            output_exception(e)


class IdaDtoRoutePath(object):
    '''
    封装两个struct的关系，包括如下属性：
    开始RouteNode
    结束RouteNode
    关系边structCode
    关系边句柄列表
    '''

    def __init__(self, startRouteNode=None, endRouteNode=None, relationEdgeFlagStructCode=None, relationHandles=None):
        self._startRouteNode = startRouteNode
        self._endRouteNode = endRouteNode
        self._relationEdgeFlagStructCode = relationEdgeFlagStructCode
        self._relationHandles = relationHandles

    def to_dict(self):
        return {
            "startRouteNode": self._startRouteNode.to_dict() if self._startRouteNode else None,
            "endRouteNode": self._endRouteNode.to_dict() if self._endRouteNode else None,
            "relationEdgeFlagStructCode": self._relationEdgeFlagStructCode,
            "relationHandles": self._relationHandles
        }
        
    def get_startRouteNode(self):
        return self._startRouteNode
    def set_startRouteNode(self, value):
        self._startRouteNode = value
    def get_endRouteNode(self):
        return self._endRouteNode
    def set_endRouteNode(self, value):
        self._endRouteNode = value
    def get_relationEdgeFlagStructCode(self):
        return self._relationEdgeFlagStructCode
    def set_relationEdgeFlagStructCode(self, value):
        self._relationEdgeFlagStructCode = value
    def get_relationHandles(self):
        return self._relationHandles
    def set_relationHandles(self, value):
        self._relationHandles = value
        
    # 使用 property 定义属性
    startRouteNode = property(get_startRouteNode, set_startRouteNode, doc="开始路径节点")
    endRouteNode = property(get_endRouteNode, set_endRouteNode, doc="结束路径节点")
    relationEdgeFlagStructCode = property(get_relationEdgeFlagStructCode, set_relationEdgeFlagStructCode, doc="关系边结构件代码")
    relationHandles = property(get_relationHandles, set_relationHandles, doc="关系边结构件句柄列表")

    def __str__(self):
        return "startRouteNode: " + str(self._startRouteNode) + ", endRouteNode: " + str(self._endRouteNode) + ", relationEdgeFlagStructCode: " + str(self._relationEdgeFlagStructCode)
    
    # 调用http，由处理端生成标识structCode的方法
    def generateFlagStructCode(self):
        # TODO: 实现生成标识structCode的逻辑
        pass

    # 同步路线图节点关系到处理端的方法
    def syncToServer(self):
        # TODO: 调用http，将关系同步到处理端
        # 同步失败时，需要回滚在Tribon中创建的标识
        pass

    def createAndDrawRoutePathFlag(self):
        '''
        创建一个路径，并在绘图中标识
        '''
        try:
            from IdaDrawUtil import create_structure, ComponentCodeEnum, ColourEnum
            
            handler = kcs_draft.element_child_first_get()
            # 设置当前视图
            kcs_draft.subpicture_current_set(handler)
            # 获取两个模型的重心
            cog1 = get_structure_info(self._startRouteNode.baseOnStructCode)['cog']
            pointCog1 = Point3D(cog1[0], cog1[1], cog1[2])
            cog2 = get_structure_info(self._endRouteNode.baseOnStructCode)['cog']
            pointCog2 = Point3D(cog2[0], cog2[1], cog2[2])

            # 创建关系边，使用属性设置器
            self._relationEdgeFlagStructCode, self._relationHandles = create_structure(None, "ECA" , ComponentCodeEnum.XFlag, pointCog1, pointCog2, ColourEnum.RED )
            
        except Exception, e:        
            output_exception(e)
            raise
        
    def deleteAndUndrawRoutePathFlag(self):
        # 删除关系边Structure
        try:
            from IdaDrawUtil import delete_structure
            if self._relationEdgeFlagStructCode and self._relationHandles:
                delete_structure(self._relationEdgeFlagStructCode, self._relationHandles)
        except Exception, e:
            output_exception(e)


class IdaDtoPathNode(object):
    '''
    封装路径节点的方法：
    包含路径节点的定义，绘制
    TODO: 待进一步完善路径节点的具体实现
    '''
    pass
