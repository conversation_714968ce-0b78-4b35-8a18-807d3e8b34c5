from KcsPoint2D import Point2D
import KcsTransformation2D
import kcs_draft
import kcs_ui
from KcsPoint2D import Point2D
from KcsPoint3D import Point3D
from KcsRline2D import Rline2D
from KcsTransformation2D import Transformation2D

import wingdbstub
kcs_ui.message_noconfirm("Debug" )

import IdaUtlDraw, IdaBasDraw

class IdaDtoRouteNode(object):

    '''
    封装路径节点的方法：
    
    '''
    # 路径节点所在结构件code
    baseOnStructCode = None
    # 路径节点标识1的结构件code
    flagX1StructCode = None
    # 路径节点标识2的结构件code
    flagX2StructCode = None


    def createAndDrawRouteNodeFlag(self, cog):
        '''
        创建一个路径节点，并在绘图中标识
        参数：
        cog: 三维重心坐标(KcsPoint3D.Point3D对象)
        '''
        # # 设置绘图颜色为红色
        # colour = KcsColour.Colour('RED')
        # kcs_draft.colour_set(colour)
        # # 设置线条样式
        # kcs_draft.linetype_set('SolidXWide')
        # 获取绘图中第一个视图的句柄
        handler = kcs_draft.element_child_first_get()
        # 设置当前视图
        kcs_draft.subpicture_current_set(handler)
        # 在以上视图中，将重心三维坐标,在指定视图中，转换为二维坐标
        point2D=Point2D()
        kcs_draft.point_transform(handler, cog, point2D)
        # 获取以重心为中心的X形状四个顶点，传入的参数为二维重心点
        verticesX = IdaUtlDraw.generate_x_vertices(point2D)
        # 创建 Point2D 对象
        top_left = Point3D(verticesX[0][0], verticesX[0][1], 0.0)
        top_right = Point3D(verticesX[1][0], verticesX[1][1], 0.0)
        bottom_left = Point3D(verticesX[2][0], verticesX[2][1], 0.0)
        bottom_right = Point3D(verticesX[3][0], verticesX[3][1], 0.0)
        # 创建两个Structure
        self.flagX1StructCode = IdaBasDraw.create_structure(None, "ECA" , IdaUtlDraw.ComponentCodeEnum.XFlag, top_left, bottom_right, IdaUtlDraw.ColourEnum.RED )
        self.flagX2StructCode = IdaBasDraw.create_structure(None, "ECA" , IdaUtlDraw.ComponentCodeEnum.XFlag, top_right, bottom_left, IdaUtlDraw.ColourEnum.RED )
        
        


    



