class RequestStructIdRepo:
    url = "erupt-api/data/query/struct"
    body = {}
    response = None

    def buildBody(self, module, type):
        self.body = {"module": module, "structure_name": type}

    def processReponse(self, response):
        self.response = response


class RequestRoutePathRepo:
    url = "erupt-api/data/modify/route"
    body = {}
    response = None

    def buildBody(self, routePathList):
        self.body = {"routePathList": routePathList}

    def processReponse(self, response):
        self.response = response
