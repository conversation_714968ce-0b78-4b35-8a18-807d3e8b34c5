#-*- encoding:mbcs -*-
# import sys   #reload()之前必须要引入模
# reload(sys)
# sys.setdefaultencoding("mbcs")
import KcsCaptureRegion2D
import KcsRectangle2D

import wingdbstub

import kcs_model

import kcs_ui
import kcs_util
import KcsStringlist
import kcs_struct
import KcsContour2D
import kcs_draft
import KcsModel
import KcsPoint2D
import KcsRline2D
import sys
import kcs_ui
import kcs_util
import kcs_draft
from KcsPoint2D               import Point2D
from KcsCursorType            import CursorType, CursorTypes
from KcsStat_point2D_req      import Stat_point2D_req, Point2dDefModes
import KcsSymbol
import KcsTransformation2D
import kcs_ic
from KcsButtonState           import ButtonState

import kcs_dex

import IdaUtlCommon

# 网络请求与json处理
import urllib2
import simplejson as json
serverAddress = "http://*************:9999/"

kcs_ui.message_noconfirm("Sssssst" )

OK = kcs_util.ok()
CANCEL  = kcs_util.cancel()
appid = 'ese944b9ac5064c6'
secret = 'E6JWA9HF5UUWVA8U51HEME0M'



# 获取token
def get_http_token():
    try:
        web_url = serverAddress + "erupt-api/open-api/create-token?appid="+ appid +"&secret=" + secret
        headers = {'Content-Type': 'application/json'}
        req = urllib2.Request(web_url, None, headers)
        response = urllib2.urlopen(req).read()
        response = json.loads(response)
        token = response["data"]["token"]
        return token
    except Exception, e:
        kcs_ui.message_noconfirm(str(e))

#
# 通用网络请求
#
import traceback, StringIO
def general_http_request(url, body, erupt):
   global serverAddress
   try:
         token = get_http_token()
         kcs_ui.message_noconfirm('begin to request center...')
         web_url = serverAddress + url
         # web_url = serverAddress + "erupt-api/data/modify/Struct"
         data = json.dumps(body)
         headers = {'Content-Type': 'application/json', 'token': token, 'erupt': erupt}
         kcs_ui.message_noconfirm("below is token")
         kcs_ui.message_noconfirm(token)
         req = urllib2.Request(web_url, data, headers)
         response = urllib2.urlopen(req).read()
         response = json.loads(response)        
         kcs_ui.message_noconfirm(str(response))
         return response

   except Exception, e:
         IdaUtlCommon.output_exception(e)
         raise

#-------------------------------------------------------------------------
# 网络请求 - 上行, 数据传送云
#-------------------------------------------------------------------------
def data_upload_common(url, body, erupt):
   global serverAddress
   try:
        token = get_http_token()
        kcs_ui.message_noconfirm('begin to upload data')
        web_url = serverAddress + url
        # web_url = serverAddress + "erupt-api/data/modify/Struct"
        data = json.dumps(body)
        headers = {'Content-Type': 'application/json', 'token': token, 'erupt': erupt}
        kcs_ui.message_noconfirm("below is token")
        kcs_ui.message_noconfirm(token)
        req = urllib2.Request(web_url, data, headers)
        response = urllib2.urlopen(req).read()
        response = json.loads(response)        
        kcs_ui.message_noconfirm(str(response))
        return response

   except Exception, e:
        kcs_ui.message_noconfirm(str(e))


#-------------------------------------------------------------------------
# 网络请求 - 下行，接收云端数
#-------------------------------------------------------------------------
# def DataDownStream():
#    try:
#       kcs_ui.message_noconfirm('begin to get data')
#       #model = SelectPartReturnModel()
#       #kcs_ui.message_confirm("Model data extracting ... ' %s'! " % model.GetName())
#       # web_url = "http://************:9999/erupt-api/data/modify/Struct"
#       web_url = serverAddress + "erupt-api/data/table/Struct"
#       data = {"pageIndex":1,"pageSize":10,"sort":None,"condition":[]}

#       data = json.dumps(data)
#       #
#       headers = {'Content-Type': 'application/json', 'token': token, 'erupt': 'Struct'}
#       req = urllib2.Request(web_url, data, headers)
#       response = urllib2.urlopen(req).read()
#       response = json.loads(response)
#       kcs_ui.message_noconfirm("below is response.get list")
#       # kcs_ui.message_noconfirm(response)
#       #UnhighlightEntities()
#       kcs_ui.message_noconfirm("will into for")
#       for element in response.get("list"):
#          kcs_ui.message_noconfirm("show if highlist")
#          kcs_ui.message_noconfirm(element.get("ifHighlisht"))
#          if element.get("ifHighlisht") == "??":
#             elem = kcs_draft.element_identify(element.get("name"))
#             kcs_draft.element_highlight(elem)
#             # kcs_ui.message_confirm("高亮" + element.get("name"))
            
      
#    except:
#       kcs_ui.message_noconfirm(kcs_struct.error)
#       print kcs_struct.error      

