#-*- encoding:mbcs -*-
"""
主菜单模块
整合所有功能的菜单界面
"""

import wingdbstub
import kcs_ui
import kcs_util
import KcsStringlist
from IdaRouteService import (
    newRoutePathSequential, 
    newRoutePathBox, 
    deleteRoutePathBox,
    createChannelManual,
    createChannelAuto,
    deleteChannelBox
)
from IdaRemoteService import (
    start_remote_server,
    stop_remote_server,
    is_remote_mode_enabled,
    enable_remote_mode,
    disable_remote_mode
)
from IdaUtlCommon import output_exception

OK = kcs_util.ok()
CANCEL = kcs_util.cancel()

def create_main_menu():
    '''创建主菜单'''
    try:
        #-------------------------------------------------------------------------
        # Create main menu
        #-------------------------------------------------------------------------
        main = KcsStringlist.Stringlist('路线图管理系统')
        main.AddString('开启/关闭远程模式')
        main.AddString('手动创建路线图')
        main.AddString('自动创建路线图')
        main.AddString('删除路线图')
        main.AddString('手动创建通道')
        main.AddString('自动创建通道')
        main.AddString('删除通道')
        main.AddString('远程服务管理')
        
        next = 1

        while next:
            try:
                # 显示远程模式状态
                remote_status = "已开启" if is_remote_mode_enabled() else "已关闭"
                menu_title = "路线图管理系统 (远程模式: %s)" % remote_status
                
                (status, index) = kcs_ui.choice_select("Main", menu_title, main)
                
                if status == OK:
                    if index == 1:
                        toggle_remote_mode()
                    elif index == 2:
                        manual_create_route()
                    elif index == 3:
                        auto_create_route()
                    elif index == 4:
                        delete_route()
                    elif index == 5:
                        manual_create_channel()
                    elif index == 6:
                        auto_create_channel()
                    elif index == 7:
                        delete_channel()
                    elif index == 8:
                        remote_service_menu()
                else:
                    next = 0
                    kcs_ui.message_noconfirm("用户退出系统")
                    
            except Exception, e:
                output_exception(e)
                continue

    except Exception, e:
        output_exception(e)

def toggle_remote_mode():
    '''切换远程模式'''
    try:
        if is_remote_mode_enabled():
            disable_remote_mode()
        else:
            enable_remote_mode()
    except Exception, e:
        output_exception(e)

def manual_create_route():
    '''手动创建路线图'''
    try:
        kcs_ui.message_noconfirm("开始手动创建路线图...")
        kcs_ui.message_noconfirm("请依次点选型材或安装件，按回车完成，ESC取消")
        newRoutePathSequential()
    except Exception, e:
        output_exception(e)

def auto_create_route():
    '''自动创建路线图'''
    try:
        kcs_ui.message_noconfirm("开始自动创建路线图...")
        kcs_ui.message_noconfirm("请框选区域，系统将自动创建路线图")
        newRoutePathBox()
    except Exception, e:
        output_exception(e)

def delete_route():
    '''删除路线图'''
    try:
        kcs_ui.message_noconfirm("开始删除路线图...")
        kcs_ui.message_noconfirm("请框选要删除的路线图区域")
        deleteRoutePathBox()
    except Exception, e:
        output_exception(e)

def manual_create_channel():
    '''手动创建通道'''
    try:
        kcs_ui.message_noconfirm("开始手动创建通道...")
        kcs_ui.message_noconfirm("请依次点选型材或安装件")
        createChannelManual()
    except Exception, e:
        output_exception(e)

def auto_create_channel():
    '''自动创建通道'''
    try:
        kcs_ui.message_noconfirm("开始自动创建通道...")
        kcs_ui.message_noconfirm("请框选区域，系统将自动创建通道")
        createChannelAuto()
    except Exception, e:
        output_exception(e)

def delete_channel():
    '''删除通道'''
    try:
        kcs_ui.message_noconfirm("开始删除通道...")
        kcs_ui.message_noconfirm("请框选要删除的通道区域")
        deleteChannelBox()
    except Exception, e:
        output_exception(e)

def remote_service_menu():
    '''远程服务管理菜单'''
    try:
        remote_menu = KcsStringlist.Stringlist('远程服务管理')
        remote_menu.AddString('启动远程服务器')
        remote_menu.AddString('停止远程服务器')
        remote_menu.AddString('数据同步')
        remote_menu.AddString('显示路线图')
        remote_menu.AddString('隐藏路线图')
        remote_menu.AddString('显示端点和通道')
        remote_menu.AddString('隐藏端点和通道')
        
        next = 1
        while next:
            try:
                (status, index) = kcs_ui.choice_select("Remote", "远程服务管理", remote_menu)
                
                if status == OK:
                    if index == 1:
                        start_remote_server_menu()
                    elif index == 2:
                        stop_remote_server_menu()
                    elif index == 3:
                        sync_data_menu()
                    elif index == 4:
                        show_route_map_menu()
                    elif index == 5:
                        hide_route_map_menu()
                    elif index == 6:
                        show_endpoints_channels_menu()
                    elif index == 7:
                        hide_endpoints_channels_menu()
                else:
                    next = 0
                    
            except Exception, e:
                output_exception(e)
                continue
                
    except Exception, e:
        output_exception(e)

def start_remote_server_menu():
    '''启动远程服务器菜单'''
    try:
        resp = kcs_ui.answer_req("确认", "是否启动远程服务器？")
        if resp == kcs_util.yes():
            kcs_ui.message_noconfirm("正在启动远程服务器...")
            start_remote_server()
        else:
            kcs_ui.message_noconfirm("取消启动远程服务器")
    except Exception, e:
        output_exception(e)

def stop_remote_server_menu():
    '''停止远程服务器菜单'''
    try:
        resp = kcs_ui.answer_req("确认", "是否停止远程服务器？")
        if resp == kcs_util.yes():
            stop_remote_server()
        else:
            kcs_ui.message_noconfirm("取消停止远程服务器")
    except Exception, e:
        output_exception(e)

def sync_data_menu():
    '''数据同步菜单'''
    try:
        kcs_ui.message_noconfirm("数据同步功能待实现")
        # TODO: 实现数据同步功能
    except Exception, e:
        output_exception(e)

def show_route_map_menu():
    '''显示路线图菜单'''
    try:
        kcs_ui.message_noconfirm("显示路线图功能待实现")
        # TODO: 实现显示路线图功能
    except Exception, e:
        output_exception(e)

def hide_route_map_menu():
    '''隐藏路线图菜单'''
    try:
        kcs_ui.message_noconfirm("隐藏路线图功能待实现")
        # TODO: 实现隐藏路线图功能
    except Exception, e:
        output_exception(e)

def show_endpoints_channels_menu():
    '''显示端点和通道菜单'''
    try:
        kcs_ui.message_noconfirm("显示端点和通道功能待实现")
        # TODO: 实现显示端点和通道功能
    except Exception, e:
        output_exception(e)

def hide_endpoints_channels_menu():
    '''隐藏端点和通道菜单'''
    try:
        kcs_ui.message_noconfirm("隐藏端点和通道功能待实现")
        # TODO: 实现隐藏端点和通道功能
    except Exception, e:
        output_exception(e)

def run():
    '''运行主程序'''
    try:
        kcs_ui.message_noconfirm("启动路线图管理系统...")
        create_main_menu()
    except Exception, e:
        output_exception(e)

if __name__ == "__main__":
    run()
