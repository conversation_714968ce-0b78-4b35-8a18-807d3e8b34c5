#-*- encoding:mbcs -*-
"""
远程服务模块
包含Socket服务器和远程数据同步功能
"""

import wingdbstub
import kcs_ui
import kcs_util
import kcs_draft
import socket
import time
import datetime
import simplejson as json
from IdaExtStruct import get_all_structure_info
from IdaUtlCommon import output_exception

HOST = '0.0.0.0'
PORT = 8000
CHUNK_SIZE = 1024 * 1024  # 1MB 每块（根据网络调整）

# 远程模式状态
remote_mode_enabled = False

def enable_remote_mode():
    '''开启远程模式'''
    global remote_mode_enabled
    remote_mode_enabled = True
    kcs_ui.message_noconfirm("远程模式已开启")

def disable_remote_mode():
    '''关闭远程模式'''
    global remote_mode_enabled
    remote_mode_enabled = False
    kcs_ui.message_noconfirm("远程模式已关闭")

def is_remote_mode_enabled():
    '''检查远程模式是否开启'''
    return remote_mode_enabled

def process_client_data(sock, request_data):
    '''处理客户端请求数据'''
    try:
        # request data的格式为：{"requestCode": "001", "requestData": {"module": "ECA", "structure_name": "*"}}
        request_data_dict = json.loads(request_data)
        request_code = request_data_dict.get("requestCode")
        request_data = request_data_dict.get("requestData")
        kcs_ui.message_noconfirm("request_data: %s" % (request_data))
        
        if request_code == "001":
            # 获取结构件信息，参数为{"module": "ECA", "structure_name": "*"}
            structure_info = get_all_structure_info(request_data.get("module"), request_data.get("structure"))
            send_data_to_client(sock, json.dumps(structure_info))
        elif request_code == "002":
            # 接收处理端显示路线图的请求，参数为handle列表
            show_route_map(request_data.get("handles"))
            send_data_to_client(sock, "")
        elif request_code == "003":
            # 接收处理端隐藏路线图的请求，参数为handle列表
            hide_route_map(request_data.get("handles"))
            send_data_to_client(sock, "")
        elif request_code == "004":
            # 显示端点和通道
            show_endpoints_and_channels(request_data.get("handles"))
            send_data_to_client(sock, "")
        elif request_code == "005":
            # 隐藏端点和通道
            hide_endpoints_and_channels(request_data.get("handles"))
            send_data_to_client(sock, "")
        elif request_code == "006":
            # 数据同步请求
            sync_data_to_server(request_data)
            send_data_to_client(sock, "")
        else:
            # 发送数据给客户端的DEMO
            send_data_to_client(sock, request_data)
            kcs_ui.message_noconfirm("Sent: %s" % (request_data))
    except Exception, e:
        send_data_to_client(sock, "")
        output_exception(e)

def show_route_map(line_handles):
    '''显示路线图'''
    try:
        if not line_handles:
            return
        for handle in line_handles:
            try:
                kcs_draft.element_visibility_set(handle, 1)
            except Exception, e:
                kcs_ui.message_noconfirm("Exception: %s" % (str(e)))
                pass
        kcs_ui.message_noconfirm("已显示 %d 个路线图元素" % len(line_handles))
    except Exception, e:
        output_exception(e)

def hide_route_map(line_handles):
    '''隐藏路线图'''
    try:
        if not line_handles:
            return
        for handle in line_handles:
            try:
                kcs_draft.element_visibility_set(handle, 0)
            except Exception, e:
                kcs_ui.message_noconfirm("Exception: %s" % (str(e)))
                pass
        kcs_ui.message_noconfirm("已隐藏 %d 个路线图元素" % len(line_handles))
    except Exception, e:
        output_exception(e)

def show_endpoints_and_channels(handles):
    '''显示端点和通道'''
    try:
        if not handles:
            return
        for handle in handles:
            try:
                kcs_draft.element_visibility_set(handle, 1)
            except Exception, e:
                pass
        kcs_ui.message_noconfirm("已显示 %d 个端点和通道元素" % len(handles))
    except Exception, e:
        output_exception(e)

def hide_endpoints_and_channels(handles):
    '''隐藏端点和通道'''
    try:
        if not handles:
            return
        for handle in handles:
            try:
                kcs_draft.element_visibility_set(handle, 0)
            except Exception, e:
                pass
        kcs_ui.message_noconfirm("已隐藏 %d 个端点和通道元素" % len(handles))
    except Exception, e:
        output_exception(e)

def sync_data_to_server(data):
    '''数据同步到服务器'''
    try:
        # TODO: 实现数据同步逻辑
        kcs_ui.message_noconfirm("数据同步功能待实现")
        pass
    except Exception, e:
        output_exception(e)

def send_data_to_client(sock, json_str):
    '''发送数据到客户端'''
    try:
        # 1. 编码字符串为字节（Python 2 中 str 本身就是 bytes，但保险起见）
        data = json_str.encode('utf-8')
        total_size = len(data)

        # 2. 构造定长10位长度头部
        header = str(total_size)
        while len(header) < 10:
            header = '0' + header  # 等价于 zfill(10)

        # 3. 发送长度头部（加上\n分隔也可）
        sock.send(header)

        # 4. 分块发送主体
        sent = 0
        while sent < total_size:
            chunk = data[sent:sent + CHUNK_SIZE]
            total_sent = 0
            while total_sent < len(chunk):
                sent_bytes = sock.send(chunk[total_sent:])
                if sent_bytes == 0:
                    raise Exception("Socket connection broken during data send.")
                total_sent += sent_bytes
            sent += total_sent

        # 5. 发送结束标志（字符串即可）
        sock.send('%EOR%')

    except Exception, e:
        output_exception(e)
        try:
            sock.send('%EOR%')
        except:
            pass

def server_task():
    '''启动socket服务器任务'''
    try:
        server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        server_socket.bind((HOST, PORT))
        server_socket.listen(1)
        kcs_ui.message_noconfirm("Server started on %s:%s" % (HOST, PORT))
        
        while True:
            try:
                # 等待客户端连接
                sock, addr = server_socket.accept()
                kcs_ui.message_noconfirm("Client connected from: %s" % str(addr))
                request_data = sock.recv(1024)
                kcs_ui.message_noconfirm("Received: %s" % (request_data))

                # 处理客户端发送的信息
                if request_data == "999\r\n":
                    kcs_ui.message_noconfirm("request_dataXXXX: %s" % (request_data))
                    kcs_ui.message_noconfirm("exit")
                    sock.send("success!%EOR%")
                    sock.close()
                    break  
                else:
                    process_client_data(sock, request_data)
                time.sleep(1)
     
            except Exception, e:  # 捕获所有异常并赋值给e
                # 获取完整异常信息
                output_exception(e)
                continue

    except Exception, e:
        output_exception(e)
        kcs_ui.message_noconfirm("Socket服务器启动失败")

def start_remote_server():
    '''启动远程服务器'''
    try:
        enable_remote_mode()
        server_task()
    except Exception, e:
        output_exception(e)
        disable_remote_mode()

def stop_remote_server():
    '''停止远程服务器'''
    try:
        disable_remote_mode()
        # TODO: 实现优雅关闭服务器的逻辑
        kcs_ui.message_noconfirm("远程服务器已停止")
    except Exception, e:
        output_exception(e)
