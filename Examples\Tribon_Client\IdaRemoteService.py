#-*- encoding:mbcs -*-
"""
杩滅▼鏈嶅姟妯″潡
鍖呭惈Socket鏈嶅姟鍣ㄥ拰杩滅▼鏁版嵁鍚屾鍔熻兘
"""

import wingdbstub
import kcs_ui
import kcs_util
import kcs_draft
import socket
import time
import datetime
import simplejson as json
from IdaExtStruct import get_all_structure_info
from IdaUtlCommon import output_exception

HOST = '0.0.0.0'
PORT = 8000
CHUNK_SIZE = 1024 * 1024  # 1MB 姣忓潡锛堟牴鎹綉缁滆皟鏁达級

# 杩滅▼妯″紡鐘舵€�
remote_mode_enabled = False

def enable_remote_mode():
    '''寮€鍚繙绋嬫ā寮�'''
    global remote_mode_enabled
    remote_mode_enabled = True
    kcs_ui.message_noconfirm("杩滅▼妯″紡宸插紑鍚�")

def disable_remote_mode():
    '''鍏抽棴杩滅▼妯″紡'''
    global remote_mode_enabled
    remote_mode_enabled = False
    kcs_ui.message_noconfirm("杩滅▼妯″紡宸插叧闂�")

def is_remote_mode_enabled():
    '''妫€鏌ヨ繙绋嬫ā寮忔槸鍚﹀紑鍚�'''
    return remote_mode_enabled

def process_client_data(sock, request_data):
    '''澶勭悊瀹㈡埛绔姹傛暟鎹�'''
    try:
        # request data鐨勬牸寮忎负锛歿"requestCode": "001", "requestData": {"module": "ECA", "structure_name": "*"}}
        request_data_dict = json.loads(request_data)
        request_code = request_data_dict.get("requestCode")
        request_data = request_data_dict.get("requestData")
        kcs_ui.message_noconfirm("request_data: %s" % (request_data))
        
        if request_code == "001":
            # 鑾峰彇缁撴瀯浠朵俊鎭紝鍙傛暟涓簕"module": "ECA", "structure_name": "*"}
            structure_info = get_all_structure_info(request_data.get("module"), request_data.get("structure"))
            send_data_to_client(sock, json.dumps(structure_info))
        elif request_code == "002":
            # 鎺ユ敹澶勭悊绔樉绀鸿矾绾垮浘鐨勮姹傦紝鍙傛暟涓篽andle鍒楄〃
            show_route_map(request_data.get("handles"))
            send_data_to_client(sock, "")
        elif request_code == "003":
            # 鎺ユ敹澶勭悊绔殣钘忚矾绾垮浘鐨勮姹傦紝鍙傛暟涓篽andle鍒楄〃
            hide_route_map(request_data.get("handles"))
            send_data_to_client(sock, "")
        elif request_code == "004":
            # 鏄剧ず绔偣鍜岄€氶亾
            show_endpoints_and_channels(request_data.get("handles"))
            send_data_to_client(sock, "")
        elif request_code == "005":
            # 闅愯棌绔偣鍜岄€氶亾
            hide_endpoints_and_channels(request_data.get("handles"))
            send_data_to_client(sock, "")
        elif request_code == "006":
            # 鏁版嵁鍚屾璇锋眰
            sync_data_to_server(request_data)
            send_data_to_client(sock, "")
        else:
            # 鍙戦€佹暟鎹粰瀹㈡埛绔殑DEMO
            send_data_to_client(sock, request_data)
            kcs_ui.message_noconfirm("Sent: %s" % (request_data))
    except Exception, e:
        send_data_to_client(sock, "")
        output_exception(e)

def show_route_map(line_handles):
    '''鏄剧ず璺嚎鍥�'''
    try:
        if not line_handles:
            return
        for handle in line_handles:
            try:
                kcs_draft.element_visibility_set(handle, 1)
            except Exception, e:
                kcs_ui.message_noconfirm("Exception: %s" % (str(e)))
                pass
        kcs_ui.message_noconfirm("宸叉樉绀� %d 涓矾绾垮浘鍏冪礌" % len(line_handles))
    except Exception, e:
        output_exception(e)

def hide_route_map(line_handles):
    '''闅愯棌璺嚎鍥�'''
    try:
        if not line_handles:
            return
        for handle in line_handles:
            try:
                kcs_draft.element_visibility_set(handle, 0)
            except Exception, e:
                kcs_ui.message_noconfirm("Exception: %s" % (str(e)))
                pass
        kcs_ui.message_noconfirm("宸查殣钘� %d 涓矾绾垮浘鍏冪礌" % len(line_handles))
    except Exception, e:
        output_exception(e)

def show_endpoints_and_channels(handles):
    '''鏄剧ず绔偣鍜岄€氶亾'''
    try:
        if not handles:
            return
        for handle in handles:
            try:
                kcs_draft.element_visibility_set(handle, 1)
            except Exception, e:
                pass
        kcs_ui.message_noconfirm("宸叉樉绀� %d 涓鐐瑰拰閫氶亾鍏冪礌" % len(handles))
    except Exception, e:
        output_exception(e)

def hide_endpoints_and_channels(handles):
    '''闅愯棌绔偣鍜岄€氶亾'''
    try:
        if not handles:
            return
        for handle in handles:
            try:
                kcs_draft.element_visibility_set(handle, 0)
            except Exception, e:
                pass
        kcs_ui.message_noconfirm("宸查殣钘� %d 涓鐐瑰拰閫氶亾鍏冪礌" % len(handles))
    except Exception, e:
        output_exception(e)

def sync_data_to_server(data):
    '''鏁版嵁鍚屾鍒版湇鍔″櫒'''
    try:
        # TODO: 瀹炵幇鏁版嵁鍚屾閫昏緫
        kcs_ui.message_noconfirm("鏁版嵁鍚屾鍔熻兘寰呭疄鐜�")
        pass
    except Exception, e:
        output_exception(e)

def send_data_to_client(sock, json_str):
    '''鍙戦€佹暟鎹埌瀹㈡埛绔�'''
    try:
        # 1. 缂栫爜瀛楃涓蹭负瀛楄妭锛圥ython 2 涓� str 鏈韩灏辨槸 bytes锛屼絾淇濋櫓璧疯锛�
        data = json_str.encode('utf-8')
        total_size = len(data)

        # 2. 鏋勯€犲畾闀�10浣嶉暱搴﹀ご閮�
        header = str(total_size)
        while len(header) < 10:
            header = '0' + header  # 绛変环浜� zfill(10)

        # 3. 鍙戦€侀暱搴﹀ご閮紙鍔犱笂\n鍒嗛殧涔熷彲锛�
        sock.send(header)

        # 4. 鍒嗗潡鍙戦€佷富浣�
        sent = 0
        while sent < total_size:
            chunk = data[sent:sent + CHUNK_SIZE]
            total_sent = 0
            while total_sent < len(chunk):
                sent_bytes = sock.send(chunk[total_sent:])
                if sent_bytes == 0:
                    raise Exception("Socket connection broken during data send.")
                total_sent += sent_bytes
            sent += total_sent

        # 5. 鍙戦€佺粨鏉熸爣蹇楋紙瀛楃涓插嵆鍙級
        sock.send('%EOR%')

    except Exception, e:
        output_exception(e)
        try:
            sock.send('%EOR%')
        except:
            pass

def server_task():
    '''鍚姩socket鏈嶅姟鍣ㄤ换鍔�'''
    try:
        server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        server_socket.bind((HOST, PORT))
        server_socket.listen(1)
        kcs_ui.message_noconfirm("Server started on %s:%s" % (HOST, PORT))
        
        while True:
            try:
                # 绛夊緟瀹㈡埛绔繛鎺�
                sock, addr = server_socket.accept()
                kcs_ui.message_noconfirm("Client connected from: %s" % str(addr))
                request_data = sock.recv(1024)
                kcs_ui.message_noconfirm("Received: %s" % (request_data))

                # 澶勭悊瀹㈡埛绔彂閫佺殑淇℃伅
                if request_data == "999\r\n":
                    kcs_ui.message_noconfirm("request_dataXXXX: %s" % (request_data))
                    kcs_ui.message_noconfirm("exit")
                    sock.send("success!%EOR%")
                    sock.close()
                    break  
                else:
                    process_client_data(sock, request_data)
                time.sleep(1)
     
            except Exception, e:  # 鎹曡幏鎵€鏈夊紓甯稿苟璧嬪€肩粰e
                # 鑾峰彇瀹屾暣寮傚父淇℃伅
                output_exception(e)
                continue

    except Exception, e:
        output_exception(e)
        kcs_ui.message_noconfirm("Socket鏈嶅姟鍣ㄥ惎鍔ㄥけ璐�")

def start_remote_server():
    '''鍚姩杩滅▼鏈嶅姟鍣�'''
    try:
        enable_remote_mode()
        server_task()
    except Exception, e:
        output_exception(e)
        disable_remote_mode()

def stop_remote_server():
    '''鍋滄杩滅▼鏈嶅姟鍣�'''
    try:
        disable_remote_mode()
        # TODO: 瀹炵幇浼橀泤鍏抽棴鏈嶅姟鍣ㄧ殑閫昏緫
        kcs_ui.message_noconfirm("杩滅▼鏈嶅姟鍣ㄥ凡鍋滄")
    except Exception, e:
        output_exception(e)
