#-*- encoding:mbcs -*-
"""
路线图业务服务模块
包含手动创建、自动创建、删除路线图等核心业务逻辑
"""

import wingdbstub
import kcs_ui
import kcs_util
import kcs_draft
import KcsModel
import KcsPoint2D
import KcsPoint3D
from KcsPoint2D import Point2D
from KcsPoint3D import Point3D
from KcsRectangle2D import Rectangle2D
from KcsCaptureRegion2D import CaptureRegion2D
from KcsStat_point2D_req import Stat_point2D_req
from KcsCursorType import CursorType

from IdaDto import IdaDtoRouteNode, IdaDtoRoutePath
from IdaDrawUtil import get_near_by_struct, point2D_reqGetStatus, point2D_reqGetButton, delete_structures
from IdaExtStruct import get_structure_info
from IdaHttUtil import general_http_request
from IdaUtlCommon import output_exception

OK = kcs_util.ok()
CANCEL = kcs_util.cancel()
COMPLETE = kcs_util.operation_complete()

def newRoutePathSequential():        
    '''
    用户顺序点选以创建RoutePath
    1. 用户连续选择进行结构件的关联, 按回车完成操作，ESC取消操作
    2. 每次完成关联，
       2.1 创建一个IdaRoutePath对象, 并加入到IdaRoutePath对象List中
       2.2 在结构件上创建红叉标记，以及连线标记
    3. 用户输入回车，调用处理端方法，提交这一批RoutePath
    4. 用户输入ESC，则取消本次操作，并删除已经创建的RoutePath对象，以及对应的标识
    '''
    drawedRouteNodes = []
    drawedRoutePaths = []
    tmpDrawedRoutePath = None
    try:
        while 1:
            point1 = KcsPoint2D.Point2D()
            resp, point1 = kcs_ui.point2D_req('选择节点坐标',point1, point2D_reqGetStatus(), point2D_reqGetButton() )

            if resp == OK:
                try:
                    kcs_ui.message_noconfirm(point1)
                    st = KcsModel.Model()
                    #根据所选坐标点，获取附近结构件
                    get_near_by_struct(point1, st)
                    # 获取结构件的全部信息
                    structure_info = get_structure_info(st.Name)
                    # 获取结构件的重心
                    cog = structure_info['cog']
                    # 实例化一个路径节点
                    idaRouteNode = IdaDtoRouteNode()
                    idaRouteNode.set_baseOnStructCode(st.Name)
                    pointCog=KcsPoint3D.Point3D(cog[0],cog[1],cog[2])
                    idaRouteNode.createAndDrawRouteNodeFlag(pointCog)
                    drawedRouteNodes.append(idaRouteNode)
                    # 实例化一个路径
                    if tmpDrawedRoutePath is None:
                        # 无新路径，则初始化一个路径示例，并设置起始节点
                        tmpDrawedRoutePath = IdaDtoRoutePath();
                        tmpDrawedRoutePath.set_startRouteNode(idaRouteNode)
                    else:
                        # 有新路径，则设置结束节点，并创建路径标识
                        tmpDrawedRoutePath.set_endRouteNode(idaRouteNode)
                        tmpDrawedRoutePath.createAndDrawRoutePathFlag()
                        drawedRoutePaths.append(tmpDrawedRoutePath)
                        # 新建一个路径，起始节点设置为当前节点
                        tmpDrawedRoutePath = IdaDtoRoutePath();
                        tmpDrawedRoutePath.set_startRouteNode(idaRouteNode)
                    continue
                except Exception, e:
                    output_exception(e)
                    continue
            elif resp == COMPLETE:
                # 提交, 调用接口，将数据上传到处理端
                result = general_http_request("rest/createRouteMap", {"routePathList": [p.to_dict() for p in drawedRoutePaths]}, "")
                delete_structures(result['removeStructCodeList'], result['removeHandlesList'])
                break
            elif resp == CANCEL:
                # 回滚
                for idaRouteNode in drawedRouteNodes:
                    idaRouteNode.deleteAndUndrawRouteNodeFlag()
                for idaRoutePath in drawedRoutePaths:
                    idaRoutePath.deleteAndUndrawRoutePathFlag()
                break
            else:
                continue
                
    except Exception, e:
        output_exception(e)
        # 回滚
        for idaRouteNode in drawedRouteNodes:
            idaRouteNode.deleteAndUndrawRouteNodeFlag()
        for idaRoutePath in drawedRoutePaths:
            idaRoutePath.deleteAndUndrawRoutePathFlag()

def newRoutePathBox():        
    '''
    用户范围框选以自动创建RoutePath
    1. 用户框选结构件, 按回车将选择的结构件发送到处理端，处理端返回连线指导后，再次按回车确认，ESC取消
    2. 用户所选的结构件先在处理端完成RoutePath的创建，处理端返回创建的RoutePath列表后，在Tribon中绘制
        2.1 如果用户确认，则调用接口，将数据上传到处理端
        2.2 如果用户取消，则回滚Tribon中绘制的标识
    '''
    try:
        # 用户选择第一个点
        point1 = Point2D()
        resp1, point1 = kcs_ui.point2D_req("Select first corner", point1)
        
        if resp1 != kcs_util.ok():
            kcs_ui.message_noconfirm("Operation cancelled")
            return []

        # 设置矩形选择光标状态
        status = Stat_point2D_req()
        status.SetDefMode('ModeCursor')
        curType = CursorType()
        curType.SetRubberRectangle(point1)
        status.SetCursorType(curType)
        status.SetHelpPoint(point1)

        # 用户选择第二个点
        point2 = Point2D()
        resp2, point2 = kcs_ui.point2D_req("Select second corner", point2, status)
        
        if resp2 != kcs_util.ok():
            kcs_ui.message_noconfirm("Operation cancelled")
            return []

        # 创建矩形区域
        rect = Rectangle2D(point1, point2)
        region = CaptureRegion2D()
        region.SetRectangle(rect)
        region.SetInside()  # 选择矩形内部的元素
        region.SetNoCut()   # 不切割元素

        # 捕获模型句柄
        model_handles = kcs_draft.model_capture(region)
        
        # 获取模型信息
        captured_models = []
        for handle in model_handles:
            try:
                model = KcsModel.Model()
                kcs_draft.model_properties_get(handle, model)
                captured_models.append(model)
            except:
                continue
        
        kcs_ui.message_noconfirm("Captured " + str(len(captured_models)) + " models")
        
        # 过滤出结构件模型
        struct_models = []
        for model in captured_models:
            if model.Type == "struct":
                struct_models.append(model)
        
        kcs_ui.message_noconfirm("Found " + str(len(struct_models)) + " structure models")

        # 调用接口，将选择的结构件发送到处理端，处理端返回连线指导后，再次按回车确认，ESC取消
        # 将选择的结构件发送到处理端
        kcs_ui.message_noconfirm("开始请求处理端...")
        result = general_http_request("rest/GenerateRouteMap", {"structCodes": [m.Name for m in struct_models]}, "")
        kcs_ui.message_noconfirm("完成处理端请求，获得如下路径：")
        # 打印出结果(是个数组，需遍历打印)
        for routePath in result['routePathList']:
            kcs_ui.message_noconfirm("RoutePath: " + str(routePath))
        
        # 处理端返回连线指导
        routePathList = result['routePathList']
        # 用于保存在Tribon中绘制的连线标识
        drawedRoutePaths = []
        drawdRouteNodes = []
        # 在Tribon中绘制连线指导
        for routePath in routePathList:
            idaRoutePath = IdaDtoRoutePath()
            idaRouteStartNode = IdaDtoRouteNode()
            idaRouteEndNode = IdaDtoRouteNode()
            idaRouteStartNode.set_baseOnStructCode(routePath['startStructCode'])
            idaRouteEndNode.set_baseOnStructCode(routePath['endStructCode'])            
            idaRouteStartNode.createAndDrawRouteNodeFlag(idaRouteStartNode.getCog())
            idaRouteEndNode.createAndDrawRouteNodeFlag(idaRouteEndNode.getCog())
            drawdRouteNodes.append(idaRouteStartNode)
            drawdRouteNodes.append(idaRouteEndNode)
            idaRoutePath.set_endRouteNode(idaRouteEndNode)
            idaRoutePath.set_startRouteNode(idaRouteStartNode)            
            idaRoutePath.createAndDrawRoutePathFlag() 
            drawedRoutePaths.append(idaRoutePath)

        # 询问用户是否确认
        resp = kcs_ui.answer_req("确认", "已创建路径图，是否确认？")
        if resp == kcs_util.yes():
            # 用户确认，保持路径
            kcs_ui.message_noconfirm("Route paths confirmed")
            # 调用接口，将数据上传到处理端
            result = general_http_request("rest/createRouteMap", {"routePathList": [p.to_dict() for p in drawedRoutePaths]}, "")
            delete_structures(result['removeStructCodeList'], result['removeHandlesList'])
        else:
            # 用户取消，删除已创建的路径
            for idaRoutePath in drawedRoutePaths:
                idaRoutePath.deleteAndUndrawRoutePathFlag()
            for idaRouteNode in drawdRouteNodes:
                idaRouteNode.deleteAndUndrawRouteNodeFlag()
            kcs_ui.message_noconfirm("Route paths cancelled")
        
        return struct_models
                
    except Exception, e:
        output_exception(e)
        # 回滚操作
        kcs_ui.message_noconfirm("Error occurred during box selection")
        return []

def deleteRoutePathBox():
    '''
    用户框选删除路线图
    TODO: 实现框选删除路线图的功能
    1. 用户框选区域
    2. 识别区域内的路线图元素
    3. 删除选中的路线图
    '''
    # TODO: 实现删除路线图的逻辑
    kcs_ui.message_noconfirm("删除路线图功能待实现")
    pass

def createChannelManual():
    '''
    手动创建通道
    用户依次点选型材或安装件，在两者最近的边上自动创建节点
    （窄边在中心创建，长边在最近点创建）
    TODO: 实现手动创建通道的功能
    '''
    # TODO: 实现手动创建通道的逻辑
    kcs_ui.message_noconfirm("手动创建通道功能待实现")
    pass

def createChannelAuto():
    '''
    自动创建通道
    用户框选，由处理中心返回待创建的通道进行创建
    TODO: 实现自动创建通道的功能
    '''
    # TODO: 实现自动创建通道的逻辑
    kcs_ui.message_noconfirm("自动创建通道功能待实现")
    pass

def deleteChannelBox():
    '''
    删除通道
    用户框选删除已创建的通道和节点
    TODO: 实现框选删除通道的功能
    '''
    # TODO: 实现删除通道的逻辑
    kcs_ui.message_noconfirm("删除通道功能待实现")
    pass

def createRouteForTray(startTray, endTray):
    '''创建两个托架之间的通道'''
    try:
        from IdaDrawUtil import placeRoute
        import KcsVector3D
        import time

        kcs_ui.message_noconfirm("开始请求处理端...")
        result = general_http_request("rest/retrieveTrayShortestPath", {"startStructCode": startTray, "endStructCode": endTray}, "")

        # 不使用回传的rotate，使用固定的
        rotation = KcsVector3D.Vector3D(0,0,1)

        # 遍历路径点，创建路线
        pathPoints = result['pathPoints']
        for i in range(len(pathPoints) - 1):
            startPoint = Point3D(pathPoints[i]['x'], pathPoints[i]['y'], pathPoints[i]['z'])
            endPoint = Point3D(pathPoints[i+1]['x'], pathPoints[i+1]['y'], pathPoints[i+1]['z'])
            routeId = "ROUTE_" + str(i) + "_" + str(int(time.time()))
            placeRoute(routeId, "ECA", "O#1", startPoint, endPoint, rotation)

        kcs_ui.message_noconfirm("通道创建完成")

    except Exception, e:
        output_exception(e)
