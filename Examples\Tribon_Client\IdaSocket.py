#-*- encoding:mbcs -*-
# import sys   #reload()之前必须要引入模
# reload(sys)
# sys.setdefaultencoding("mbcs")
import KcsCaptureRegion2D
import KcsRectangle2D

import wingdbstub

import kcs_model

import kcs_ui
import kcs_util
import KcsStringlist
import kcs_struct
import KcsContour2D
import kcs_draft
import KcsModel
import KcsPoint2D
import KcsRline2D
import sys
import kcs_ui
import kcs_util
import kcs_draft
from KcsPoint2D               import Point2D
from KcsCursorType            import CursorType, CursorTypes
from KcsStat_point2D_req      import Stat_point2D_req, Point2dDefModes
import KcsSymbol
import KcsTransformation2D
import kcs_ic
from KcsButtonState           import ButtonState

import kcs_dex
import IdaUtlCommon

# 网络请求与json处理
import urllib2
import simplejson as json

serverAddress = "http://*************:9999/"

kcs_ui.message_noconfirm("Sssssst" )

OK = kcs_util.ok()
CANCEL  = kcs_util.cancel()


import time
import socket
import datetime
from IdaExtStruct import get_all_structure_info

HOST = '0.0.0.0'
PORT = 8000
CHUNK_SIZE = 1024 * 1024  # 1MB 每块（根据网络调整）


def process_client_data(sock, request_data):
    try:
        # request data的格式为：{"requestCode": "001", "requestData": {"module": "ECA", "structure_name": "*"}}
        request_data_dict = json.loads(request_data)
        request_code = request_data_dict.get("requestCode")
        request_data = request_data_dict.get("requestData")
        kcs_ui.message_noconfirm("request_data: %s" % (request_data))
        if request_code == "001":
            # 获取结构件信息，参数为{"module": "ECA", "structure_name": "*"}
            structure_info = get_all_structure_info(request_data.get("module"), request_data.get("structure"))
            send_data_to_client(sock, json.dumps(structure_info))
        elif request_code == "002":
            # 接收处理端显示路线图的请求，参数为handle列表
            line_handles = request_data.get("handles")
            for handle in line_handles:
                try:
                    kcs_draft.element_visibility_set(handle, 1)
                except Exception, e:
                    kcs_ui.message_noconfirm("Exception: %s" % (str(e)))
                    pass
            send_data_to_client(sock, "")
        elif request_code == "003":
            # 接收处理端隐藏路线图的请求，参数为handle列表
            line_handles = request_data.get("handles")
            for handle in line_handles:
                try:
                    kcs_draft.element_visibility_set(handle, 0)
                except:
                    kcs_ui.message_noconfirm("Exception: %s" % (str(e)))
                    pass
            send_data_to_client(sock, "")
        else:
            # 发送数据给客户端的DEMO
            send_data_to_client(sock, request_data)
            kcs_ui.message_noconfirm("Sent: %s" % (request_data))
    except Exception, e:
        send_data_to_client(sock, "")
        IdaUtlCommon.output_exception(e)


# 发送数据
def send_data_to_client(sock, json_str):
    try:
        # 1. 编码字符串为字节（Python 2 中 str 本身就是 bytes，但保险起见）
        data = json_str.encode('utf-8')
        total_size = len(data)

        # 2. 构造定长10位长度头部
        header = str(total_size)
        while len(header) < 10:
            header = '0' + header  # 等价于 zfill(10)

        # 3. 发送长度头部（加上\n分隔也可）
        sock.send(header)

        # 4. 分块发送主体
        sent = 0
        while sent < total_size:
            chunk = data[sent:sent + CHUNK_SIZE]
            total_sent = 0
            while total_sent < len(chunk):
                sent_bytes = sock.send(chunk[total_sent:])
                if sent_bytes == 0:
                    raise Exception("Socket connection broken during data send.")
                total_sent += sent_bytes
            sent += total_sent

        # 5. 发送结束标志（字符串即可）
        sock.send('%EOR%')

    except Exception, e:
        IdaUtlCommon.output_exception(e)
        try:
            sock.send('%EOR%')
        except:
            pass


# 启动socket服务
def server_task():
    server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    server_socket.bind((HOST, PORT))
    server_socket.listen(1)
    kcs_ui.message_noconfirm("Server started on %s:%s" % (HOST,PORT))
    while True:
        try:
            # 等待客户端连接
            sock, addr = server_socket.accept()
            kcs_ui.message_noconfirm("Client connected from: %s" % str(addr))
            request_data = sock.recv(1024)
            kcs_ui.message_noconfirm("Received: %s" % (request_data))

            # 处理客户端发送的信息
            if request_data == "999\r\n":
                kcs_ui.message_noconfirm("request_dataXXXX: %s" % (request_data))
                kcs_ui.message_noconfirm("exit")
                sock.send("success!%EOR%")
                sock.close()
                break  
            else:
                process_client_data(sock, request_data)
            time.sleep(1)
 
        except Exception, e:  # 捕获所有异常并赋值给e
            # 获取完整异常信息
            IdaUtlCommon.output_exception(e)
            continue
            # if 'sock' in locals():  # 安全关闭socket
            #     sock.close()
            #     kcs_ui.message_noconfirm("Connection closed")
            # break


def run():
    server_task()


run()