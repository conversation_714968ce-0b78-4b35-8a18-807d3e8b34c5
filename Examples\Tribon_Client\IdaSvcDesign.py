# 处理交互设计端的请求

import kcs_ui
import kcs_util
import kcs_draft

import KcsModel
import KcsPoint2D,KcsPoint3D
import KcsModel


import wingdbstub
kcs_ui.message_noconfirm("Debug" )

from IdaUtlDraw import point2D_reqGetStatus, point2D_reqGetButton
import IdaBasDraw, IdaDtoRouteNode

OK = kcs_util.ok()
CANCEL  = kcs_util.cancel()
COMPLETE = kcs_util.operation_complete()

def newRoutePathSequential():        
        '''
        用户顺序点选以创建RoutePath
        1. 用户连续选择进行结构件的关联, 按回车完成操作，ESC取消操作
        2. 每次完成关联，
           2.1 创建一个IdaRoutePath对象, 并加入到IdaRoutePath对象List中
           2.2 在结构件上创建红叉标记，以及连线标记
        3. 用户输入回车，调用处理端方法，提交这一批RoutePath
        4. 用户输入ESC，则取消本次操作，并删除已经创建的RoutePath对象，以及对应的标识
        '''
        while 1:
            point1 = KcsPoint2D.Point2D()
            resp, point1 = kcs_ui.point2D_req('选择节点坐标',point1, point2D_reqGetStatus(), point2D_reqGetButton() )

            if resp == OK:
                kcs_ui.message_noconfirm(point1)
                st = KcsModel.Model()
                #根据所选坐标点，获取附近结构件
                IdaBasDraw.get_near_by_struct(point1, st)
                # 获取结构件的全部信息
                structure_info = IdaBasDraw.get_structure_info(st.Name)
                # 获取结构件的重心
                cog = structure_info['cog']
                # 实例化一个路径节点
                idaRouteNode = IdaDtoRouteNode.IdaDtoRouteNode()
                pointCog=KcsPoint3D.Point3D(cog[0],cog[1],cog[2])
                idaRouteNode.createAndDrawRouteNodeFlag(pointCog)
                continue
            elif resp == COMPLETE:
                # 提交
                break
            else:
                # 回滚
                break   


        