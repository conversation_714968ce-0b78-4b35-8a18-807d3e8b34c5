import IdaUtlCommon

##############################################################################################################################
# 5.UtilDraw部分
##############################################################################################################################    

#-*- encoding:mbcs -*-
# import sys   #reload()之前必须要引入模
# reload(sys)
# sys.setdefaultencoding("mbcs")

import wingdbstub


import kcs_ui
import kcs_util
import KcsPoint2D
import kcs_ui
import kcs_util
import kcs_draft
from KcsPoint2D               import Point2D
from KcsCursorType            import CursorType, CursorTypes
from KcsStat_point2D_req      import Stat_point2D_req, Point2dDefModes
from KcsButtonState           import ButtonState
import KcsColour


# 网络请求与json处理
import simplejson as json


kcs_ui.message_noconfirm("Debug" )

OK = kcs_util.ok()
CANCEL  = kcs_util.cancel()


#-------------------------------------------------------------------------
# 工具方法
#-------------------------------------------------------------------------
def point2D_reqGetStatus():
    status = Stat_point2D_req()
    status.SetDefMode('ModeCursor')
    curType = CursorType()
    curType.SetCrossHair()
    status.SetCursorType(curType)
    helpPoint = KcsPoint2D.Point2D(0.0, 0.0)
    status.SetHelpPoint(helpPoint)
    return status

def point2D_reqGetButton():
    buttons = ButtonState()
    buttons.LockEnabled = 0
    buttons.OptionsEnabled = 1
    buttons.LockChecked = None
    return buttons


# 根据一个中心点，返回其X形状的四个顶点
def generate_x_vertices(center_point, size=1.0):
    cx, cy = center_point.GetX(), center_point.GetY()

    offset = size * 0.70710678118

    top_left = (cx - offset, cy + offset)
    top_right = (cx + offset, cy + offset)
    bottom_left = (cx - offset, cy - offset)
    bottom_right = (cx + offset, cy - offset)

    return (top_left, top_right, bottom_left, bottom_right)

# 根据一个中心点，返回其X形状的四个顶点(Point3D)
def generate_x_vertices_3d(center_point, size=50.0):
    cx, cy, cz = center_point.GetX(), center_point.GetY(), center_point.GetZ()

    offset = size * 0.70710678118

    top_left = (cx - offset/2, cy + offset , cz - offset/2)
    top_right = (cx + offset/2, cy + offset , cz + offset/2)
    bottom_left = (cx - offset/2, cy - offset , cz - offset/2)
    bottom_right = (cx + offset/2, cy - offset , cz + offset/2)

    return (top_left, top_right, bottom_left, bottom_right)

# 定义一个用于选择颜色的枚举
class ColourEnum:
    RED = "RED"
    GREEN = "GREEN"
    BLUE = "BLUE"
    YELLOW = "YELLOW"
    PURPLE = "PURPLE"
    CYAN = "CYAN"
    ORANGE = "ORANGE"
    PINK = "PINK"
    BROWN = "BROWN"
    GRAY = "GRAY"
    BLACK = "BLACK"
    WHITE = "WHITE"

# 根据枚举，返回对应的KcsColour对象
def get_colour_by_enum(colour_enum):
    return KcsColour.Colour(colour_enum.value)


def getColourByName(colour):
    return KcsColour.Colour(colour)


class ComponentCodeEnum:
    XFlag = "O#1"
    # XFlag = "F#10*10"  # 使用扁钢 10x10 作为标识

    

    





##############################################################################################################################
# 4.BasDraw部分
##############################################################################################################################    



#-*- encoding:mbcs -*-
# import sys   #reload()之前必须要引入模
# reload(sys)
# sys.setdefaultencoding("mbcs")


import wingdbstub


import KcsStringlist
import KcsContour2D
import KcsModel
import KcsPoint2D
import KcsRline2D
import sys
from KcsPoint2D               import Point2D
from KcsCursorType            import CursorType, CursorTypes
from KcsStat_point2D_req      import Stat_point2D_req, Point2dDefModes
from KcsButtonState           import ButtonState
import KcsVector3D, KcsColour, kcs_att, kcs_draft, kcs_struct, kcs_util, kcs_ui, kcs_model
from IdaExtStruct import get_structure_info
import random
kcs_ui.message_noconfirm("Start Debug" )

import time

OK = kcs_util.ok()
CANCEL  = kcs_util.cancel()



#-------------------------------------------------------------------------
# 基础方法
#-------------------------------------------------------------------------

def validate_component_code(component_code):
    """
    验证组件代码是否有效
    参数：
    component_code: 组件代码字符串
    返回：
    有效的组件代码字符串
    """
    # 常用的有效组件代码列表
    valid_codes = [
        "F#10*10",      # 扁钢 10x10
        "F#20*10",      # 扁钢 20x10
        "L#100*120*8*10", # 角钢
        "O#1",          # 圆钢
    ]

    if component_code in valid_codes:
        return component_code
    else:
        # 如果不在列表中，尝试使用 pseudoname_profile 生成
        try:
            return kcs_struct.pseudoname_profile('F', 10, 10)  # 默认使用扁钢 10x10
        except:
            return "F#10*10"  # 最后的备选方案
# 获取坐标附近的钢结构件
def get_near_by_struct(pt, model):
    try:
        model.SetType("struct")
        kcs_draft.model_identify(pt, model)
    except:
        kcs_ui.message_noconfirm("No structure found")

#-------------------------------------------------------------------------
# 获取坐标附件的钢结构件
#-------------------------------------------------------------------------
def getNearByStructureByPoint(pt, model):
    model.SetType("struct")
    kcs_draft.model_identify(pt, model)    

# 创建一个结构件的基础方法
def create_structure(nodeId, module, nodeComponent, startPoint, endPoint, colour, rotation=None):
    """
    放置一个用于标识节点的模型, 参数为放置节点标识的3维坐标点
    注意：
    1. nodeId如果超过了22个字符会被截取
    参数：
    nodeId: 节点的ID
    module: 节点所属的模块
    nodeComponent: 节点的结构件类型
    startPoint: 节点的起始坐标
    endPoint: 节点的结束坐标
    colour: 节点的颜色, 枚举
    """
    try:
        # 判断nodeId是否为空，为空则调用接口获取
        # if nodeId is None:
        #     nodeId = get_new_node_id()

        # 检查nodeId长度，截取前22个字符
        # nodeId = nodeId[:22]
        # 生成字符串类型的时间戳作为nodeId，再加一个5位的随机数
        if nodeId is None:
            # nodeId = str(int(time.time())) + str(random.randint(10000,99999))
            result = general_http_request("rest/retrieveStructIdentityCode", {"structType": nodeComponent, "requestAmount": 1}, "")
            kcs_ui.message_noconfirm("Got nodeId: " + str(result[0]))
            nodeId = str(result[0])
        # 判断是否有未save得struct，有则cancel
        try:
            active_struct = kcs_struct.struct_name_get()
            kcs_ui.message_noconfirm("One structure " + active_struct + " is active state")
            kcs_struct.struct_cancel()
        except:
            kcs_ui.message_noconfirm("No active structure.")
        # 如果colour为None, 则使用默认颜色
        if colour is None:
            colour = get_colour_by_enum(ColourEnum.BLUE)
        else:
            colour = getColourByName(colour)
        kcs_struct.struct_new(nodeId, module, colour)
        if rotation is None:
            rotation    = KcsVector3D.Vector3D(0.0, 1.0, 0.0)

        # 验证并获取有效的组件代码
        # valid_component = validate_component_code(nodeComponent)
        profilePart = kcs_struct.profile_new_2point3D(nodeComponent, startPoint, endPoint, rotation)
        kcs_util.coord_to_pos(1, startPoint.GetX())
        save = kcs_struct.struct_save()
        # 显示以上创建的model
        model = KcsModel.Model("struct", nodeId)
        kcs_att.model_save(model)
        kcs_util.coord_to_pos(1, startPoint.GetX())
        kcs_draft.model_draw(model)
        return nodeId,[p._ElementHandle__handle for p in kcs_draft.model_handle_get(model)]
    except Exception, e:        
        IdaUtlCommon.output_exception(e)
        raise
   

# 删除一个结构件的基础方法
def delete_structure(nodeId, handles):
    """
    删除一个结构件
    参数：
    nodeId: 节点的ID
    """
    try:
        #删除handles
        for handle in handles:
            kcs_draft.element_delete(handle)
        #从databank中删除
        kcs_struct.struct_delete(nodeId)
        #刷新绘图
        kcs_draft.dwg_repaint()
    except Exception, e:
        IdaUtlCommon.output_exception(e)
        raise

# 删除多个结构件的基础方法
def delete_structures(nodeIds, handles):
    """
    删除一个结构件
    参数：
    nodeId: 节点的ID
    """
    try:
        #删除handles
        if handles is not None:
            for handle in handles:
                kcs_draft.element_delete(handle)
        #从databank中删除
        if nodeIds is not None:
            for nodeId in nodeIds:
                kcs_struct.struct_delete(nodeId)
        #刷新绘图
        kcs_draft.dwg_repaint()
    except Exception, e:
        IdaUtlCommon.output_exception(e)
        raise    

# 显示或隐藏一个结构件的基础方法
def show_or_hide_structure_by_id(nodeId, isShow):
    """
    显示或隐藏一个结构件
    参数：
    nodeId: 节点的ID
    isShow: 是否显示
    """
    try:
        model = KcsModel.Model("struct", nodeId)
        handle = kcs_draft.element_identify(nodeId)
        if isShow:                    
            kcs_draft.element_visibility_set(handle, 1)    
        else:
            handle = kcs_draft.element_identify(nodeId, 0)
    except Exception, e:
        IdaUtlCommon.output_exception(e)
        raise  

# 创建一个路线图路线
def placeRoute(routeId, module, routeComponent, startPoint, endPoint, rotation):
    """
    放置一个用于通道连接的模型, 参数为连接的起始和结束坐标
    该方法会创建通道
    注意：
    1. routeId如果超过了22个字符会被截取
    """
    try:
        # 检查routeId长度，截取前22个字符
        routeId = routeId[:22]
        # kcs_struct.struct_cancel()
        colour = KcsColour.Colour('BLUE')
        kcs_struct.struct_new(routeId, module, colour)
        if rotation is None:
            rotation    = KcsVector3D.Vector3D(0.0, 1.0, 0.0)
        profilePart = kcs_struct.profile_new_2point3D(routeComponent, startPoint, endPoint, rotation)
        kcs_util.coord_to_pos(1, startPoint.GetX())        
        save = kcs_struct.struct_save()
        # 显示以上创建的model
        model = KcsModel.Model("struct", routeId)
        kcs_att.model_save(model)  
        kcs_util.coord_to_pos(1, startPoint.GetX())              
        kcs_draft.model_draw(model)
    except Exception, e:
        kcs_struct.struct_cancel()
        IdaUtlCommon.output_exception(e)
        raise

# 创建一个路径节点
def placePathNode(nodeId, module, nodeComponent, startPoint, endPoint, rotation=None):
    """
    放置一个用于标识路径节点的模型, 参数为放置节点标识的3维坐标点
    注意：
    1. nodeId如果超过了22个字符会被截取
    返回参数：
    1. nodeId: 节点的ID
    2. model: 节点的模型
    3. handles: 节点的句柄列表

    """
    try:
        # 检查nodeId长度，截取前22个字符
        nodeId = nodeId[:22]
        # kcs_struct.struct_cancel()
        colour = KcsColour.Colour('BLUE')
        kcs_struct.struct_new(nodeId, module, colour)
        if rotation is None:
            rotation = KcsVector3D.Vector3D(0.0, 0.0, 0.0)
        # rotation = KcsVector3D.Vector3D(-0.707106769084930419921875,0.707106769084930419921875,0)
        # rotation = KcsVector3D.Vector3D(-0.707,0.707,0)    
        # rotation = KcsVector3D.Vector3D(0,0,1) 

        kcs_ui.message_noconfirm("旋转为：" + str(rotation))
        kcs_ui.message_noconfirm("开始profilePart")           
        profilePart = kcs_struct.profile_new_2point3D(nodeComponent, startPoint, endPoint, rotation)
        # kcs_struct.misc_comp_new(nodeComponent, startPoint, rotation,rotation)
        kcs_util.coord_to_pos(1, startPoint.GetX())
        save = kcs_struct.struct_save()
        # 显示以上创建的model
        model = KcsModel.Model("struct", nodeId)
        kcs_att.model_save(model)  
        kcs_util.coord_to_pos(1, startPoint.GetX())              
        kcs_draft.model_draw(model)
        return nodeId, model, [p._ElementHandle__handle for p in kcs_draft.model_handle_get(model)]
    except:
        kcs_ui.message_noconfirm("placePathNode出现错误，进入异常处理")
        # IdaUtlCommon.output_exception(e)
        kcs_struct.struct_cancel()
        raise

##############################################################################################################################
# 3.DTO部分
##############################################################################################################################    


from KcsPoint2D import Point2D
import KcsTransformation2D
import kcs_draft
import kcs_ui
from KcsPoint2D import Point2D
from KcsPoint3D import Point3D
from KcsRline2D import Rline2D
from KcsTransformation2D import Transformation2D
import types
import wingdbstub
kcs_ui.message_noconfirm("Debug" )

import IdaUtlDraw, IdaBasDraw

class IdaDtoRouteNode(object):

    '''
    封装路径节点的方法：
    包含路径节点所在结构件代码和两个标识结构件的代码及句柄
    '''

    def __init__(self, baseOnStructCode=None, flagX1StructCode=None, flagX1Handles=None, flagX2StructCode=None, flagX2Handles=None):
        self._baseOnStructCode = baseOnStructCode
        self._flagX1StructCode = flagX1StructCode
        self._flagX1Handles = flagX1Handles
        self._flagX2StructCode = flagX2StructCode
        self._flagX2Handles = flagX2Handles

    def to_dict(self):
        return {
            "baseOnStructCode": self._baseOnStructCode,
            "flagX1StructCode": self._flagX1StructCode,
            "flagX1Handles": self._flagX1Handles,
            "flagX2StructCode": self._flagX2StructCode,
            "flagX2Handles": self._flagX2Handles
        }

    def get_baseOnStructCode(self):
        return self._baseOnStructCode
    def set_baseOnStructCode(self, value):
        self._baseOnStructCode = value
    def get_flagX1StructCode(self):
        return self._flagX1StructCode
    def set_flagX1StructCode(self, value):
        self._flagX1StructCode = value
    def get_flagX1Handles(self):
        return self._flagX1Handles
    def set_flagX1Handles(self, value):
        self._flagX1Handles = value
    def get_flagX2StructCode(self):
        return self._flagX2StructCode
    def set_flagX2StructCode(self, value):
        self._flagX2StructCode = value
    def get_flagX2Handles(self):
        return self._flagX2Handles
    def set_flagX2Handles(self, value):
        self._flagX2Handles = value

    # 使用 property 定义属性
    baseOnStructCode = property(get_baseOnStructCode, set_baseOnStructCode, doc="路径节点所在结构件code")
    flagX1StructCode = property(get_flagX1StructCode, set_flagX1StructCode, doc="路径节点标识1的结构件code")
    flagX1Handles = property(get_flagX1Handles, set_flagX1Handles, doc="路径节点标识1的结构件句柄列表")
    flagX2StructCode = property(get_flagX2StructCode, set_flagX2StructCode, doc="路径节点标识2的结构件code")
    flagX2Handles = property(get_flagX2Handles, set_flagX2Handles, doc="路径节点标识2的结构件句柄列表")

    def __str__(self):
        return "baseOnStructCode: " + str(self._baseOnStructCode) + ", flagX1StructCode: " + str(self._flagX1StructCode) + ", flagX2StructCode: " + str(self._flagX2StructCode)

    def getCog(self):
        cog = get_structure_info(self._baseOnStructCode)['cog']
        pointCog=KcsPoint3D.Point3D(cog[0],cog[1],cog[2])
        return pointCog

    def createAndDrawRouteNodeFlag(self, cog):
        '''
        创建一个路径节点，并在绘图中标识
        参数：
        cog: 三维重心坐标(KcsPoint3D.Point3D对象)
        '''
        try:
            # 获取绘图中第一个视图的句柄
            handler = kcs_draft.element_child_first_get()
            # 设置当前视图
            kcs_draft.subpicture_current_set(handler)
            # 获取以重心为中心的X形状四个顶点，传入的参数为三维重心点
            verticesX = generate_x_vertices_3d(cog)
            # 创建 Point3D 对象
            top_left = Point3D(verticesX[0][0], verticesX[0][1], verticesX[0][2])
            top_right = Point3D(verticesX[1][0], verticesX[1][1], verticesX[1][2])
            bottom_left = Point3D(verticesX[2][0], verticesX[2][1], verticesX[2][2])
            bottom_right = Point3D(verticesX[3][0], verticesX[3][1], verticesX[3][2])
            # 创建两个Structure，使用属性设置器
            self._flagX1StructCode, self._flagX1Handles = create_structure(None, "ECA" , ComponentCodeEnum.XFlag, top_left, bottom_right, ColourEnum.RED )
            self._flagX2StructCode, self._flagX2Handles = create_structure(None, "ECA" , ComponentCodeEnum.XFlag, top_right, bottom_left, ColourEnum.RED )
        except Exception, e:        
            IdaUtlCommon.output_exception(e)
            raise
        
    def deleteAndUndrawRouteNodeFlag(self):
        # 删除两个Structure
        if self._flagX1StructCode and self._flagX1Handles:
            delete_structure(self._flagX1StructCode, self._flagX1Handles)
        if self._flagX2StructCode and self._flagX2Handles:
            delete_structure(self._flagX2StructCode, self._flagX2Handles)

  

class IdaDtoRoutePath(object):
    '''
    封装两个struct的关系，包括如下属性：
    开始RouteNode
    结束RouteNode
    关系边structCode
    关系边句柄列表
    '''

    def __init__(self, startRouteNode=None, endRouteNode=None, relationEdgeFlagStructCode=None, relationHandles=None):
        self._startRouteNode = startRouteNode
        self._endRouteNode = endRouteNode
        self._relationEdgeFlagStructCode = relationEdgeFlagStructCode
        self._relationHandles = relationHandles

    def to_dict(self):
        return {
            "startRouteNode": self._startRouteNode.to_dict(),
            "endRouteNode": self._endRouteNode.to_dict(),
            "relationEdgeFlagStructCode": self._relationEdgeFlagStructCode,
            "relationHandles": self._relationHandles
        }

    def get_startRouteNode(self):
        return self._startRouteNode
    def set_startRouteNode(self, value):
        self._startRouteNode = value
    def get_endRouteNode(self):
        return self._endRouteNode
    def set_endRouteNode(self, value):
        self._endRouteNode = value
    def get_relationEdgeFlagStructCode(self):
        return self._relationEdgeFlagStructCode
    def set_relationEdgeFlagStructCode(self, value):
        self._relationEdgeFlagStructCode = value
    def get_relationHandles(self):
        return self._relationHandles
    def set_relationHandles(self, value):
        self._relationHandles = value

    # 使用 property 定义属性
    startRouteNode = property(get_startRouteNode, set_startRouteNode, doc="开始路径节点")
    endRouteNode = property(get_endRouteNode, set_endRouteNode, doc="结束路径节点")
    relationEdgeFlagStructCode = property(get_relationEdgeFlagStructCode, set_relationEdgeFlagStructCode, doc="关系边结构件代码")
    relationHandles = property(get_relationHandles, set_relationHandles, doc="关系边结构件句柄列表")






    def __str__(self):
        return "startRouteNode: " + str(self._startRouteNode) + ", endRouteNode: " + str(self._endRouteNode) + ", relationEdgeFlagStructCode: " + str(self._relationEdgeFlagStructCode)
    
    # 调用http，由处理端生成标识structCode的方法
    def generateFlagStructCode(self):
        pass

    # 同步路线图节点关系到处理端的方法
    def syncToServer(self):
        # 调用http，将关系同步到处理端
        # 同步失败时，需要回滚在Tribon中创建的标识
        pass

    def createAndDrawRoutePathFlag(self):
        '''
        创建一个路径，并在绘图中标识
        '''
        try:
            handler = kcs_draft.element_child_first_get()
            # 设置当前视图
            kcs_draft.subpicture_current_set(handler)
            # 获取两个模型的重心
            cog1 = get_structure_info(self._startRouteNode.baseOnStructCode)['cog']
            pointCog1=KcsPoint3D.Point3D(cog1[0],cog1[1],cog1[2])
            cog2 = get_structure_info(self._endRouteNode.baseOnStructCode)['cog']
            pointCog2=KcsPoint3D.Point3D(cog2[0],cog2[1],cog2[2])

            # 创建关系边，使用属性设置器
            self._relationEdgeFlagStructCode, self._relationHandles = create_structure(None, "ECA" , ComponentCodeEnum.XFlag, pointCog1, pointCog2, ColourEnum.RED )
            
        except Exception, e:        
            IdaUtlCommon.output_exception(e)
            raise
        
    def deleteAndUndrawRoutePathFlag(self):
        # 删除关系边Structure
        if self._relationEdgeFlagStructCode and self._relationHandles:
            delete_structure(self._relationEdgeFlagStructCode, self._relationHandles)


    
class IdaDtoPathNode(object):
    '''
    封装路径节点的方法：
    包含路径节点的定义，绘制
    '''


  

##############################################################################################################################
# 2. Service部分
##############################################################################################################################    

# 处理交互设计端的请求

import kcs_ui
import kcs_util
import kcs_draft

import KcsModel
import KcsPoint2D,KcsPoint3D
import KcsModel

from IdaHttUtil import general_http_request

import wingdbstub
kcs_ui.message_noconfirm("Debug" )

OK = kcs_util.ok()
CANCEL  = kcs_util.cancel()
COMPLETE = kcs_util.operation_complete()

def newRoutePathSequential():        
    '''
    用户顺序点选以创建RoutePath
    1. 用户连续选择进行结构件的关联, 按回车完成操作，ESC取消操作
    2. 每次完成关联，
        2.1 创建一个IdaRoutePath对象, 并加入到IdaRoutePath对象List中
        2.2 在结构件上创建红叉标记，以及连线标记
    3. 用户输入回车，调用处理端方法，提交这一批RoutePath
    4. 用户输入ESC，则取消本次操作，并删除已经创建的RoutePath对象，以及对应的标识
    '''
    drawedRouteNodes = []
    drawedRoutePaths = []
    tmpDrawedRoutePath = None
    try:
        while 1:
            point1 = KcsPoint2D.Point2D()
            resp, point1 = kcs_ui.point2D_req('选择节点坐标',point1, point2D_reqGetStatus(), point2D_reqGetButton() )

            if resp == OK:
                try:
                    kcs_ui.message_noconfirm(point1)
                    st = KcsModel.Model()
                    #根据所选坐标点，获取附近结构件
                    get_near_by_struct(point1, st)
                    # 获取结构件的全部信息
                    structure_info = get_structure_info(st.Name)
                    # 获取结构件的重心
                    cog = structure_info['cog']
                    # 实例化一个路径节点
                    idaRouteNode = IdaDtoRouteNode()
                    idaRouteNode.set_baseOnStructCode(st.Name)
                    pointCog=KcsPoint3D.Point3D(cog[0],cog[1],cog[2])
                    idaRouteNode.createAndDrawRouteNodeFlag(pointCog)
                    drawedRouteNodes.append(idaRouteNode)
                    # 实例化一个路径
                    if tmpDrawedRoutePath is None:
                        # 无新路径，则初始化一个路径示例，并设置起始节点
                        tmpDrawedRoutePath = IdaDtoRoutePath();
                        tmpDrawedRoutePath.set_startRouteNode(idaRouteNode)
                    else:
                        # 有新路径，则设置结束节点，并创建路径标识
                        tmpDrawedRoutePath.set_endRouteNode(idaRouteNode)
                        tmpDrawedRoutePath.createAndDrawRoutePathFlag()
                        drawedRoutePaths.append(tmpDrawedRoutePath)
                        # 新建一个路径，起始节点设置为当前节点
                        tmpDrawedRoutePath = IdaDtoRoutePath();
                        tmpDrawedRoutePath.set_startRouteNode(idaRouteNode)
                    continue
                except Exception, e:
                    IdaUtlCommon.output_exception(e)
                    continue
            elif resp == COMPLETE:
                # 提交, 调用接口，将数据上传到处理端
                result = general_http_request("rest/createRouteMap", {"routePathList": [p.to_dict() for p in drawedRoutePaths]}, "")
                delete_structures(result['removeStructCodeList'], result['removeHandlesList'])
                break
            elif resp == CANCEL:
                # 回滚
                for idaRouteNode in drawedRouteNodes:
                    idaRouteNode.deleteAndUndrawRouteNodeFlag()
                for idaRoutePath in drawedRoutePaths:
                    idaRoutePath.deleteAndUndrawRoutePathFlag()
                break
            else:
                continue
                
    except Exception, e:
        IdaUtlCommon.output_exception(e)
        # 回滚
        for idaRouteNode in drawedRouteNodes:
            idaRouteNode.deleteAndUndrawRouteNodeFlag()
        for idaRoutePath in drawedRoutePaths:
            idaRoutePath.deleteAndUndrawRoutePathFlag()

import kcs_ui
import kcs_util
import kcs_draft
import KcsModel
from KcsPoint2D import Point2D
from KcsPoint3D import Point3D
from KcsRectangle2D import Rectangle2D
from KcsCaptureRegion2D import CaptureRegion2D
from KcsStat_point2D_req import Stat_point2D_req
from KcsCursorType import CursorType

def newRoutePathBox():
    '''
    用户范围框选以自动创建RoutePath
    1. 用户框选结构件, 按回车将选择的结构件发送到处理端，处理端返回连线指导后，再次按回车确认，ESC取消
    2. 用户所选的结构件先在处理端完成RoutePath的创建，处理端返回创建的RoutePath列表后，在Tribon中绘制
        2.1 如果用户确认，则调用接口，将数据上传到处理端
        2.2 如果用户取消，则回滚Tribon中绘制的标识
    '''
    try:
        # 用户选择第一个点
        point1 = Point2D()
        resp1, point1 = kcs_ui.point2D_req("Select first corner", point1)

        if resp1 != kcs_util.ok():
            kcs_ui.message_noconfirm("Operation cancelled")
            return

        # 设置矩形选择光标状态
        status = Stat_point2D_req()
        status.SetDefMode('ModeCursor')
        curType = CursorType()
        curType.SetRubberRectangle(point1)
        status.SetCursorType(curType)
        status.SetHelpPoint(point1)

        # 用户选择第二个点
        point2 = Point2D()
        resp2, point2 = kcs_ui.point2D_req("Select second corner", point2, status)

        if resp2 != kcs_util.ok():
            kcs_ui.message_noconfirm("Operation cancelled")
            return

        # 创建矩形区域
        rect = Rectangle2D(point1, point2)
        region = CaptureRegion2D()
        region.SetRectangle(rect)
        region.SetInside()  # 选择矩形内部的元素
        region.SetNoCut()   # 不切割元素

        # 捕获模型句柄
        model_handles = kcs_draft.model_capture(region)

        # 获取模型信息
        captured_models = []
        for handle in model_handles:
            try:
                model = KcsModel.Model()
                kcs_draft.model_properties_get(handle, model)
                captured_models.append(model)
            except:
                continue

        kcs_ui.message_noconfirm("Captured " + str(len(captured_models)) + " models")

        # 打印捕获的模型名称（用于调试）
        for model in captured_models:
            kcs_ui.message_noconfirm("Model: " + model.Name + " Type: " + model.Type)

        # 过滤出结构件模型
        struct_models = []
        for model in captured_models:
            if model.Type == "struct":
                struct_models.append(model)

        kcs_ui.message_noconfirm("Found " + str(len(struct_models)) + " structure models")

        # 调用接口，将选择的结构件发送到处理端，处理端返回连线指导后，再次按回车确认，ESC取消
        # 将选择的结构件发送到处理端
        kcs_ui.message_noconfirm("开始请求处理端...")
        result = general_http_request("rest/GenerateRouteMap", {"structCodes": [m.Name for m in struct_models]}, "")
        kcs_ui.message_noconfirm("完成处理端请求，获得如下路径：")
        # 打印出结果(是个数组，需遍历打印)
        for routePath in result['routePathList']:
            kcs_ui.message_noconfirm("RoutePath: " + str(routePath))
        
        # 处理端返回连线指导
        routePathList = result['routePathList']
        # 用于保存在Tribon中绘制的连线标识
        drawedRoutePaths = []
        drawdRouteNodes = []
        # 在Tribon中绘制连线指导
        for routePath in routePathList:
            idaRoutePath = IdaDtoRoutePath()
            idaRouteStartNode = IdaDtoRouteNode()
            idaRouteEndNode = IdaDtoRouteNode()
            idaRouteStartNode.set_baseOnStructCode(routePath['startStructCode'])
            idaRouteEndNode.set_baseOnStructCode(routePath['endStructCode'])            
            idaRouteStartNode.createAndDrawRouteNodeFlag(idaRouteStartNode.getCog())
            idaRouteEndNode.createAndDrawRouteNodeFlag(idaRouteEndNode.getCog())
            drawdRouteNodes.append(idaRouteStartNode)
            drawdRouteNodes.append(idaRouteEndNode)
            idaRoutePath.set_endRouteNode(idaRouteEndNode)
            idaRoutePath.set_startRouteNode(idaRouteStartNode)            
            idaRoutePath.createAndDrawRoutePathFlag() 
            drawedRoutePaths.append(idaRoutePath)
            # kcs_ui.message_noconfirm("Created " + str(len(drawedRoutePaths)) + " route paths")
        # 询问用户是否确认

        
        resp = kcs_ui.answer_req("确认", "已创建路径图，是否确认？")
        if resp == kcs_util.yes():
            # 用户确认，保持路径
            kcs_ui.message_noconfirm("Route paths confirmed")
            # 调用接口，将数据上传到处理端
            result = general_http_request("rest/createRouteMap", {"routePathList": [p.to_dict() for p in drawedRoutePaths]}, "")
            delete_structures(result['removeStructCodeList'], result['removeHandlesList'])
        else:
            # 用户取消，删除已创建的路径
            for idaRoutePath in drawedRoutePaths:
                idaRoutePath.deleteAndUndrawRoutePathFlag()
            for idaRouteNode in drawdRouteNodes:
                idaRouteNode.deleteAndUndrawRouteNodeFlag()
            kcs_ui.message_noconfirm("Route paths cancelled")

        # 返回结构件模型列表供后续处理
        return struct_models

    except Exception, e:
        IdaUtlCommon.output_exception(e)
        # 回滚操作
        kcs_ui.message_noconfirm("Error occurred during box selection")
        for idaRoutePath in drawedRoutePaths:
            idaRoutePath.deleteAndUndrawRoutePathFlag()
        for idaRouteNode in drawdRouteNodes:
            idaRouteNode.deleteAndUndrawRouteNodeFlag()
        kcs_ui.message_noconfirm("Route paths cancelled")
        raise

# 废弃，待删除
# def processSelectedStructures(struct_models):
#     '''
#     处理框选的结构件，创建路径节点和路径
#     参数：
#     struct_models: 框选得到的结构件模型列表
#     '''
#     try:
#         drawedRouteNodes = []

#         # 为每个结构件创建路径节点
#         for model in struct_models:
#             # 获取结构件的全部信息
#             structure_info = get_structure_info(model.Name)
#             # 获取结构件的重心
#             cog = structure_info['cog']

#             # 实例化一个路径节点
#             idaRouteNode = IdaDtoRouteNode()
#             idaRouteNode.baseOnStructCode = model.Name
#             pointCog = KcsPoint3D.Point3D(cog[0], cog[1], cog[2])
#             idaRouteNode.createAndDrawRouteNodeFlag(pointCog)
#             drawedRouteNodes.append(idaRouteNode)

#         # kcs_ui.message_noconfirm("Created " + str(len(drawedRouteNodes)) + " route nodes")

#         # 询问用户是否确认
#         resp = kcs_ui.answer_req("Confirm route nodes creation?", "Yes", "No")
#         if resp[0] == kcs_util.ok() and resp[1] == 1:
#             # 用户确认，保持节点
#             kcs_ui.message_noconfirm("Route nodes confirmed")
#             return drawedRouteNodes
#         else:
#             # 用户取消，删除已创建的节点
#             for idaRouteNode in drawedRouteNodes:
#                 idaRouteNode.deleteAndUndrawRouteNodeFlag()
#             kcs_ui.message_noconfirm("Route nodes cancelled")
#             return []

#     except Exception, e:
#         IdaUtlCommon.output_exception(e)
#         # 回滚操作
#         for idaRouteNode in drawedRouteNodes:
#             idaRouteNode.deleteAndUndrawRouteNodeFlag()
#         return []

# 废弃，待删除
# def newRoutePathBoxAndProcess():
#     '''
#     框选区域并处理结构件的完整流程
#     '''
#     # 框选获取结构件
#     struct_models = newRoutePathBox()

#     if len(struct_models) > 0:
#         # 处理选中的结构件
#         route_nodes = processSelectedStructures(struct_models)
#         return route_nodes
#     else:
#         kcs_ui.message_noconfirm("No structures selected")
#         return []


##############################################################################################################################
# 一. Adapter部分
##############################################################################################################################

# Adapter部分
# 交换设计端连接器，用于接收来自Tribon 绘图交互的请求入口

#1. 用户点选，手动创建路线图
# newRoutePathSequential()

# 废弃，待删除
#2. 用户框选，自动创建路线图
# newRoutePathBoxAndProcess()

#3. 测试框选功能，自动创建路线图
def testBoxSelection():
    '''
    测试框选功能
    '''
    try:
        models = newRoutePathBox()
        kcs_ui.message_noconfirm("Test completed. Found " + str(len(models)) + " models")
    except Exception, e:
        IdaUtlCommon.output_exception(e)
        kcs_ui.message_noconfirm("Test failed")

# 运行测试
testBoxSelection()

#4. 测试数据抽取
# from IdaExtStruct import dex_extract # 临时加入，仅用于测试数据抽取
# structureName = "LD-0719-00001"
# componentName = dex_extract("STRU.ITEM('" + structureName + "').GRO(1).PART_ID(1).COMP_N")[0].get('value').strip()
# componentType = dex_extract("COMP('" + componentName + "').GEN_PR.TYP")[0].get('value')
# kcs_ui.message_noconfirm("componentType: " + str(componentType))
# componentSize = dex_extract("COMP('" + componentName + "').GEN_PR.SIZ")[0].get('value')
# kcs_ui.message_noconfirm("componentSize: " + str(componentType))
# componentHeight = dex_extract("COMP('" + componentName + "').ACC.ACC_HE").get('value')
# kcs_ui.message_noconfirm("componentHeight: " + str(componentHeight))

# 5. 创建两个托架的通道
def createRouteForTray(startTray, endTray):
    try:
        # 选择startTray

        kcs_ui.message_noconfirm("开始请求处理端...")
        result = general_http_request("rest/retrieveTrayShortestPath", {"startStructCode": startTray, "endStructCode": endTray}, "")
        # kcs_ui.message_noconfirm("完成处理端请求，获得如下路径：" + str(result))
        

        # 不使用回传的rotate，使用固定的
        rotation = KcsVector3D.Vector3D(0,0,1) 

        startTrayPoint3DP1 = KcsPoint3D.Point3D(result['startLine']['startPoint']['x'], result['startLine']['startPoint']['y'], result['startLine']['startPoint']['z'])
        startTrayPoint3DP2 = KcsPoint3D.Point3D(result['startLine']['endPoint']['x'], result['startLine']['endPoint']['y'], result['startLine']['endPoint']['z'])
        endTrayPoint3Dp1 = KcsPoint3D.Point3D(result['endLine']['startPoint']['x'], result['endLine']['startPoint']['y'], result['endLine']['startPoint']['z'])
        endTrayPoint3Dp2 = KcsPoint3D.Point3D(result['endLine']['endPoint']['x'], result['endLine']['endPoint']['y'], result['endLine']['endPoint']['z'])
        # 创建路径节点
        node1Id = None
        node2Id = None
        node1Code = "ECA-TS-" + str(int(time.time()))
        node1Id, node1Model, node1Handles = placePathNode(node1Code, "ECA", 'SQU#125', startTrayPoint3DP1, startTrayPoint3DP2, rotation)
        node2Code = "ECA-TE-" + str(int(time.time()))
        node2Id, node2Model, node2Handles = placePathNode(node2Code, "ECA", 'SQU#125', endTrayPoint3Dp1, endTrayPoint3Dp2, rotation)

        resp = kcs_ui.answer_req("确认", "已创建，是否确认？")
        if resp == kcs_util.yes():
            # 用户确认，保持路径
            kcs_ui.message_noconfirm("Path Nodes confirmed")
        else:
            # 用户取消，删除已创建的节点
            delete_structure(node1Id, node1Handles)
            delete_structure(node2Id, node2Handles)
            kcs_ui.message_noconfirm("Route paths cancelled")
    except:
        kcs_ui.message_noconfirm("创建节点失败，回退操作")
        # IdaUtlCommon.output_exception(e)
        try:
            if node1Id is not None and node1Handles is not None:
                delete_structure(node1Id, node1Handles)
        except:
            pass            
        try:
            if node2Id is not None and node2Handles is not None:
                delete_structure(node2Id, node2Handles)
        except:
            pass    
        raise

# 测试z创建两个托架的通道
try:
    point1 = KcsPoint2D.Point2D()
    resp, point1 = kcs_ui.point2D_req('选择安装件坐标',point1, point2D_reqGetStatus(), point2D_reqGetButton() )

    if resp == OK:
        kcs_ui.message_noconfirm(point1)
        startTray = KcsModel.Model()
        #根据所选坐标点，获取附近结构件
        get_near_by_struct(point1, startTray)
        kcs_ui.message_noconfirm("起始托架：" + startTray.Name)
        point2 = KcsPoint2D.Point2D()
        resp, point2 = kcs_ui.point2D_req('选择安装件坐标',point2, point2D_reqGetStatus(), point2D_reqGetButton() )
        if resp == OK:
            kcs_ui.message_noconfirm(point2)
            endTray = KcsModel.Model()
            get_near_by_struct(point2, endTray)
            kcs_ui.message_noconfirm("结束托架：" + endTray.Name)
            createRouteForTray(startTray.Name, endTray.Name)
except Exception, e:
    IdaUtlCommon.output_exception(e)


# import KcsStat_point3D_req
#6. 测试更新struct的属性
# def Update():
#    try:
#       # coordinate for node point for cableway material
#       stat    = KcsStat_point3D_req.Stat_point3D_req()
#       point   = KcsPoint3D.Point3D()
#       res     = kcs_ui.point3D_req("Indicate start point", stat, point)
#       # vector route
#       route   = KcsVector3D.Vector3D(-1.0, 0.0, 1.0)
#       # vector rotation
#       rotat   = KcsVector3D.Vector3D(0.0, 0.0, 1.0)
#       # length
#     #   length  = kcs_ui.int_req("Key in length")
#     #   # width
#     #   width  = kcs_ui.int_req("Key in width")
#     #   # height
#     #   height  = kcs_ui.int_req("Key in height")
#       # call structure function
#       kcs_struct.struct_cway_data(point, route, rotat,0,0,0)
#    except:
#       kcs_ui.message_noconfirm("Invalid input !" )
#       print kcs_ui.error

# Update()      