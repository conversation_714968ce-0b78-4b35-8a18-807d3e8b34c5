#-*- encoding:mbcs -*-
# import sys   #reload()之前必须要引入模
# reload(sys)
# sys.setdefaultencoding("mbcs")

import wingdbstub


import kcs_ui
import kcs_util
import KcsPoint2D
import kcs_ui
import kcs_util
import kcs_draft
from KcsPoint2D               import Point2D
from KcsCursorType            import CursorType, CursorTypes
from KcsStat_point2D_req      import Stat_point2D_req, Point2dDefModes
from KcsButtonState           import ButtonState
import KcsColour


# 网络请求与json处理
import simplejson as json


kcs_ui.message_noconfirm("Debug" )

OK = kcs_util.ok()
CANCEL  = kcs_util.cancel()


#-------------------------------------------------------------------------
# 工具方法
#-------------------------------------------------------------------------
def point2D_reqGetStatus():
    status = Stat_point2D_req()
    status.SetDefMode('ModeCursor')
    curType = CursorType()
    curType.SetCrossHair()
    status.SetCursorType(curType)
    helpPoint = KcsPoint2D.Point2D(0.0, 0.0)
    status.SetHelpPoint(helpPoint)
    return status

def point2D_reqGetButton():
    buttons = ButtonState()
    buttons.LockEnabled = 0
    buttons.OptionsEnabled = 1
    buttons.LockChecked = None
    return buttons


# 根据一个中心点，返回其X形状的四个顶点
def generate_x_vertices(center_point, size=1.0):
    cx, cy = center_point.GetX(), center_point.GetY()

    offset = size * 0.70710678118

    top_left = (cx - offset, cy + offset)
    top_right = (cx + offset, cy + offset)
    bottom_left = (cx - offset, cy - offset)
    bottom_right = (cx + offset, cy - offset)

    return (top_left, top_right, bottom_left, bottom_right)

# 定义一个用于选择颜色的枚举
class ColourEnum:
    RED = "RED"
    GREEN = "GREEN"
    BLUE = "BLUE"
    YELLOW = "YELLOW"
    PURPLE = "PURPLE"
    CYAN = "CYAN"
    ORANGE = "ORANGE"
    PINK = "PINK"
    BROWN = "BROWN"
    GRAY = "GRAY"
    BLACK = "BLACK"
    WHITE = "WHITE"

# 根据枚举，返回对应的KcsColour对象
def get_colour_by_enum(colour_enum):
    return KcsColour.Colour(colour_enum.value)


def getColourByName(colour):
    return KcsColour.Colour(colour)


class ComponentCodeEnum:
    # XFlag = "O#1"
    XFlag = "SQU#125"

    

    







