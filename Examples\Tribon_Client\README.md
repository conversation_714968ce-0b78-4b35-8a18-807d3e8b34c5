# Tribon Client 路线图管理系统

## 项目结构重构说明

本项目已经重构为模块化的架构，将原来的 `IdaTstMain.py` 和 `IdaSocket.py` 拆分为多个职责明确的模块。

## 核心文件结构

### 主要模块

1. **IdaMainMenu.py** - 主菜单模块
   - 整合所有功能的菜单界面
   - 提供用户交互入口
   - 调用各个业务模块

2. **IdaRouteService.py** - 路线图业务服务模块
   - 手动创建路线图 (`newRoutePathSequential`)
   - 自动创建路线图 (`newRoutePathBox`)
   - 删除路线图 (`deleteRoutePathBox`)
   - 手动创建通道 (`createChannelManual`)
   - 自动创建通道 (`createChannelAuto`)
   - 删除通道 (`deleteChannelBox`)

3. **IdaRemoteService.py** - 远程服务模块
   - Socket服务器管理
   - 远程数据同步
   - 显示/隐藏路线图和通道
   - 远程模式控制

4. **IdaDto.py** - 数据传输对象模块
   - `IdaDtoRouteNode` - 路径节点数据结构
   - `IdaDtoRoutePath` - 路径数据结构
   - `IdaDtoPathNode` - 路径节点数据结构（待完善）

5. **IdaDrawUtil.py** - 绘图工具模块
   - 结构件创建、删除、显示隐藏
   - 绘图基础工具函数
   - 颜色和组件代码枚举

### 依赖模块

6. **IdaExtStruct.py** - 结构件信息提取模块
7. **IdaHttUtil.py** - HTTP请求工具模块
8. **IdaHttRequest.py** - HTTP请求封装模块
9. **IdaUtlCommon.py** - 通用工具模块

## 菜单功能

### 主菜单选项

1. **开启/关闭远程模式** - 切换远程模式状态
2. **手动创建路线图** - 用户依次点选型材或安装件创建路线图
3. **自动创建路线图** - 用户框选区域自动创建路线图
4. **删除路线图** - 用户框选删除路线图
5. **手动创建通道** - 用户依次点选创建通道
6. **自动创建通道** - 用户框选自动创建通道
7. **删除通道** - 用户框选删除通道
8. **远程服务管理** - 远程服务相关功能

### 远程服务管理子菜单

1. **启动远程服务器** - 启动Socket服务器
2. **停止远程服务器** - 停止Socket服务器
3. **数据同步** - 与处理端同步数据
4. **显示路线图** - 显示路线图元素
5. **隐藏路线图** - 隐藏路线图元素
6. **显示端点和通道** - 显示端点和通道元素
7. **隐藏端点和通道** - 隐藏端点和通道元素

## 使用方法

### 启动系统

```python
# 运行主菜单
python IdaMainMenu.py
```

或者在 Tribon 环境中：

```python
from IdaMainMenu import run
run()
```

### 功能状态

#### 已实现功能 ✅
- 手动创建路线图
- 自动创建路线图（框选）
- 远程服务器基础框架
- 数据传输对象结构
- 绘图工具基础功能

#### 待完善功能 🚧
- 删除路线图
- 手动创建通道
- 自动创建通道
- 删除通道
- 数据同步
- 显示/隐藏路线图和通道的UI操作

## 备份文件

以下文件已移动到 `backup/` 目录：

- `IdaTstMain.py` - 原主文件（已拆分）
- `IdaSocket.py` - 原Socket服务文件（已拆分）
- `IdaAdpDesign.py` - 适配器设计文件
- `IdaDtoRouteNode.py` - 原DTO文件（已重构）
- `IdaSvcDesign.py` - 服务设计文件
- `IdaVitesseInspect.py` - 检查工具文件
- `IdaHttBase.py` - HTTP基础文件

## 开发说明

### 添加新功能

1. 在相应的服务模块中添加业务逻辑
2. 在 `IdaMainMenu.py` 中添加菜单选项
3. 如需新的数据结构，在 `IdaDto.py` 中定义
4. 如需新的绘图功能，在 `IdaDrawUtil.py` 中实现

### TODO 标记

代码中使用 `# TODO:` 标记了需要进一步完善的功能，这些功能的框架已经搭建好，等待具体需求确认后实现。

### 错误处理

所有模块都使用 `IdaUtlCommon.output_exception()` 进行统一的异常处理和日志输出。

## Python 2.3 兼容性

本项目已针对 Python 2.3 进行了兼容性优化：

### 修复的语法问题

1. **多行导入语法** - 将多行导入改为单行导入
   ```python
   # 修复前（不兼容）
   from IdaRouteService import (
       newRoutePathSequential,
       newRoutePathBox
   )

   # 修复后（兼容）
   from IdaRouteService import newRoutePathSequential, newRoutePathBox
   ```

2. **列表推导式** - 将列表推导式改为传统循环
   ```python
   # 修复前（可能不兼容）
   result = [p.to_dict() for p in drawedRoutePaths]

   # 修复后（兼容）
   result = []
   for p in drawedRoutePaths:
       result.append(p.to_dict())
   ```

3. **异常处理语法** - 使用 Python 2.3 兼容的异常处理
   ```python
   # 正确的 Python 2.3 语法
   except Exception, e:
       output_exception(e)
   ```

### 启动方式

推荐使用以下文件启动系统：

1. **start.py** - 完整启动文件，包含详细的导入调试信息
2. **simple_test.py** - 简单测试文件，验证基本功能
3. **minimal_test.py** - 最小化测试，逐步验证每个模块

## 注意事项

1. 确保 Tribon 环境正确配置
2. 所有网络请求需要配置正确的服务器地址
3. 远程模式需要确保网络连接正常
4. 建议在测试环境中先验证功能后再在生产环境使用
5. **Python 版本要求**: 兼容 Python 2.3 及以上版本
