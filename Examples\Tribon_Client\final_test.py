#-*- encoding:mbcs -*-
"""
最终测试文件 - Python 2.3 兼容性验证
"""

def test_syntax():
    """测试基本语法兼容性"""
    print("Testing Python 2.3 syntax compatibility...")
    
    # 测试异常处理语法
    try:
        x = 1 / 0
    except Exception, e:
        print("Exception handling syntax OK")
    
    # 测试字典创建
    test_dict = {
        "key1": "value1",
        "key2": "value2"
    }
    print("Dictionary syntax OK")
    
    # 测试列表操作（替代列表推导式）
    source_list = [1, 2, 3, 4, 5]
    result_list = []
    for item in source_list:
        result_list.append(item * 2)
    print("List operations OK")
    
    print("Basic syntax test passed!")

def test_imports():
    """测试模块导入"""
    print("Testing module imports...")
    
    try:
        # 测试单行导入
        from IdaUtlCommon import output_exception
        print("IdaUtlCommon import OK")
        
        # 测试多个导入
        from IdaDrawUtil import ColourEnum, ComponentCodeEnum
        print("IdaDrawUtil import OK")
        
        # 测试类导入
        from IdaDto import IdaDtoRouteNode, IdaDtoRoutePath
        print("IdaDto import OK")
        
        # 测试函数导入
        from IdaRouteService import newRoutePathSequential
        print("IdaRouteService import OK")
        
        # 测试远程服务导入
        from IdaRemoteService import enable_remote_mode
        print("IdaRemoteService import OK")
        
        print("All imports successful!")
        return True
        
    except Exception, e:
        print("Import failed: " + str(e))
        return False

def test_functionality():
    """测试基本功能"""
    print("Testing basic functionality...")
    
    try:
        from IdaDto import IdaDtoRouteNode, IdaDtoRoutePath
        from IdaDrawUtil import ColourEnum, ComponentCodeEnum
        
        # 测试对象创建
        node = IdaDtoRouteNode()
        path = IdaDtoRoutePath()
        print("Object creation OK")
        
        # 测试属性设置
        node.set_baseOnStructCode("TEST_STRUCT")
        print("Property setting OK")
        
        # 测试属性获取
        struct_code = node.get_baseOnStructCode()
        print("Property getting OK")
        
        # 测试字典转换
        node_dict = node.to_dict()
        print("Dictionary conversion OK")
        
        # 测试枚举访问
        color = ColourEnum.RED
        component = ComponentCodeEnum.XFlag
        print("Enum access OK")
        
        print("Functionality test passed!")
        return True
        
    except Exception, e:
        print("Functionality test failed: " + str(e))
        return False

def main():
    """主测试函数"""
    print("=" * 50)
    print("Python 2.3 Compatibility Test")
    print("=" * 50)
    
    # 运行所有测试
    test_syntax()
    print("-" * 30)
    
    import_success = test_imports()
    print("-" * 30)
    
    if import_success:
        functionality_success = test_functionality()
    else:
        functionality_success = False
    
    print("-" * 30)
    
    # 总结
    if import_success and functionality_success:
        print("ALL TESTS PASSED!")
        print("System is ready for Python 2.3")
    else:
        print("SOME TESTS FAILED!")
        print("Please check the error messages above")
    
    print("=" * 50)

if __name__ == "__main__":
    main()
