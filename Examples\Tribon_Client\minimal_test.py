#-*- encoding:mbcs -*-

# 最小化测试 - 逐步测试每个导入

print("Step 1: Testing basic Python")
try:
    x = 1
    y = 2
    z = x + y
    print("Basic Python OK")
except Exception, e:
    print("Basic Python failed: " + str(e))

print("Step 2: Testing single import")
try:
    from IdaUtlCommon import output_exception
    print("IdaUtlCommon import OK")
except Exception, e:
    print("IdaUtlCommon import failed: " + str(e))

print("Step 3: Testing multiple imports from same module")
try:
    from IdaDrawUtil import ColourEnum
    from IdaDrawUtil import ComponentCodeEnum
    print("IdaDrawUtil imports OK")
except Exception, e:
    print("IdaDrawUtil imports failed: " + str(e))

print("Step 4: Testing comma-separated imports")
try:
    from IdaRouteService import newRoutePathSequential, newRoutePathBox
    print("IdaRouteService comma imports OK")
except Exception, e:
    print("IdaRouteService comma imports failed: " + str(e))

print("Step 5: Testing class imports")
try:
    from IdaDto import IdaDtoRouteNode
    from IdaDto import IdaDtoRoutePath
    print("IdaDto class imports OK")
except Exception, e:
    print("IdaDto class imports failed: " + str(e))

print("Step 6: Testing function call")
try:
    node = IdaDtoRouteNode()
    print("Class instantiation OK")
except Exception, e:
    print("Class instantiation failed: " + str(e))

print("Minimal test completed")
