#-*- encoding:mbcs -*-
"""
简单测试文件 - Python 2.3 兼容
"""

print("Starting simple test...")

try:
    print("Testing basic imports...")
    
    # 测试基础模块导入
    import kcs_ui
    import kcs_util
    import KcsStringlist
    print("Basic KCS imports successful")
    
    # 测试自定义模块导入
    from IdaUtlCommon import output_exception
    print("IdaUtlCommon import successful")
    
    from IdaDrawUtil import ColourEnum, ComponentCodeEnum
    print("IdaDrawUtil import successful")
    
    from IdaDto import IdaDtoRouteNode, IdaDtoRoutePath
    print("IdaDto import successful")
    
    from IdaRouteService import newRoutePathSequential
    print("IdaRouteService import successful")
    
    from IdaRemoteService import enable_remote_mode
    print("IdaRemoteService import successful")
    
    from IdaMainMenu import create_main_menu
    print("IdaMainMenu import successful")
    
    print("All imports successful!")
    
    # 测试基本功能
    print("Testing basic functionality...")
    
    # 测试DTO创建
    node = IdaDtoRouteNode()
    print("DTO creation successful")
    
    # 测试枚举
    color = ColourEnum.RED
    component = ComponentCodeEnum.XFlag
    print("Enum access successful")
    
    print("Simple test completed successfully!")
    
except Exception, e:
    print("Error occurred: " + str(e))
    try:
        output_exception(e)
    except:
        import traceback
        traceback.print_exc()
