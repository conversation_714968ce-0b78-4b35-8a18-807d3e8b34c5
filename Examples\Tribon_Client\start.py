#-*- encoding:mbcs -*-
"""
启动文件 - Python 2.3 兼容版本
"""

print("正在启动路线图管理系统...")

try:
    # 逐步导入，便于调试
    print("导入基础模块...")
    import kcs_ui
    import kcs_util
    import KcsStringlist
    
    print("导入工具模块...")
    from IdaUtlCommon import output_exception
    
    print("导入绘图模块...")
    from IdaDrawUtil import ColourEnum, ComponentCodeEnum
    
    print("导入数据模块...")
    from IdaDto import IdaDtoRouteNode, IdaDtoRoutePath
    
    print("导入业务模块...")
    # 单独导入每个函数，避免多行导入语法
    from IdaRouteService import newRoutePathSequential
    from IdaRouteService import newRoutePathBox
    from IdaRouteService import deleteRoutePathBox
    from IdaRouteService import createChannelManual
    from IdaRouteService import createChannelAuto
    from IdaRouteService import deleteChannelBox
    
    print("导入远程服务模块...")
    from IdaRemoteService import start_remote_server
    from IdaRemoteService import stop_remote_server
    from IdaRemoteService import is_remote_mode_enabled
    from IdaRemoteService import enable_remote_mode
    from IdaRemoteService import disable_remote_mode
    
    print("导入主菜单模块...")
    from IdaMainMenu import create_main_menu
    
    print("所有模块导入成功!")
    
    # 启动主菜单
    print("启动主菜单...")
    create_main_menu()
    
except Exception, e:
    print("启动失败: " + str(e))
    try:
        output_exception(e)
    except:
        import traceback
        traceback.print_exc()

print("系统已退出")
