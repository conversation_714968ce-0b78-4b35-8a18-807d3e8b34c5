#-*- encoding:mbcs -*-
"""
测试主文件
用于测试新的模块结构
"""

import wingdbstub

try:
    # 测试导入所有模块
    print("Testing imports...")
    
    from IdaMainMenu import run
    from IdaRouteService import newRoutePathSequential, newRoutePathBox
    from IdaRemoteService import enable_remote_mode, disable_remote_mode
    from IdaDto import IdaDtoRouteNode, IdaDtoRoutePath
    from IdaDrawUtil import ColourEnum, ComponentCodeEnum
    
    print("All imports successful!")
    
    # 运行主菜单
    print("Starting main menu...")
    run()
    
except Exception as e:
    print("Error occurred: " + str(e))
    import traceback
    traceback.print_exc()
