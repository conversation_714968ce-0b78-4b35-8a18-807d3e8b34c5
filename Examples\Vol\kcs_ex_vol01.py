#----------------------------------------------------------------------------------
#
#      NAME:
#
#          kcs_ex_volumes.py
#
#      PURPOSE:
#
#               This program presents kcs_vol: volumes handling.
#
#
#
#----------------------------------------------------------------------------------
import kcs_vol
import kcs_ui
import kcs_util


#----------------------------------------------------------------------------------
# Create main menu
#-------------------------------------------------------------------------------------------------------------

while 1:
   actions = (
      'Volume New',
      'Volume Close',
      'Volume Open',
      'Volume Save',
      'Volume Save As',
      'Volume Exists',
      'Volume Delete'
      )

   (status, index) = kcs_ui.choice_select('Volumes','Volumes', actions)

   if status == kcs_util.ok():
      if index == 1:
         res, name= kcs_ui.string_req('Enter Name:','')
         res, ext= kcs_ui.string_req('Enter Extension:','')
         fFloat = float(ext)
         kcs_vol.vol_new(name, fFloat)
      elif index == 2:
         kcs_vol.vol_close()
      elif index == 3:
         res, name= kcs_ui.string_req('Enter Name:','')
         res, ext= kcs_ui.string_req('Enter Extension:','')
         fFloat = float(ext)
         kcs_vol.vol_open(name, fFloat)
      elif index == 4:
         kcs_vol.vol_save()
      elif index == 5:
         res, name= kcs_ui.string_req('Enter Name:','')
         kcs_vol.vol_save_as(name)
      elif index == 6:
         res, name= kcs_ui.string_req('Enter Name:','')
         res = kcs_vol.vol_exist(name)
         if res == 1:
            answer = kcs_ui.message_confirm('Volume Exist')
         if res == 0:
            answer = kcs_ui.message_confirm('Volume Does not Exist')
      elif index == 7:
         res, name= kcs_ui.string_req('Enter Name:','')
         kcs_vol.vol_delete(name)
   else:
      print "User interrupted!"
      break;


#----------------------------------------------------------------------------------





