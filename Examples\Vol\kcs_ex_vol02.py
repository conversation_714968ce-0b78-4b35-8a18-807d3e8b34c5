#----------------------------------------------------------------------------------
#
#      NAME:
#
#          kcs_ex_vol02.py
#
#      PURPOSE:
#
#               This program presents kcs_vol: primitives handling.
#
#
#
#----------------------------------------------------------------------------------
import kcs_vol
import kcs_draft
import string
import kcs_ui
import kcs_util
import KcsPoint3D
import KcsPoint2D
import KcsArc3D
import KcsVector3D
import KcsContour2D
import KcsColour
import KcsModel
from KcsRline2D import Rline2D
from KcsText import Text
import KcsVolPrimitiveBase
import KcsVolPrimitiveBlock
import KcsVolPrimitiveRevolution
import KcsVolPrimitiveGeneralCylinder
import KcsVolPrimitiveTorusSegment
import KcsVolPrimitiveTruncatedCone
import KcsVolPrimitiveRevolution
import KcsVolPrimitiveSphericalCap
import KcsBox
from KcsStat_point2D_req      import Stat_point2D_req, Point2dDefModes
from KcsStat_point3D_req      import Stat_point3D_req
from KcsCursorType            import CursorType, CursorTypes


#----------------------------------------------------------------------------------
# Create main menu
#-------------------------------------------------------------------------------------------------------------
def SelectPrimitive():
   res = kcs_vol.subvol_list()
   strList = []
   for item in res:
      strList.append(str(item))
   (status1, index1) = kcs_ui.string_select('Subvolumes', 'Subvolumes Select:', '', strList)
   if status1 == kcs_util.ok():
     list = kcs_vol.prim_list(res[index1-1])
     strList = []
     for item in list:
         strList.append(str(item))
     (status2, index2) = kcs_ui.string_select('Primitives', 'Primitives Found:', '', strList)
   if (status2 == kcs_util.ok()):
      return [res[index1-1], list[index2-1]]
#-------------------------------------------------------------------------------------------------------------
def SelectSubvolume():
   res = kcs_vol.subvol_list()
   strList = []
   for item in res:
      strList.append(str(item))
   (status, index) = kcs_ui.string_select('Subvolumes', 'Subvolumes Select:', '', strList)
   if status == kcs_util.ok():
      return res[index-1]
#-------------------------------------------------------------------------------------------------------------
def EditProperties(obj):
   listing = PropertiesToList(obj)
   (status, index) = kcs_ui.string_select('Properties', 'Edit Properties:', '', listing)
   if status == kcs_util.ok():
      res, location = kcs_ui.string_req('Enter Colour:','')
      col = KcsColour.Colour(location)
      obj.SetColour(col)
      res, location = kcs_ui.string_req('Enter Density:','')
      fFloat = float(location)
      obj.SetDensity(fFloat)
      res, location = kcs_ui.string_req('Enter Softness:','')
      nInt = int(location)
      obj.SetSoftness(nInt)
      listing = PropertiesToList(obj)

   return [status, obj]

#-------------------------------------------------------------------------------------------------------------

def IndicateContour():                                          # asks user to indicate line by 2 points
   point1 = KcsPoint2D.Point2D()
   point2 = KcsPoint2D.Point2D()


   prompt = 'Indicate contour start point, OC to exit'
   resp, point1 = kcs_ui.point2D_req(prompt, point1)
   contour = KcsContour2D.Contour2D(point1)
   status = Stat_point2D_req()
   status.SetDefMode('ModeCursor')
   CurType = CursorType()
   CurType.SetRubberBand(point1)
   status.SetCursorType(CurType)
   status.SetHelpPoint(point1)
   resp, point2 = kcs_ui.point2D_req('Second point', point2, status)
   contour.AddLine(point2)
   answer = kcs_ui.answer_req('Contour', 'More Segments ?')

   while answer == 201:
      status = Stat_point2D_req()
      status.SetDefMode('ModeCursor')

      CurType = CursorType()
      CurType.SetRubberBand(point2)
      status.SetCursorType(CurType)
      status.SetHelpPoint(point2)

      resp, point2 = kcs_ui.point2D_req('Next point', point2, status)
      if resp == kcs_util.ok():
         contour.AddLine(point2)
         answer = kcs_ui.answer_req('Contour', 'More Segments ?')
      else:
         answer = 202
   return contour

#-------------------------------------------------------------------------------------------------------------

def PropertiesToList(properties):

   if not isinstance(properties, KcsVolPrimitiveBase.VolPrimitiveBase):
      return []
   list = []
   res = properties.GetColour()
   list.append('Colour: ' + res.Name())
   res = properties.GetDensity()
   list.append('Density: ' + str(res))
   res = properties.GetSoftness()
   list.append('Softness: ' + str(res))

   return list

#-------------------------------------------------------------------------------------------------------------

def RedrawVolume():
   name = kcs_vol.vol_name_get()
   model = KcsModel.Model('unplaced volume', name)
   kcs_draft.model_draw( model )
   kcs_draft.dwg_repaint()

#-------------------------------------------------------------------------------------------------------------

def CreateBlock():
   point = KcsPoint3D.Point3D()
   orgPt = KcsPoint3D.Point3D(0,0,0)
   uAxis = KcsVector3D.Vector3D(1,0,0)
   vAxis = KcsVector3D.Vector3D(0,1,0)
   wLen  = 400
   uLen  = 100
   vLen  = 200

   status = kcs_util.ok()

   while status == kcs_util.ok() :
      actions = (
         'Origin: '+str(orgPt),
         'U Axis: '+str(uAxis),
         'Create')

      (status, index) = kcs_ui.choice_select('Create Block','', actions)
      if status == kcs_util.ok():
         if index == 1:
            stat = Stat_point3D_req()
            stat.Initial3D = 1
            (resp, orgPt) = kcs_ui.point3D_req('Select origin point', stat, orgPt)
         elif index == 2:
            stat = Stat_point3D_req()
            stat.Initial3D = 1
            stat.Helppnt  = orgPt
            stat.Helpdef  = 1
            stat.Locktype = 0
            (resp, point) = kcs_ui.point3D_req('Select U Axis', stat, point )
            if resp == kcs_util.ok():
               uAxis.SetFromVectorDifference( KcsVector3D.Vector3D(point.X, point.Y, point.Z), KcsVector3D.Vector3D(orgPt.X, orgPt.Y, orgPt.Z) )
               uLen = uAxis.Length()
               vLen = uLen * 2
               if uAxis.X != 0 or uAxis.Y != 0:
                  vAxis = KcsVector3D.Vector3D( -uAxis.Y , uAxis.X, 0 )
               else:
                  vAxis = KcsVector3D.Vector3D( 0, -uAxis.Z , uAxis.Y )
         elif index == 3:

            wLen = vLen*2.0

            block =  KcsVolPrimitiveBlock.VolPrimitiveBlock()
            block.Box = KcsBox.Box( orgPt, uAxis, vAxis, wLen, vLen, uLen)
            block.Box.WidthDir = uAxis

            nSubVol = 1
            number = kcs_vol.prim_add(nSubVol, block)
            break


def CreateGeneralCylinder():

   point = KcsPoint3D.Point3D()
   orgPt = KcsPoint3D.Point3D(0,0,0)
   uAxis = KcsVector3D.Vector3D(1,0,0)
   vAxis = KcsVector3D.Vector3D(0,1,0)
   wLen  = 100.0

   status = kcs_util.ok()

   while status == kcs_util.ok() :
      actions = (
         'Origin: '+str(orgPt),
         'U Axis: '+str(uAxis),
         'Create')

      (status, index) = kcs_ui.choice_select('Create general cylinder','', actions)
      if status == kcs_util.ok():
         if index == 1:
            stat = Stat_point3D_req()
            stat.Initial3D = 1
            (resp, orgPt) = kcs_ui.point3D_req('Select origin point', stat, orgPt)
         elif index == 2:
            stat = Stat_point3D_req()
            stat.Initial3D = 1
            stat.Helppnt  = orgPt
            stat.Helpdef  = 1
            stat.Locktype = 0
            (resp, point) = kcs_ui.point3D_req('Select U Axis', stat, point )
            if resp == kcs_util.ok():
               uAxis.SetFromVectorDifference( KcsVector3D.Vector3D(point.X, point.Y, point.Z), KcsVector3D.Vector3D(orgPt.X, orgPt.Y, orgPt.Z) )
               uLen = uAxis.Length()
               if uAxis.X != 0 or uAxis.Y != 0:
                  vAxis = KcsVector3D.Vector3D( -uAxis.Y , uAxis.X, 0 )
               else:
                  vAxis = KcsVector3D.Vector3D( 0, -uAxis.Z , uAxis.Y )
         elif index == 3:
            uAxis.SetToUnitVector()
            vAxis.SetToUnitVector()
            cylinder =  KcsVolPrimitiveGeneralCylinder.VolPrimitiveGeneralCylinder()
            cylinder.SetOrigin( orgPt )
            cylinder.SetUAxis( uAxis )
            cylinder.SetVAxis( vAxis )
            cylinder.SetHeight( wLen )
            cont = KcsContour2D.Contour2D(KcsPoint2D.Point2D(0,0))
            cont.AddLine(KcsPoint2D.Point2D(200, 0))
            cont.AddLine(KcsPoint2D.Point2D(0, 100))
            cont.AddArc(KcsPoint2D.Point2D(0, 0),50)
            cylinder.SetContour(cont)
            nInt = 1
            number = kcs_vol.prim_add(nInt, cylinder)
            break

def CreateTruncatedCone():
   point = KcsPoint3D.Point3D()
   orgPt = KcsPoint3D.Point3D(0,0,0)
   uAxis = KcsVector3D.Vector3D(1,0,0)
   uLen  = 100.0
   topDiam = 100.0
   botDiam = 200.0

   status = kcs_util.ok()

   while status == kcs_util.ok() :
      actions = (
         'Origin: '+str(orgPt),
         'U Axis: '+str(uAxis),
         'Bot diam: '+str(botDiam),
         'Top diam: '+str(topDiam),
         'Create')

      (status, index) = kcs_ui.choice_select('Create truncated cone','', actions)
      if status == kcs_util.ok():
         if index == 1:
            stat = Stat_point3D_req()
            stat.Initial3D = 1
            (resp, orgPt) = kcs_ui.point3D_req('Select origin point', stat, orgPt)
         elif index == 2:
            stat = Stat_point3D_req()
            stat.Initial3D = 1
            stat.Helppnt  = orgPt
            stat.Helpdef  = 1
            stat.Locktype = 0
            (resp, point) = kcs_ui.point3D_req('Select U Axis', stat, point )
            if resp == kcs_util.ok():
               uAxis.SetFromVectorDifference( KcsVector3D.Vector3D(point.X, point.Y, point.Z), KcsVector3D.Vector3D(orgPt.X, orgPt.Y, orgPt.Z) )
               uLen = uAxis.Length()
         elif index == 3:
            res, vaulue = kcs_ui.string_req('Enter Bottom Diameter:','')
            if res == kcs_util.ok():
               botDiam = float(vaulue)
         elif index == 4:
            res, vaulue = kcs_ui.string_req('Enter Top Diameter:','')
            if res == kcs_util.ok():
               topDiam = float(vaulue)
         elif index == 5:
            cone =  KcsVolPrimitiveTruncatedCone.VolPrimitiveTruncatedCone()
            cone.SetOrigin( orgPt )
            cone.SetUAxis( uAxis )
            cone.SetHeight(uLen)
            cone.SetDiameters(botDiam, topDiam)
            nSubVol = 1
            number = kcs_vol.prim_add(nSubVol, cone)
            break

def CreateTorus():
   startPt = KcsPoint3D.Point3D(0,0,0)
   endPt = KcsPoint3D.Point3D(500,0,0)
   amp   = KcsVector3D.Vector3D( 0,20,0)
   diam  = 100

   status = kcs_util.ok()

   while status == kcs_util.ok() :
      actions = (
         'Start: '+str(startPt),
         'End: '+str(endPt),
         'Radius: '+str(diam),
         'Create')

      (status, index) = kcs_ui.choice_select('Create torus','', actions)
      if status == kcs_util.ok():
         if index == 1:
            stat = Stat_point3D_req()
            stat.Initial3D = 1
            (resp, startPt) = kcs_ui.point3D_req('Select start point', stat, startPt)
         elif index == 2:
            stat = Stat_point3D_req()
            stat.Initial3D = 1
            (resp, endPt) = kcs_ui.point3D_req('Select end point', stat, endPt)
         elif index == 3:
            res, vaulue = kcs_ui.string_req('Enter Radius:','')
            if res == kcs_util.ok():
               diam = float(vaulue)
         elif index == 4:
            torus = KcsVolPrimitiveTorusSegment.VolPrimitiveTorusSegment()
            arc = KcsArc3D.Arc3D(startPt , endPt , amp)
            torus.SetArc(arc)
            torus.SetRadius( diam )
            nSubVol = 1
            number = kcs_vol.prim_add(nSubVol, torus)
            break

def CreateRevolution():
   point = KcsPoint3D.Point3D()
   orgPt = KcsPoint3D.Point3D(0,0,0)
   uAxis = KcsVector3D.Vector3D(1,0,0)
   uLen  = 100.0
   topDiam = 100.0
   botDiam = 200.0

   status = kcs_util.ok()

   while status == kcs_util.ok() :
      actions = (
         'Origin: '+str(orgPt),
         'U Axis: '+str(uAxis),
         'Create')

      (status, index) = kcs_ui.choice_select('Create revolution','', actions)
      if status == kcs_util.ok():
         if index == 1:
            stat = Stat_point3D_req()
            stat.Initial3D = 1
            (resp, orgPt) = kcs_ui.point3D_req('Select origin point', stat, orgPt)
         elif index == 2:
            stat = Stat_point3D_req()
            stat.Initial3D = 1
            stat.Helppnt  = orgPt
            stat.Helpdef  = 1
            stat.Locktype = 0
            (resp, point) = kcs_ui.point3D_req('Select U Axis', stat, point )
            if resp == kcs_util.ok():
               uAxis.SetFromVectorDifference( KcsVector3D.Vector3D(point.X, point.Y, point.Z), KcsVector3D.Vector3D(orgPt.X, orgPt.Y, orgPt.Z) )
               uLen = uAxis.Length()
         elif index == 3:
            revol = KcsVolPrimitiveRevolution.VolPrimitiveRevolution()
            revol.SetUAxis(uAxis)
            revol.SetOrigin( orgPt )
            cont = KcsContour2D.Contour2D(KcsPoint2D.Point2D(100,0))
            cont.AddLine(KcsPoint2D.Point2D(200, 0))
            cont.AddLine(KcsPoint2D.Point2D(200, 100))
            cont.AddLine(KcsPoint2D.Point2D(100, 100))
            cont.AddLine(KcsPoint2D.Point2D(100, 0))
            revol.SetContour(cont)
            nSubVol = 1
            number = kcs_vol.prim_add(nSubVol, revol)
            break

def CreateCap():
   point = KcsPoint3D.Point3D()
   orgPt = KcsPoint3D.Point3D(0,0,0)
   uAxis = KcsVector3D.Vector3D(1,0,0)
   uLen  = 100.0
   rad   = 200.0
   ampl  = 100.0

   status = kcs_util.ok()

   while status == kcs_util.ok() :
      actions = (
         'Origin   : '+str(orgPt),
         'U Axis   : '+str(uAxis),
         'Radius   : '+str(rad),
         'Amplitude: '+str(ampl),
         'Create')

      (status, index) = kcs_ui.choice_select('Create cap','', actions)
      if status == kcs_util.ok():
         if index == 1:
            stat = Stat_point3D_req()
            stat.Initial3D = 1
            (resp, orgPt) = kcs_ui.point3D_req('Select origin point', stat, orgPt)
         elif index == 2:
            stat = Stat_point3D_req()
            stat.Initial3D = 1
            stat.Helppnt  = orgPt
            stat.Helpdef  = 1
            stat.Locktype = 0
            (resp, point) = kcs_ui.point3D_req('Select U Axis', stat, point )
            if resp == kcs_util.ok():
               uAxis.SetFromVectorDifference( KcsVector3D.Vector3D(point.X, point.Y, point.Z), KcsVector3D.Vector3D(orgPt.X, orgPt.Y, orgPt.Z) )
               uLen = uAxis.Length()
         elif index == 3:
            res, vaulue = kcs_ui.string_req('Enter Radius:','')
            if res == kcs_util.ok():
               rad = float(vaulue)
         elif index == 4:
            res, vaulue = kcs_ui.string_req('Enter Amplitude:','')
            if res == kcs_util.ok():
               ampl = float(vaulue)
         elif index == 5:
            cap = KcsVolPrimitiveSphericalCap.VolPrimitiveSphericalCap()
            cap.SetUAxis( uAxis )
            cap.SetOrigin( orgPt )
            cap.SetRadius( rad )
            cap.SetAmplitude( ampl )
            nSubVol = 1
            number = kcs_vol.prim_add(nSubVol, cap)
            break

#-------------------------------------------------------------------------------------------------------------

while 1:
   actions = (
      'Add Primitive (Block)',
      'Add Primitive (General Cylinder)',
      'Add Primitive (Truncated Cone)',
      'Add Primitive (Torus Segment)',
      'Add Primitive (Revolution)',
      'Add Primitive (Spherical Cap)',
      'Primitive List',
      'Properties Set',
      'Properties Get',
      'Primitive Delete'
      )

   (status, index) = kcs_ui.choice_select('Primitives','Primitives', actions)

   if status == kcs_util.ok():
      if index == 1:
         CreateBlock()
         RedrawVolume()
      elif index == 2:
         CreateGeneralCylinder()
         RedrawVolume()
      elif index == 3:
         CreateTruncatedCone()
         RedrawVolume()
      elif index == 4:
         CreateTorus()
         RedrawVolume()
      elif index == 5:
         CreateRevolution()
         RedrawVolume()
      elif index == 6:
         CreateCap()
         RedrawVolume()
      elif index == 7:
         nInt = SelectSubvolume()
         list = kcs_vol.prim_list(int(nInt))
         strList = []
         for item in list:
            strList.append(str(item))
         (status2, index3) = kcs_ui.string_select('Primitives', 'Primitives Found:', '', strList)
      elif index == 8:
         (nSub, nPrim) = SelectPrimitive()
         obj = kcs_vol.prim_properties_get(nSub, nPrim)
         res, prop = EditProperties(obj)
         if res == kcs_util.ok() :
            kcs_vol.prim_properties_set(nSub, nPrim, prop)
            RedrawVolume()
      elif index == 9:
        (nSub, nPrim) = SelectPrimitive()
        obj = kcs_vol.prim_properties_get(nSub, nPrim)
        listing = PropertiesToList(obj)
        (status, index) = kcs_ui.string_select('Properties', 'Properties of Primitive:', '', listing)
      elif index == 10:
        (nSub, nPrim) = SelectPrimitive()
        obj = kcs_vol.prim_delete(nSub, nPrim)
   else:
      print "User interrupted!"
      break;


#----------------------------------------------------------------------------------

