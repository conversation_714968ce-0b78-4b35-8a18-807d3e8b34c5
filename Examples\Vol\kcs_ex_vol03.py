import kcs_draft
import kcs_vol
import kcs_ui
import kcs_util
import KcsPoint3D
import KcsVector3D
import KcsVolConnection

def SubvolumeList():
   list = kcs_vol.subvol_list()
   items = map( str, list )
   kcs_ui.string_select('Subvolumes List', '', '', items )

def SubvolumeAdd():
   kcs_vol.subvol_add()
   kcs_ui.message_confirm('New subvolume added')

def SubvolumeDelete():
   list = kcs_vol.subvol_list()
   items = map( str, list )
   (res, index ) = kcs_ui.string_select('Subvolume delete', 'List of subvolumes ', 'Please select subvolume to delete', items )
   if res == kcs_util.ok() :
      kcs_vol.subvol_delete( list[index-1] )
      kcs_ui.message_confirm('Subvolume deleted')

def ConnectionList():
   list = kcs_vol.conn_list()
   items = map( lambda x : 'Type %i  No %i' % (x.GetConnType(), x.GetConnNo()), list )
   kcs_ui.string_select('Connections List', '', '', items )

def ConnectionAdd():
   res, connType = kcs_ui.int_req('Input connection type (1..9)', 1)

   if res == kcs_util.ok():
      res, connNo   = kcs_ui.int_req('Input connection number (1..999)', 1)

   if res == kcs_util.ok():
      conn = KcsVolConnection.VolConnection( connType, connNo)
      kcs_vol.conn_add(conn)
      kcs_ui.message_confirm('New connection added')

def ConnectionDelete():
   list = kcs_vol.conn_list()
   items = map( lambda x : 'Type %i  No %i' % (x.GetConnType(), x.GetConnNo()), list )
   (res, index ) = kcs_ui.string_select('Connection delete', 'List of connections', 'Please select connection to delete', items )
   if res == kcs_util.ok() :
      kcs_vol.conn_delete( list[index-1] )
      kcs_ui.message_confirm('Connection deleted')

def ConnPropGet():
   list = kcs_vol.conn_list()
   items = map( lambda x : 'Type %i  No %i' % (x.GetConnType(), x.GetConnNo()), list )
   (res, index ) = kcs_ui.string_select('Connection properties get', 'List of connections', 'Please select connection', items )
   if res == kcs_util.ok() :
      prop = kcs_vol.conn_properties_get( list[index-1] )
      kcs_ui.message_confirm('Connection properties\n' + str(prop) )

def ConnPropSet():
   list = kcs_vol.conn_list()
   items = map( lambda x : 'Type %i  No %i' % (x.GetConnType(), x.GetConnNo()), list )
   (res, index ) = kcs_ui.string_select('Connection properties set', 'List of connections', 'Please select connection', items )
   if res == kcs_util.ok() :
      prop = kcs_vol.conn_properties_get( list[index-1] )
      res, desc = kcs_ui.string_req('Input connection description', '')
      if res == kcs_util.ok() :
         prop.SetDescription( desc )
         res, comp = kcs_ui.string_req('Input connection component', '')
         if res == kcs_util.ok() :
            prop.SetComponentName( comp )
            kcs_vol.conn_properties_set( prop )
            prop = kcs_vol.conn_properties_get( prop )
            kcs_ui.message_confirm('Connection properties\n' + str(prop) )


actions = (
      'Subvolume list',
      'Subvolume add',
      'Subvolume delete',
      'Connections list',
      'Connection add',
      'Connection delete',
      'Connection prop get',
      'Connection prop set'
      )

status = kcs_util.ok()

while status == kcs_util.ok() :
   (status, index) = kcs_ui.choice_select('Subvolume operations','', actions)

   try:

      if status == kcs_util.ok():
         if index == 1:
            SubvolumeList()
         elif index == 2:
            SubvolumeAdd()
         elif index == 3:
            SubvolumeDelete()
         elif index == 4:
            ConnectionList()
         elif index == 5:
            ConnectionAdd()
         elif index == 6:
            ConnectionDelete()
         elif index == 7:
            ConnPropGet()
         elif index == 8:
            ConnPropSet()
   except Exception, e:
      kcs_ui.message_debug( e )
