
#
#      NAME:
#          KcsAssembly.py
#
#      PURPOSE:
#          The class holds information about a Assembly
#
#          Do NOT change the names of the attributes, they are used by
#          the Vitesse interface. Users may only add or change methods
#
#      ATTRIBUTES
import types
import string
import copy

from KcsTransformation3D import Transformation3D
from KcsVector3D import Vector3D
from KcsPoint3D import Point3D
from KcsBox import Box
from KcsDate import Date

class Assembly(object):
   'class holds information about Assembly object'

   Orientation  = ('', 'UpRight', 'Upside Down', 'Fore Down', 'Aft Down', 'Portside Down', 'Starboard Down', 'Automatic', 'Specific Panel')

   __ErrorMessages = { TypeError : 'not supported argument type, see documentation of Assembly class',
                       IndexError: 'not supported index value, see documentation of Assembly class',
                       ValueError: 'not supported value, see documentation of Assembly class' }

#
#      METHOD:
#          __init__
#
#      PURPOSE:
#          To create an instance of the class
#
#      INPUT:
#          Parameters:
#
   def __init__(self):
      'to create instance of the class'

      self.__BuildStrategyNum    = 6
      self.__BuildStrategySep    = '&'

      self.__Name                = ''
      self.__Descrip             = ''
      self.__BuildStrat          = self.__BuildStrategyNum * ['']
      self.__PlanningUnit        = ''
      self.__Type                = ''
      self.__WorkingLocation     = ''
      self.__Destination         = ''
      self.__DesignStatus        = ''
      self.__ProductionStatus    = ''
      self.__Orientation         = Assembly.Orientation[0]
      self.__SpecificPanel       = ''
      self.__Level               = 0
      self.__ShortLevelDesc      = ''
      self.__LongLevelDesc       = ''
      self.__PlannedStartDate    = None
      self.__PlannedEndDate      = None
      self.__ActualStartDate     = None
      self.__ActualEndDate       = None
      self.__EstimatedWeight     = 0.0
      self.__EstimatedCOG        = Point3D()
      self.__CalculatedWeight    = 0.0
      self.__CalculatedCOG       = Point3D()
      self.__GlobalCoordBox      = Box(Point3D(0, 0, 0), Vector3D(1, 0, 0), Vector3D(0, 1, 0), 0, 0, 0)
      self.__AssemblyCoordBox    = Box(Point3D(0, 0, 0), Vector3D(1, 0, 0), Vector3D(0, 1, 0), 0, 0, 0)
      self.__OrientationMatrix   = Transformation3D()

#
#      METHOD:
#          __cmp__
#
#      PURPOSE:
#          implements cmp(o1, o2) function

   def __cmp__(self, other):
      'implementation of cmp(o1, o2) function'

      # if None object return not equal
      if type(other)==types.NoneType:
         return 1

      if not isinstance(other, Assembly):
         raise TypeError, Assembly.__ErrorMessages[TypeError]

      if string.lower(self.GetName()) != string.lower(other.GetName()):
         return 1
      if string.lower(self.GetDescription()) != string.lower(other.GetDescription()):
         return 1
      if len(self.GetBuildingStrategy()) != len(other.GetBuildingStrategy()):
         return 1
      if string.lower(self.GetPlanningUnit()) != string.lower(other.GetPlanningUnit()):
         return 1
      if string.lower(self.__Type) != string.lower(other.__Type):
         return 1
      if string.lower(self.__WorkingLocation) != string.lower(other.__WorkingLocation):
         return 1
      if string.lower(self.__Destination) != string.lower(other.__Destination):
         return 1
      if string.lower(self.__DesignStatus) != string.lower(other.__DesignStatus):
         return 1
      if string.lower(self.__ProductionStatus) != string.lower(other.__ProductionStatus):
         return 1
      if string.lower(self.__Orientation) != string.lower(other.__Orientation):
         return 1
      if string.lower(self.__SpecificPanel) != string.lower(other.__SpecificPanel):
         return 1
      if self.__Level != other.__Level:
         return 1
      if string.lower(self.__ShortLevelDesc) != string.lower(other.__ShortLevelDesc):
         return 1
      if string.lower(self.__LongLevelDesc) != string.lower(other.__LongLevelDesc):
         return 1
      if self.__PlannedStartDate != other.__PlannedStartDate:
         return 1
      if self.__PlannedEndDate != other.__PlannedEndDate:
         return 1
      if self.__ActualStartDate != other.__ActualStartDate:
         return 1
      if self.__ActualEndDate != other.__ActualEndDate:
         return 1
      if self.__EstimatedWeight != other.__EstimatedWeight:
         return 1
      if self.__EstimatedCOG != other.__EstimatedCOG:
         return 1
      if self.__CalculatedWeight != other.__CalculatedWeight:
         return 1
      if self.__CalculatedCOG != other.__CalculatedCOG:
         return 1
      if self.__GlobalCoordBox != other.__GlobalCoordBox:
         return 1
      if self.__AssemblyCoordBox != other.__AssemblyCoordBox:
         return 1
      if self.__OrientationMatrix != other.__OrientationMatrix:
         return 1
      return 0

#
#      METHOD:
#          __repr__
#
#      PURPOSE:
#          To print the class
#
   def __repr__(self):
      'returns string representation of Assembly object'

      if self.__Level == 0:
         levelstr = '   Level: None'
      else:
         levelstr = '   Level: %d   Short desc: %s    Long desc: %s' % (self.__Level, self.__ShortLevelDesc, self.__LongLevelDesc)

      if self.__GlobalCoordBox.IsEmpty():
         globalboxstr = '   Global bounding box: Empty'
      else:
         globalboxstr = '   Global bounding box: Defined'

      if self.__AssemblyCoordBox.IsEmpty():
         localboxstr = '   Assembly bounding box: Empty'
      else:
         localboxstr = '   Assembly bounding box: Defined'

      tup = (
         'Assembly:',
         '   Name: ' + self.__Name,
         '   Description: ' + self.__Descrip,
         '   Building Strategy 1: ' + self.__BuildStrat[0],
         '   Building Strategy 2: ' + self.__BuildStrat[1],
         '   Building Strategy 3: ' + self.__BuildStrat[2],
         '   Building Strategy 4: ' + self.__BuildStrat[3],
         '   Building Strategy 5: ' + self.__BuildStrat[4],
         '   Building Strategy 6: ' + self.__BuildStrat[5],
         '   Planning Unit: ' + self.__PlanningUnit,
         '   Type: ' + self.__Type,
         '   Working Location: ' + self.__WorkingLocation,
         '   Destination: ' + self.__Destination,
         '   Design Status: ' + self.__DesignStatus,
         '   Production Status: ' + self.__ProductionStatus,
         '   Orientation: ' + self.__Orientation,
         '   Specific Panel: ' + self.__SpecificPanel,
         levelstr,
         '   Planned Start Date: ' + str(self.__PlannedStartDate),
         '   Planned End Date: ' + str(self.__PlannedEndDate),
         '   Actual Start Date: ' + str(self.__ActualStartDate),
         '   Actual End Date: ' + str(self.__ActualEndDate),
         '   Estimated Weight: ' + str(self.__EstimatedWeight),
         '   Estimated COG: %f, %f, %f' % (self.__EstimatedCOG.X, self.__EstimatedCOG.Y, self.__EstimatedCOG.Z),
         '   Calculated Weight: ' + str(self.__CalculatedWeight),
         '   Calculated COG: %f, %f, %f' % (self.__CalculatedCOG.X, self.__CalculatedCOG.Y, self.__CalculatedCOG.Z),
         globalboxstr,
         localboxstr,
         '   Orientation Matrix: %f, %f, %f, %f,\n %f, %f, %f, %f,\n %f, %f, %f, %f,\n %f, %f, %f, %f' %
         (self.__OrientationMatrix.matrix11, self.__OrientationMatrix.matrix12, self.__OrientationMatrix.matrix13, self.__OrientationMatrix.matrix14,
          self.__OrientationMatrix.matrix21, self.__OrientationMatrix.matrix22, self.__OrientationMatrix.matrix23, self.__OrientationMatrix.matrix24,
          self.__OrientationMatrix.matrix31, self.__OrientationMatrix.matrix32, self.__OrientationMatrix.matrix33, self.__OrientationMatrix.matrix34,
          self.__OrientationMatrix.matrix41, self.__OrientationMatrix.matrix42, self.__OrientationMatrix.matrix43, self.__OrientationMatrix.matrix44),
         )
      return string.join(tup, '\n')

# -----------------------------------------------------------------------------------------------------------------
#
#      METHOD:
#         SetDescription
#
#      PURPOSE:
#          set assembly description
#
#      INPUT:
#          Parameters:
#          descrip        string         assembly description
#
#      RESULT:
#          assembly description will be updated
#

   def SetDescription(self, descrip):
      'sets assembly description'

      if type(descrip) != types.StringType:
         raise TypeError, Assembly.__ErrorMessages[TypeError]

      self.__Descrip = descrip

# -----------------------------------------------------------------------------------------------------------------
#
#      METHOD:
#         GetDescription
#
#      PURPOSE:
#          get assembly description
#
#      INPUT:
#          Parameters:
#            None
#
#      RESULT:
#          string         assembly description
#

   def GetDescription(self):
      'returns description of assembly object'

      return self.__Descrip


# -----------------------------------------------------------------------------------------------------------------
#
#      METHOD:
#         SetBuildingStrategy
#
#      PURPOSE:
#          set building strategy info
#          this function will divide string to 6 building strategy info if index==-1
#
#      INPUT:
#          Parameters:
#          bldstrat        string         building strategy info
#          index           integer        this is optional, if given only indexed line will be changed
#
#      RESULT:
#          building strategy info will be updated
#

   def SetBuildingStrategy(self, bldstrat, index=-1):
      'sets building strategy info'

      if type(bldstrat) != types.StringType or type(index) != types.IntType:
         raise TypeError, ErrorMessges[TypeError]
      if not -1 <= index < self.__BuildStrategyNum:
         raise IndexError, Assembly.__ErrorMessages[IndexError]

      if index==-1:
         lines = string.split(bldstrat, self.__BuildStrategySep)
         for index in range(0, 6):
            if len(lines)>index:
               self.__BuildStrat[index] = lines[index]
            else:
               self.__BuildStrat[index] = ''
      else:
         self.__BuildStrat[index] = bldstrat


# -----------------------------------------------------------------------------------------------------------------
#
#      METHOD:
#         GetBuildingStrategy
#
#      PURPOSE:
#          get building strategy info
#          this function will join 6 building strategies line to one string if index==-1
#
#      INPUT:
#          Parameters:
#            None
#
#      RESULT:
#          string         assembly description
#

   def GetBuildingStrategy(self, index=-1):
      'returns building strategy info'

      if type(index) != types.IntType:
         raise TypeError, Assembly.__ErrorMessages[TypeError]
      if not -1 <= index < self.__BuildStrategyNum:
         raise IndexError, Assembly.__ErrorMessages[IndexError]

      if index==-1:
         return string.join(self.__BuildStrat, self.__BuildStrategySep)
      else:
         return self.__BuildStrat[index]

# -----------------------------------------------------------------------------------------------------------------
#
#      METHOD:
#         SetName
#
#      PURPOSE:
#          set assembly user name
#
#      INPUT:
#          Parameters:
#          name        string         assembly user name
#
#      RESULT:
#          assembly name will be updated
#

   def SetName(self, name):
      'set name of assembly object'

      if type(name) != types.StringType:
         raise TypeError, Assembly.__ErrorMessages[TypeError]

      self.__Name = name

# -----------------------------------------------------------------------------------------------------------------
#
#      METHOD:
#         GetName
#
#      PURPOSE:
#          get assembly name
#
#      INPUT:
#          Parameters:
#            None
#
#      RESULT:
#          string         assembly name
#

   def GetName(self):
      'returns name of assembly object'
      return self.__Name

# -----------------------------------------------------------------------------------------------------------------
#
#      METHOD:
#         SetPlanningUnit
#
#      PURPOSE:
#          set assembly planning unit
#
#      INPUT:
#          Parameters:
#          unit        string         assembly planning unit
#
#      RESULT:
#          assembly planning unit will be updated
#

   def SetPlanningUnit(self, unit):
      'sets assembly planning unit'

      if type(unit) != types.StringType:
         raise TypeError, Assembly.__ErrorMessages[TypeError]

      self.__PlanningUnit = unit

# -----------------------------------------------------------------------------------------------------------------
#
#      METHOD:
#         GetPlanningUnit
#
#      PURPOSE:
#          get planning unit
#
#      INPUT:
#          Parameters:
#            None
#
#      RESULT:
#          string         assembly planning unit
#

   def GetPlanningUnit(self):
      'returns assembly planning unit'
      return self.__PlanningUnit

# -----------------------------------------------------------------------------------------------------------------
#
#      METHOD:
#         SetType
#
#      PURPOSE:
#          set assembly type
#
#      INPUT:
#          Parameters:
#          typestr        string         assembly type
#
#      RESULT:
#          assembly type will be updated
#

   def SetType(self, typestr):
      'sets assembly type'

      if type(typestr) != types.StringType:
         raise TypeError, Assembly.__ErrorMessages[TypeError]

      self.__Type = typestr

# -----------------------------------------------------------------------------------------------------------------
#
#      METHOD:
#         GetType
#
#      PURPOSE:
#          get assembly type
#
#      INPUT:
#          Parameters:
#            None
#
#      RESULT:
#          string         assembly type
#

   def GetType(self):
      'returns assembly object type'
      return self.__Type


# -----------------------------------------------------------------------------------------------------------------
#
#      METHOD:
#         SetDesignStatus
#
#      PURPOSE:
#          set assembly design status
#
#      INPUT:
#          Parameters:
#          status        string         assembly design status
#
#      RESULT:
#          assembly design status will be updated
#

   def SetDesignStatus(self, status):
      'sets design status of assembly object'

      if type(status) != types.StringType:
         raise TypeError, Assembly.__ErrorMessages[TypeError]

      self.__DesignStatus = status

# -----------------------------------------------------------------------------------------------------------------
#
#      METHOD:
#         GetDesignStatus
#
#      PURPOSE:
#          get assembly design status
#
#      INPUT:
#          Parameters:
#            None
#
#      RESULT:
#          string         assembly design status
#

   def GetDesignStatus(self):
      'returns desing status of assembly object'
      return self.__DesignStatus


# -----------------------------------------------------------------------------------------------------------------
#
#      METHOD:
#         SetWorkingLocation
#
#      PURPOSE:
#          set assembly working location
#
#      INPUT:
#          Parameters:
#          workloc        string         assembly working location
#
#      RESULT:
#          assembly working location will be updated
#

   def SetWorkingLocation(self, workloc):
      'sets working location of assembly object'

      if type(workloc) != types.StringType:
         raise TypeError, Assembly.__ErrorMessages[TypeError]

      self.__WorkingLocation = workloc

# -----------------------------------------------------------------------------------------------------------------
#
#      METHOD:
#         GetWorkingLocation
#
#      PURPOSE:
#          get working location
#
#      INPUT:
#          Parameters:
#            None
#
#      RESULT:
#          string         assembly working location
#

   def GetWorkingLocation(self):
      'returns working location of assembly object'
      return self.__WorkingLocation

# -----------------------------------------------------------------------------------------------------------------
#
#      METHOD:
#         SetDestination
#
#      PURPOSE:
#          set assembly destination
#
#      INPUT:
#          Parameters:
#          dest        string         assembly destination
#
#      RESULT:
#          assembly destination will be updated
#

   def SetDestination(self, dest):
      'sets assembly destination'
      if type(dest) != types.StringType:
         raise TypeError, Assembly.__ErrorMessages[TypeError]

      self.__Destination = dest

# -----------------------------------------------------------------------------------------------------------------
#
#      METHOD:
#         GetDestination
#
#      PURPOSE:
#          get destination
#
#      INPUT:
#          Parameters:
#            None
#
#      RESULT:
#          string         assembly destination
#

   def GetDestination(self):
      'returns assembly destination'
      return self.__Destination


# -----------------------------------------------------------------------------------------------------------------
#
#      METHOD:
#         SetProductionStatus
#
#      PURPOSE:
#          set assembly production status
#
#      INPUT:
#          Parameters:
#          prodstat        string         assembly production status
#
#      RESULT:
#          assembly production status will be updated
#

   def SetProductionStatus(self, prodstat):
      'sets production status of assembly object'

      if type(prodstat) != types.StringType:
         raise TypeError, Assembly.__ErrorMessages[TypeError]

      self.__ProductionStatus = prodstat

# -----------------------------------------------------------------------------------------------------------------
#
#      METHOD:
#         GetProductionStatus
#
#      PURPOSE:
#          get production status
#
#      INPUT:
#          Parameters:
#            None
#
#      RESULT:
#          string         assembly production status
#

   def GetProductionStatus(self):
      'returns production status of assembly object'

      return self.__ProductionStatus

# -----------------------------------------------------------------------------------------------------------------
#
#      METHOD:
#         SetOrientation
#
#      PURPOSE:
#          set assembly orientation
#
#      INPUT:
#          Parameters:
#          orient        string         assembly orientation
#
#      RESULT:
#          assembly orientation will be updated
#

   def SetOrientation(self, orient):
      if type(orient) != types.StringType:
         raise TypeError, Assembly.__ErrorMessages[TypeError]
      if orient not in Assembly.Orientation:
         raise ValueError, Assembly.__ErrorMessages[ValueError]

      self.__Orientation = orient

# -----------------------------------------------------------------------------------------------------------------
#
#      METHOD:
#         GetOrientation
#
#      PURPOSE:
#          get assembly orientation
#
#      INPUT:
#          Parameters:
#            None
#
#      RESULT:
#          string         assembly orientation
#

   def GetOrientation(self):
      'get assembly orientation'
      return self.__Orientation

# -----------------------------------------------------------------------------------------------------------------
#
#      METHOD:
#         SetSpecificPanel
#
#      PURPOSE:
#          set specific panel
#
#      INPUT:
#          Parameters:
#          panel        string         assembly specific panel
#
#      RESULT:
#          assembly specific panel will be updated
#

   def SetSpecificPanel(self, panel):
      'sets assembly specific panel'

      if type(panel) != types.StringType:
         raise TypeError, Assembly.__ErrorMessages[TypeError]

      self.__SpecificPanel = panel

# -----------------------------------------------------------------------------------------------------------------
#
#      METHOD:
#         GetSpecificPanel
#
#      PURPOSE:
#          get assembly specific panel
#
#      INPUT:
#          Parameters:
#            None
#
#      RESULT:
#          string         assembly specific panel
#

   def GetSpecificPanel(self):
      'returns specific panel'

      return self.__SpecificPanel

# -----------------------------------------------------------------------------------------------------------------
#
#      METHOD:
#         SetEstimatedCOG
#
#      PURPOSE:
#          set estimated COG of assembly
#
#      INPUT:
#          Parameters:
#          point           Point3D         assembly estimated COG as point
#              or
#          x, y, z         reals           assembly estimated COG as 3 reals
#
#      RESULT:
#          assembly estimated COG will be updated
#

   def SetEstimatedCOG(self, *args):
      'set estimated center of gravity'

      if len(args)==1:              # 3D point
         if not isinstance(args[0], Point3D):
            raise TypeError, Assembly.__ErrorMessages[TypeError]
         self.__EstimatedCOG = Point3D(args[0].X, args[0].Y, args[0].Z)
      elif len(args)==3:            # 3 reals x, y, z
         self.__EstimatedCOG = Point3D(args[0], args[1], args[2])
      else:
         raise TypeError, Assembly.__ErrorMessages[TypeError]

# -----------------------------------------------------------------------------------------------------------------
#
#      METHOD:
#         GetEstimatedCOG
#
#      PURPOSE:
#          get assembly estimated COG
#
#      INPUT:
#          Parameters:
#            None
#
#      RESULT:
#          Point3D         assembly estimated COG
#

   def GetEstimatedCOG(self):
      'get estimated center of gravity'
      return copy.deepcopy(self.__EstimatedCOG)

# -----------------------------------------------------------------------------------------------------------------
#
#      METHOD:
#         SetEstimatedWeight
#
#      PURPOSE:
#          set estimated weight of assembly
#
#      INPUT:
#          Parameters:
#          weight         real           assembly estimated weight
#
#      RESULT:
#          assembly estimated weight will be updated
#

   def SetEstimatedWeight(self, weight):
      'sets estimated weight of assembly object'

      if type(weight) != types.FloatType and type(weight) != types.IntType and type(weight) != types.LongType:
         raise TypeError, Assembly.__ErrorMessages[TypeError]
      self.__EstimatedWeight = weight

# -----------------------------------------------------------------------------------------------------------------
#
#      METHOD:
#         GetEstimatedWeight
#
#      PURPOSE:
#          get assembly estimated weight
#
#      INPUT:
#          Parameters:
#            None
#
#      RESULT:
#          real         assembly estimated COG
#

   def GetEstimatedWeight(self):
      'returns estimated weight of asssembly object'

      return self.__EstimatedWeight

# -----------------------------------------------------------------------------------------------------------------
#
#      METHOD:
#         __SetCalculatedCOG
#
#      PURPOSE:
#          set calculated COG of assembly
#
#      INPUT:
#          Parameters:
#          point           Point3D         assembly calculated COG as point
#              or
#          x, y, z         reals           assembly calculated COG as 3 reals
#
#      RESULT:
#          assembly calculated COG will be updated
#

   def __SetCalculatedCOG(self, *args):
      'sets calculated point of gravity'

      if len(args)==1:              # 3D point
         if not isinstance(args[0], Point3D):
            raise TypeError, Assembly.__ErrorMessages[TypeError]
         self.__CalculatedCOG = Point3D(args[0].X, args[0].Y, args[0].Z)
      elif len(args)==3:            # 3 reals x, y, z
         self.__CalculatedCOG = Point3D(args[0], args[1], args[2])
      else:
         raise TypeError, Assembly.__ErrorMessages[TypeError]

# -----------------------------------------------------------------------------------------------------------------
#
#      METHOD:
#         GetCalculatedCOG
#
#      PURPOSE:
#          get assembly calculated COG
#
#      INPUT:
#          Parameters:
#            None
#
#      RESULT:
#          Point3D         assembly calculated COG
#

   def GetCalculatedCOG(self):
      'returns calculated point of gravity as Point3D'
      return copy.deepcopy(self.__CalculatedCOG)

# -----------------------------------------------------------------------------------------------------------------
#
#      METHOD:
#         __SetCalculatedWeight
#
#      PURPOSE:
#          set calculated weight of assembly
#
#      INPUT:
#          Parameters:
#          weight         real           assembly calculated weight
#
#      RESULT:
#          assembly calculated weight will be updated
#

   def __SetCalculatedWeight(self, weight):
      'sets calculated weight'

      if type(weight) != types.FloatType and type(weight) != types.IntType and type(weight) != types.LongType:
         raise TypeError, Assembly.__ErrorMessages[TypeError]
      self.__CalculatedWeight = weight

# -----------------------------------------------------------------------------------------------------------------
#
#      METHOD:
#         GetCalculatedWeight
#
#      PURPOSE:
#          get assembly calculated weight
#
#      INPUT:
#          Parameters:
#            None
#
#      RESULT:
#          real         assembly calculated COG
#

   def GetCalculatedWeight(self):
      'returns calculated weight'
      return self.__CalculatedWeight

# -----------------------------------------------------------------------------------------------------------------
#
#      METHOD:
#         SetPlannedStartDate
#
#      PURPOSE:
#          set planned start date
#
#      INPUT:
#          Parameters:
#              see: SetDate function in KcsDate.Date documentation
#
#      RESULT:
#          assembly planned start date will be updated
#

   def SetPlannedStartDate(self, *args):
      'set plannned start date'
      if len(args)==1 and (args[0] == None or isinstance(args[0], Date)):
         self.__PlannedStartDate = args[0]
      else:
         self.__PlannedStartDate = Date(*args)

# -----------------------------------------------------------------------------------------------------------------
#
#      METHOD:
#         GetPlannedStartDate
#
#      PURPOSE:
#          get assembly planned start date
#
#      INPUT:
#          Parameters:
#            None
#
#      RESULT:
#           Date     instance of KcsDate.Date class      assembly planned start date
#

   def GetPlannedStartDate(self):
      'get planned start date'
      return copy.deepcopy(self.__PlannedStartDate)

# -----------------------------------------------------------------------------------------------------------------
#
#      METHOD:
#         SetPlannedEndDate
#
#      PURPOSE:
#          set planned end date
#
#      INPUT:
#          Parameters:
#              see: SetDate function in KcsDate.Date documentation
#
#      RESULT:
#          assembly planned start end will be updated
#

   def SetPlannedEndDate(self, *args):
      'set planned end date'
      if len(args)==1 and (args[0] == None or isinstance(args[0], Date)):
         self.__PlannedEndDate = args[0]
      else:
         self.__PlannedEndDate = Date(*args)

# -----------------------------------------------------------------------------------------------------------------
#
#      METHOD:
#         GetPlannedEndDate
#
#      PURPOSE:
#          get assembly planned end date
#
#      INPUT:
#          Parameters:
#            None
#
#      RESULT:
#           Date     instance of KcsDate.Date class      assembly planned end date
#

   def GetPlannedEndDate(self):
      'returns planned end date'
      return copy.deepcopy(self.__PlannedEndDate)

# -----------------------------------------------------------------------------------------------------------------
#
#      METHOD:
#         SetActualStartDate
#
#      PURPOSE:
#          set actual start date
#
#      INPUT:
#          Parameters:
#              see: SetDate function in KcsDate.Date documentation
#
#      RESULT:
#          assembly actual start date will be updated
#

   def SetActualStartDate(self, *args):
      'sets actula start date'
      if len(args)==1 and (args[0] == None or isinstance(args[0], Date)):
         self.__ActualStartDate = args[0]
      else:
         self.__ActualStartDate = Date(*args)

# -----------------------------------------------------------------------------------------------------------------
#
#      METHOD:
#         GetActualStartDate
#
#      PURPOSE:
#          get assembly actual start date
#
#      INPUT:
#          Parameters:
#            None
#
#      RESULT:
#           Date     instance of KcsDate.Date class      assembly actual start date
#

   def GetActualStartDate(self):
      'returns actual start date'
      return copy.deepcopy(self.__ActualStartDate)

# -----------------------------------------------------------------------------------------------------------------
#
#      METHOD:
#         SetActualEndDate
#
#      PURPOSE:
#          set actual end date
#
#      INPUT:
#          Parameters:
#              see: SetDate function in KcsDate.Date documentation
#
#      RESULT:
#          assembly actual end date will be updated
#

   def SetActualEndDate(self, *args):
      'sets actual end date'
      if len(args)==1 and (args[0] == None or isinstance(args[0], Date)):
         self.__ActualEndDate = args[0]
      else:
         self.__ActualEndDate = Date(*args)

# -----------------------------------------------------------------------------------------------------------------
#
#      METHOD:
#         GetActualEndDate
#
#      PURPOSE:
#          get assembly actual end date
#
#      INPUT:
#          Parameters:
#            None
#
#      RESULT:
#           Date     instance of KcsDate.Date class      assembly actual end date
#             or
#           None     if date is not set
#

   def GetActualEndDate(self):
      'get actual end date'
      return copy.deepcopy(self.__ActualEndDate)

# -----------------------------------------------------------------------------------------------------------------
#
#      METHOD:
#         __SetGlobalCoordBox
#
#      PURPOSE:
#          set global coordinates bounding box
#
#      INPUT:
#          Parameters:
#              see: SetAxisParallelBox function in KcsBox.Box documentation
#
#      RESULT:
#          global coordinate bounding box will be updated
#

   def __SetGlobalCoordBox(self, *args):
      'sets global coordinate box'
      apply(self.__GlobalCoordBox.SetAxisParallelBox, args)

# -----------------------------------------------------------------------------------------------------------------
#
#      METHOD:
#         GetGlobalCoordBox
#
#      PURPOSE:
#          get assembly global coordinate bounding box
#
#      INPUT:
#          Parameters:
#            None
#
#      RESULT:
#           Box     instance of KcsBox.Box class      global coordinate bounding box
#

   def GetGlobalCoordBox(self):
      'returns global coordinate box'
      return copy.deepcopy(self.__GlobalCoordBox)

# -----------------------------------------------------------------------------------------------------------------
#
#      METHOD:
#         __SetAssemblyCoordBox
#
#      PURPOSE:
#          set assembly coordinates bounding box
#
#      INPUT:
#          Parameters:
#              see: SetAxisParallelBox function in KcsBox.Box documentation
#
#      RESULT:
#          assembly coordinate bounding box will be updated
#

   def __SetAssemblyCoordBox(self, *args):
      'set assembly coordinates box'
      apply(self.__AssemblyCoordBox.SetAxisParallelBox, args)

# -----------------------------------------------------------------------------------------------------------------
#
#      METHOD:
#         GetAssemblyCoordBox
#
#      PURPOSE:
#          get assembly coordinate bounding box
#
#      INPUT:
#          Parameters:
#            None
#
#      RESULT:
#           Box     instance of KcsBox.Box class      global coordinate bounding box
#

   def GetAssemblyCoordBox(self):
      'returns assembly coordinates box'

      return copy.deepcopy(self.__AssemblyCoordBox)

# -----------------------------------------------------------------------------------------------------------------
#
#      METHOD:
#         SetLevel
#
#      PURPOSE:
#          set assembly level
#
#      INPUT:
#          Parameters:
#              id             integer        level id, valid values are:
#                                               1-100    level id
#                                               0        means that levels will not be set
#              shortdesc      string         short level description
#              longdesc       string         long level description
#
#      RESULT:
#          assembly level will be updated
#

   def SetLevel(self, id=0, shortdesc='', longdesc=''):
      'sets level of assembly object'

      if type(id) != types.IntType and type(id) != types.LongType or type(shortdesc) != types.StringType or type(longdesc) != types.StringType:
         raise TypeError, Assembly.__ErrorMessages[TypeError]
      if id<0 or id>100:
         raise ValueError, Assembly.__ErrorMessages[ValueError]
      self.__Level            = id
      self.__ShortLevelDesc   = shortdesc
      self.__LongLevelDesc    = longdesc

# -----------------------------------------------------------------------------------------------------------------
#
#      METHOD:
#         GetLevel
#
#      PURPOSE:
#          get level
#
#      INPUT:
#          Parameters:
#            None
#
#      RESULT:
#           (integer, string, string)     - level information as tuple: levelId, short description, long description
#

   def GetLevel(self):
      'returns level of assembly object'
      return (self.__Level, self.__ShortLevelDesc, self.__LongLevelDesc)

# -----------------------------------------------------------------------------------------------------------------
#
#      METHOD:
#         __SetOrientationMatrix
#
#      PURPOSE:
#          set orientation matrix of assembly
#
#      INPUT:
#          Parameters:
#          matrix             Transformation3D        assembly orientation matrix as matrix transformation
#              or
#          type and elements  integer and reals       assembly orientation matrix as type and array numbers real
#
#      RESULT:
#          assembly orientation matrix will be updated
#

   def SetOrientationMatrix(self, *args):
      #'sets assembly orientation matrix'

      if len(args)==1:              # Transformation3D
         if not isinstance(args[0], Transformation3D):
            raise TypeError, Assembly.__ErrorMessages[TypeError]
         apply(self.__OrientationMatrix.SetFromTransformation, args)
      elif len(args)==2:            # typ of matrix and array 16 elements real
         apply(self.__OrientationMatrix.SetByRowFromArray, args)
      else:
         raise TypeError, Assembly.__ErrorMessages[TypeError]

# -----------------------------------------------------------------------------------------------------------------
#
#      METHOD:
#         GetOrientationMatrix
#
#      PURPOSE:
#          get assembly orientation matrix
#
#      INPUT:
#          Parameters:
#            None
#
#      RESULT:
#          Transformation3D         assembly orientation matrix
#

   def GetOrientationMatrix(self):
      'returns assembly orientation matrix as Transformation3D'
      return copy.deepcopy(self.__OrientationMatrix)

#-------------------------------------------------------------------------------------------------------------------
#
#      New style of access to attributes from Python version 2.2
#

   Name              = property (GetName, SetName, None, 'Name - assembly user name')
   Descrip           = property (GetDescription, SetDescription, None, 'Descrip - assembly description')

                     # 'Set' divides input string to 6 building strategy info, 'Get' combines into one output string
   BuildStrat        = property (GetBuildingStrategy, SetBuildingStrategy, None, 'BuildStrat - building strategy info')

   PlanningUnit      = property (GetPlanningUnit, SetPlanningUnit, None, 'PlanningUnit - ssembly planning unit')
   Type              = property (GetType, SetType, None, 'Type - assembly type')
   WorkingLocation   = property (GetWorkingLocation, SetWorkingLocation, None, 'WorkingLocation - assembly working location')
   Destination       = property (GetDestination, SetDestination, None, 'Destination - assembly destination')
   DesignStatus      = property (GetDesignStatus, SetDesignStatus, None, 'DesignStatus - assembly design status')
   ProductionStatus  = property (GetProductionStatus, SetProductionStatus, None, 'ProductionStatus - assembly production status')
   AssemblyOrientation = property (GetOrientation, SetOrientation, None, 'AssemblyOrientation - assembly orientation, must be one of ')#+str(Assembly.Orientation))
   SpecificPanel     = property (GetSpecificPanel, SetSpecificPanel, None, 'SpecificPanel - assembly specific panel')

   def GetLevelOnly(self):
      return self.__Level
   def SetLevelOnly(self, id):
      if type(id) != types.IntType and type(id) != types.LongType:
         raise TypeError, Assembly.__ErrorMessages[TypeError]
      if id<0 or id>100:
         raise ValueError, Assembly.__ErrorMessages[ValueError]
      self.__Level   = id

   Level             = property (GetLevelOnly, SetLevelOnly, None, 'Level - assembly level id')

   def GetShortLevelDesc(self):
      return self.__ShortLevelDesc
   def SetShortLevelDesc(self, shortdesc):
      if type(shortdesc) != types.StringType:
         raise TypeError, Assembly.__ErrorMessages[TypeError]
      self.__ShortLevelDesc   = shortdesc

   ShortLevelDesc    = property (GetShortLevelDesc, SetShortLevelDesc, None, 'ShortLevelDesc - assembly level short description')

   def GetLongLevelDesc(self):
      return self.__LongLevelDesc
   def SetLongLevelDesc(self, longdesc):
      if type(longdesc) != types.StringType:
         raise TypeError, Assembly.__ErrorMessages[TypeError]
      self.__LongLevelDesc   = longdesc

   LongLevelDesc     = property (GetLongLevelDesc, SetLongLevelDesc, None, 'LongLevelDesc - assembly level long description')

   PlannedStartDate  = property (GetPlannedStartDate, SetPlannedStartDate, None, 'PlannedStartDate - assembly planned start date')
   PlannedEndDate    = property (GetPlannedEndDate, SetPlannedEndDate, None, 'PlannedEndDate - assembly planned end date')
   ActualStartDate   = property (GetActualStartDate, SetActualStartDate, None, 'ActualStartDate - assembly actual start date')
   ActualEndDate     = property (GetActualEndDate, SetActualEndDate, None, 'ActualEndDate - assembly actual end date')
   EstimatedWeight   = property (GetEstimatedWeight, SetEstimatedWeight, None, 'EstimatedWeight - estimated weight of assembly object')
   EstimatedCOG      = property (GetEstimatedCOG, SetEstimatedCOG, None, 'EstimatedCOG - estimated center of gravity of assembly object')
   OrientationMatrix = property (GetOrientationMatrix, SetOrientationMatrix, None, 'OrientationMatrix - assembly orientation matrix')

   #readonly
   CalculatedWeight  = property (GetCalculatedWeight, None, None, 'CalculatedWeight - calcutated weight of assembly object (readonly)')
   CalculatedCOG     = property (GetCalculatedCOG, None, None, 'CalculatedCOG - calculated center of gravity of assembly object (readonly)')
   GlobalCoordBox    = property (GetGlobalCoordBox, None, None, 'GlobalCoordBox - assembly global coordinate bounding box (readonly)')
   AssemblyCoordBox  = property (GetAssemblyCoordBox, None, None, 'AssemblyCoordBox - assembly coordinates bounding box (readonly)')
