#
#      NAME:
#          KcsAssemblyKeyInItem.py
#
#      PURPOSE:
#          The class holds information about a key-in item
#
#          Do NOT change the names of the attributes, they are used by
#          the Vitesse interface. Users may only add or change methods
#

import types
import string
import copy

from KcsPoint3D import Point3D
from KcsBox import Box
from KcsDate import Date


class AssemblyKeyInItem(object):
   'holds information about assembly key-in item'

   __ErrorMessages = { TypeError : 'not supported argument type, see documentation of AssemblyKeyInItem class',
                     ValueError: 'not supported value, see documentation of AssemblyKeyInItem class' }

   ItemTypes = { 'key-in part' : 1, 'key-in component' : 2 }

#
#      METHOD:
#          __init__
#
#      PURPOSE:
#          To create an instance of the class
#
#      INPUT:
#          Parameters:
#
   def __init__(self):
      'constructor of AssemblyKeyInItem class'
      self.__ItemType   = self.ItemTypes['key-in part']
      self.__Name       = ''
      self.__COG        = Point3D()
      self.__Quantity   = 1
      self.__PosNumber  = ''
      self.__Quality    = ''
      self.__Weight     = 0.0
      self.__Descrip    = ''
      self.__Comment    = ''

#
#      METHOD:
#          __cmp__
#
#      PURPOSE:
#          implements cmp(o1, o2) function

   def __cmp__(self, other):
      'implements cmp(o1, o2) function'

      # if None object return not equal
      if type(other)==types.NoneType:
         return 1

      if not isinstance(other, AssemblyKeyInItem):
         raise TypeError, AssemblyKeyInItem.__ErrorMessages[TypeError]

      if self.__ItemType != other.__ItemType:
         return 1
      if string.lower(self.__Name) != string.lower(other.__Name):
         return 1
      if self.__COG != other.__COG:
         return 1
      if self.__Quantity != other.__Quantity:
         return 1
      if self.__PosNumber != other.__PosNumber:
         return 1
      if self.__Quality != other.__Quality:
         return 1
      if self.__Weight != other.__Weight:
         return 1
      if string.lower(self.__Descrip) != string.lower(other.__Descrip):
         return 1
      if string.lower(self.__Comment) != string.lower(other.__Comment):
         return 1
      return 0



#
#      METHOD:
#          __repr__
#
#      PURPOSE:
#          To print the class
#
   def __repr__(self):
      'returns string representation of AssemblyKeyInItem instance'

      tup = (
         'AssemblyKeyInItem:',
         '   Type: ' + str(self.ItemTypes.keys()[self.ItemTypes.values().index(self.__ItemType)]),
         '   Name: ' + self.__Name,
         '   COG: %f, %f, %f' % (self.__COG.X, self.__COG.Y, self.__COG.Z),
         '   Quantity: ' + str(self.__Quantity),
         '   Position Number: ' + str(self.__PosNumber),
         '   Quality: ' + str(self.__Quality),
         '   Weight: ' + str(self.__Weight),
         '   Description: ' + str(self.__Descrip),
         '   Comment: ' + self.__Comment
         )
      return string.join(tup, '\n')

# -----------------------------------------------------------------------------------------------------------------
#
#      METHOD:
#         SetItemType
#
#      PURPOSE:
#          set key-in item type
#
#      INPUT:
#          Parameters:
#          typestr        string         key-in item type
#
#      RESULT:
#          key-in item type will be updated
#

   def SetItemType(self, typestr):
      'sets key-in item type'

      if type(typestr) != types.StringType:
         raise TypeError, AssemblyKeyInItem.__ErrorMessages[TypeError]

      if typestr not in self.ItemTypes.keys():
         raise ValueError, AssemblyKeyInItem.__ErrorMessages[ValueError]

      self.__ItemType = self.ItemTypes[typestr]

# -----------------------------------------------------------------------------------------------------------------
#
#      METHOD:
#         GetItemType
#
#      PURPOSE:
#          get key-in item type
#
#      INPUT:
#          Parameters:
#            None
#
#      RESULT:
#          string         key-in item type
#

   def GetItemType(self):
      'returns key-in item type as string'
      return self.ItemTypes.keys()[self.ItemTypes.values().index(self.__ItemType)]

# -----------------------------------------------------------------------------------------------------------------
#
#      METHOD:
#         SetName
#
#      PURPOSE:
#          set key-in item name
#
#      INPUT:
#          Parameters:
#          name        string         key-in item name
#
#      RESULT:
#          key-in item name will be updated
#

   def SetName(self, name):
      'sets name of assembly key-in item'
      if type(name) != types.StringType:
         raise TypeError, AssemblyKeyInItem.__ErrorMessages[TypeError]

      self.__Name = name

# -----------------------------------------------------------------------------------------------------------------
#
#      METHOD:
#         GetName
#
#      PURPOSE:
#          get key-in item name
#
#      INPUT:
#          Parameters:
#            None
#
#      RESULT:
#          string         key-in item name
#

   def GetName(self):
      return self.__Name

# -----------------------------------------------------------------------------------------------------------------
#
#      METHOD:
#         SetDescription
#
#      PURPOSE:
#          set key-in item description
#
#      INPUT:
#          Parameters:
#          descrip        string         key-in item description
#
#      RESULT:
#          key-in item description will be updated
#

   def SetDescription(self, descrip):
      'sets description of key-in item'
      if type(descrip) != types.StringType:
         raise TypeError, AssemblyKeyInItem.__ErrorMessages[TypeError]

      self.__Descrip = descrip

# -----------------------------------------------------------------------------------------------------------------
#
#      METHOD:
#         GetDescription
#
#      PURPOSE:
#          get key-in item description
#
#      INPUT:
#          Parameters:
#            None
#
#      RESULT:
#          string         key-in item description
#

   def GetDescription(self):
      'returns description of key-in item as string'
      return self.__Descrip

# -----------------------------------------------------------------------------------------------------------------
#
#      METHOD:
#         SetCOG
#
#      PURPOSE:
#          set COG of key-in item
#
#      INPUT:
#          Parameters:
#          point           Point3D         COG as point
#              or
#          x, y, z         reals           COG as 3 reals
#
#      RESULT:
#          key-in item COG will be updated
#

   def SetCOG(self, *args):
      'sets key-in item center of gravity'
      if len(args)==1:              # 3D point
         if not isinstance(args[0], Point3D):
            raise TypeError, AssemblyKeyInItem.__ErrorMessages[TypeError]
         self.__COG = Point3D(args[0].X, args[0].Y, args[0].Z)
      elif len(args)==3:            # 3 reals x, y, z
         if (type(args[0]) != types.FloatType and type(args[0]) != types.IntType and type(args[0]) != types.LongType) or\
            (type(args[1]) != types.FloatType and type(args[1]) != types.IntType and type(args[0]) != types.LongType) or\
            (type(args[2]) != types.FloatType and type(args[2]) != types.IntType and type(args[0]) != types.LongType):
            raise TypeError, AssemblyKeyInItem.__ErrorMessages[TypeError]
         self.__COG = Point3D(args[0], args[1], args[2])
      else:
         raise TypeError, AssemblyKeyInItem.__ErrorMessages[TypeError]

# -----------------------------------------------------------------------------------------------------------------
#
#      METHOD:
#         GetCOG
#
#      PURPOSE:
#          get key-in item COG
#
#      INPUT:
#          Parameters:
#            None
#
#      RESULT:
#          Point3D         key-in item COG
#

   def GetCOG(self):
      'returns center of gravity as Point3D'
      return copy.deepcopy(self.__COG)

# -----------------------------------------------------------------------------------------------------------------
#
#      METHOD:
#         SetQuantity
#
#      PURPOSE:
#          set key-in item quantity
#
#      INPUT:
#          Parameters:
#          quantity        integer         key-in item quantity
#
#      RESULT:
#          key-in item quantity will be updated
#

   def SetQuantity(self, quantity):
      'sets key-in item quantity'
      if type(quantity) != types.IntType:
         raise TypeError, AssemblyKeyInItem.__ErrorMessages[TypeError]
      if quantity < 0:
         raise ValueError, AssemblyKeyInItem.__ErrorMessages[ValueError]

      self.__Quantity = quantity

# -----------------------------------------------------------------------------------------------------------------
#
#      METHOD:
#         GetQuantity
#
#      PURPOSE:
#          get key-in item quantity
#
#      INPUT:
#          Parameters:
#            None
#
#      RESULT:
#          integer         key-in item quantity
#

   def GetQuantity(self):
      'returns key-in item quantity'
      return self.__Quantity

# -----------------------------------------------------------------------------------------------------------------
#
#      METHOD:
#         SetPositionNumber
#
#      PURPOSE:
#          set key-in item position number
#
#      INPUT:
#          Parameters:
#          posnum        string         key-in item position number
#
#      RESULT:
#          key-in item position number will be updated
#

   def SetPositionNumber(self, posnum):
      'sets position number'
      if type(posnum) != types.StringType:
         raise TypeError, AssemblyKeyInItem.__ErrorMessages[TypeError]

      self.__PosNumber = posnum

# -----------------------------------------------------------------------------------------------------------------
#
#      METHOD:
#         GetPositionNumber
#
#      PURPOSE:
#          get key-in item position number
#
#      INPUT:
#          Parameters:
#            None
#
#      RESULT:
#          string         key-in item position number
#

   def GetPositionNumber(self):
      'returns position number of key-in item'
      return self.__PosNumber

# -----------------------------------------------------------------------------------------------------------------
#
#      METHOD:
#         SetQuality
#
#      PURPOSE:
#          set key-in item quality number
#
#      INPUT:
#          Parameters:
#          quality        string         key-in item quality
#
#      RESULT:
#          key-in item quality will be updated
#

   def SetQuality(self, quality):
      'sets quality of key-in item'
      if type(quality) != types.StringType:
         raise TypeError, AssemblyKeyInItem.__ErrorMessages[TypeError]

      self.__Quality = quality

# -----------------------------------------------------------------------------------------------------------------
#
#      METHOD:
#         GetQuality
#
#      PURPOSE:
#          get key-in item quality
#
#      INPUT:
#          Parameters:
#            None
#
#      RESULT:
#          string         key-in item quality
#

   def GetQuality(self):
      'returns quality of key-in item'
      return self.__Quality

# -----------------------------------------------------------------------------------------------------------------
#
#      METHOD:
#         SetWeight
#
#      PURPOSE:
#          set weight of key-in item
#
#      INPUT:
#          Parameters:
#          weight         real           key-in item weight
#
#      RESULT:
#          key-in item weight will be updated
#

   def SetWeight(self, weight):
      'sets weight of key-in item'
      if type(weight) != types.FloatType and type(weight) != types.IntType and type(weight) != types.LongType:
         raise TypeError, AssemblyKeyInItem.__ErrorMessages[TypeError]
      self.__Weight = weight + 0.0

# -----------------------------------------------------------------------------------------------------------------
#
#      METHOD:
#         GetWeight
#
#      PURPOSE:
#          get key-in item weight
#
#      INPUT:
#          Parameters:
#            None
#
#      RESULT:
#          real         key-in item weight
#

   def GetWeight(self):
      'returns key-in item weight'
      return self.__Weight

# -----------------------------------------------------------------------------------------------------------------
#
#      METHOD:
#         SetComment
#
#      PURPOSE:
#          set key-in item comment
#
#      INPUT:
#          Parameters:
#          comment        string         key-in item comment
#
#      RESULT:
#          key-in item comment will be updated
#

   def SetComment(self, comment):
      'sets comment of key-in item'
      if type(comment) != types.StringType:
         raise TypeError, AssemblyKeyInItem.__ErrorMessages[TypeError]

      self.__Comment = comment

# -----------------------------------------------------------------------------------------------------------------
#
#      METHOD:
#         GetComment
#
#      PURPOSE:
#          get key-in item comment
#
#      INPUT:
#          Parameters:
#            None
#
#      RESULT:
#          string         key-in item comment
#

   def GetComment(self):
      'returns key-in item comment'
      return self.__Comment

#-------------------------------------------------------------------------------------------------------------------
#
#      New style of access to attributes from Python version 2.2
#

   ItemType  = property (GetItemType, SetItemType, None, 'ItemType - assembly key-in item type string (one of AssemblyKeyInItem.ItemTypes)')
   Name      = property (GetName, SetName, None, 'Name - assembly key-in item name')
   COG       = property (GetCOG, SetCOG, None, 'COG - centre of gravity of assembly key-in item')
   Quantity  = property (GetQuantity, SetQuantity, None, 'Quantity - quantity of assembly key-in item')
   PosNumber = property (GetPositionNumber, SetPositionNumber, None, 'PosNumber - assembly key-in item position number')
   Quality   = property (GetQuality, SetQuality, None, 'Quality - assembly key-in item quality')
   Weight    = property (GetWeight, SetWeight, None, 'Weight - assembly key-in item weight')
   Descrip   = property (GetDescription, SetDescription, None, 'Descrip - assembly key-in item description')
   Comment   = property (GetComment, SetComment, None, 'Comment - assembly key-in item comment')
