
#
#      NAME:
#          KcsCap.py
#
#      PURPOSE:
#          The  class holds information about a cap. The cap consists
#          of an origin, a direction, a diameter and an amplitude.
#
#          Do NOT change the names of the attributes, they are used by
#          the Vitesse interface. Users may only add or change methods
#
#      ATTRIBUTES:
#          Origin         Point3D     The origin (centre of the base circle)
#          Direction      Vector3D    The direction (normal to the base circle)
#          Diameter       real        The diameter of the base circle
#          Amplitude      real        The amplitude

import string
import types
from KcsPoint3D import Point3D
from KcsVector3D import Vector3D

class Cap(object):

     __ErrorMessages = {  TypeError : 'not supported argument type, see documentation of Cap class',
                        ValueError: 'not supported value type, see documentation of Cap class' }

#
#      METHOD:
#          __init__
#
#      PURPOSE:
#          To create an instance of the class
#
#      INPUT:
#          Parameters:
#          orig          The origin
#          dir           The direction
#          diam          The diameter
#          ampl          The amplitude

     def __init__(self, orig, dir, diam, ampl):
        self.Origin = orig
        self.Direction = dir
        self.Diameter = diam
        self.Amplitude = ampl


#
#      METHOD:
#          __repr__
#
#      PURPOSE:
#          To print the class

     def __repr__(self):
      tup = (
            'Cap:',
            '   origin   :' + str(self.Origin),
            '   direction:' + str(self.Direction),
            '   diameter :' + str(self.Diameter),
            '   amplitude:' + str(self.Amplitude))
      return string.join (tup, '\n')

#-------------------------------------------------------------------------------------------------------------------
#
#      New style of access to attributes from Python version 2.2
#

     def getOrigin(self): return self.__Origin
     def setOrigin(self, point):
        if not isinstance(point,Point3D):
           raise ValueError, self.__ErrorMessages[ValueError]
        self.__Origin = Point3D(point.X, point.Y, point.Z)
     Origin    = property (getOrigin, setOrigin, None, 'Origin - cap origin')

     def getDirection(self): return self.__Direction
     def setDirection(self, vector):
        if not isinstance(vector, Vector3D):
           raise ValueError, self.__ErrorMessages[ValueError]
        self.__Direction = Vector3D(vector.X, vector.Y, vector.Z)
     Direction = property (getDirection, setDirection, None, 'Direction - cap direction vector')

     def getDiameter(self): return self.__Diameter
     def setDiameter(self, value):
        if not type(value) in (types.IntType, types.FloatType, types.LongType):
           raise ValueError, self.__ErrorMessages[ValueError]
        self.__Diameter = value
     Diameter  = property (getDiameter, setDiameter, None, 'Diameter - cap diameter')

     def getAmplitude(self): return self.__Amplitude
     def setAmplitude(self, value):
        if not type(value) in (types.IntType, types.FloatType, types.LongType):
           raise ValueError, self.__ErrorMessages[ValueError]
        self.__Amplitude = value
     Amplitude = property (getAmplitude, setAmplitude, None, 'Amplitude - cap amplitude')
