
#
#      NAME:
#          KcsCone.py
#
#      PURPOSE:
#          The cone class holds information about a cone. The cone consists
#          of an origin, a direction, a length and two diameters.
#
#          Do NOT change the names of the attributes, they are used by
#          the Vitesse interface. Users may only add or change methods
#
#      ATTRIBUTES:
#          Origin         Point3D      The origin (centre of first end circle)
#          Direction      Vector3D     The direction
#          Length         real         The length
#          Diameter1      real         The diameter of first end circle
#          Diameter2      real         The diameter of second end circle

from KcsPoint3D import Point3D
from KcsVector3D import Vector3D
import string
import types

ErrorMessages = { TypeError : 'not supported argument type, see documentation of KcsCone.Cone class',
                  ValueError : 'not supported argument value, see documentation of KcsCone.Cone class'}

class Cone(object):

#
#      METHOD:
#          __init__
#
#      PURPOSE:
#          To create an instance of the class. At least one diameter must be greater than 0.
#
#      INPUT:
#          Parameters:
#          orig           The origin
#          dir            The direction
#          len            The length
#          diam1          The first diameter
#          diam2          The second diameter (default diam1)

     def __init__(self, orig, dir, len, diam1, diam2 = None):
        if diam2==None:
           diam2 = diam1
        if not (diam1>0 or diam2>0):
           raise ValueError, ErrorMessages[ValueError]
        self.Origin = Point3D(orig.X,orig.Y,orig.Z)
        self.Direction = Vector3D(dir.X,dir.Y,dir.Z)
        self.Length = len
        self.Diameter1 = diam1
        self.Diameter2 = diam2


#
#      METHOD:
#          __repr__
#
#      PURPOSE:
#          To print the class

     def __repr__(self):
      tup = (
        'Cone:',
        '   origin   :' + str(self.Origin ),
        '   direction:' + str(self.Direction ),
        '   length   :' + str(self.Length ),
        '   diameter1:' + str(self.Diameter1 ),
        '   diameter2:' + str(self.Diameter2))
      return string.join (tup, '\n')


#-------------------------------------------------------------------------------------------------------------------
#
#      New style of access to attributes from Python version 2.2
#
     def getOrigin(self): return self.__Origin
     def setOrigin(self, value):
        if not isinstance(value,Point3D):
           raise TypeError, ErrorMessages[TypeError]
        self.__Origin = Point3D(value.X, value.Y, value.Z)
     Origin = property (getOrigin, setOrigin, None, 'Origin - cone origin (centre of first end circle)')

     def getDirection(self): return self.__Direction
     def setDirection(self, value):
        if not isinstance(value,Vector3D):
           raise TypeError, ErrorMessages[TypeError]
        self.__Direction = Vector3D(value.X, value.Y, value.Z)
     Direction = property (getDirection, setDirection, None, 'Direction - cone direction')

     def getLength(self): return self.__Length
     def setLength(self, value):
        if type(value) not in [types.IntType, types.LongType, types.FloatType]:
           raise TypeError, ErrorMessages[TypeError]
        self.__Length = value
     Length = property (getLength, setLength, None, 'Length - cone length')

     def getDiameter1(self): return self.__Diameter1
     def setDiameter1(self, value):
        if type(value) not in [types.IntType, types.LongType, types.FloatType]:
           raise TypeError, ErrorMessages[TypeError]
        if not (value>0 or self.__Diameter2>0):
           raise ValueError, ErrorMessages[ValueError]
        self.__Diameter1 = value
     Diameter1 = property (getDiameter1, setDiameter1, None, 'Diameter1 -  the diameter of first end circle')

     def getDiameter2(self): return self.__Diameter2
     def setDiameter2(self, value):
        if type(value) not in [types.IntType, types.LongType, types.FloatType]:
           raise TypeError, ErrorMessages[TypeError]
        if not (value>0 or self.__Diameter1>0):
           raise ValueError, ErrorMessages[ValueError]
        self.__Diameter2 = value
     Diameter2 = property (getDiameter2, setDiameter2, None, 'Diameter2 -  the diameter of first end circle')
