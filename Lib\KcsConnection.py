
#
#      NAME:
#          KcsConnection.py
#
#      PURPOSE:
#          The class holds information about a connection.
#
#          Do NOT change the names of the attributes, they are used by
#          the Vitesse interface. Users may only add or change methods
#
#      ATTRIBUTES:
#          Pos            Point3D       Origin of the connection
#          Dir            Vector3D      Direction of the connection
#          Type           integer       The type of the connection (1-9)
#          Number         integer       The number of the connection (1-199)
#          Descr          string        The description of the connection

from KcsPoint3D import Point3D
from KcsVector3D import Vector3D
import string

ErrorMessages = { TypeError : 'not supported argument type, see documentation of Connection class',
                  ValueError : 'not supported argument value, see documentation of Connection class'}

class Connection(object):

#
#      METHOD:
#          __init__
#
#      PURPOSE:
#          To create an instance of the class
#
#      INPUT:
#          Parameters:
#          pos           Point3D        Origin of the connection
#          dir           Vector3D       Direction of the connection
#          type          integer        Type of the connection
#          number        integer        Number of the connection
#          descr         string         Description of the connection

     def __init__(self, pos, dir, type, number, descr = ""):
        self.Pos = pos
        self.Dir = dir
        self.Type = type
        self.Number = number
        self.Descr = descr


#
#      METHOD:
#          __repr__
#
#      PURPOSE:
#          To print the class

     def __repr__(self):
      tup = (
        'Connection:',
        '   position   :' + str( self.Pos),
        '   direction  :' + str( self.Dir),
        '   type       :' + str( self.Type),
        '   number     :' + str( self.Number),
        '   description:' + str( self.Descr))
      return string.join (tup, '\n')

#-------------------------------------------------------------------------------------------------------------------
#
#      New style of access to attributes from Python version 2.2
#

     def getPos(self): return self.__Pos
     def setPos(self, value):
        if not isinstance(value, Point3D):
           raise TypeError, ErrorMessages[TypeError]
        self.__Pos = Point3D(value.X, value.Y, value.Z)
     Pos = property (getPos, setPos, None, 'Pos - origin of the connection')

     def getDir(self): return self.__Dir
     def setDir(self,value):
        if not isinstance(value, Vector3D):
           raise TypeError, ErrorMessages[TypeError]
        self.__Dir = Vector3D(value.X, value.Y, value.Z)
     Dir = property (getDir, setDir, None, 'Dir - direction of the connection')

     def getType(self): return self.__Type
     def setType(self, value):
        if not isinstance(value,int):
           raise TypeError, ErrorMessages[TypeError]
        if value<1 or value>9:
           raise ValueError, ErrorMessages[ValueError]
        self.__Type = value
     Type = property (getType, setType, None, 'Type - The type of the connection (1-9)')

     def getNumber(self): return self.__Number
     def setNumber(self, value):
        if not isinstance(value,int):
           raise TypeError, ErrorMessages[TypeError]
        if value<1 or value>199:
           raise ValueError, ErrorMessages[ValueError]
        self.__Number = value
     Number = property (getNumber, setNumber, None, 'Number - The number of the connection (1-199)')

     def getDescr(self): return self.__Descr
     def setDescr(self, value):
        if not isinstance(value,str):
           raise TypeError, ErrorMessages[TypeError]
        self.__Descr = value
     Descr = property (getDescr, setDescr, None, 'Descr - connection description')
