#
#      NAME:
#          KcsElementHandle.py
#
#      PURPOSE:
#          The ElementHandle class holds information about a element handle.
#
#          Do NOT change the names of the attributes, they are used by
#          the Vitesse interface. Users may only add or change methods
#
#      ATTRIBUTES:
#          __handle            integer        element handle

import types

class ElementHandle:

#
#      METHOD:
#          __init__
#
#      PURPOSE:
#          To create an instance of the class
#
#      INPUT:
#          Parameters:
#           None


   def __init__(self):
      self.__handle = -1

#
#      METHOD:
#          __repr__
#
#      PURPOSE:
#          To print the class

   def __repr__(self):
      return 'handle: %d' % self.__handle


#
#      METHOD:
#          __cmp__
#
#      PURPOSE:
#          Defines compare operator

   def __cmp__(self, other):
      if not isinstance(other, ElementHandle) and type(other) != type(1):
         return -1

      if type(other) == types.IntType:
         handle = other
      else:
         handle = other.__handle

      if self.__handle > handle:
         return 1
      elif self.__handle < handle:
         return -1
      else:
         return 0
