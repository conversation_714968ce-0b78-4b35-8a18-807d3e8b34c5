#
#      NAME:
#          PipeCheck.py
#
#      PURPOSE:
#          The PipeCheck class contains information which checks has to be performed
#
#          Do NOT change the names of the attributes, they are used by
#          the Vitesse interface. Users may only add or change methods
#
#      ATTRIBUTES:
#          __PosNoCheck          Integer       Position name checking
#          __ExcessCheck         Integer       Excess checking
#          __BendingCheck        Integer       Bending checking
#          __ExtrusionCheck      Integer       Extrusion checking
#          __FeedCheck           Integer       Feed checking
#          __FrameCheck          Integer       Frame checking
#          __LengthCheck         Integer       Length checking
#          __LooseCheck          Integer       Loose checking
#          __RotationCheck       Integer       Rotation checking
#          __JointCheck          Integer       Joint checking
#          __NonConnCheck        Integer       Non connected connection checking
#          __WeldGapsCheck       Integer       Weld gaps checking
#
#      METHODS:
#          SetPosNoCheck                        Check position name
#          GetPosNoCheck                        Gets check position name state
#          SetExcessCheck                       Check excess
#          GetExcessCheck                       Gets check excess state
#          SetBendingCheck                      Check bending
#          GetBendingCheck                      Gets check bending state
#          SetExtrusionCheck                    Check extrusion
#          GetExtrusionCheck                    Gets check extrusion state
#          SetFeedCheck                         Check feed
#          GetFeedCheck                         Gets check feed state
#          SetFrameCheck                        Check frame
#          GetFrameCheck                        Gets check frame state
#          SetLengthCheck                       Check length
#          GetLengthCheck                       Gets check length state
#          SetLooseCheck                        Check loose
#          GetLooseCheck                        Gets check loose state
#          SetRotationCheck                     Check rotation
#          GetRotationCheck                     Gets check rotation state
#          SetJointCheck                        Check joint
#          GetJointCheck                        Gets check joint state
#          SetNonConnCheck                      Check non connected
#          GetNonConnCheck                      Gets check non connected state
#          SetWeldGapsCheck                     Check weld gaps
#          GetWeldGapsCheck                     Gets check weld gaps state


import types
import string

ErrorMessages = { TypeError : 'not supported argument type, see documentation of PipeCheck class',
                  ValueError : 'not supported argument value, see documentation of PipeCheck class'}

class PipeCheck(object):

#
#      METHOD:
#          __init__
#
#      PURPOSE:
#          To create an instance of the class
#
#      INPUT:
#          Parameters:
#          None

    def __init__(self):
          self.__PosNoCheck          = 0
          self.__ExcessCheck         = 0
          self.__BendingCheck        = 0
          self.__ExtrusionCheck      = 0
          self.__FeedCheck           = 0
          self.__FrameCheck          = 0
          self.__LengthCheck         = 0
          self.__LooseCheck          = 0
          self.__RotationCheck       = 0
          self.__JointCheck          = 0
          self.__NonConnCheck        = 0
          self.__WeldGapsCheck       = 0

#
#      METHOD:
#          __repr__
#
#      PURPOSE:
#          To print the class

    def __repr__(self):
        tup = (
            "Position name      :" + str(self.__PosNoCheck),
            "Excess             :" + str(self.__ExcessCheck),
            "Bending            :" + str(self.__BendingCheck),
            "Extrusion          :" + str(self.__ExtrusionCheck),
            "Feed min           :" + str(self.__FeedCheck),
            "Frame              :" + str(self.__FrameCheck),
            "Length             :" + str(self.__LengthCheck),
            "Loose parts        :" + str(self.__LooseCheck),
            "Rotation           :" + str(self.__RotationCheck),
            "Joint              :" + str(self.__JointCheck),
            "Non connected      :" + str(self.__NonConnCheck),
            "Weld gaps          :" + str(self.__WeldGapsCheck))
        return string.join (tup, '\n')


#
#      METHOD:
#          SetPosNoCheck
#
#      PURPOSE:
#          To check position names
#
#      INPUT:
#          Parameters:
#          State       Integer               ( State=0 don't check , State=1 check)

    def SetPosNoCheck(self, State = 1):
            if type(State) != type(0):
                raise TypeError, ErrorMessages[TypeError]
            self.__PosNoCheck = State


#
#      METHOD:
#          GetPosNoCheck
#
#      PURPOSE:
#          To get check position name state
#      INPUT:
#          Parameters:
#          None
#
#      RESULT:
#          State


    def GetPosNoCheck(self):
        return self.__PosNoCheck


#
#      METHOD:
#          SetExcessCheck
#
#      PURPOSE:
#          To check excess
#
#      INPUT:
#          Parameters:
#          State       Integer               ( State=0 don't check , State=1 check)

    def SetExcessCheck(self, State = 1):
            if type(State) != type(0):
                raise TypeError, ErrorMessages[TypeError]
            self.__ExcessCheck = State


#
#      METHOD:
#          GetExcessCheck
#
#      PURPOSE:
#          To get check excess state
#      INPUT:
#          Parameters:
#          None
#
#      RESULT:
#          State


    def GetExcessCheck(self):
        return self.__ExcessCheck


#
#      METHOD:
#          SetBendingCheck
#
#      PURPOSE:
#          To check bending
#
#      INPUT:
#          Parameters:
#          State       Integer             ( State=0 don't check , State=1 check)

    def SetBendingCheck(self, State = 1):
            if type(State) != type(0):
                raise TypeError, ErrorMessages[TypeError]
            self.__BendingCheck = State


#
#      METHOD:
#          SetExtrusionCheck
#
#      PURPOSE:
#          To get check bending state
#      INPUT:
#          Parameters:
#          None
#
#      RESULT:
#          State


    def GetBendingCheck(self):
        return self.__BendingCheck


#
#      METHOD:
#          SetExtrusionCheck
#
#      PURPOSE:
#          To check extrusion
#
#      INPUT:
#          Parameters:
#          State       Integer                   ( State=0 don't check , State=1 check)

    def SetExtrusionCheck(self, State = 1):
            if type(State) != type(0):
                raise TypeError, ErrorMessages[TypeError]
            self.__ExtrusionCheck = State


#
#      METHOD:
#          GetExtrusionCheck
#
#      PURPOSE:
#          To get check extrusion state
#      INPUT:
#          Parameters:
#          None
#
#      RESULT:
#          State


    def GetExtrusionCheck(self):
        return self.__ExtrusionCheck

#
#      METHOD:
#          SetFeedCheck
#
#      PURPOSE:
#          To check feed
#
#      INPUT:
#          Parameters:
#          State       Integer               ( State=0 don't check , State=1 check)

    def SetFeedCheck(self, State = 1):
            if type(State) != type(0):
                raise TypeError, ErrorMessages[TypeError]
            self.__FeedCheck = State


#
#      METHOD:
#          GetFeedCheck
#
#      PURPOSE:
#          To get check feed state
#      INPUT:
#          Parameters:
#          None
#
#      RESULT:
#          State


    def GetFeedCheck(self):
        return self.__FeedCheck

#
#      METHOD:
#          SetFrameCheck
#
#      PURPOSE:
#          To check frame
#
#      INPUT:
#          Parameters:
#          State       Integer               ( State=0 don't check , State=1 check)

    def SetFrameCheck(self, State = 1):
            if type(State) != type(0):
                raise TypeError, ErrorMessages[TypeError]
            self.__FrameCheck = State


#
#      METHOD:
#          GetFrameCheck
#
#      PURPOSE:
#          To get check frame state
#      INPUT:
#          Parameters:
#          None
#
#      RESULT:
#          State


    def GetFrameCheck(self):
        return self.__FrameCheck

#
#      METHOD:
#          SetLengthCheck
#
#      PURPOSE:
#          To check length
#
#      INPUT:
#          Parameters:
#          State       Integer               ( State=0 don't check , State=1 check)

    def SetLengthCheck(self, State = 1):
            if type(State) != type(0):
                raise TypeError, ErrorMessages[TypeError]
            self.__LengthCheck = State


#
#      METHOD:
#          GetLengthCheck
#
#      PURPOSE:
#          To get check length state
#      INPUT:
#          Parameters:
#          None
#
#      RESULT:
#          State


    def GetLengthCheck(self):
        return self.__LengthCheck

#
#      METHOD:
#          SetLooseCheck
#
#      PURPOSE:
#          To check loose
#
#      INPUT:
#          Parameters:
#          State       Integer               ( State=0 don't check , State=1 check)

    def SetLooseCheck(self, State = 1):
            if type(State) != type(0):
                raise TypeError, ErrorMessages[TypeError]
            self.__LooseCheck = State


#
#      METHOD:
#          GetLooseCheck
#
#      PURPOSE:
#          To get check loose state
#      INPUT:
#          Parameters:
#          None
#
#      RESULT:
#          State


    def GetLooseCheck(self):
        return self.__LooseCheck

#
#      METHOD:
#          SetRotationCheck
#
#      PURPOSE:
#          To check rotation
#
#      INPUT:
#          Parameters:
#          State       Integer               ( State=0 don't check , State=1 check)

    def SetRotationCheck(self, State = 1):
            if type(State) != type(0):
                raise TypeError, ErrorMessages[TypeError]
            self.__RotationCheck = State


#
#      METHOD:
#          GetRotationCheck
#
#      PURPOSE:
#          To get check rotation state
#      INPUT:
#          Parameters:
#          None
#
#      RESULT:
#          State


    def GetRotationCheck(self):
        return self.__RotationCheck


#
#      METHOD:
#          SetJointCheck
#
#      PURPOSE:
#          To check joint
#
#      INPUT:
#          Parameters:
#          State       Integer               ( State=0 don't check , State=1 check)

    def SetJointCheck(self, State = 1):
            if type(State) != type(0):
                raise TypeError, ErrorMessages[TypeError]
            self.__JointCheck = State


#
#      METHOD:
#          GetJointCheck
#
#      PURPOSE:
#          To get check joint state
#      INPUT:
#          Parameters:
#          None
#
#      RESULT:
#          State


    def GetJointCheck(self):
        return self.__JointCheck

#
#      METHOD:
#          SetNonConnCheck
#
#      PURPOSE:
#          To check non connected connections
#
#      INPUT:
#          Parameters:
#          State       Integer               ( State=0 don't check , State=1 check)

    def SetNonConnCheck(self, State = 1):
            if type(State) != type(0):
                raise TypeError, ErrorMessages[TypeError]
            self.__NonConnCheck = State


#
#      METHOD:
#          GetNonConnCheck
#
#      PURPOSE:
#          To get check non connected connections state
#      INPUT:
#          Parameters:
#          None
#
#      RESULT:
#          State


    def GetNonConnCheck(self):
        return self.__NonConnCheck

#
#      METHOD:
#          SetWeldGapsCheck
#
#      PURPOSE:
#          To check weld gaps
#
#      INPUT:
#          Parameters:
#          State       Integer               ( State=0 don't check , State=1 check)

    def SetWeldGapsCheck(self, State = 1):
            if type(State) != type(0):
                raise TypeError, ErrorMessages[TypeError]
            self.__WeldGapsCheck = State


#
#      METHOD:
#          GetWeldGapsCheck
#
#      PURPOSE:
#          To get check weld gaps state
#      INPUT:
#          Parameters:
#          None
#
#      RESULT:
#          State


    def GetWeldGapsCheck(self):
        return self.__WeldGapsCheck

#-------------------------------------------------------------------
#
#      New style of access to attributes from Python version 2.2
#
    PosNoCheck          = property (GetPosNoCheck, SetPosNoCheck)
    ExcessCheck         = property (GetExcessCheck, SetExcessCheck)
    BendingCheck        = property (GetBendingCheck, SetBendingCheck)
    ExtrusionCheck      = property (GetExtrusionCheck, SetExtrusionCheck)
    FeedCheck           = property (GetFeedCheck ,SetFeedCheck )
    FrameCheck          = property (GetFrameCheck , SetFrameCheck)
    LengthCheck         = property (GetLengthCheck , SetLengthCheck)
    LooseCheck          = property (GetLooseCheck , SetLooseCheck)
    RotationCheck       = property (GetRotationCheck , SetRotationCheck)
    JointCheck          = property (GetJointCheck , SetJointCheck)
    NonConnCheck        = property (GetNonConnCheck , SetNonConnCheck)
    WeldGapsCheck       = property (GetWeldGapsCheck , SetWeldGapsCheck)
