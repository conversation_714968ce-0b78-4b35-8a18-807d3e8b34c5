#
#      NAME:
#          PipeCheckSettings.py
#
#      PURPOSE:
#          The PipeCheckSettings class contains settings for pipe check functions
#
#          Do NOT change the names of the attributes, they are used by
#          the Vitesse interface. Users may only add or change methods
#
#      ATTRIBUTES:
#          __PosNoCheck          Integer       Position name checking on/off
#          __BendingCheck        Integer       Bending checking on/off
#          __ExtrusionCheck      Integer       Extrusion checking on/off
#
#      METHODS:
#          SetPosNoCheck                        Sets/resets position name checking
#          GetPosNoCheck                        Gets position name checking state
#          SetBendingCheck                      Sets/resets bending checking
#          GetBendingCheck                      Gets bending checking state
#          SetExtrusionCheck                    Sets/resets extrusion checking
#          GetExtrusionCheck                    Gets extrusion checking state


import types
import string

ErrorMessages = { TypeError : 'not supported argument type, see documentation of PipeCheckSettings class',
                  ValueError : 'not supported argument value, see documentation of PipeCheckSettings class'}

class PipeCheckSettings(object):

#
#      METHOD:
#          __init__
#
#      PURPOSE:
#          To create an instance of the class
#
#      INPUT:
#          Parameters:
#          None

    def __init__(self):
          self.__PosNoCheck         = None
          self.__BendCheck          = None
          self.__ExtrusionCheck     = None

#
#      METHOD:
#          __repr__
#
#      PURPOSE:
#          To print the class

    def __repr__(self):
        tup = (
            "Check position name :" + str(self.__PosNoCheck),
            "Check bending       :" + str(self.__BendCheck),
            "Check extrusion     :" + str(self.__ExtrusionCheck))
        return string.join (tup, '\n')


#
#      METHOD:
#          SetPosNoCheck
#
#      PURPOSE:
#          To set position name checking state
#
#      INPUT:
#          Parameters:
#          State       Integer              Position name checking state
#                                           ( State=0 off , State=1 on)

    def SetPosNoCheck(self, State = 1):
            if State!=None and type(State) != type(0):
                raise TypeError, ErrorMessages[TypeError]
            self.__PosNoCheck = State


#
#      METHOD:
#          GetPosNoCheck
#
#      PURPOSE:
#          To get position name checking state
#      INPUT:
#          Parameters:
#          None
#
#      RESULT:
#          Position name checking state


    def GetPosNoCheck(self):
        return self.__PosNoCheck


#
#      METHOD:
#          SetBendingCheck
#
#      PURPOSE:
#          To set bending checking state
#
#      INPUT:
#          Parameters:
#          State       Integer              Bending checking state
#                                           ( State=0 off , State=1 on)

    def SetBendingCheck(self, State = 1):
            if State!=None and type(State) != type(0):
                raise TypeError, ErrorMessages[TypeError]
            self.__BendCheck = State


#
#      METHOD:
#          SetExtrusionCheck
#
#      PURPOSE:
#          To get bending checking state
#      INPUT:
#          Parameters:
#          None
#
#      RESULT:
#          Bending checking state


    def GetBendingCheck(self):
        return self.__BendCheck


#
#      METHOD:
#          SetExtrusionCheck
#
#      PURPOSE:
#          To set extrusion checking state
#
#      INPUT:
#          Parameters:
#          State       Integer              Extrusion checking state
#                                           ( State=0 off , State=1 on)

    def SetExtrusionCheck(self, State = 1):
            if State!=None and type(State) != type(0):
                raise TypeError, ErrorMessages[TypeError]
            self.__ExtrusionCheck = State


#
#      METHOD:
#          GetExtrusionCheck
#
#      PURPOSE:
#          To get extrusion checking state
#      INPUT:
#          Parameters:
#          None
#
#      RESULT:
#          Extrusion checking state


    def GetExtrusionCheck(self):
        return self.__ExtrusionCheck

#-------------------------------------------------------------------
#
#      New style of access to attributes from Python version 2.2
#
    PosNoCheck = property (GetPosNoCheck , SetPosNoCheck)
    BendingCheck = property (GetBendingCheck , SetBendingCheck)
    ExtrusionCheck = property (GetExtrusionCheck , SetExtrusionCheck)

