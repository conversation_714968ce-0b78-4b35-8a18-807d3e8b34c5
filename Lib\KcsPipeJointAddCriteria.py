#
#      NAME:
#          PipeJointAddCriteria.py
#
#      PURPOSE:
#          The PipeJointAddCriteria class contains information how add joint
#
#          Do NOT change the names of the attributes, they are used by
#          the Vitesse interface. Users may only add or change methods
#
#      ATTRIBUTES:
#          __JointType         String       Joint type
#          __Direction         Vector3D     Angled joint direction
#          __ExtPipeName       PipeName     External pipe name
#
#      METHODS:
#          SetJointType                     Sets joint type
#          GetJointType                     Gets joint type
#          SetDirection                     Sets angled joint direction
#          GetDirection                     Gets angled joint direction
#          SetExternalPipe                  Sets external pipe name
#          GetExternalPipe                  Gets external pipe name
#          SetDistance                      Sets insert distance
#          GetDistance                      Gets insert distance


import types
import string
import KcsVector3D
import KcsPipeName


class PipeJointAddCriteria(object):

#
#    Variables
#
    ErrorMessages = { TypeError : 'not supported argument type, see documentation of PipeJointAddCriteria class',
                      ValueError : 'not supported argument value, see documentation of PipeJointAddCriteria class'}

    JointTypes    = ("insert", "thread", "weld", "mitre")

#
#      METHOD:
#          __init__
#
#      PURPOSE:
#          To create an instance of the class
#
#      INPUT:
#          Parameters:
#          None

    def __init__(self):
          self.__JointType          = self.JointTypes[0]
          self.__Direction          = None
          self.__ExtPipeName        = None
          self.__Distance           = None
#
#      METHOD:
#          __repr__
#
#      PURPOSE:
#          To print the class

    def __repr__(self):
        tup = (
            "Joint type     :" + str(self.__JointType),
            "Direction      :" + str(self.__Direction),
            "Distance       :" + str(self.__Distance),
            "Pipe name      :" + str(self.__ExtPipeName ))
        return string.join (tup, '\n')

#
#      METHOD:
#          SetJointType
#
#      PURPOSE:
#          To set joint type
#
#      INPUT:
#          Parameters:
#          Type       String               Joint type

    def SetJointType(self, Type):
            if type(Type) != type(""):
                raise TypeError, self.ErrorMessages[TypeError]

            if Type not in self.JointTypes:
                raise ValueError, self.ErrorMessages[ValueError]

            self.__JointType = Type


#
#      METHOD:
#          GetJointType
#
#      PURPOSE:
#          To get joint type
#      INPUT:
#          Parameters:
#          None
#
#      RESULT:
#          joint type


    def GetJointType(self):
        return self.__JointType

#
#      METHOD:
#          SetDirection
#
#      PURPOSE:
#          To set joint direction
#
#      INPUT:
#          Parameters:
#          Type       String               Direction

    def SetDirection(self, Direction):
            if not isinstance(Direction, KcsVector3D.Vector3D) and Direction != None:
                raise TypeError, self.ErrorMessages[TypeError]

            self.__Direction = Direction


#
#      METHOD:
#          GetDirection
#
#      PURPOSE:
#          To get joint direction
#      INPUT:
#          Parameters:
#          None
#
#      RESULT:
#          Direction


    def GetDirection(self):
        return self.__Direction

#
#      METHOD:
#          SetExternalPipe
#
#      PURPOSE:
#          To set external pipe name
#
#      INPUT:
#          Parameters:
#          Parameters:
#          name       1)PipeName               Pipe name
#                     2)String                 Pipe name

    def SetExternalPipe(self, Name):
            if type(Name) == type(""):
                self.__ExtPipeName = KcsPipeName.PipeName(Name)
            elif isinstance(Name, KcsPipeName.PipeName) or Name == None:
                self.__ExtPipeName = Name
            else:
                raise TypeError, self.ErrorMessages[TypeError]


#
#      METHOD:
#          GetExternalPipe
#
#      PURPOSE:
#          To get external pipe name
#      INPUT:
#          Parameters:
#          None
#
#      RESULT:
#          Pipe name


    def GetExternalPipe(self):
        return self.__ExtPipeName

#
#      METHOD:
#          SetDistance
#
#      PURPOSE:
#          To set distance from connection to insert node
#
#      INPUT:
#          Parameters:
#          Distance       Real             Distance

    def SetDistance(self, Distance):
            if type(Distance) != type(0.0) and type(Distance) != type(0) and Distance != None:
                raise TypeError, ErrorMessages[TypeError]
            self.__Distance = Distance


#
#      METHOD:
#          GetDistance
#
#      PURPOSE:
#          To get insert distance
#      INPUT:
#          Parameters:
#          None
#
#      RESULT:
#          Distance


    def GetDistance(self):
        return self.__Distance

#-------------------------------------------------------------------
#
#      New style of access to attributes from Python version 2.2
#
    JointType          = property (GetJointType , SetJointType)
    Direction          = property (GetDirection , SetDirection)
    ExtPipeName        = property (GetExternalPipe , SetExternalPipe)
    Distance           = property (GetDistance , SetDistance)
