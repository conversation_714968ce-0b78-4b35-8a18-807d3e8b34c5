#
#      NAME:
#          PipeMaterial.py
#
#      PURPOSE:
#          The PipeMaterial class contains add material information
#
#          Do NOT change the names of the attributes, they are used by
#          the Vitesse interface. Users may only add or change methods
#
#      ATTRIBUTES:
#          __AddType           Integer      Add type ( 0= add material to pipe,
#                                                      1= add to straight part,
#                                                      2= add to bend part,
#                                                      3= add mitre material)
#          __StraightMat       String       Straight material component
#          __BendMat           String       Bend material component
#
#      METHODS:
#          SetStraightMaterial              Sets material for straight parts
#          GetStraightMaterial              Gets straight material
#          SetBendMaterial                  Sets material for bend parts
#          GetBendMaterial                  Gets bend material
#          SetType                          Sets add operation type
#          GetType                          Gets add operation type


import types
import string


class PipeMaterial(object):

#
#    Variables
#
    ErrorMessages = { TypeError : 'not supported argument type, see documentation of PipeMaterial class',
                      ValueError : 'not supported argument value, see documentation of PipeMaterial class'}

    AddType = {  'pipe'     : 0,
                 'straight' : 1,
                 'bend'     : 2,
                 'mitre'    : 3}
#
#      METHOD:
#          __init__
#
#      PURPOSE:
#          To create an instance of the class
#
#      INPUT:
#          Parameters:
#          None

    def __init__(self):
          self.__AddType            = 0
          self.__StraightMat        = None
          self.__BendMat            = None
#
#      METHOD:
#          __repr__
#
#      PURPOSE:
#          To print the class

    def __repr__(self):
        tup = (
            "Add type           :" + str(self.GetType()),
            "Straight material  :" + str(self.__StraightMat),
            "Bend material      :" + str(self.__BendMat ))
        return string.join (tup, '\n')

#
#      METHOD:
#          SetType
#
#      PURPOSE:
#          To set add material operation type
#
#      INPUT:
#          Parameters:
#          Type       String               Type

    def SetType(self, Type):
            if type(Type) != type(""):
                raise TypeError, self.ErrorMessages[TypeError]

            if Type not in self.AddType.keys():
                raise ValueError, self.ErrorMessages[ValueError]

            self.__AddType = self.AddType[Type]


#
#      METHOD:
#          GetType
#
#      PURPOSE:
#          To get operation type
#      INPUT:
#          Parameters:
#          None
#
#      RESULT:
#          Route type


    def GetType(self):
        index = self.AddType.values().index(self.__AddType)
        return self.AddType.keys()[index]


#
#      METHOD:
#          SetStraightMaterial
#
#      PURPOSE:
#          To set straight material
#
#      INPUT:
#          Parameters:
#          Material       String               Straight material

    def SetStraightMaterial(self, Material):
            if type(Material) != type("") and Material != None:
                raise TypeError, self.ErrorMessages[TypeError]

            self.__StraightMat = Material


#
#      METHOD:
#          GetStraightMaterial
#
#      PURPOSE:
#          To get straight material
#      INPUT:
#          Parameters:
#          None
#
#      RESULT:
#          Material


    def GetStraightMaterial(self):
        return self.__StraightMat


#
#      METHOD:
#          SetBendMaterial
#
#      PURPOSE:
#          To set bend material
#
#      INPUT:
#          Parameters:
#          Material       String               Bend material

    def SetBendMaterial(self, Material):
            if type(Material) != type("") and Material != None:
                raise TypeError, self.ErrorMessages[TypeError]

            self.__BendMat = Material


#
#      METHOD:
#          GetBendMaterial
#
#      PURPOSE:
#          To get bend material
#      INPUT:
#          Parameters:
#          None
#
#      RESULT:
#          Material


    def GetBendMaterial(self):
        return self.__BendMat

#-------------------------------------------------------------------
#
#      New style of access to attributes from Python version 2.2
#

    Type = property ( GetType , SetType)
    StraightMat = property ( GetStraightMaterial , SetStraightMaterial)
    BendMat = property ( GetBendMaterial , SetBendMaterial )
