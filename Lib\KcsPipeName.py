#
#      NAME:
#          PipeName.py
#
#      PURPOSE:
#          The PipeName class contains information about pipe name
#
#          Do NOT change the names of the attributes, they are used by
#          the Vitesse interface. Users may only add or change methods
#
#      ATTRIBUTES:
#          __Name            String        Pipe Name
#
#      METHODS:
#          GetModule                           Gets module name
#          GetSystem                           Gets system name
#          GetLineNo                           Gets line number
#          SetName                             Sets pipe name
#          GetName                             Gets pipe name
#          SplitName                           Gets pipe module, system, line number

import types
import string

ErrorMessages = { TypeError : 'not supported argument type, see documentation of PipeName class',
                  ValueError : 'not supported argument value, see documentation of PipeName class'}

class PipeName(object):

#
#      METHOD:
#          __init__
#
#      PURPOSE:
#          To create an instance of the class
#
#      INPUT:
#          Parameters:
#          args       argument list              Pipe Name defined as one of cases below
#                     1) None                    Empty name
#                     2) string                  Pipe name
#                     3) string,string,integer   Module, System , Line number

    def __init__(self, *args ):
        if len(args) == 0:
            self.__Name = ""
        else:
            self.SetName(*args)

#
#      METHOD:
#          __repr__
#
#      PURPOSE:
#          To print the class

    def __repr__(self):
        return self.GetName()

#
#      METHOD:
#          GetModule
#
#      PURPOSE:
#          To get module name
#
#      INPUT:
#          Parameters:
#          None
#
#      RESULT:
#          Module name


    def GetModule(self):

        return self.SplitName()[0]

#
#      METHOD:
#          GetSystem
#
#      PURPOSE:
#          To get system name
#
#      INPUT:
#          Parameters:
#          None
#
#      RESULT:
#          System name


    def GetSystem(self):
        return self.SplitName()[1]

#
#      METHOD:
#          GetLineNo
#
#      PURPOSE:
#          To get line number
#
#      INPUT:
#          Parameters:
#          None
#
#      RESULT:
#          Line number


    def GetLineNo(self):
        return self.SplitName()[2]

#
#      METHOD:
#          SetName
#
#      PURPOSE:
#          To set pipe name
#
#      INPUT:
#          Parameters:
#          args       argument list              Pipe Name defined as one of cases below
#                     1) string                  Pipe name
#                     2) string,string,integer   Module, System , Line number


    def SetName(self, *args):
        if len(args) == 1 :
            Name = args[0]
            if type(Name) == type(""):
                self.__Name = Name
            elif type(Name) == types.TupleType:
                if len(Name) == 4:
                    self.__Name = Name[0]+"-"+Name[1]+"-"+Name[2]+str(Name[3])
                elif len(Name) == 3:
                    self.__Name = Name[0]+"-"+Name[1]+str(Name[2])
                else :
                    raise ValueError, ErrorMessages[ValueError]
            else :
                raise TypeError, ErrorMessages[TypeError]
            #check if name is correct
            self.SplitName()
        elif len(args) == 3 :
            if type(args[0]) != type("") or type(args[1]) != type("") or type(args[2]) != type(0):
                raise TypeError, ErrorMessages[TypeError]

            Name = args[0]+"-"+args[1]+str(args[2])
            self.__Name = Name
            #check if name is correct
            self.SplitName()
        else:
            raise ValueError, ErrorMessages[ValueError]


#
#      METHOD:
#          GetName
#
#      PURPOSE:
#          To get pipe name
#
#      INPUT:
#          Parameters:
#          None
#
#      RESULT:
#          Pipe name


    def GetName(self):
        return self.__Name


#
#      METHOD:
#          SplitName
#
#      PURPOSE:
#          To split pipe name into module,system,line number
#
#      INPUT:
#          Parameters:
#          None
#
#      RESULT:
#          Pipe name


    def SplitName(self):
        res = string.split(self.__Name, "-")

        #acceptable format P-M-SL or M-SL
        if (len(res)<2 or len(res)>3):
            raise ValueError, ErrorMessages[ValueError]

        moduleName      = res[-2]
        subsystemName   = res[-1]

        #separate line number digits
        nIndex = 0
        for nIndex in range(-1, -len(subsystemName), -1):
            if subsystemName[nIndex] not in string.digits :
                nIndex = nIndex+1
                break

        systemName = subsystemName[:nIndex]
        lineName   = subsystemName[nIndex:]

        #convert line number to integer
        try:
            lineNo = int(lineName)
        except:
            raise ValueError, ErrorMessages[ValueError]

        #check if names are not empty
        if len(moduleName)==0 or len (systemName)==0 or len(lineName)==0 :
            raise ValueError, ErrorMessages[ValueError]

        return moduleName, systemName, lineNo

#-------------------------------------------------------------------
#
#      New style of access to attributes from Python version 2.2
#
    Name = property (GetName, SetName)
