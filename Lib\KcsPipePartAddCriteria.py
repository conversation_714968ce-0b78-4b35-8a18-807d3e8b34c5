#
#      NAME:
#          PipePartAddCriteria.py
#
#      PURPOSE:
#          The PipePartAddCriteria class contains information how add part
#
#          Do NOT change the names of the attributes, they are used by
#          the Vitesse interface. Users may only add or change methods
#
#      ATTRIBUTES:
#          __AddType           Integer      Add type ( 0= Add to part, 1= Add on surface)
#          __Component         String       Component name
#          __SurfPoint         Point3D      Point on surface  (required for on surface connection)
#          __Direction         Vector3D     Part direction     (optional)
#          __Orientation       Vector3D     Part orientation   (optional)
#          __Length            Vector3D     Part orientation   (optional)
#          __ExtPipeName       PipeName     External pipe name (optional)
#          __Distance          Real         Distance of insert (optional)
#          __Angle             Real         Cut angle (radians)(optional)
#          __CutBlenType       Integer      Cut building lenght type (0 = No cut, 1 = Cut) (optional)
#
#      METHODS:
#          SetConnType                      Sets connection type
#          GetConnType                      Gets connection type
#          SetComponent                     Sets part component
#          GetComponent                     Gets part component
#          SetLength                        Sets part lenght
#          GetLength                        Gets part length
#          SetSurfPoint                     Sets surface point
#          GetSurfPoint                     Gets surface point
#          SetDirection                     Sets part direction
#          GetDirection                     Gets part direction
#          SetOrientation                   Sets part orientation
#          GetOrientation                   Gets part orientation
#          SetExternalPipe                  Sets external pipe name
#          GetExternalPipe                  Gets external pipe name
#          SetDistance                      Sets insert distance
#          GetDistance                      Gets insert distance
#          SetAngle                         Sets cut angle
#          GetAngle                         Gets cut angle
#          SetCutBlenType                   Sets building length type
#          GetCutBlenType                   Gets building length type


import types
import string
import KcsPoint3D
import KcsVector3D
import KcsPipeName


class PipePartAddCriteria(object):

#
#    Variables
#
    ErrorMessages = { TypeError : 'not supported argument type, see documentation of PipePartAddCriteria class',
                      ValueError : 'not supported argument value, see documentation of PipePartAddCriteria class'}

    ConnectionType = { 'part'       : 0,
                       'surface'    : 1}
#
#      METHOD:
#          __init__
#
#      PURPOSE:
#          To create an instance of the class
#
#      INPUT:
#          Parameters:
#          None

    def __init__(self):
          self.__AddType            = 0
          self.__Component          = None
          self.__Length             = None
          self.__SurfPoint          = None
          self.__Orientation        = None
          self.__Direction          = None
          self.__ExtPipeName        = None
          self.__Distance           = None
          self.__Angle              = None
          self.__CutBlenType        = 0
#
#      METHOD:
#          __repr__
#
#      PURPOSE:
#          To print the class

    def __repr__(self):
        tup = (
            "Add type        :" + str(self.GetConnType()),
            "Component       :" + str(self.__Component),
            "Surface point   :" + str(self.__SurfPoint),
            "Direction       :" + str(self.__Direction),
            "Oreintation     :" + str(self.__Orientation),
            "Length          :" + str(self.__Length),
            "Distance        :" + str(self.__Distance),
            "Angle           :" + str(self.__Angle),
            "Cut Blen type   :" + str(self.__CutBlenType),
            "Pipe name       :" + str(self.__ExtPipeName ))
        return string.join (tup, '\n')

#
#      METHOD:
#          SetConnType
#
#      PURPOSE:
#          To set connection type
#
#      INPUT:
#          Parameters:
#          Type       String               Connection type

    def SetConnType(self, Type):
            if type(Type) != type(""):
                raise TypeError, self.ErrorMessages[TypeError]

            if Type not in self.ConnectionType.keys():
                raise ValueError, self.ErrorMessages[ValueError]

            self.__AddType = self.ConnectionType[Type]


#
#      METHOD:
#          GetConnType
#
#      PURPOSE:
#          To get connection type
#      INPUT:
#          Parameters:
#          None
#
#      RESULT:
#          joint type


    def GetConnType(self):
        index = self.ConnectionType.values().index(self.__AddType)
        return self.ConnectionType.keys()[index]

#
#      METHOD:
#          SetDirection
#
#      PURPOSE:
#          To set part direction
#
#      INPUT:
#          Parameters:
#          Type       Vector3D               Direction

    def SetDirection(self, Direction):
            if not isinstance(Direction, KcsVector3D.Vector3D) and Direction != None:
                raise TypeError, self.ErrorMessages[TypeError]

            self.__Direction = Direction


#
#      METHOD:
#          GetDirection
#
#      PURPOSE:
#          To get part direction
#      INPUT:
#          Parameters:
#          None
#
#      RESULT:
#          Direction


    def GetDirection(self):
        return self.__Direction

#
#      METHOD:
#          SetOrientation
#
#      PURPOSE:
#          To set part orientation
#
#      INPUT:
#          Parameters:
#          Type       Vector3D               Orientation

    def SetOrientation(self, Orientation):
            if not isinstance(Orientation, KcsVector3D.Vector3D) and Orientation != None:
                raise TypeError, self.ErrorMessages[TypeError]

            self.__Orientation = Orientation


#
#      METHOD:
#          GetOrientation
#
#      PURPOSE:
#          To get part Orientation
#      INPUT:
#          Parameters:
#          None
#
#      RESULT:
#          Orientation


    def GetOrientation(self):
        return self.__Orientation


#
#      METHOD:
#          SetExternalPipe
#
#      PURPOSE:
#          To set external pipe name
#
#      INPUT:
#          Parameters:
#          name       1)PipeName               Pipe name
#                     2)String                 Pipe name

    def SetExternalPipe(self, Name):
            if type(Name) == type(""):
                self.__ExtPipeName = KcsPipeName.PipeName(Name)
            elif isinstance(Name, KcsPipeName.PipeName) or Name == None:
                self.__ExtPipeName = Name
            else:
                raise TypeError, self.ErrorMessages[TypeError]


#
#      METHOD:
#          GetExternalPipe
#
#      PURPOSE:
#          To get external pipe name
#      INPUT:
#          Parameters:
#          None
#
#      RESULT:
#          Pipe name


    def GetExternalPipe(self):
        return self.__ExtPipeName

#
#      METHOD:
#          SetComponent
#
#      PURPOSE:
#          To set part component
#
#      INPUT:
#          Parameters:
#          Type       String               Component

    def SetComponent(self, Component):
            if type(Component) != type("") and Component != None:
                raise TypeError, self.ErrorMessages[TypeError]

            self.__Component = Component


#
#      METHOD:
#          GetComponent
#
#      PURPOSE:
#          To get part component
#      INPUT:
#          Parameters:
#          None
#
#      RESULT:
#          Component


    def GetComponent(self):
        return self.__Component

#
#      METHOD:
#          SetLength
#
#      PURPOSE:
#          To set part length
#
#      INPUT:
#          Parameters:
#          Type       String               Length

    def SetLength(self, Length):
            if type(Length) != type(0.0) and type(Length) != type(0) and Length != None:
                raise TypeError, self.ErrorMessages[TypeError]

            self.__Length = Length


#
#      METHOD:
#          SetLength
#
#      PURPOSE:
#          To get part length
#      INPUT:
#          Parameters:
#          None
#
#      RESULT:
#          Length


    def GetLength(self):
        return self.__Length

#
#      METHOD:
#          SetSurfPoint
#
#      PURPOSE:
#          To set surface point ( required for on surface connections)
#
#      INPUT:
#          Parameters:
#          Point       Point3D               Surface point

    def SetSurfPoint(self, Point):
            if not isinstance(Point, KcsPoint3D.Point3D) and Point != None:
                raise TypeError, self.ErrorMessages[TypeError]

            self.__SurfPoint = Point


#
#      METHOD:
#          GetSurfPoint
#
#      PURPOSE:
#          To get surface point
#      INPUT:
#          Parameters:
#          None
#
#      RESULT:
#          Surface point


    def GetSurfPoint(self):
        return self.__SurfPoint


#
#      METHOD:
#          SetDistance
#
#      PURPOSE:
#          To set distance from connection to inserted part node
#
#      INPUT:
#          Parameters:
#          Distance       Real             Distance

    def SetDistance(self, Distance):
            if type(Distance) != type(0.0) and type(Distance) != type(0) and Distance != None:
                raise TypeError, ErrorMessages[TypeError]
            self.__Distance = Distance


#
#      METHOD:
#          GetDistance
#
#      PURPOSE:
#          To get insert distance
#      INPUT:
#          Parameters:
#          None
#
#      RESULT:
#          Distance


    def GetDistance(self):
        return self.__Distance
#
#      METHOD:
#          SetAngle
#
#      PURPOSE:
#          To set cut angle of angled component
#
#      INPUT:
#          Parameters:
#          Angle       Real             Angle

    def SetAngle(self, Angle):
            if type(Angle) != type(0.0) and type(Angle) != type(0) and Angle != None:
                raise TypeError, ErrorMessages[TypeError]
            self.__Angle = Angle


#
#      METHOD:
#          GetAngle
#
#      PURPOSE:
#          To get cut angle (radians)
#      INPUT:
#          Parameters:
#          None
#
#      RESULT:
#          Angle


    def GetAngle(self):
        return self.__Angle


#
#      METHOD:
#          SetCutBlenType
#
#      PURPOSE:
#          To set cut building lenght type
#
#      INPUT:
#          Parameters:
#          Type       String        Cut building length type ( 0 = No cut, 1= cut off building length)

    def SetCutBlenType(self, Type = 0):
            if type(Type) != type(0):
                raise TypeError, ErrorMessages[TypeError]
            self.__CutBlenType = Type


#
#      METHOD:
#          GetCutBlenType
#
#      PURPOSE:
#          To get cut building length type
#      INPUT:
#          Parameters:
#          None
#
#      RESULT:
#          cut blen type


    def GetCutBlenType(self):
        return self.__CutBlenType


#-------------------------------------------------------------------
#
#      New style of access to attributes from Python version 2.2
#
    AddType            = property ( GetConnType, SetConnType)
    Component          = property ( GetComponent , SetComponent)
    Length             = property ( GetLength , SetLength)
    SurfPoint          = property ( GetSurfPoint , SetSurfPoint)
    Orientation        = property ( GetOrientation , SetOrientation)
    Direction          = property ( GetDirection , SetDirection)
    ExtPipeName        = property ( GetExternalPipe , SetExternalPipe)
    Distance           = property ( GetDistance , SetDistance)
    Angle              = property ( GetAngle , SetAngle )
    CutBlenType        = property ( GetCutBlenType , SetCutBlenType )
