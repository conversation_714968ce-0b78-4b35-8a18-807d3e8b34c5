#
#      NAME:
#          PipePartProp.py
#
#      PURPOSE:
#          The PipePartProp class contains information about pipe part properties
#
#          Do NOT change the names of the attributes, they are used by
#          the Vitesse interface. Users may only add or change methods
#
#      ATTRIBUTES:
#          __MatNote           String        Material note
#          __AcqCode           Integer       Acquisition code
#          __JointCode         Integer       Joint code
#          __WeldCode          Integer       Weld code
#          __PlaceCode         Integer       Placing code
#          __LooseCode         Integer       Loose code
#          __InsulType         Integer       Insulation status (0/1 - no/yes insulation)
#          __InsulComp         String        Insulation Component
#
#      METHODS:
#          SetMaterialNote                      Sets material note
#          GetMaterialNote                      Gets material note
#          SetAcquisitionCode                   Sets acquisition code
#          GetAcquisitionCode                   Gets acquisition code
#          SetJointCode                         Sets joint code
#          GetJointCode                         Gets joint code
#          SetWeldCode                          Sets weld code
#          GetWeldCode                          Gets weld code
#          SetPlacingCode                       Sets placing code
#          GetPlacingCode                       Gets placing code
#          SetLooseCode                         Sets loose code
#          GetLooseCode                         Gets loose code
#          SetInsulation                        Sets insulation
#          GetInsulation                        Gets insulation


import types
import string

ErrorMessages = { TypeError : 'not supported argument type, see documentation of PipePartProp class',
                  ValueError : 'not supported argument value, see documentation of PipePartProp class'}

class PipePartProp(object):

#
#      METHOD:
#          __init__
#
#      PURPOSE:
#          To create an instance of the class
#
#      INPUT:
#          Parameters:
#          None

    def __init__(self):
          self.__MatNote          = None
          self.__AcqCode          = None
          self.__JointCode        = None
          self.__WeldCode         = None
          self.__PlaceCode        = None
          self.__LooseCode        = None
          self.__InsulComp        = None
          self.__InsulType        = None

#
#      METHOD:
#          __repr__
#
#      PURPOSE:
#          To print the class

    def __repr__(self):
        tup = (
            "Material note      :" + str(self.__MatNote),
            "Acquisition code   :" + str(self.__AcqCode),
            "Joint code         :" + str(self.__JointCode),
            "Weld code          :" + str(self.__WeldCode),
            "Placing code       :" + str(self.__PlaceCode),
            "Loose code         :" + str(self.__LooseCode),
            "Insulation comp    :" + str(self.__InsulComp),
            "Insulation type    :" + str(self.__InsulType))
        return string.join (tup, '\n')


#
#      METHOD:
#          SetMaterialNote
#
#      PURPOSE:
#          To set part material note
#
#      INPUT:
#          Parameters:
#          Note       string              Material note

    def SetMaterialNote(self, Note):
            if type(Note) != type(""):
                raise TypeError, ErrorMessages[TypeError]
            self.__MatNote = Note


#
#      METHOD:
#          GetMaterialNote
#
#      PURPOSE:
#          To get spool material note
#      INPUT:
#          Parameters:
#          None
#
#      RESULT:
#          material note


    def GetMaterialNote(self):
        return self.__MatNote



#
#      METHOD:
#          SetAcquisitionCode
#
#      PURPOSE:
#          To set acquisition code
#
#      INPUT:
#          Parameters:
#          Code       Integer              acquisition code

    def SetAcquisitionCode(self, Code):
            if type(Code) != type(0):
                raise TypeError, ErrorMessages[TypeError]
            self.__AcqCode = Code


#
#      METHOD:
#          GetAcquisitionCode
#
#      PURPOSE:
#          To get heat code
#      INPUT:
#          Parameters:
#          None
#
#      RESULT:
#          Acquisition code


    def GetAcquisitionCode(self):
        return self.__AcqCode


#
#      METHOD:
#          SetJointCode
#
#      PURPOSE:
#          To set joint code
#
#      INPUT:
#          Parameters:
#          Code       Integer              joint code

    def SetJointCode(self, Code):
            if type(Code) != type(0):
                raise TypeError, ErrorMessages[TypeError]
            self.__JointCode = Code


#
#      METHOD:
#          GetJointCode
#
#      PURPOSE:
#          To get joint code
#      INPUT:
#          Parameters:
#          None
#
#      RESULT:
#          Joint code


    def GetJointCode(self):
        return self.__JointCode




#
#      METHOD:
#          SetWeldCode
#
#      PURPOSE:
#          To set weld code
#
#      INPUT:
#          Parameters:
#          Code       Integer              weld code

    def SetWeldCode(self, Code):
            if type(Code) != type(0):
                raise TypeError, ErrorMessages[TypeError]
            self.__WeldCode = Code


#
#      METHOD:
#          SetWeldCode
#
#      PURPOSE:
#          To get weld code
#      INPUT:
#          Parameters:
#          None
#
#      RESULT:
#          Weld code


    def GetWeldCode(self):
        return self.__WeldCode



#
#      METHOD:
#          SetPlacingCode
#
#      PURPOSE:
#          To set placing code
#
#      INPUT:
#          Parameters:
#          Code       Integer              placing code

    def SetPlacingCode(self, Code):
            if type(Code) != type(0):
                raise TypeError, ErrorMessages[TypeError]
            self.__PlaceCode = Code


#
#      METHOD:
#          GetPlacingCode
#
#      PURPOSE:
#          To get placing code
#      INPUT:
#          Parameters:
#          None
#
#      RESULT:
#          Placing code


    def GetPlacingCode(self):
        return self.__PlaceCode



#
#      METHOD:
#          SetLooseCode
#
#      PURPOSE:
#          To set loose code
#
#      INPUT:
#          Parameters:
#          Code       Integer              loose code

    def SetLooseCode(self, Code):
            if type(Code) != type(0):
                raise TypeError, ErrorMessages[TypeError]
            self.__LooseCode = Code


#
#      METHOD:
#          GetLooseCode
#
#      PURPOSE:
#          To get loose code
#      INPUT:
#          Parameters:
#          None
#
#      RESULT:
#          Loose code


    def GetLooseCode(self):
        return self.__LooseCode



#
#      METHOD:
#          SetInsulation
#
#      PURPOSE:
#          To set part insulation
#
#      INPUT:
#          Parameters:
#          Comp       string              Insulation component
#          Status     Integer             = 0 No insulation
#                                         = 1 Use insulation

    def SetInsulation(self, Comp, Type = 1):
            if type(Comp) != type("") or type(Type) != type(0):
                raise TypeError, ErrorMessages[TypeError]
            self.__InsulComp = Comp
            self.__InsulType = Type


#
#      METHOD:
#          GetInsulation
#
#      PURPOSE:
#          To get part insultion
#      INPUT:
#          Parameters:
#          None
#
#      RESULT:
#           Tuple:  ( insultaion component, insulation status)


    def GetInsulation(self):
        return self.__InsulComp, self.__InsulType


#-------------------------------------------------------------------
#
#      New style of access to attributes from Python version 2.2
#
    MatNote          = property (GetMaterialNote , SetMaterialNote)
    AcqCode          = property (GetAcquisitionCode , SetAcquisitionCode)
    JointCode        = property (GetJointCode , SetJointCode)
    WeldCode         = property (GetWeldCode , SetWeldCode)
    PlaceCode        = property (GetPlacingCode , SetPlacingCode)
    LooseCode        = property (GetLooseCode , SetLooseCode)
    def GetInsulComp(self): return self.__InsulComp
    def SetInsulComp(self,value):
      if type(value) != type(""):
         raise TypeError, ErrorMessages[TypeError]
      self.__InsulComp = value
    InsulComp        = property (GetInsulComp , SetInsulComp)
    def GetInsulType(self): return self.__InsulType
    def SetInsulType(self,value):
      if type(value) != type(0):
         raise TypeError, ErrorMessages[TypeError]
      self.__InsulType = value
    InsulType        = property (GetInsulType , SetInsulType)
