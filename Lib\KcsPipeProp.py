#
#      NAME:
#          PipeProp.py
#
#      PURPOSE:
#          The PipeProp class contains information about pipe properties
#
#          Do NOT change the names of the attributes, they are used by
#          the Vitesse interface. Users may only add or change methods
#
#      ATTRIBUTES:
#          __BendRad           Real          Bending radius
#          __Color             String        Pipe color
#          __SketchNote        String        Sketch note
#          __JointCode         Integer       Joint code
#          __WeldCode          Integer       Weld code
#          __HeatCode          Integer       HeatCode
#          __SurfTrCode        Integer       Surface treatment code
#          __TestPress         Real          Test pressure
#          __PlanUnit          String        Planning unit
#
#      METHODS:
#          SetBendRadius                        Sets bend radius
#          GetBendRadius                        Gets bend radius
#          SetColor                             Sets color
#          GetColor                             Gets color
#          SetSketchNote                        Sets sketch note
#          GetSketchNote                        Gets sketch note
#          SetJointCode                         Sets joint code
#          GetJointCode                         Gets joint code
#          SetWeldCode                          Sets weld code
#          GetWeldCode                          Gets weld code
#          SetHeatCode                          Sets heat code
#          GetHeatCode                          Gets heat code
#          SetSurfaceTreatmentCode              Sets surface treatment code
#          GetSurfaceTreatmentCode              Gets surface treatment code
#          SetTestPressure                      Sets test pressure
#          GetTestPressure                      Gets test pressure
#          SetPlanningUnit                      Sets planning unit
#          GetPlanningUnit                      Gets planning unit


import types
import string
import KcsColour

ErrorMessages = { TypeError : 'not supported argument type, see documentation of PipeProp class',
                  ValueError : 'not supported argument value, see documentation of PipeProp class'}

class PipeProp(object):

#
#      METHOD:
#          __init__
#
#      PURPOSE:
#          To create an instance of the class
#
#      INPUT:
#          Parameters:
#          None

    def __init__(self):
          self.__BendRad           = None
          self.__Color             = None
          self.__SketchNote        = None
          self.__JointCode         = None
          self.__WeldCode          = None
          self.__HeatCode          = None
          self.__SurfTrCode        = None
          self.__TestPress         = None
          self.__PlanUnit          = None


#
#      METHOD:
#          __repr__
#
#      PURPOSE:
#          To print the class

    def __repr__(self):
        tup = (
            "Bending radius     :" + str(self.__BendRad),
            "Color              :" + str(self.__Color),
            "Sketch note        :" + str(self.__SketchNote),
            "Joint code         :" + str(self.__JointCode),
            "Weld code          :" + str(self.__WeldCode),
            "Heat code          :" + str(self.__HeatCode),
            "Surface treatment  :" + str(self.__SurfTrCode),
            "Terst pressure     :" + str(self.__TestPress),
            "Planning unit      :" + str(self.__PlanUnit))
        return string.join (tup, '\n')


#
#      METHOD:
#          SetColor
#
#      PURPOSE:
#          To set color
#
#      INPUT:
#          Parameters:
#          Color       1) string              Color string
#                      2) KcsColour           Color

    def SetColor(self, Color):
            if type(Color) == type("") :
                self.__Color = KcsColour.Colour(Color)
            elif isinstance(Color, KcsColour.Colour):
                self.__Color = Color
            else :
                raise TypeError, ErrorMessages[TypeError]

#
#      METHOD:
#          GetColor
#
#      PURPOSE:
#          To get color
#      INPUT:
#          Parameters:
#          None
#
#      RESULT:
#          Color


    def GetColor(self):
        return self.__Color

#
#      METHOD:
#          SetSketchNote
#
#      PURPOSE:
#          To set sketch note
#
#      INPUT:
#          Parameters:
#          Note       string              Sketch note string

    def SetSketchNote(self, Note):
            if type(Note) != type(""):
                raise TypeError, ErrorMessages[TypeError]
            self.__SketchNote = Note


#
#      METHOD:
#          GetSketchNote
#
#      PURPOSE:
#          To get skech note
#      INPUT:
#          Parameters:
#          None
#
#      RESULT:
#          Sketch note


    def GetSketchNote(self):
        return self.__SketchNote


#
#      METHOD:
#          SetJointCode
#
#      PURPOSE:
#          To set joint code
#
#      INPUT:
#          Parameters:
#          Code       Integer              joint code

    def SetJointCode(self, Code):
            if type(Code) != type(0):
                raise TypeError, ErrorMessages[TypeError]
            self.__JointCode = Code


#
#      METHOD:
#          GetJointCode
#
#      PURPOSE:
#          To get joint code
#      INPUT:
#          Parameters:
#          None
#
#      RESULT:
#          Joint code


    def GetJointCode(self):
        return self.__JointCode

#
#      METHOD:
#          SetWeldCode
#
#      PURPOSE:
#          To set weld code
#
#      INPUT:
#          Parameters:
#          Code       Integer              weld code

    def SetWeldCode(self, Code):
            if type(Code) != type(0):
                raise TypeError, ErrorMessages[TypeError]
            self.__WeldCode = Code


#
#      METHOD:
#          GetWeldCode
#
#      PURPOSE:
#          To get weld code
#      INPUT:
#          Parameters:
#          None
#
#      RESULT:
#          Weld code


    def GetWeldCode(self):
        return self.__WeldCode


#
#      METHOD:
#          SetHeatCode
#
#      PURPOSE:
#          To set heat code
#
#      INPUT:
#          Parameters:
#          Code       Integer              heat code

    def SetHeatCode(self, Code):
            if type(Code) != type(0):
                raise TypeError, ErrorMessages[TypeError]
            self.__HeatCode = Code


#
#      METHOD:
#          GetHeatCode
#
#      PURPOSE:
#          To get heat code
#      INPUT:
#          Parameters:
#          None
#
#      RESULT:
#          Heat code


    def GetHeatCode(self):
        return self.__HeatCode


#
#      METHOD:
#          SetSurfaceTreatmentCode
#
#      PURPOSE:
#          To set surface treatment code
#
#      INPUT:
#          Parameters:
#          Code       Integer              surface treatment code

    def SetSurfaceTreatmentCode(self, Code):
            if type(Code) != type(0):
                raise TypeError, ErrorMessages[TypeError]
            self.__SurfTrCode = Code


#
#      METHOD:
#          GetSurfaceTreatmentCode
#
#      PURPOSE:
#          To get surface treatment code
#      INPUT:
#          Parameters:
#          None
#
#      RESULT:
#          Surface treatment code


    def GetSurfaceTreatmentCode(self):
        return self.__SurfTrCode


#
#      METHOD:
#          SetTestPressure
#
#      PURPOSE:
#          To set test pressure
#
#      INPUT:
#          Parameters:
#          Code       Real              test pressure

    def SetTestPressure(self, Code):
            if type(Code) != type(0.0) and type(Code) != type(0):
                raise TypeError, ErrorMessages[TypeError]
            self.__TestPress = Code


#
#      METHOD:
#          GetTestPressure
#
#      PURPOSE:
#          To get test pressure
#      INPUT:
#          Parameters:
#          None
#
#      RESULT:
#          Test pressure


    def GetTestPressure(self):
        return self.__TestPress


#
#      METHOD:
#          SetPlanningUnit
#
#      PURPOSE:
#          To set planning unit
#
#      INPUT:
#          Parameters:
#          PlUnit       String             planning unit

    def SetPlanningUnit(self, PlUnit):
            if type(PlUnit) != type(""):
                raise TypeError, ErrorMessages[TypeError]
            self.__PlanUnit = PlUnit


#
#      METHOD:
#          GetPlanningUnit
#
#      PURPOSE:
#          To get planning unit
#      INPUT:
#          Parameters:
#          None
#
#      RESULT:
#          Planning unit


    def GetPlanningUnit(self):
        return self.__PlanUnit


#
#      METHOD:
#          SetBendRadius
#
#      PURPOSE:
#          To set bending radius
#
#      INPUT:
#          Parameters:
#          Radius       Real             radius

    def SetBendRadius(self, Radius):
            if type(Radius) != type(0.0) and type(Radius) != type(0):
                raise TypeError, ErrorMessages[TypeError]
            self.__BendRad = Radius


#
#      METHOD:
#          GetBendRadius
#
#      PURPOSE:
#          To get bending radius
#      INPUT:
#          Parameters:
#          None
#
#      RESULT:
#          bending radius


    def GetBendRadius(self):
        return self.__BendRad

#-------------------------------------------------------------------
#
#      New style of access to attributes from Python version 2.2
#
    BendRad     = property (GetBendRadius , SetBendRadius)
    PipeColor   = property (GetColor , SetColor)
    SketchNote  = property (GetSketchNote , SetSketchNote)
    JointCode   = property (GetJointCode , SetJointCode)
    WeldCode    = property (GetWeldCode , SetWeldCode)
    HeatCode    = property (GetHeatCode , SetHeatCode)
    SurfTrCode  = property (GetSurfaceTreatmentCode , SetSurfaceTreatmentCode)
    TestPress   = property (GetTestPressure , SetTestPressure)
    PlanUnit    = property (GetPlanningUnit , SetPlanningUnit)
