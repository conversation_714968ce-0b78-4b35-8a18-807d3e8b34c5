#
#      NAME:
#          PipeRoute.py
#
#      PURPOSE:
#          The PipeRoute class contains routing information
#
#          Do NOT change the names of the attributes, they are used by
#          the Vitesse interface. Users may only add or change methods
#
#      ATTRIBUTES:
#          __AddType           Integer      Add type ( 0= Route from part, 1= Route from surface, 2= Route from point)
#          __FreePoint         Point3D      Free point  (required for free point routing)
#          __SurfPoint         Point3D      Point on surface  (required for on surface routing)
#          __PartId            Integer      Part Id ( required for part routing)
#          __Connection        Integer      Connection number ( required for part routing)
#          __ExtPipeName       PipeName     External pipe name (reqired for routing from/to external pipe)
#
#      METHODS:
#          SetRouteType                     Sets route type
#          GetRouteType                     Gets route type
#          SetSurfPoint                     Sets surface point
#          GetSurfPoint                     Gets surface point
#          SetFreePoint                     Sets free point
#          GetFreePoint                     Gets free point
#          SetPartId                        Sets part Id
#          GetPartId                        Gets part Id
#          SetConnection                    Sets part connection
#          GetConnection                    Gets part connection
#          SetExternalPipe                  Sets external pipe name
#          GetExternalPipe                  Gets external pipe name


import types
import string
import KcsPoint3D
import KcsPipeName


class PipeRoute(object):

#
#    Variables
#
    ErrorMessages = { TypeError : 'not supported argument type, see documentation of PipeRoute class',
                      ValueError : 'not supported argument value, see documentation of PipeRoute class'}

    RouteType = {      'part'       : 0,
                       'surface'    : 1,
                       'point'      : 2}
#
#      METHOD:
#          __init__
#
#      PURPOSE:
#          To create an instance of the class
#
#      INPUT:
#          Parameters:
#          None

    def __init__(self):
          self.__AddType            = 0
          self.__SurfPoint          = None
          self.__FreePoint          = None
          self.__PartId             = None
          self.__Connection         = None
          self.__ExtPipeName        = None
#
#      METHOD:
#          __repr__
#
#      PURPOSE:
#          To print the class

    def __repr__(self):
        tup = (
            "Route type     :" + str(self.GetRouteType()),
            "Surface pooint :" + str(self.__SurfPoint),
            "Free point     :" + str(self.__FreePoint),
            "Part Id        :" + str(self.__PartId),
            "Connection     :" + str(self.__Connection),
            "Pipe name      :" + str(self.__ExtPipeName ))
        return string.join (tup, '\n')

#
#      METHOD:
#          SetRouteType
#
#      PURPOSE:
#          To set route type
#
#      INPUT:
#          Parameters:
#          Type       String               Route type

    def SetRouteType(self, Type):
            if type(Type) != type(""):
                raise TypeError, self.ErrorMessages[TypeError]

            if Type not in self.RouteType.keys():
                raise ValueError, self.ErrorMessages[ValueError]

            self.__AddType = self.RouteType[Type]


#
#      METHOD:
#          GetRouteType
#
#      PURPOSE:
#          To get route type
#      INPUT:
#          Parameters:
#          None
#
#      RESULT:
#          Route type


    def GetRouteType(self):
        index = self.RouteType.values().index(self.__AddType)
        return self.RouteType.keys()[index]


#
#      METHOD:
#          SetExternalPipe
#
#      PURPOSE:
#          To set external pipe name
#
#      INPUT:
#          Parameters:
#          name       1)PipeName               Pipe name
#                     2)String                 Pipe name

    def SetExternalPipe(self, Name):
            if type(Name) == type(""):
                self.__ExtPipeName = KcsPipeName.PipeName(Name)
            elif isinstance(Name, KcsPipeName.PipeName) or Name == None:
                self.__ExtPipeName = Name
            else:
                raise TypeError, self.ErrorMessages[TypeError]


#
#      METHOD:
#          GetExternalPipe
#
#      PURPOSE:
#          To get external pipe name
#      INPUT:
#          Parameters:
#          None
#
#      RESULT:
#          Pipe name


    def GetExternalPipe(self):
        return self.__ExtPipeName


#
#      METHOD:
#          SetSurfPoint
#
#      PURPOSE:
#          To set surface point ( required for on surface routing)
#
#      INPUT:
#          Parameters:
#          Point       Point3D               Surface point

    def SetSurfPoint(self, Point):
            if not isinstance(Point, KcsPoint3D.Point3D) and Point != None:
                raise TypeError, self.ErrorMessages[TypeError]

            self.__SurfPoint = Point


#
#      METHOD:
#          GetSurfPoint
#
#      PURPOSE:
#          To get surface point
#      INPUT:
#          Parameters:
#          None
#
#      RESULT:
#          Surface point


    def GetSurfPoint(self):
        return self.__SurfPoint


#
#      METHOD:
#          SetFreePoint
#
#      PURPOSE:
#          To set free point ( required for free point routing)
#
#      INPUT:
#          Parameters:
#          Point       Point3D               Surface point

    def SetFreePoint(self, Point):
            if not isinstance(Point, KcsPoint3D.Point3D) and Point != None:
                raise TypeError, self.ErrorMessages[TypeError]

            self.__FreePoint = Point


#
#      METHOD:
#          GetFreePoint
#
#      PURPOSE:
#          To get free point
#      INPUT:
#          Parameters:
#          None
#
#      RESULT:
#          Free point


    def GetFreePoint(self):
        return self.__FreePoint


#
#      METHOD:
#          SetPartId
#
#      PURPOSE:
#          To set part Id ( required for part routing)
#
#      INPUT:
#          Parameters:
#          PartId       Integer               Part Id

    def SetPartId(self, PartId):
            if type(PartId)!= type(0) and PartId != None:
                raise TypeError, self.ErrorMessages[TypeError]

            self.__PartId = PartId


#
#      METHOD:
#          GetPartId
#
#      PURPOSE:
#          To get Part Id
#      INPUT:
#          Parameters:
#          None
#
#      RESULT:
#          PartId


    def GetPartId(self):
        return self.__PartId


#
#      METHOD:
#          SetConnection
#
#      PURPOSE:
#          To set part connection ( required for part routing)
#
#      INPUT:
#          Parameters:
#          Conn       Integer               Part Id

    def SetConnection(self, Conn):
            if type(Conn)!= type(0) and Conn != None:
                raise TypeError, self.ErrorMessages[TypeError]

            self.__Connection = Conn


#
#      METHOD:
#          GetConnection
#
#      PURPOSE:
#          To get part connection
#      INPUT:
#          Parameters:
#          None
#
#      RESULT:
#          Connection


    def GetConnection(self):
        return self.__Connection


#-------------------------------------------------------------------
#
#      New style of access to attributes from Python version 2.2
#
    Type            = property (GetRouteType , SetRouteType)
    SurfPoint       = property (GetSurfPoint , SetSurfPoint)
    FreePoint       = property (GetFreePoint , SetFreePoint)
    PartId          = property (GetPartId , SetPartId)
    Connection      = property (GetConnection , SetConnection)
    ExtPipeName     = property (GetExternalPipe , SetExternalPipe)
