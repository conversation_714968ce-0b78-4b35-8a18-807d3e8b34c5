#
#      NAME:
#          PipeSpoolProp.py
#
#      PURPOSE:
#          The PipeSpoolProp class contains information about pipe spool properties
#
#          Do NOT change the names of the attributes, they are used by
#          the Vitesse interface. Users may only add or change methods
#
#      ATTRIBUTES:
#          __PosNo             String        Position name
#          __SketchNote        String        Sketch note
#          __SketchType        String        Sketch type
#          __HeatCode          Integer       HeatCode
#          __SurfTrCode        Integer       Surface treatment code
#          __TestPress         Real          Test pressure
#          __PlanUnit          String        Planning unit
#          __BMNote            String        Bill of material note
#
#      METHODS:
#          SetPosNo                             Sets position name
#          GetPosNo                             Gets position name
#          SetSketchNote                        Sets sketch note
#          GetSketchNote                        Gets sketch note
#          SetSketchType                        Sets sketch type
#          GetSketchType                        Gets sketch type
#          SetHeatCode                          Sets heat code
#          GetHeatCode                          Gets heat code
#          SetSurfaceTreatmentCode              Sets surface treatment code
#          GetSurfaceTreatmentCode              Gets surface treatment code
#          SetTestPressure                      Sets test pressure
#          GetTestPressure                      Gets test pressure
#          SetPlanningUnit                      Sets planning unit
#          GetPlanningUnit                      Gets planning unit
#          SetBMNote                            Sets bill of material note
#          GetBMNote                            Gets bill of material note


import types
import string

ErrorMessages = { TypeError : 'not supported argument type, see documentation of PipeSpoolProp class',
                  ValueError : 'not supported argument value, see documentation of PipeSpoolProp class'}

class PipeSpoolProp(object):

#
#      METHOD:
#          __init__
#
#      PURPOSE:
#          To create an instance of the class
#
#      INPUT:
#          Parameters:
#          None

    def __init__(self):
          self.__PosNo             = None
          self.__SketchNote        = None
          self.__SketchType        = None
          self.__HeatCode          = None
          self.__SurfTrCode        = None
          self.__TestPress         = None
          self.__PlanUnit          = None
          self.__BMNote            = None

#
#      METHOD:
#          __repr__
#
#      PURPOSE:
#          To print the class

    def __repr__(self):
        tup = (
            "Position name      :" + str(self.__PosNo),
            "Sketch note        :" + str(self.__SketchNote),
            "Sketch type        :" + str(self.__SketchType),
            "Heat code          :" + str(self.__HeatCode),
            "Surface treatment  :" + str(self.__SurfTrCode),
            "Terst pressure     :" + str(self.__TestPress),
            "Planning unit      :" + str(self.__PlanUnit),
            "Bill of material note:" + str(self.__BMNote))
        return string.join (tup, '\n')


#
#      METHOD:
#          SetPosNo
#
#      PURPOSE:
#          To set spool position name
#
#      INPUT:
#          Parameters:
#          Note       string              Position name

    def SetPosNo(self, Note):
            if type(Note) != type(""):
                raise TypeError, ErrorMessages[TypeError]
            self.__PosNo = Note


#
#      METHOD:
#          GetPosNo
#
#      PURPOSE:
#          To get spool posotion name
#      INPUT:
#          Parameters:
#          None
#
#      RESULT:
#          position name


    def GetPosNo(self):
        return self.__PosNo

#
#      METHOD:
#          SetSketchNote
#
#      PURPOSE:
#          To set sketch note
#
#      INPUT:
#          Parameters:
#          Note       string              Sketch note string

    def SetSketchNote(self, Note):
            if type(Note) != type(""):
                raise TypeError, ErrorMessages[TypeError]
            self.__SketchNote = Note


#
#      METHOD:
#          GetSketchNote
#
#      PURPOSE:
#          To get sketch note
#      INPUT:
#          Parameters:
#          None
#
#      RESULT:
#          Sketch note


    def GetSketchNote(self):
        return self.__SketchNote



#
#      METHOD:
#          SetBMNote
#
#      PURPOSE:
#          To set bill of material note
#
#      INPUT:
#          Parameters:
#          Note       string              Bill of material note string

    def SetBMNote(self, Note):
            if type(Note) != type(""):
                raise TypeError, ErrorMessages[TypeError]
            self.__BMNote = Note


#
#      METHOD:
#          GetBMNote
#
#      PURPOSE:
#          To get bill of material note
#      INPUT:
#          Parameters:
#          None
#
#      RESULT:
#          Bill of material note


    def GetBMNote(self):
        return self.__BMNote



#
#      METHOD:
#          SetSketchType
#
#      PURPOSE:
#          To set sketch type
#
#      INPUT:
#          Parameters:
#          Note       string              Sketch type

    def SetSketchType(self, Note):
            if type(Note) != type(""):
                raise TypeError, ErrorMessages[TypeError]
            self.__SketchType = Note


#
#      METHOD:
#          GetSketchType
#
#      PURPOSE:
#          To get sketch type
#      INPUT:
#          Parameters:
#          None
#
#      RESULT:
#          Sketch note


    def GetSketchType(self):
        return self.__SketchType


#
#      METHOD:
#          SetHeatCode
#
#      PURPOSE:
#          To set heat code
#
#      INPUT:
#          Parameters:
#          Code       Integer              heat code

    def SetHeatCode(self, Code):
            if type(Code) != type(0):
                raise TypeError, ErrorMessages[TypeError]
            self.__HeatCode = Code


#
#      METHOD:
#          GetHeatCode
#
#      PURPOSE:
#          To get heat code
#      INPUT:
#          Parameters:
#          None
#
#      RESULT:
#          Heat code


    def GetHeatCode(self):
        return self.__HeatCode


#
#      METHOD:
#          SetSurfaceTreatmentCode
#
#      PURPOSE:
#          To set surface treatment code
#
#      INPUT:
#          Parameters:
#          Code       Integer              surface treatment code

    def SetSurfaceTreatmentCode(self, Code):
            if type(Code) != type(0):
                raise TypeError, ErrorMessages[TypeError]
            self.__SurfTrCode = Code


#
#      METHOD:
#          GetSurfaceTreatmentCode
#
#      PURPOSE:
#          To get surface treatment code
#      INPUT:
#          Parameters:
#          None
#
#      RESULT:
#          Surface treatment code


    def GetSurfaceTreatmentCode(self):
        return self.__SurfTrCode


#
#      METHOD:
#          SetTestPressure
#
#      PURPOSE:
#          To set test pressure
#
#      INPUT:
#          Parameters:
#          Code       Real              test pressure

    def SetTestPressure(self, Code):
            if type(Code) != type(0.0) and type(Code) != type(0):
                raise TypeError, ErrorMessages[TypeError]
            self.__TestPress = Code


#
#      METHOD:
#          GetTestPressure
#
#      PURPOSE:
#          To get test pressure
#      INPUT:
#          Parameters:
#          None
#
#      RESULT:
#          Test pressure


    def GetTestPressure(self):
        return self.__TestPress


#
#      METHOD:
#          SetPlanningUnit
#
#      PURPOSE:
#          To set planning unit
#
#      INPUT:
#          Parameters:
#          PlUnit       String             planning unit

    def SetPlanningUnit(self, PlUnit):
            if type(PlUnit) != type(""):
                raise TypeError, ErrorMessages[TypeError]
            self.__PlanUnit = PlUnit


#
#      METHOD:
#          GetPlanningUnit
#
#      PURPOSE:
#          To get planning unit
#      INPUT:
#          Parameters:
#          None
#
#      RESULT:
#          Planning unit


    def GetPlanningUnit(self):
        return self.__PlanUnit


#-------------------------------------------------------------------
#
#      New style of access to attributes from Python version 2.2
#
    PosNo             = property (GetPosNo , SetPosNo)
    SketchNote        = property (GetSketchNote , SetSketchNote)
    SketchType        = property (GetSketchType , SetSketchType)
    HeatCode          = property (GetHeatCode , SetHeatCode)
    SurfTrCode        = property (GetSurfaceTreatmentCode , SetSurfaceTreatmentCode)
    TestPress         = property (GetTestPressure , SetTestPressure)
    PlanUnit          = property (GetPlanningUnit , SetPlanningUnit)
    BMNote            = property (GetBMNote , SetBMNote)
