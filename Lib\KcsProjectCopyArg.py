#      NAME:
#          KcsProjectCopyArg.py
#
#      PURPOSE:
#          The class holds information about an object in a transfer set
#
#          Do NOT change the names of the attributes, they are used by
#          the Vitesse interface. Users may only add or change methods
#
#      ATTRIBUTES:
#          __designStatus       string              design status.
#          __manufactureStatus  string              manufacture status
#          __assemblyStatus     string              assembly status
#          __mtrlctrlStatus     string              mtrl ctrl status

class ProjectCopyArg(object):

#-------------------------------------------------------------------
#
#      METHOD:
#          __init__
#
#      PURPOSE:
#          To create an instance of the class
#
#
   __ErrorMessages = { TypeError : 'not supported argument type, see ProjectCopyArg documentation',
                       ValueError : 'not supported argument value, see ProjectCopyArg documentation'}
   def __init__(self):
      self.__designStatus = -1
      self.__manufactureStatus = -1
      self.__assemblyStatus = -1
      self.__mtrlctrlStatus = -1

#-------------------------------------------------------------------
#
#      METHOD:
#          __repr__
#
#      PURPOSE:
#          To print the class

   def __repr__(self):
      return 'ProjectCopyArg : %d %d %d %d' % (
      self.getDesignStatus(), self.getManufactureStatus(),
      self.getAssemblyStatus(), self.getMtrlctrlStatus() )


#-------------------------------------------------------------------
#
#      METHOD:
#          __Set
#
#      PURPOSE:
#
#
#      INPUT:
#
#      RESULT:
#
#

   def __Set(self, designStatus, manufactureStatus, assemblyStatus, mtrlctrlStatus):
      if not isinstance(designStatus,int):
         raise TypeError, self.__ErrorMessages[TypeError]
      self.__designStatus = designStatus
      if not isinstance(manufactureStatus,int):
         raise TypeError, self.__ErrorMessages[TypeError]
      self.__manufactureStatus = manufactureStatus
      if not isinstance(assemblyStatus,int):
         raise TypeError, self.__ErrorMessages[TypeError]
      self.__assemblyStatus = assemblyStatus
      if not isinstance(mtrlctrlStatus,int):
         raise TypeError, self.__ErrorMessages[TypeError]
      self.__mtrlctrlStatus = mtrlctrlStatus

#-------------------------------------------------------------------
#
#      METHOD:
#          __Get
#
#      PURPOSE:
#          Get info
#
#

   def Get(self):
      return '%d %d %d %d' % (
      self.getDesignStatus(), self.getManufactureStatus(),
      self.getAssemblyStatus(), self.getMtrlctrlStatus() )


#-------------------------------------------------------------------
#
#      METHOD:
#          getDesignStatus
#
#      PURPOSE:
#           get
#
#
   def getDesignStatus(self):
      return self.__designStatus



#-------------------------------------------------------------------
#
#      METHOD:
#          getManufactureStatus
#
#      PURPOSE:
#           get
#
#
   def getManufactureStatus(self):
      return self.__manufactureStatus



#-------------------------------------------------------------------
#
#      METHOD:
#          getAssemblyStatus
#
#      PURPOSE:
#           get
#
#
   def getAssemblyStatus(self):
      return self.__assemblyStatus



#-------------------------------------------------------------------
#
#      METHOD:
#          getMtrlctrlStatus
#
#      PURPOSE:
#           get
#
#
   def getMtrlctrlStatus(self):
      return self.__mtrlctrlStatus



#-------------------------------------------------------------------
#
#      METHOD:
#          setDesignStatus
#
#      PURPOSE:
#           set
#
#

   def setDesignStatus(self, value):
      if not isinstance(value,int):
         raise TypeError, self.__ErrorMessages[TypeError]
      self.__designStatus = value

#-------------------------------------------------------------------
#
#      METHOD:
#          setManufactureStatus
#
#      PURPOSE:
#           set
#
#

   def setManufactureStatus(self, value):
      if not isinstance(value,int):
         raise TypeError, self.__ErrorMessages[TypeError]
      self.__manufactureStatus = value

#-------------------------------------------------------------------
#
#      METHOD:
#          setAssemblyStatus
#
#      PURPOSE:
#           set
#
#

   def setAssemblyStatus(self, value):
      if not isinstance(value,int):
         raise TypeError, self.__ErrorMessages[TypeError]
      self.__assemblyStatus = value

#-------------------------------------------------------------------
#
#      METHOD:
#          setMtrlctrlStatus
#
#      PURPOSE:
#           set
#
#

   def setMtrlctrlStatus(self, value):
      if not isinstance(value,int):
         raise TypeError, self.__ErrorMessages[TypeError]
      self.__mtrlctrlStatus = value

#-------------------------------------------------------------------
#
#      New style of access to attributes from Python version 2.2
#
   DesignStatus = property (getDesignStatus , setDesignStatus)
   ManufactureStatus = property (getManufactureStatus , setManufactureStatus)
   AssemblyStatus = property (getAssemblyStatus , setAssemblyStatus)
   MtrlctrlStatus = property (getMtrlctrlStatus , setMtrlctrlStatus)

#------------------------------------------------------------------------------
#  Start of main body
#------------------------------------------------------------------------------
if __name__ == "__main__":
   a = ProjectCopyArg()
   a.DesignStatus = 123
   a.AssemblyStatus = 456
   a.ManufactureStatus = 3
   a.MtrlctrlStatus = 2
   print a
