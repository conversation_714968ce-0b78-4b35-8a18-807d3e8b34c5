
#
#      NAME:
#          KcsTorus.py
#
#      PURPOSE:
#          The holds information about a torus segment. The torus segment consists
#          of an origin, two normals, a radius and a diameter.
#
#          Do NOT change the names of the attributes, they are used by
#          the Vitesse interface. Users may only add or change methods
#
#      ATTRIBUTES:
#          Origin         Point3D     The origin (intersection between Norm1 and Norm2e)
#          Norm1          Vector3D    The normal to torus cross section at segment start
#          Norm2          Vector3D    The normal to torus cross section at segment end
#          Radius         real        The radius of the torus
#          Diameter       real        The diameter of the torus cross section

from KcsPoint3D import Point3D
from KcsVector3D import Vector3D
import string

class Torus:

#
#      METHOD:
#          __init__
#
#      PURPOSE:
#          To create an instance of the class
#
#      INPUT:
#          Parameters:
#          orig          The origin
#          norm1         The "start" normal
#          norm2         The "end" normal
#          rad           The radius
#          diam          The diameter

     def __init__(self, orig, norm1, norm2, rad, diam):
        self.Origin = Point3D(orig.X,orig.Y,orig.Z)
        self.Norm1 = Vector3D(norm1.X,norm1.Y,norm1.Z)
        self.Norm2 = Vector3D(norm2.X,norm2.Y,norm2.Z)
        self.Radius = rad
        self.Diameter = diam


#
#      METHOD:
#          __repr__
#
#      PURPOSE:
#          To print the class

     def __repr__(self):
      tup = (
        'Torus:',
        '   origin:   ' + str(self.Origin),
        '   norm1:    ' + str(self.Norm1),
        '   norm2:    ' + str(self.Norm2),
        '   radius:   ' + str(self.Radius),
        '   diameter: ' + str(self.Diameter))
      return string.join (tup, '\n')
