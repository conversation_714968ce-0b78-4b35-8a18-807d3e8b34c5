#
#      NAME:
#          KcsVolConnection.py
#
#      PURPOSE:
#          The class holds information about volume connection
#
#          Do NOT change the names of the attributes, they are used by
#          the Vitesse interface. Users may only add or change methods
#
#      ATTRIBUTES:
#
#          ( Fields __ConnNo and __ConnType are used to identify connection )

#          __ConnNo              Integer       Connection number
#          __ConnType            Integer       Connection type
#
#          __Position            Point3D       Position point
#          __Direction           Vector3D      Direction vector
#          __Description         String        Description string
#          __ComponentName       String        Component name
#
#      METODS:
#          SetConnNo              Set connection number
#          GetConnNo              Get connection number
#          SetConnType            Set connection type
#          GetConnType            Get connection type
#          SetDescription         Set connection description
#          GetDescription         Get connection description
#          SetComponentName       Set connection component name
#          GeComponentName        Get connection component name
#          SetPosition            Set connection position
#          GetPosition            Get connection position
#          SetDirection           Set connection direction
#          GetDirection           Get connection direction


import KcsPoint3D
import KcsVector3D

import types
import string

ErrorMessages = { TypeError : 'not supported argument type, see documentation of VolConnection class',
                  ValueError : 'not supported argument value, see documentation of VolConnection class'}


class VolConnection :

#
#      METHOD:
#          __init__
#
#      PURPOSE:
#          To create an instance of the class
#
#      INPUT:
#          Parameters:
#
#          ConnType        Connection type   (default 0 )
#          ConnNo          Connection number (default 0 )
#

    def __init__(self, ConnType=0, ConnNo=0):
        self.__ConnNo      = ConnNo
        self.__ConnType    = ConnType
        self.__Position    = None
        self.__Direction   = None
        self.__Description = None
        self.__ComponentName = None

#
#      METHOD:
#          __repr__
#
#      PURPOSE:
#          To print the class

    def __repr__(self):
        tup = (
            "Connection type   :" + str(self.__ConnType),
            "Connection no     :" + str(self.__ConnNo),
            "Position          :" + str(self.__Position),
            "Direction         :" + str(self.__Direction),
            "Description       :" + str(self.__Description),
            "Component name    :" + str(self.__ComponentName))
        return string.join (tup, '\n')

#
#      METHOD:
#          SetConnNo
#
#      PURPOSE:
#          To set connection number
#
#      INPUT:
#          Parameters:
#          ConnNo       Integer                Connection number


    def SetConnNo(self, ConnNo):
        if type(ConnNo) == type(0) :
            self.__ConnNo = ConnNo
        else :
            raise TypeError, ErrorMessages[TypeError]

#
#      METHOD:
#          GetConnNo
#
#      PURPOSE:
#          To get connection number
#      INPUT:
#          Parameters:
#          None
#
#      RESULT:
#          Integer      Connection number


    def GetConnNo(self):
        return self.__ConnNo

#
#      METHOD:
#          SetConnType
#
#      PURPOSE:
#          To set connection type
#
#      INPUT:
#          Parameters:
#          ConnType       Integer                Connection type


    def SetConnType(self, ConnType):
        if type(ConnType) == type(0) :
           self.__ConnType = ConnType
        else :
           raise TypeError, ErrorMessages[TypeError]


#
#      METHOD:
#          GetConnType
#
#      PURPOSE:
#          To get connection type
#      INPUT:
#          Parameters:
#          None
#
#      RESULT:
#          Integer         Connection type


    def GetConnType(self):
        return self.__ConnType


#
#      METHOD:
#          SetPosition
#
#      PURPOSE:
#          To set connection position
#
#      INPUT:
#          Parameters:
#          Position       Point3D                Connection position


    def SetPosition(self, Position):
        if isinstance(Position, KcsPoint3D.Point3D ) :
            self.__Position = Position
        else :
            raise TypeError, ErrorMessages[TypeError]

#
#      METHOD:
#          GetPosition
#
#      PURPOSE:
#          To get connection position
#      INPUT:
#          Parameters:
#          None
#
#      RESULT:
#          Point3D        Connection position


    def GetPosition(self):
        return self.__Position



#
#      METHOD:
#          SetDirection
#
#      PURPOSE:
#          To set connection direction vector
#
#      INPUT:
#          Parameters:
#          Position       Vector3D                Direction vector


    def SetDirection(self, Direction):
        if isinstance(Direction, KcsVector3D.Vector3D ):
            self.__Direction = Direction
        else :
            raise TypeError, ErrorMessages[TypeError]

#
#      METHOD:
#          GetDirection
#
#      PURPOSE:
#          To get connection direction vector
#      INPUT:
#          Parameters:
#          None
#
#      RESULT:
#          Vector3D        Direction vector


    def GetDirection(self):
        return self.__Direction


#
#      METHOD:
#          SetDescription
#
#      PURPOSE:
#          To set connection description
#
#      INPUT:
#          Parameters:
#          Description       String                Description text


    def SetDescription(self, Description):
        if type(Description) == type('') :
            self.__Description = Description
        else :
            raise TypeError, ErrorMessages[TypeError]

#
#      METHOD:
#          GetDescription
#
#      PURPOSE:
#          To get connection description
#      INPUT:
#          Parameters:
#          None
#
#      RESULT:
#          String        Description text


    def GetDescription(self):
        return self.__Description


#
#      METHOD:
#          SetComponentName
#
#      PURPOSE:
#          To set connection Component name
#
#      INPUT:
#          Parameters:
#          ComponentName       String                Component name


    def SetComponentName(self, ComponentName):
        if type(ComponentName) == type('') :
            self.__ComponentName = ComponentName
        else :
            raise TypeError, ErrorMessages[TypeError]

#
#      METHOD:
#          GetComponentName
#
#      PURPOSE:
#          To get connection Component name
#      INPUT:
#          Parameters:
#          None
#
#      RESULT:
#          String        Component name


    def GetComponentName(self):
        return self.__ComponentName
