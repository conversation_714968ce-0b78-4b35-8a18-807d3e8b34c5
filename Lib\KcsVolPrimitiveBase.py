#
#      NAME:
#          KcsVolPrimitiveBase.py
#
#      PURPOSE:
#
#          To hold options for Primitive Properties.
#
#          Do NOT change the names of the attributes, they are used by
#          the Vitesse interface. Users may only add or change methods
#

import types
import KcsColour
import string

class VolPrimitiveBase(object):

#-----------------------------------------------------------------------
# Errors
#-----------------------------------------------------------------------

   __ErrorMessages = { TypeError : 'not supported argument type, see documentation of VolPrimitiveBase class',
                       ValueError: 'not supported value, see documentation of VolPrimitiveBase class' }

#-----------------------------------------------------------------------

   def __init__(self):
      'inits VolPrimitiveBase'
#-----------------------------------------------------------------------
#     Standard Primitive Properties
#-----------------------------------------------------------------------
      self.Colour                    = KcsColour.Colour()
      self.Density                   = 0.0
      self.Softness                  = 0

#------------------------------------------------------------------
#
#      METHOD:
#          __repr__
#
#      PURPOSE:
#          To print the class
#-----------------------------------------------------------------------

   def __repr__(self):
      tup = (
            'VolPrimitiveBase:',
            'Density  :' + str(self.Density),
            'Softness :' + str(self.Softness),
            str(self.Colour))
      return string.join (tup, '\n')

#-----------------------------------------------------------------------
#        Set Methods
#-----------------------------------------------------------------------

   def SetColour(self, col):
      'Defines the primitive colour'

      if not isinstance(col, KcsColour.Colour):
         raise TypeError, VolPrimitiveBase.__ErrorMessages[TypeError]
      self.__Colour = col
#-----------------------------------------------------------------------

   def SetDensity(self, den):
      'Defines density of primitve from properties'
      if type(den) in [ types.FloatType, types.IntType, types.LongType ]:
         self.__Density = den
      else:
         raise TypeError, VolPrimitiveBase.__ErrorMessages[TypeError]

#-----------------------------------------------------------------------
   def SetSoftness(self, soft):
      'Defines softness of primitive from properties'
      if type(soft) == types.IntType:
         self.__Softness = soft
      else:
         raise TypeError, VolPrimitiveBase.__ErrorMessages[TypeError]
#-----------------------------------------------------------------------
#        Get Methods
#-----------------------------------------------------------------------
   def GetColour(self):
      'Gets colour of primitve from properties'
      return self.__Colour
#-----------------------------------------------------------------------
   def GetDensity(self):
      'Gets density of primitve from properties'
      return self.__Density
#-----------------------------------------------------------------------
   def GetSoftness(self):
      'Gets softness of primitve from properties'
      return self.__Softness

#-----------------------------------------------------------------------
#  Properties
#-----------------------------------------------------------------------

   Colour   = property (GetColour,  SetColour, None,   'Colour - primitive colour')
   Density  = property (GetDensity, SetDensity, None,  'Density - primitive density')
   Softness = property (GetSoftness, SetSoftness, None,'Softness - primitive softness')
