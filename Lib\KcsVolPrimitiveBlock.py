#
#      NAME:
#          KcsVolPrimitiveBlock.py
#
#      PURPOSE:
#
#          To hold options for Primitive Properties.
#
#          Do NOT change the names of the attributes, they are used by
#          the Vitesse interface. Users may only add or change methods
#

import types
import KcsBox
import KcsPoint3D
import KcsVector3D
from KcsVolPrimitiveBase import VolPrimitiveBase


class VolPrimitiveBlock(VolPrimitiveBase):

#-----------------------------------------------------------------------
# Errors
#-----------------------------------------------------------------------

   __ErrorMessages = { TypeError : 'not supported argument type, see documentation of VolPrimitiveBlock class',
                       ValueError: 'not supported value, see documentation of VolPrimitiveBlock class' }

#-----------------------------------------------------------------------

   def __init__(self):
      'inits VolPrimitiveBlock'
      VolPrimitiveBase.__init__(self)
#-----------------------------------------------------------------------
#     Standard Primitive Properties
#-----------------------------------------------------------------------
      self.Box  = KcsBox.Box()

#------------------------------------------------------------------
#
#      METHOD:
#          __repr__
#
#      PURPOSE:
#          To print the class
#------------------------------------------------------------------

   def __repr__(self):
      return str( self.Box )

#-----------------------------------------------------------------------
#        Set Methods
#-----------------------------------------------------------------------
   def SetBox(self, box):
      'Defines the primitive box'
      if not isinstance(box, KcsBox.Box):
         raise TypeError, VolPrimitiveBlock.__ErrorMessages[TypeError]
      self.__Box = box

#-----------------------------------------------------------------------
#        Get Methods
#-----------------------------------------------------------------------
   def GetBox(self):
      'Gets primitive box'
      return self.__Box

   Box = property (GetBox, SetBox, None, 'Box - Primitive box')
