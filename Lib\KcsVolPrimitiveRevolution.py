#
#      NAME:
#          KcsVolPrimitiveRevolution.py
#
#      PURPOSE:
#
#          To hold options for Primitive Revolution.
#
#          Do NOT change the names of the attributes, they are used by
#          the Vitesse interface. Users may only add or change methods
#

import types
import copy
import string
import KcsPoint3D
import KcsPoint2D
import KcsVector3D
import KcsContour2D
from KcsVolPrimitiveBase import VolPrimitiveBase

class VolPrimitiveRevolution(VolPrimitiveBase):

#-----------------------------------------------------------------------
# Errors
#-----------------------------------------------------------------------

   __ErrorMessages = { TypeError : 'not supported argument type, see documentation of VolPrimitiveRevolution class',
                       ValueError: 'not supported value, see documentation of VolPrimitiveRevolution class' }

#-----------------------------------------------------------------------

   def __init__(self):
      'inits VolPrimitiveRevolution'
      VolPrimitiveBase.__init__(self)
#-----------------------------------------------------------------------
#     Standard Primitive Options
#-----------------------------------------------------------------------
      self.Origin                    = KcsPoint3D.Point3D(0,0,0)
      self.UAxis                     = KcsVector3D.Vector3D(1, 0, 0)
      self.VAxis                     = KcsVector3D.Vector3D(0, 1, 0)
      self.ArcTol                    = 0
      self.Contour                   = KcsContour2D.Contour2D(KcsPoint2D.Point2D(0,0))

#------------------------------------------------------------------
#
#      METHOD:
#          __repr__
#
#      PURPOSE:
#          To print the class
#-----------------------------------------------------------------------

   def __repr__(self):
      tup = (
            'VolPrimitiveRevolution:',
            'Origin :' + str(self.Origin),
            'UAxis  :' + str(self.UAxis),
            'VAxis  :' + str(self.VAxis),
            'ArcTol :' + str(self.ArcTol),
            str(self.Contour))
      return string.join (tup, '\n')

#-----------------------------------------------------------------------
#        Set Methods
#-----------------------------------------------------------------------
   def SetOrigin(self, pnt):
      'Defines the Origin'
      if not isinstance(pnt, KcsPoint3D.Point3D):
         raise TypeError, VolPrimitiveRevolution.__ErrorMessages[TypeError]
      self.__Origin = KcsPoint3D.Point3D( pnt.X, pnt.Y, pnt.Z )
#-----------------------------------------------------------------------
   def SetUAxis(self, pnt):
      'Defines the UAxis Vector'
      if not isinstance(pnt, KcsVector3D.Vector3D):
         raise TypeError, VolPrimitiveRevolution.__ErrorMessages[TypeError]
      self.__uAxis = KcsVector3D.Vector3D( pnt.X, pnt.Y, pnt.Z )
#-----------------------------------------------------------------------
   def SetVAxis(self, pnt):
      'Defines the VAxis Vector'
      if not isinstance(pnt, KcsVector3D.Vector3D):
         raise TypeError, VolPrimitiveRevolution.__ErrorMessages[TypeError]
      self.__vAxis = KcsVector3D.Vector3D( pnt.X, pnt.Y, pnt.Z )
#-----------------------------------------------------------------------
   def SetTolerance(self, tol):
      'Defines the Tolerance'
      if not type(tol) in [ types.FloatType , types.IntType, types.LongType ]:
         raise TypeError, VolPrimitiveRevolution.__ErrorMessages[TypeError]
      self.__arcTol = tol
#-----------------------------------------------------------------------
   def SetContour(self, con):
      'Defines the Contour'
      if not isinstance(con, KcsContour2D.Contour2D):
         raise TypeError, VolPrimitiveRevolution.__ErrorMessages[TypeError]
      self.__Contour = copy.deepcopy( con )
#-----------------------------------------------------------------------
#        Get Methods
#-----------------------------------------------------------------------
   def GetTolerance(self):
      'Gets tolerance'
      return self.__arcTol
#-----------------------------------------------------------------------
   def GetContour(self):
      'Gets Contour'
      return self.__Contour
#-----------------------------------------------------------------------
   def GetUAxis(self):
      'Gets UAxis'
      return self.__uAxis
#-----------------------------------------------------------------------
   def GetVAxis(self):
      'Gets VAxis'
      return self.__vAxis
#-----------------------------------------------------------------------
   def GetOrigin(self):
      'Gets Origin'
      return self.__Origin

#-----------------------------------------------------------------------
# Properties
#-----------------------------------------------------------------------

   Origin    = property (GetOrigin,  SetOrigin, None,   'Origin - primitive origin')
   UAxis     = property (GetUAxis,  SetUAxis, None,   'UAxis - primitive u axis')
   VAxis     = property (GetVAxis,  SetVAxis, None,   'VAxis - primitive v axis')
   ArcTol    = property (GetTolerance,  SetTolerance, None, 'ArcTol - arc segment tolerance')
   Contour   = property (GetContour,  SetContour, None, 'Contour - contour 2D defining primitive shape')

