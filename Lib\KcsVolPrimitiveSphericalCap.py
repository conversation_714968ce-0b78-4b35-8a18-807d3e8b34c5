#
#      NAME:
#          KcsVolPrimitiveSphericalCap.py
#
#      PURPOSE:
#
#          To hold options for Primitive Spherical Cap.
#
#          Do NOT change the names of the attributes, they are used by
#          the Vitesse interface. Users may only add or change methods
#

import types
import string
import KcsPoint3D
import KcsVector3D
from KcsVolPrimitiveBase import VolPrimitiveBase

class VolPrimitiveSphericalCap(VolPrimitiveBase):

#-----------------------------------------------------------------------
# Errors
#-----------------------------------------------------------------------

   __ErrorMessages = { TypeError : 'not supported argument type, see documentation of VolPrimitiveSphericalCap class',
                       ValueError: 'not supported value, see documentation of VolPrimitiveSphericalCap class' }

#-----------------------------------------------------------------------

   def __init__(self):
      'inits VolPrimitiveSphericalCap'
      VolPrimitiveBase.__init__(self)
#-----------------------------------------------------------------------
#     Standard Primitive Options
#-----------------------------------------------------------------------
      self.Origin                    = KcsPoint3D.Point3D(0,0,0)
      self.UAxis                     = KcsVector3D.Vector3D(1, 0, 0)
      self.Amplitude                 = 0.0
      self.Radius                    = 0.0

#------------------------------------------------------------------
#
#      METHOD:
#          __repr__
#
#      PURPOSE:
#          To print the class
#-----------------------------------------------------------------------

   def __repr__(self):
      tup = (
            'VolPrimitiveSphericalCap:',
            'Origin   :' + str(self.Origin),
            'UAxis    :' + str(self.UAxis),
            'Amplitude:' + str(self.Amplitude),
            'Radius   :' + str(self.Radius))
      return string.join (tup, '\n')


#-----------------------------------------------------------------------
#        Set Methods
#-----------------------------------------------------------------------
   def SetOrigin(self, pnt):
      'Defines the Origin'
      if not isinstance(pnt, KcsPoint3D.Point3D):
         raise TypeError, VolPrimitiveSphericalCap.__ErrorMessages[TypeError]
      self.__Origin = KcsPoint3D.Point3D( pnt.X, pnt.Y, pnt.Z )
#-----------------------------------------------------------------------
   def SetUAxis(self, pnt):
      'Defines the UAxis Vector'
      if not isinstance(pnt, KcsVector3D.Vector3D):
         raise TypeError, VolPrimitiveSphericalCap.__ErrorMessages[TypeError]
      self.__uAxis = KcsVector3D.Vector3D( pnt.X, pnt.Y, pnt.Z )
#-----------------------------------------------------------------------
   def SetAmplitude(self, ampl):
      'Defines the Amplitude of Cap'
      if not type(ampl) in [ types.FloatType , types.IntType, types.LongType ]:
         raise TypeError, VolPrimitiveSphericalCap.__ErrorMessages[TypeError]
      self.__ampl = ampl
#-----------------------------------------------------------------------
   def SetRadius(self, rad):
      'Defines the Bottom Radius of Cap'
      if not type(rad) in [ types.FloatType , types.IntType, types.LongType ]:
         raise TypeError, VolPrimitiveSphericalCap.__ErrorMessages[TypeError]
      self.__bottomRad = rad
#-----------------------------------------------------------------------
#        Get Methods
#-----------------------------------------------------------------------
   def GetAmplitude(self):
      'Gets amplitude of cap'
      return self.__ampl
#-----------------------------------------------------------------------
   def GetRadius(self):
      'Gets bottom radius of cap'
      return self.__bottomRad
#-----------------------------------------------------------------------
   def GetUAxis(self):
      'Gets UAxis'
      return self.__uAxis
#-----------------------------------------------------------------------
   def GetOrigin(self):
      'Gets Origin'
      return self.__Origin
#-----------------------------------------------------------------------

#-----------------------------------------------------------------------
# Properties
#-----------------------------------------------------------------------

   Origin    = property (GetOrigin,  SetOrigin, None,   'Origin - primitive origin')
   UAxis     = property (GetUAxis,  SetUAxis, None,   'UAxis - primitive u axis')
   Radius    = property (GetRadius,  SetRadius, None,   'Radius - bottom radius')
   Amplitude = property (GetAmplitude,  SetAmplitude, None, 'Amplitude - cap amplitude')

