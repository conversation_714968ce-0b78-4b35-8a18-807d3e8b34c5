#
#      NAME:
#          KcsVolPrimitiveTorusSegment.py
#
#      PURPOSE:
#
#          To hold options for Primitive Torus Segment.
#
#          Do NOT change the names of the attributes, they are used by
#          the Vitesse interface. Users may only add or change methods
#

import types
import copy
import string
import KcsPoint3D
import KcsVector3D
import KcsArc3D
from KcsVolPrimitiveBase import VolPrimitiveBase


class VolPrimitiveTorusSegment(VolPrimitiveBase):

#-----------------------------------------------------------------------
# Errors
#-----------------------------------------------------------------------

   __ErrorMessages = { TypeError : 'not supported argument type, see documentation of VolPrimitiveTorusSegment class',
                       ValueError: 'not supported value, see documentation of VolPrimitiveTorusSegment class' }

#-----------------------------------------------------------------------

   def __init__(self):
      'inits VolPrimitiveTorusSegment'
      VolPrimitiveBase.__init__(self)
#-----------------------------------------------------------------------
#     Standard Primitive Options
#-----------------------------------------------------------------------
      self.ArcSeg                   = KcsArc3D.Arc3D(KcsPoint3D.Point3D(0,0,0), KcsPoint3D.Point3D(0,0,0), KcsVector3D.Vector3D(0,0,0))
      self.Radius                   = 0.0

#------------------------------------------------------------------
#
#      METHOD:
#          __repr__
#
#      PURPOSE:
#          To print the class
#-----------------------------------------------------------------------

   def __repr__(self):
      tup = (
            'VolPrimitiveTorusSegment:',
            'ArcSeg :' + str(self.ArcSeg),
            'Radius :' + str(self.Radius))
      return string.join (tup, '\n')

#-----------------------------------------------------------------------
#        Set Methods
#-----------------------------------------------------------------------
   def SetArc(self, arc):
      'Defines the ArcSegment'
      if not isinstance(arc, KcsArc3D.Arc3D):
         raise TypeError, VolPrimitiveTorusSegment.__ErrorMessages[TypeError]
      self.__arcSeg = copy.deepcopy( arc )
#-----------------------------------------------------------------------
   def SetRadius(self, rad):
      'Defines the Radius'
      if not type(rad) in [ types.FloatType , types.IntType, types.LongType ]:
         raise TypeError, VolPrimitiveTorusSegment.__ErrorMessages[TypeError]
      self.__diam = rad

#-----------------------------------------------------------------------
#        Get Methods
#-----------------------------------------------------------------------
   def GetRadius(self):
      'Gets Radius'
      return self.__diam
#-----------------------------------------------------------------------
   def GetArc(self):
      'Gets ArcSegment'
      return self.__arcSeg

#-----------------------------------------------------------------------
#  Obsolete
#-----------------------------------------------------------------------
   def SetDiameter(self, diam):
      self.Radius = diam/2.0

#-----------------------------------------------------------------------
# Properties
#-----------------------------------------------------------------------

   ArcSeg    = property (GetArc,  SetArc, None, 'ArcSeg - arc segment')
   Radius    = property (GetRadius,  SetRadius, None, 'Radius - segment radius')


