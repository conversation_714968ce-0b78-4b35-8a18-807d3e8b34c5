#
#      NAME:
#          KcsVolPrimitiveTruncatedCone.py
#
#      PURPOSE:
#
#          To hold options for Primitive Truncated Cone.
#
#          Do NOT change the names of the attributes, they are used by
#          the Vitesse interface. Users may only add or change methods
#

import types
import string
import KcsPoint3D
import KcsVector3D
from KcsVolPrimitiveBase import VolPrimitiveBase

class VolPrimitiveTruncatedCone(VolPrimitiveBase):

#-----------------------------------------------------------------------
# Errors
#-----------------------------------------------------------------------

   __ErrorMessages = { TypeError : 'not supported argument type, see documentation of VolPrimitiveTruncatedCone class',
                       ValueError: 'not supported value, see documentation of VolPrimitiveTruncatedCone class' }

#-----------------------------------------------------------------------

   def __init__(self):
      'inits VolPrimitiveTruncatedCone'
      VolPrimitiveBase.__init__(self)
#-----------------------------------------------------------------------
#     Standard Primitive Options
#-----------------------------------------------------------------------
      self.Origin                    = KcsPoint3D.Point3D(0,0,0)
      self.UAxis                     = KcsVector3D.Vector3D(1, 0, 0)
      self.BottomDiameter            = 0.0
      self.TopDiameter               = 0.0
      self.Height                    = 0.0

#------------------------------------------------------------------
#
#      METHOD:
#          __repr__
#
#      PURPOSE:
#          To print the class
#-----------------------------------------------------------------------

   def __repr__(self):
      tup = (
            'VolPrimitiveTruncatedCone:',
            'Origin    :' + str(self.Origin),
            'UAxis     :' + str(self.UAxis),
            'Height    :' + str(self.Height),
            'BottomDiameter:' + str(self.BottomDiameter),
            'TopDiameter   :' + str(self.TopDiameter))
      return string.join (tup, '\n')

#-----------------------------------------------------------------------
#        Set Methods
#-----------------------------------------------------------------------
   def SetOrigin(self, pnt):
      'Defines the Origin'
      if not isinstance(pnt, KcsPoint3D.Point3D):
         raise TypeError, VolPrimitiveTruncatedCone.__ErrorMessages[TypeError]
      self.__Origin = KcsPoint3D.Point3D( pnt.X, pnt.Y, pnt.Z )
#-----------------------------------------------------------------------
   def SetUAxis(self, pnt):
      'Defines the UAxis Vector'
      if not isinstance(pnt, KcsVector3D.Vector3D):
         raise TypeError, VolPrimitiveTruncatedCone.__ErrorMessages[TypeError]
      self.__uAxis = KcsVector3D.Vector3D( pnt.X, pnt.Y, pnt.Z )
#-----------------------------------------------------------------------
   def SetDiameters(self, diam1, diam2):
      'Defines the Diameters'

      self.BottomDiameter = diam1
      self.TopDiameter = diam2
#-----------------------------------------------------------------------
   def SetTopDiameter(self, diam):
      'Defines the Diameter'
      if not type(diam) in [ types.FloatType , types.IntType, types.LongType ]:
         raise TypeError, VolPrimitiveTruncatedCone.__ErrorMessages[TypeError]
      self.__TopDiam = diam

#-----------------------------------------------------------------------
   def SetBottomDiameter(self, diam):
      'Defines the Diameter'
      if not type(diam) in [ types.FloatType , types.IntType, types.LongType ]:
         raise TypeError, VolPrimitiveTruncatedCone.__ErrorMessages[TypeError]
      self.__BottomDiam = diam

#-----------------------------------------------------------------------
   def SetHeight(self, height):
      'Defines the Height of Cone'
      if not type(height) in [ types.FloatType , types.IntType, types.LongType ]:
         raise TypeError, VolPrimitiveTruncatedCone.__ErrorMessages[TypeError]
      self.__Height = height
#-----------------------------------------------------------------------
#        Get Methods
#-----------------------------------------------------------------------
   def GetDiameters(self):
      'Gets Diameters'
      return[self.__BottomDiam, self.__TopDiam]

#-----------------------------------------------------------------------
   def GetTopDiameter(self):
      'Gets Top Diameter'
      return self.__TopDiam

#-----------------------------------------------------------------------
   def GetBottomDiameter(self):
      'Gets Bottom Diameter'
      return self.__BottomDiam

#-----------------------------------------------------------------------
   def GetHeight(self):
      'Gets Height'
      return self.__Height
#-----------------------------------------------------------------------
   def GetUAxis(self):
      'Gets UAxis'
      return self.__uAxis
#-----------------------------------------------------------------------
   def GetOrigin(self):
      'Gets Origin'
      return self.__Origin

#-----------------------------------------------------------------------
# Properties
#-----------------------------------------------------------------------

   Origin         = property (GetOrigin,  SetOrigin, None,   'Origin - primitive origin')
   UAxis          = property (GetUAxis,  SetUAxis, None,   'UAxis - primitive u axis')
   BottomDiameter = property (GetBottomDiameter,  SetBottomDiameter, None,   'BottomDiam - bottom diameter')
   TopDiameter    = property (GetTopDiameter,  SetTopDiameter, None, 'TopDiam - top diameter')
   Height         = property (GetHeight,  SetHeight, None, 'Height - primitive height')


