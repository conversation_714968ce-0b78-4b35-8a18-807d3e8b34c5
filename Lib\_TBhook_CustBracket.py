#
#  _TBhook_CustBracket - Customized Bracket panel hook
#
#  If this script is found in the PYTHONPATH it will be used to create bracket
#  panels automatically. The signature of the interface methods must not be changed.
#
import string
import kcs_dex
import kcs_ui
import kcs_util
import kcs_draft
import kcs_hullpan
import KcsPoint2D
import KcsModel

OK = kcs_util.ok()
CANCEL = kcs_util.cancel()


#-----------------------------------------------------------------
#
#    Function GetPanel
#
#    This is a helpfunction for Bracket1.
#    Interactively picks the panel, checking its type and name
#
#    Returns a tuple:
#
#    status can  we continue? (OK, or not OK)
#    panel  boundary panel name
#-----------------------------------------------------------------

def Get<PERSON>anel(message):
  point = KcsPoint2D.Point2D()
  while 1:
    # Indicate a point in the drawing
    res = kcs_ui.point2D_req(message, point)
    if res[0] == OK:
      try:
        model = KcsModel.Model()
        # Identify, what has been indicated?
        res = kcs_draft.model_identify(point,model)
        # Check the type of the identified model
        if model.Type != "plane panel":
          kcs_ui.message_confirm("Plane panel expected!")
        else:
          # Highlight the model item
          handle = kcs_draft.element_highlight(res[1])
          # Get user confirmation
          ask = kcs_ui.answer_req(message, \
                model.Name+", OK?")
          # Turn the highlight off
          kcs_draft.highlight_off(handle)
          if ask == kcs_util.yes():
            return (OK, model.Name) # OK, panel approved
      except:
        kcs_ui.message_noconfirm("Model not found!")
    else:
      return (CANCEL, "") # User cancelled the input

#-------------------------------------------------------------
#
#    Function Bracket1
#    Example of bracket generating code
#
#-------------------------------------------------------------

def Bracket1(BracketName):


  status = CANCEL
  Result = 0

  position = kcs_ui.string_req("Bracket position ( FR-term or X-Coord)")
  if position[0] == OK:
    FRTerm = str(position[1])
    tanktop = GetPanel("Indicate tanktop")
    if tanktop[0] == OK:
      girder = GetPanel("Indicate girder")
      if girder[0] == OK:
        status = OK

  if status == OK:
    try:
      # Initialise the panel
      kcs_ui.message_confirm( "Initiating panel")
      kcs_hullpan.pan_init(BracketName, "Double bottom plate")
      try:
        # PANEL statement
        st = "PAN, '" + BracketName +  "', SBP, DT=123, BRA, X=" + \
             FRTerm + ";"
        kcs_ui.message_confirm( "Creating panel")
        kcs_hullpan.stmt_exec_single(0, st, BracketName)

        # BOUNDARY statement
        st = "BOU, SUR=1" + "/ '" + tanktop[1] + \
             "'/ '" + girder[1] + "';"
        print(" BOU", st)
        kcs_ui.message_confirm( "Creating boundary")
        kcs_hullpan.stmt_exec_single(0, st, BracketName)

        # PLATE statement
        st = "PLA, MAT=12, MSI=FOR, QUA=A36;"
        print(" PLA", st)
        kcs_ui.message_confirm( "Creating plate")
        kcs_hullpan.stmt_exec_single(0, st, BracketName)

        # NOTCH statement
        st = "NOT, R50, COR=1,2,3;"
        print(" NOT", st)
        kcs_ui.message_confirm( "Creating notch")
        kcs_hullpan.stmt_exec_single(0, st, BracketName)

        # store the panel
        kcs_ui.message_confirm( "Storing bracket panel")
        kcs_hullpan.pan_store_single(BracketName)
        model = KcsModel.Model("plane panel", BracketName)
        try:
          kcs_draft.model_draw(model)
        except:
          kcs_ui.message_confirm("Error redrawing the panel!")

      finally:
        # terminate the scheme
        kcs_ui.message_confirm( "Skipping panel")
        kcs_hullpan.pan_skip_single(BracketName)
        return Result

    except:
      kcs_ui.message_confirm("Error creating the panel!")
      Result = 1
      return Result

#------------------------------------------------------------------------------
#    BracketList - the list of Bracket types to select from
#
#    The bracket list connects the symbol number in the bracket symbol font
#    d012sy095 ( displayed if option "other brackets") with a bracket number
#    and name. The bracket numbers are in the same order as the symbol numbers
#    but indexed from 0 and upwards.
#
#
#    !! Please make sure that the list of Bracket names end with an empty string
#       as this is used as the signal to terminate the reading of names !!
#
#
#------------------------------------------------------------------------------

BracketList = (
  "BRA_NAME0",
  "BRA_NAME1",
  "BRA_NAME2",
  ""
)


#-----------------------------------------------------------------------------
#    Interface methods - must not be changed regading input parameters & returns
#-----------------------------------------------------------------------------



#-----------------------------------------------------------------------------
#     Function getBracketName -
#
#     Returns the bracket name of a choosen symbol numer
#
#----------------------------------------------------------------------------


def getBracketName(BracketNo):
  try:
    return BracketList[BracketNo]
  except:
    return None

#-----------------------------------------------------------------------------
#     Function setBracketContour -
#
#     The Bracket definition function
#     The function should return an integer result
#     code, 0 for success, and 1 as exception
#
#-----------------------------------------------------------------------------


def setBracketContour( BracketNo, BracketName):

  if BracketNo == 0:
    res = Bracket1( BracketName)
    BraRes = res
  elif BracketNo == 1:
    BraRes = 1
  elif BracketNo == 2:
    BraRes = 1
  else:
    BraRes = 1

  return BraRes
