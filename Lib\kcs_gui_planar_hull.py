def file_new():
   return 33036

def file_open():
   return 33035

def file_close():
   return 33605

def file_save():
   return 33037

def file_save_as():
   return 33038

def file_revision_create_new_revision():
   return 32986

def file_revision_freeze_revision():
   return 32987

def file_revision_unfreeze_revision():
   return 32988

def file_print_preview():
   return 57609

def file_print():
   return 57610

def file_subdrawing_define():
   return 33603

def file_subdrawing_return():
   return 33604

def file_drawing_reference_define():
   return 33609

def file_drawing_reference_collapse():
   return 33610

def file_drawing_reference_expand():
   return 33611

def file_drawing_reference_exchange():
   return 33612

def file_drawing_reference_dissolve():
   return 33613

def file_drawing_reference_show():
   return 33614

def file_transfer():
   return 33041

def file_import_dxf():
   return 33004

def file_import_iges():
   return 33007

def file_export_dxf():
   return 33005

def file_export_iges():
   return 33008

def file_databank_list():
   return 33620

def file_databank_delete_in():
   return 33621

def file_pack():
   return 32949

def file_properties():
   return 33039

def file_exit():
   return 33042

def edit_undo():
   return 33066

def edit_delete_geometry():
   return 32975

def edit_delete_subpicture():
   return 32976

def edit_delete_dimension():
   return 32884

def edit_delete_part_of_dimension():
   return 32995

def edit_delete_note():
   return 32888

def edit_delete_position_number():
   return 32980

def edit_delete_hatch_pattern():
   return 32984

def edit_delete_hatch_pattern_island():
   return 32902

def edit_delete_cloud():
   return 32947

def edit_delete_markup():
   return 32981

def edit_delete_symbol():
   return 32862

def edit_delete_text_singleline():
   return 32871

def edit_delete_text_multiline():
   return 32872

def edit_delete_construction_lines():
   return 32983

def edit_delete_by_layer():
   return 32985

def edit_delete_by_area():
   return 32977

def edit_text():
   return 32870

def view_repaint():
   return 33070

def view_zoom_in():
   return 33072

def view_zoom_out():
   return 33074

def view_zoom_subpicture():
   return 33050

def view_zoom_auto():
   return 33071

def view_zoom_previous():
   return 33049

def view_zoom_double():
   return 33043

def view_zoom_half():
   return 33044

def view_zoom_define_window():
   return 33047

def view_zoom_select_window():
   return 33048

def view_zoom_windows():
   return 65535

def view_zoom_edit_windows():
   return 40006

def view_pan():
   return 33073

def view_layer():
   return 33018

def view_grid_visible():
   return 32811

def view_grid_snap():
   return 32810

def view_grid_define():
   return 33003

def view_envelope_define():
   return 33606

def view_envelope_remove():
   return 33607

def view_shading_zoom_model():
   return 33055

def view_shading_top_view():
   return 32831

def view_shading_frame_view_looking_aft():
   return 32832

def view_shading_frame_view_looking_for():
   return 32838

def view_shading_side_view_looking_port():
   return 32833

def view_shading_isometric_view_looking_aft():
   return 32834

def view_shading_isometric_view_looking_for():
   return 32839

def view_treeview():
   return 34366

def view_status_bar():
   return 59393

def view_message_window():
   return 32823

def view_console():
   return 34383

def view_log_viewer():
   return 33636

def view_toolbars_2d_point_mode():
   return 31102

def view_toolbars_3d_request():
   return 31107

def view_toolbars_collision_control():
   return 31121

def view_toolbars_controls():
   return 31103

def view_toolbars_dimension():
   return 31115

def view_toolbars_equipment_placed_volume():
   return 31112

def view_toolbars_geometry():
   return 31101

def view_toolbars_geometry_2():
   return 31114

def view_toolbars_lock():
   return 31109

def view_toolbars_planar_hull():
   return 31111

def view_toolbars_predefined_windows():
   return 31118

def view_toolbars_scheme_editor():
   return 31120

def view_toolbars_shading():
   return 31104

def view_toolbars_standard():
   return 31108

def view_toolbars_subpicture():
   return 31110

def view_toolbars_subpicture_level():
   return 31106

def view_toolbars_symbol():
   return 31117

def view_toolbars_text():
   return 31116

def view_toolbars_transformation():
   return 31105

def view_toolbars_triboncom():
   return 31119

def view_toolbars_vitesse():
   return 31113

def insert_model():
   return 32931

def insert_point():
   return 32771

def insert_line():
   return 32772

def insert_arc():
   return 32773

def insert_polyline():
   return 32774

def insert_spline():
   return 32776

def insert_conic():
   return 32775

def insert_rectangle():
   return 32777

def insert_square():
   return 32778

def insert_parallel_curve():
   return 32780

def insert_fillet():
   return 32915

def insert_hatch_pattern():
   return 32901

def insert_construction_lines():
   return 32999

def insert_text_singleline():
   return 32864

def insert_text_multiline():
   return 32982

def insert_underline():
   return 32867

def insert_symbol():
   return 32857

def insert_subpicture():
   return 32942

def insert_standard_detail():
   return 33009

def insert_plate_part():
   return 33623

def insert_drawing_form():
   return 33015

def insert_nodes_contour():
   return 32899

def insert_nodes_spline():
   return 32897

def insert_tangents_contour():
   return 32900

def insert_tangents_spline():
   return 32898

def format_colour():
   return 32990

def format_line_type():
   return 32991

def format_layer():
   return 32992

def format_hatch_pattern():
   return 32993

def format_defaults():
   return 32998

def format_local_origin():
   return 32950

def zap_operation_complete():
   return 33062

def zap_quit():
   return 33063

def zap_cancel():
   return 33064

def zap_options():
   return 33065

def zap_all():
   return 33067

def zap_subpicture_level_view():
   return 33076

def zap_subpicture_level_subview():
   return 33077

def zap_subpicture_level_component():
   return 33078

def zap_subpicture_level_subcomponent():
   return 33079

def tools_model_present():
   return 32935

def tools_model_cross_section():
   return 32936

def tools_model_collision_control():
   return 32928

def tools_model_exchange():
   return 32934

def tools_model_copy():
   return 32933

def tools_model_view_exchange():
   return 33760

def tools_model_view_autoscale():
   return 33762

def tools_model_view_autoscale_all():
   return 33775

def tools_model_view_change_projection():
   return 33763

def tools_model_view_slice():
   return 33764

def tools_model_view_clear():
   return 33761

def tools_model_view_explode():
   return 33766

def tools_model_view_validate():
   return 33768

def tools_model_view_hull_symmetry():
   return 33767

def tools_model_view_launch_batch_hidden_line():
   return 33765

def tools_model_object_revision_list():
   return 34771

def tools_model_object_revision_save():
   return 34772

def tools_collision_control_collide():
   return 34411

def tools_collision_control_collide_selected():
   return 34413

def tools_collision_control_collide_within_box():
   return 34414

def tools_collision_control_clearance_distance():
   return 34415

def tools_backdrop():
   return 32930

def tools_subpicture_current():
   return 32997

def tools_subpicture_save():
   return 32943

def tools_subpicture_save_as_std_detail():
   return 33010

def tools_subpicture_save_as_std_hatch_pattern():
   return 33012

def tools_subpicture_exchange():
   return 32941

def tools_subpicture_rename():
   return 32940

def tools_subpicture_redefine_transformation():
   return 32944

def tools_subpicture_regroup():
   return 32945

def tools_subpicture_split():
   return 32946

def tools_vitesse_run_script():
   return 33503

def tools_vitesse_run_selected():
   return 34035

def tools_vitesse_edit():
   return 34034

def tools_vitesse_debug():
   return 34037

def tools_vitesse_log_window():
   return 34038

def tools_vitesse_reload():
   return 34039

def tools_vitesse_options():
   return 34040

def tools_vitesse_stop_script():
   return 34041

def tools_geometry_macro():
   return 33608

def tools_inquiry_model():
   return 33026

def tools_inquiry_name():
   return 33027

def tools_inquiry_verify():
   return 33030

def tools_inquiry_used_layers():
   return 33022

def tools_inquiry_workspace():
   return 33031

def tools_inquiry_drawing_status():
   return 33028

def tools_drawing_form_open():
   return 33016

def tools_drawing_form_save():
   return 33017

def tools_text_font_open():
   return 33615

def tools_text_font_save():
   return 33616

def tools_symbol_open():
   return 33617

def tools_symbol_save():
   return 33618

def tools_symbol_remove():
   return 33619

def tools_symbol_explode():
   return 32861

def tools_insert_std_hatch_pattern():
   return 33011

def tools_equipment_delete():
   return 32916

def tools_equipment_place():
   return 32917

def tools_equipment_move():
   return 32918

def tools_equipment_convert_placed_volume():
   return 32919

def tools_equipment_assembly():
   return 32920

def tools_equipment_place_deck_outfitting():
   return 32921

def tools_equipment_update_data():
   return 32922

def tools_equipment_display_preliminary_production_status():
   return 32923

def tools_equipment_display_preliminary_model_status():
   return 32924

def tools_equipment_remove_unsolved_references():
   return 32925

def tools_equipment_ready():
   return 32948

def tools_volume_place():
   return 32926

def tools_volume_move_placed():
   return 32927

def tools_volume_delete_placed():
   return 33624

def tools_volume_create_unit():
   return 32929

def tools_volume_export_to_xml():
   return 33699

def tools_data_management_attributes():
   return 33982

def tools_data_management_change_status():
   return 33980

def tools_inspect_drawing():
   return 33033

def tools_clean_workspace():
   return 33034

def tools_show_accelerators():
   return 33040

def tools_global_database():
   return 32989

def tools_message_confirm():
   return 33000

def tools_preferences():
   return 32994

def dimension_linear():
   return 32874

def dimension_radius():
   return 32875

def dimension_diameter():
   return 32876

def dimension_angle():
   return 32877

def dimension_curved():
   return 32878

def dimension_area():
   return 32879

def dimension_distance():
   return 32880

def dimension_3d_linear():
   return 32890

def dimension_3d_axis_parallel():
   return 32891

def dimension_3d_coordinate():
   return 32892

def dimension_3d_curve_length():
   return 32894

def dimension_3d_weight_and_cog():
   return 32893

def dimension_3d_shell_profile_mounting_angle():
   return 32895

def dimension_3d_distance_to_plane_leader():
   return 32896

def dimension_3d_distance_to_plane_box():
   return 32881

def dimension_control_point_activate_assembly():
   return 33964

def dimension_control_point_add_measure():
   return 33965

def dimension_control_point_add_auxiliary():
   return 33966

def dimension_control_point_insert_measure():
   return 33967

def dimension_control_point_insert_auxiliary():
   return 33968

def dimension_control_point_renumber():
   return 33969

def dimension_control_point_delete():
   return 33970

def dimension_control_point_define_coordinate_system():
   return 33971

def dimension_control_point_save():
   return 33972

def dimension_control_point_import():
   return 33974

def dimension_control_point_export():
   return 33973

def annotate_general_note():
   return 32886

def annotate_position_number():
   return 32885

def annotate_config_note_config_set():
   return 32978

def annotate_config_note_config_move():
   return 32979

def annotate_auto_note_pipe():
   return 32889

def annotate_hull_note_pos_no():
   return 33120

def annotate_hull_note_assembly():
   return 33121

def annotate_hull_note_bevel():
   return 33122

def annotate_hull_note_grinding():
   return 34068

def annotate_hull_note_hole_dim():
   return 33714

def annotate_markup():
   return 33773

def annotate_ruler():
   return 34000

def annotate_position_ruler():
   return 32882

def annotate_coordinate_axis():
   return 34770

def annotate_pipe_restriction_symbol():
   return 33769

def annotate_general_restriction_symbol():
   return 33770

def annotate_cross():
   return 33771

def annotate_cloud():
   return 33772

def modify_move_text():
   return 32868

def modify_move_symbol():
   return 32859

def modify_move_reference():
   return 32887

def modify_copy_text():
   return 32866

def modify_copy_symbol():
   return 32858

def modify_copy_geometry():
   return 32856

def modify_copy_subpicture():
   return 32939

def modify_transform_geometry():
   return 32952

def modify_transform_subpicture():
   return 32938

def modify_trim_to_entity():
   return 32910

def modify_trim_by_length():
   return 32911

def modify_trim_gap():
   return 32912

def modify_trim_cutout():
   return 32913

def modify_trim_fillet():
   return 32914

def modify_stretch():
   return 32909

def modify_chain():
   return 32907

def modify_dechain():
   return 32908

def modify_properties_text():
   return 32869

def modify_properties_symbol():
   return 32860

def modify_dimension():
   return 32883

def modify_cloud():
   return 32937

def modify_colour():
   return 32903

def modify_line_type():
   return 32904

def modify_layer():
   return 32905

def modify_general():
   return 32906

def structure_new():
   return 33470

def structure_activate():
   return 33471

def structure_save():
   return 33472

def structure_cancel():
   return 33473

def structure_delete():
   return 33474

def structure_list_activated():
   return 33475

def structure_part_transform():
   return 33476

def structure_part_new():
   return 33477

def structure_part_delete():
   return 33478

def structure_part_modify():
   return 33479

def structure_part_rename():
   return 33480

def structure_part_flip():
   return 33482

def structure_part_move():
   return 33481

def structure_part_copy():
   return 33483

def structure_part_hole():
   return 33484

def structure_part_hole_insertion():
   return 33925

def structure_part_end_cut_keyin():
   return 33486

def structure_part_end_cut_fit_beam_model():
   return 33901

def structure_part_end_cut_fit_beam_2d_line():
   return 33900

def structure_part_end_cut_modify_model():
   return 33921

def structure_part_end_cut_modify_2d_line():
   return 33920

def structure_part_end_cut_snip():
   return 33922

def structure_part_end_cut_delete():
   return 33923

def structure_part_end_cut_mitre_conn():
   return 33924

def structure_part_plate_trim():
   return 33927

def structure_part_bent_plate_create():
   return 33928

def structure_part_bent_plate_split():
   return 33929

def structure_part_fillet_profile():
   return 33931

def structure_part_information():
   return 33485

def structure_multiple_parts_transform():
   return 33907

def structure_multiple_parts_duplicate():
   return 33906

def structure_multiple_parts_delete():
   return 33908

def structure_multiple_parts_stretch():
   return 33902

def structure_multiple_parts_split():
   return 33903

def structure_multiple_parts_put_to():
   return 33909

def structure_multiple_parts_copy_from():
   return 33910

def structure_multiple_parts_mirror_transform():
   return 33911

def structure_multiple_parts_mirror_duplicate():
   return 33912

def structure_multiple_parts_win_option_in():
   return 33913

def structure_multiple_parts_win_option_cross():
   return 33914

def structure_multiple_parts_win_option_incross():
   return 33915

def structure_standard_unit_general_seat_inside():
   return 33904

def structure_standard_unit_general_seat_outside():
   return 33905

def structure_transform():
   return 33487

def structure_insert():
   return 33488

def structure_rename():
   return 33489

def structure_production_information_assembly_model():
   return 33490

def structure_production_information_assembly_part():
   return 33491

def structure_production_information_position_number():
   return 33492

def structure_production_information_hull_marking_on():
   return 33493

def structure_production_information_hull_marking_off():
   return 33494

def structure_production_information_hull_marking_check():
   return 33495

def structure_production_information_hull_marking_define_contour():
   return 34810

def structure_production_information_hull_marking_display_contour():
   return 34812

def structure_production_information_hull_marking_delete_contour():
   return 34811

def structure_production_information_split():
   return 33496

def structure_group_move():
   return 33497

def structure_group_duplicate():
   return 33498

def structure_group_rename():
   return 33594

def structure_group_colour():
   return 33709

def structure_group_delete():
   return 33937

def structure_group_win_option_in():
   return 33913

def structure_group_win_option_cross():
   return 33914

def structure_group_win_option_incross():
   return 33915

def structure_general_bar_section():
   return 33926

def structure_standard_output():
   return 33499

def structure_standard_input():
   return 33500

def structure_standard_replace():
   return 33501

def structure_standard_define_origin():
   return 33934

def structure_standard_display_origin():
   return 33935

def structure_standard_description():
   return 33936

def structure_cableway_component():
   return 33919

def structure_connect():
   return 33916

def structure_disconnect():
   return 33917

def structure_display_connections():
   return 33918

def planar_model_create():
   return 33096

def planar_model_modify():
   return 33097

def planar_model_edit():
   return 33103

def planar_model_assembly():
   return 33102

def planar_model_divide():
   return 33104

def planar_model_split_sti():
   return 33105

def planar_model_modify_sti_end():
   return 33090

def planar_model_delete():
   return 33106

def planar_panel_copy():
   return 33111

def planar_panel_move():
   return 33112

def planar_panel_rename():
   return 33776

def planar_panel_split():
   return 33113

def planar_panel_remove_seam():
   return 33430

def planar_panel_jumbo_split():
   return 33095

def planar_panel_modify_symm():
   return 33429

def planar_panel_topology():
   return 33691

def planar_panel_compare():
   return 34065

def planar_panel_knuckled():
   return 33093

def planar_panel_recreate():
   return 33431

def planar_panel_panbkt():
   return 33091

def planar_panel_list():
   return 33094

def planar_panel_delete():
   return 33110

def planar_scheme_input():
   return 33085

def planar_scheme_show():
   return 33088

def planar_scheme_edit():
   return 33087

def planar_scheme_run_mode():
   return 33089

def planar_scheme_gen_in_background():
   return 33427

def planar_scheme_extract_scheme():
   return 33682

def planar_scheme_create_list():
   return 33086

def planar_editor_new():
   return 33780

def planar_editor_open():
   return 33781

def planar_editor_close():
   return 33782

def planar_editor_save():
   return 33783

def planar_editor_edit_cut():
   return 33784

def planar_editor_edit_copy():
   return 33785

def planar_editor_edit_paste():
   return 33786

def planar_editor_edit_select_all():
   return 33787

def planar_editor_edit_find():
   return 33788

def planar_editor_edit_find_next():
   return 33789

def planar_editor_edit_replace():
   return 33790

def planar_editor_statement_run_this():
   return 33792

def planar_editor_statement_run_all():
   return 33798

def planar_editor_statement_run_all_changed():
   return 33799

def planar_editor_statement_run():
   return 33793

def planar_editor_statement_copy():
   return 33794

def planar_editor_statement_paste():
   return 33795

def planar_editor_statement_delete():
   return 33797

def planar_editor_statement_new():
   return 33796

def planar_view_create():
   return 33114

def planar_view_detail():
   return 33115

def planar_view_recreate():
   return 33116

def planar_view_modify():
   return 33117

def planar_view_select():
   return 33118

def planar_view_edit():
   return 34768

def planar_view_properties():
   return 34769

def planar_posno_scope():
   return 33130

def planar_posno_set():
   return 33131

def planar_posno_change():
   return 33132

def planar_posno_copy():
   return 33133

def planar_posno_auto():
   return 33134

def planar_rso_create():
   return 33800

def planar_rso_recreate():
   return 33092

def planar_rso_delete():
   return 33801

def planar_rso_disconnect():
   return 33812

def planar_select_activate():
   return 33107

def planar_select_store():
   return 33108

def planar_select_skip():
   return 33109

def planar_select_store_and_skip():
   return 33139

def planar_select_list_activated():
   return 33140

def hull_tools_curve_create_curve():
   return 33126

def hull_tools_curve_store_curve():
   return 33127

def hull_tools_functional_descriptions():
   return 33433

def hull_tools_shrinkage_info():
   return 33128

def hull_tools_panel_intersect():
   return 33123

def hull_tools_default_parameters_update():
   return 33360

def hull_tools_default_parameters_reinit():
   return 33125

def hull_tools_recreate_limit_tables():
   return 33777

def hull_tools_ppi_hull():
   return 33432

def hull_tools_dimensioning():
   return 33646

def hull_tools_painting_areas_create_input():
   return 34502

def hull_tools_painting_areas_calculate():
   return 34503

def hull_tools_hull_mark_prepare():
   return 34605

def hull_tools_hull_mark_project():
   return 34606

def viewport_new_viewport():
   return 34400

def viewport_new_floating_viewport():
   return 34408

def viewport_new_shaded_viewport():
   return 34409

def viewport_new_shaded_floating_viewport():
   return 34410

def viewport_close():
   return 34401

def viewport_close_all():
   return 34402

def viewport_next():
   return 34403

def viewport_previous():
   return 34404

def viewport_cascade():
   return 34405

def viewport_tile_horizontally():
   return 34406

def viewport_tile_vertically():
   return 34407

def viewport_0_main_viewport():
   return 34420

def help_planar_hull_help():
   return 57667

def help_aveva_on_the_web():
   return 34001

def help_about_planar_hull():
   return 57664

def toolbar_geometry():
   return 126

def toolbar_2d_point_mode():
   return 127

def toolbar_controls():
   return 130

def toolbar_shading():
   return 131

def toolbar_transformation():
   return 128

def toolbar_subpicture_level():
   return 129

def toolbar_3d_request():
   return 139

def toolbar_standard():
   return 149

def toolbar_lock():
   return 141

def toolbar_subpicture():
   return 191

def toolbar_curved_hull():
   return 192

def toolbar_nesting():
   return 193

def toolbar_nesting_bump_functions():
   return 211

def toolbar_planar_hull():
   return 194

def toolbar_pipe():
   return 195

def toolbar_pipe_model():
   return 196

def toolbar_pipe_material():
   return 197

def toolbar_pipe_production():
   return 198

def toolbar_pipe_align_frame():
   return 250

def toolbar_ventilation():
   return 199

def toolbar_ventilation_modelling():
   return 200

def toolbar_ventilation_material():
   return 201

def toolbar_ventilation_production():
   return 202

def toolbar_cable():
   return 203

def toolbar_cable_modelling():
   return 204

def toolbar_cableway():
   return 205

def toolbar_cableway_modelling():
   return 206

def toolbar_cableway_material():
   return 207

def toolbar_cableway_production():
   return 208

def toolbar_structure():
   return 209

def toolbar_structure_modelling():
   return 210

def toolbar_structure_win_option():
   return 247

def toolbar_structure_standard_unit():
   return 303

def toolbar_structure_multi_parts():
   return 304

def toolbar_structure_group():
   return 305

def toolbar_structure_endcut():
   return 306

def toolbar_equipment_placed_volume():
   return 218

def toolbar_vitesse():
   return 219

def toolbar_geometry_2():
   return 220

def toolbar_dimension():
   return 221

def toolbar_text():
   return 222

def toolbar_symbol():
   return 223

def toolbar_predefined_windows():
   return 225

def toolbar_triboncom():
   return 320

def toolbar_scheme_editor():
   return 375

def toolbar_plcm():
   return 384

def toolbar_collision_control():
   return 400

