#!wing
#!version=4.0
##################################################################
# Wing IDE project file                                          #
##################################################################
[project attributes]
proj.directory-list = [{'dirloc': loc('.'),
                        'excludes': (),
                        'filter': '*',
                        'include_hidden': False,
                        'recursive': True,
                        'watch_for_changes': True}]
proj.file-type = 'normal'
[user attributes]
console.id-seed = 7
console.toolbox = [{'autosave': False,
                    'ctype': ('loc',
                              u'C:\\Tribon\\M3\\Vitesse\\kcs_ex_struct_test.py'),
                    'env': ('project',
                            ['']),
                    'id': 'cmd-1',
                    'io_encoding': None,
                    'key_binding': None,
                    'line_mode': True,
                    'pseudo_tty': False,
                    'pypath': ('project',
                               ''),
                    'raise_panel': True,
                    'rundir': ('project',
                               ''),
                    'title': None}]
debug.breakpoints = {loc('kcs_ex_struct_test.py'): {133: (0,
        None,
        1,
        0),
        316: (0,
              None,
              1,
              0),
        327: (1,
              None,
              1,
              0)}}
debug.err-values = {None: {},
                    loc('kcs_ex_struct_test.py'): {}}
debug.var-col-widths = [0.45244956772334294,
                        0.54755043227665701]
guimgr.overall-gui-state = {'windowing-policy': 'combined-window',
                            'windows': [{'name': 'sIMhSSKpgI0qBoVWlgmvOTSDxB'\
        'W0YMZU',
        'size-state': '',
        'type': 'dock',
        'view': {'area': 'tall',
                 'constraint': None,
                 'current_pages': [0,
                                   2],
                 'full-screen': False,
                 'notebook_display': 'normal',
                 'notebook_percent': 0.25,
                 'override_title': None,
                 'pagelist': [('debug-stack',
                               'tall',
                               1,
                               {}),
                              ('indent',
                               'tall',
                               2,
                               {}),
                              ('project',
                               'tall',
                               0,
                               {'tree-state': {'file-sort-method': 'by name',
        'list-files-first': False,
        'tree-states': {'deep': {'column-widths': [1.0],
                                 'expanded-nodes': [(4,),
        (4,
         5)],
                                 'selected-nodes': [(24,)],
                                 'top-node': (6,)}},
        'tree-style': 'deep'}}),
                              ('snippets',
                               'tall',
                               0,
                               {}),
                              ('source-assistant',
                               'tall',
                               2,
                               {'docstring-during-complete': False,
                                'wrap-lines': True}),
                              ('browser',
                               'tall',
                               0,
                               {})],
                 'primary_view_state': {'area': 'wide',
        'constraint': None,
        'current_pages': [2,
                          7],
        'notebook_display': 'normal',
        'notebook_percent': 0.13268156424581001,
        'override_title': None,
        'pagelist': [('bookmarks',
                      'wide',
                      1,
                      {}),
                     ('debug-breakpoints',
                      'wide',
                      0,
                      {}),
                     ('debug-io',
                      'wide',
                      1,
                      {'attrib-starts': [],
                       'first-line': 0,
                       'folded-linenos': [],
                       'sel-line': 1,
                       'sel-line-start': 3,
                       'selection_end': 3,
                       'selection_start': 3}),
                     ('debug-probe',
                      'wide',
                      2,
                      {'attrib-starts': [],
                       'first-line': 0,
                       'folded-linenos': [],
                       'history': {None: ["kcs_draft.model_identify(pint, 'P"\
        "N-001')\n",
        'model = KcsModel.Model()\n',
        "model.SetName('PN-001')\n",
        'kcs_draft.model_identify(pint,model)\n',
        'kcs_draft.model_identify(None,model)\n',
        'nHandle.ElementHandle\n',
        'nHandle.\n',
        'nHandle.__doc__\n',
        'kcs_draft.subpicture_name_get(nHandle)\n',
        'transf = KcsTransformation2D.Transformation2D()\n',
        'import KcsTransformation2D\n',
        'ransf = KcsTransformation2D.Transformation2D()\n',
        'kcs_draft.element_transformation_get(nHandle.ElementHandle(),ransf)'\
        '\n',
        'kcs_draft.element_transformation_get(nHandle,ransf)\n',
        'kcs_draft.subpicture_name_get(nHandle) \n',
        'model = KcsModel. Model()\n',
        'kcs_draft. model_properties_get(nHandle,model)\n',
        'model.\n',
        'mo\n',
        'model\n',
        'cont      = KcsContour2D.Contour2D()\n',
        'kcs_draft.contour_properties_get(nHandle,cont)\n',
        'a = kcs_draft.contour_properties_get(nHandle,cont)\n',
        'a.\n',
        'a.GetCenterPoint\n',
        'current2DPoint = kcs_draft.contour_properties_get(nHandle,KcsContou'\
        'r2D.Contour2D()).GetCenterPoint\n',
        'previousHandle is not None\n',
        'if previous2DPoint is not None\n',
        'if previous2DPoint == None\n',
        'if previous2DPoint == None:\n\n\nexit;\n   return a\nexit\nquit()\n'\
        '\n',
        'previous2DPoint == None\n',
        'previous2DPoint is not None\n',
        'id1.X\n',
        'id1\n',
        'id1.\n',
        'id1.__getattribute__\n',
        'current2DPoint.\n',
        'current2DPoint.GetPointOnContour\n',
        'point = kcs_draft.point_new()\n',
        'point = KcsPoint2D()\n',
        'point = KcsPoint2D.\n',
        'point = KcsPoint2D.Point2D\n',
        'point\n',
        'current2DPoint.GetPointOnContour()\n',
        'current2DPoint.GetPointOnContour(point)\n',
        'current2DPoint.Length()\n',
        'current2DPoint.Length\n',
        'nt()\n',
        'bcurrent2DPoint = kcs_draft.contour_properties_get(nHandle,KcsConto'\
        'ur2D.Contour2D()).GetCenterPoint()\n',
        'current2DPoint = kcs_draft.contour_properties_get(nHandle,KcsContou'\
        'r2D.Contour2D()).GetCenterPoint()\n',
        'point = KcsPoint2D(nHandle)\n',
        'point = KcsPoint2D.Point2D(nHandle)\n',
        'current2DPoint = kcs_draft.contour_properties_get(nHandle,KcsContou'\
        'r2D.Contour2D())\n',
        'current2DPoint\n',
        'current2DPoint.AddLine()\n',
        'nHandle.__init__\n',
        'nHandle.__init__.__str__\n',
        'nHandle.__module__\n',
        'nHandle._ElementHandle__handle\n',
        'current2DPoint = KcsContour2D.Contour2D()\n',
        'kcs_draft.contour_properties_get(nHandle,current2DPoint)\n',
        'point = KcsPoint2D.Point2D()\n',
        'kcs_draft.contour_properties_get(nHandle,point)\n',
        'contour = KcsContour2D.Contour2D(point)\n',
        'kcs_draft.contour_properties_get(nHandle,contour)\n',
        'contour.GetCenterPoint\n',
        'contour.GetPointOnContour\n',
        'contour.GetPointOnContour()\n',
        'contour.GetCenterPoint()\n',
        "kcs_draft. element_identify('PN-001')\n",
        "kcs_draft.element_identify('PN-001')\n",
        'nh\n',
        'nHandle\n',
        'import KcsSymbol\n',
        'kcs_draft.symbol_properties_get(nHandle, KcsSymbol.Symbol())\n',
        'current2DPoint = kcs_draft.symbol_properties_get(nHandle, KcsSymbol'\
        '.Symbol())\n',
        'current2DPoint.GetPosition\n',
        'current2DPoint.GetPosition()\n',
        'urllib2.Request(web_url, data, headers)\n',
        'req.get_host\n',
        'req.get_method.__str__',
        'data\n',
        'json.dumps(data)d',
        'req\n',
        'data = "{\\"pageIndex\\":1,\\"pageSize\\":10,\\"sort\\":null,\\"con'\
        'dition\\":[]}"\n',
        "data = '''{\\\"pageIndex\\\":1,\\\"pageSize\\\":10,\\\"sort\\\":nul"\
        "l,\\\"condition\\\":[]}'''\n",
        "data = json.dumps(data)\nheaders = {'Content-Type': 'application/js"\
        "on', 'APPID': 'SuperAppId', 'erupt': 'Struct'}\nreq = urllib2.Reque"\
        "st(web_url, data, headers)\nurllib2.urlopen(req).read()\n\n",
        'urllib2.urlopen(req)\n',
        "data = '''{\"pageIndex\":1,\"pageSize\":10,\"sort\":null,\"conditio"\
        "n\":[]}'''\ndata = json.dumps(data)\nheaders = {'Content-Type': 'ap"\
        "plication/json', 'APPID': 'SuperAppId', 'erupt': 'Struct'}\nreq = u"\
        "rllib2.Request(web_url, data, headers)\n\n",
        'data = {"pageIndex":1,"pageSize":10,"sort":null,"condition":[]}\n',
        'data = {"pageIndex":1,"pageSize":10,"sort":None,"condition":[]}\n',
        'data = json.dumps(data)\n',
        "headers = {'Content-Type': 'application/json', 'APPID': 'SuperAppId"\
        "', 'erupt': 'Struct'}\n",
        'req = urllib2.Request(web_url, data, headers)\n',
        'urllib2.urlopen(req).read()\n',
        'response = urllib2.urlopen(req).read()\n',
        'response = json.loads(response)\n',
        'response\n',
        'response.get("list")\n',
        'for element in response.get("list"):\n         if element.get("ifHi'\
        'ghlisht") == "Y":\n            kcs_ui.message_confirm("\xe9\xab\x98'\
        '\xe4\xba\xae\xef\xbc\x9a" + element.get("name"))\n\n'],
                                   u'file:C:/Tribon/M3/Vitesse/kcs_ex_struct_test.py': [''\
        'CurType\n',
        'model.GetName\n',
        'model.N\n',
        'mode\n',
        'model.Name\n',
        'data\n',
        'ex\n']},
                       'sel-line': 0,
                       'sel-line-start': 0,
                       'selection_end': 0,
                       'selection_start': 0}),
                     ('debug-exceptions',
                      'wide',
                      0,
                      {}),
                     ('messages',
                      'wide',
                      2,
                      {'current-domain': 0}),
                     ('debug-modules',
                      'wide',
                      1,
                      {}),
                     ('os-command',
                      'wide',
                      1,
                      {'last-percent': 0.80000000000000004,
                       'toolbox-percent': 1.0,
                       'toolbox-tree-sel': 'cmd-1'}),
                     ('python-shell',
                      'wide',
                      2,
                      {'attrib-starts': [],
                       'first-line': 0,
                       'folded-linenos': [],
                       'history': {},
                       'sel-line': 2,
                       'sel-line-start': 141,
                       'selection_end': 141,
                       'selection_start': 141}),
                     ('interactive-search',
                      'wide',
                      0,
                      {'fScope': {'fFileSetName': u'All Source Files',
                                  'fLocation': None,
                                  'fRecursive': True,
                                  'fType': 'project-files'},
                       'fSearchSpec': {'fEndPos': None,
                                       'fIncludeLinenos': True,
                                       'fInterpretBackslashes': False,
                                       'fMatchCase': False,
                                       'fOmitBinary': True,
                                       'fRegexFlags': 46,
                                       'fReplaceText': '',
                                       'fReverse': False,
                                       'fSearchText': u'down',
                                       'fStartPos': 0,
                                       'fStyle': 'text',
                                       'fWholeWords': False,
                                       'fWrap': True},
                       'fUIOptions': {'fAutoBackground': True,
                                      'fFilePrefix': 'short-file',
                                      'fFindAfterReplace': True,
                                      'fInSelection': False,
                                      'fIncremental': True,
                                      'fReplaceOnDisk': False,
                                      'fShowFirstMatch': False,
                                      'fShowLineno': True,
                                      'fShowReplaceWidgets': False},
                       'replace-entry-expanded': False,
                       'search-entry-expanded': False}),
                     ('batch-search',
                      'wide',
                      0,
                      {'fScope': {'fFileSetName': u'All Source Files',
                                  'fLocation': None,
                                  'fRecursive': True,
                                  'fType': 'project-files'},
                       'fSearchSpec': {'fEndPos': None,
                                       'fIncludeLinenos': True,
                                       'fInterpretBackslashes': False,
                                       'fMatchCase': False,
                                       'fOmitBinary': True,
                                       'fRegexFlags': 46,
                                       'fReplaceText': '',
                                       'fReverse': False,
                                       'fSearchText': u'',
                                       'fStartPos': 0,
                                       'fStyle': 'text',
                                       'fWholeWords': False,
                                       'fWrap': True},
                       'fUIOptions': {'fAutoBackground': True,
                                      'fFilePrefix': 'short-file',
                                      'fFindAfterReplace': True,
                                      'fInSelection': False,
                                      'fIncremental': True,
                                      'fReplaceOnDisk': False,
                                      'fShowFirstMatch': False,
                                      'fShowLineno': True,
                                      'fShowReplaceWidgets': False},
                       'replace-entry-expanded': False,
                       'search-entry-expanded': False}),
                     ('debug-data',
                      'wide',
                      0,
                      {}),
                     ('testing',
                      'wide',
                      0,
                      {'added-files': [loc('test.py')],
                       'filter': '',
                       'recent-filters': None,
                       'sort-order': 'alpha'}),
                     ('debug-watch',
                      'wide',
                      1,
                      {'node-states': [('eval',
        'kcs_ui'),
                                       ('eval',
        'kcs_ui'),
                                       ('eval',
        'ex'),
                                       ('eval',
        'CurType'),
                                       ('eval',
        'data'),
                                       ('eval',
        'nHandle'),
                                       ('eval',
        'region'),
                                       ('eval',
        'region'),
                                       ('eval',
        'data'),
                                       ('eval',
        'response')],
                       'tree-state': {'column-widths': [0.5171102661596958,
        0.4828897338403042],
                                      'expanded-nodes': [],
                                      'selected-nodes': [(0,)],
                                      'top-node': (0,)}})],
        'primary_view_state': {'editor_states': {'bookmarks': ([(loc('kcs_ex_struct_test.py'),
        {'attrib-starts': [],
         'first-line': 0,
         'folded-linenos': [],
         'sel-line': 8,
         'sel-line-start': 194,
         'selection_end': 194,
         'selection_start': 194},
        1747983603.6029999),
        [loc('unknown:untitled-1.py'),
         {'attrib-starts': [],
          'first-line': 0,
          'folded-linenos': [],
          'sel-line': 0,
          'sel-line-start': 0,
          'selection_end': 0,
          'selection_start': 0},
         1747983625.0050001]],
        1),
        'current-loc': None,
        'editor-states': {loc('Examples/Drafting/kcs_ex_draft22.py'): {'attr'\
        'ib-starts': [('GetEntityHandles',
                       26)],
        'first-line': 25,
        'folded-linenos': [],
        'sel-line': 52,
        'sel-line-start': 2002,
        'selection_end': 2013,
        'selection_start': 2013},
                          loc('Examples/Drafting/kcs_ex_draft30.py'): {'attr'\
        'ib-starts': [('GetElements',
                       147)],
        'first-line': 85,
        'folded-linenos': [],
        'sel-line': 164,
        'sel-line-start': 6486,
        'selection_end': 6503,
        'selection_start': 6503},
                          loc('kcs_ex_struct_test.py'): {'attrib-starts': [],
        'first-line': 0,
        'folded-linenos': [],
        'sel-line': 8,
        'sel-line-start': 194,
        'selection_end': 194,
        'selection_start': 194}},
        'has-focus': True,
        'locked': False},
                               'open_files': [u'Examples/Drafting/kcs_ex_draft22.py',
        u'Examples/Drafting/kcs_ex_draft30.py',
        u'kcs_ex_struct_test.py',
        'unknown:untitled-1.py']},
        'saved_notebook_display': None,
        'split_percents': {0: 0.24807826694619148},
        'splits': 2,
        'tab_location': 'top',
        'user_data': {}},
                 'saved_notebook_display': None,
                 'split_percents': {0: 0.5},
                 'splits': 2,
                 'tab_location': 'left',
                 'user_data': {}},
        'window-alloc': (80,
                         183,
                         1603,
                         851)}]}
guimgr.recent-documents = [loc('unknown:untitled-1.py'),
                           loc('kcs_ex_struct_test.py'),
                           loc('Examples/Drafting/kcs_ex_draft30.py'),
                           loc('Examples/Drafting/kcs_ex_draft22.py')]
guimgr.visual-state = {loc('Examples/Drafting/kcs_ex_draft20.py'): {'attrib-'\
        'starts': [('EntityFromPoint',
                    62)],
        'first-line': 50,
        'folded-linenos': [],
        'sel-line': 67,
        'sel-line-start': 2591,
        'selection_end': 2591,
        'selection_start': 2591},
                       loc('Lib/KcsContour2D.py'): {'attrib-starts': [('Cont'\
        'our2D',
        40),
        ('Contour2D.Length',
         439)],
        'first-line': 435,
        'folded-linenos': [],
        'sel-line': 440,
        'sel-line-start': 10720,
        'selection_end': 10720,
        'selection_start': 10720},
                       loc('Lib/KcsStringlist.py'): {'attrib-starts': [('Str'\
        'inglist',
        13),
        ('Stringlist.AddString',
         51)],
        'first-line': 40,
        'folded-linenos': [],
        'sel-line': 52,
        'sel-line-start': 1080,
        'selection_end': 1080,
        'selection_start': 1080},
                       loc('Trigger/trig_draft_init.py'): {'attrib-starts': [],
        'first-line': 0,
        'folded-linenos': [],
        'sel-line': 0,
        'sel-line-start': 0,
        'selection_end': 0,
        'selection_start': 0},
                       loc('kcs_ex_model05.py'): {'attrib-starts': [],
        'first-line': 0,
        'folded-linenos': [],
        'sel-line': 0,
        'sel-line-start': 0,
        'selection_end': 0,
        'selection_start': 0},
                       loc('kcs_ex_struct_test.py'): {'attrib-starts': [('Cr'\
        'eatePoint2DReqStatus',
        36)],
        'first-line': 30,
        'folded-linenos': [],
        'sel-line': 42,
        'sel-line-start': 1150,
        'selection_end': 1182,
        'selection_start': 1182},
                       loc('test.py'): {'attrib-starts': [('A',
        20)],
        'first-line': 13,
        'folded-linenos': [],
        'sel-line': 21,
        'sel-line-start': 494,
        'selection_end': 507,
        'selection_start': 507},
                       loc('wingdbstub.py'): {'attrib-starts': [],
        'first-line': 0,
        'folded-linenos': [],
        'sel-line': 0,
        'sel-line-start': 0,
        'selection_end': 0,
        'selection_start': 0},
                       loc('x-wingide-zip://c:/Python23/Lib/site-packages/simplejson-2.0.9-py2.3.egg//simplejson/encoder.py'): {''\
        'attrib-starts': [('JSONEncoder',
                           69),
                          ('JSONEncoder.default',
                           158)],
        'first-line': 77,
        'folded-linenos': [],
        'sel-line': 176,
        'sel-line-start': 6463,
        'selection_end': 6463,
        'selection_start': 6463}}
proj.build-cmd = {loc('kcs_ex_struct_test.py'): ('project',
        None)}
proj.env-vars = {None: ('default',
                        ['']),
                 loc('kcs_ex_struct_test.py'): ('project',
        ['']),
                 loc('test.py'): ('project',
                                  [''])}
proj.pyexec = {None: ('custom',
                      'c:\\Python23\\python.exe'),
               loc('test.py'): ('custom',
                                'c:\\Python23\\python.exe')}
proj.pypath = {None: ('custom',
                      'c:\\Python23;'),
               loc('test.py'): ('custom',
                                'c:\\Python23;')}
search.search-history = [u'down',
                         u'SPLIT_PLA_MEAS',
                         u'model_capture']
