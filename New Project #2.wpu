#!wing
#!version=6.0
##################################################################
# Wing IDE project file : User-specific branch                   #
##################################################################
[user attributes]
debug.breakpoints = {loc('kcs_ex_struct_test.py'): {26L: (0,
        None,
        1,
        0),
        183L: (0,
               None,
               1,
               0)},
                     loc('test.py'): {19: (0,
        None,
        1,
        0)},
                     loc('untitled-2.py'): {1L: (0,
        None,
        1,
        0),
        3L: (0,
             None,
             1,
             0)}}
debug.err-values = {loc('../../../Program Files (x86)/Wing IDE 6.1/src/testing/runners/run_unittests_xml.py'): {},
                    loc('test.py'): {},
                    loc('untitled-2.py'): {}}
debug.show-args-dialog = {loc('test.py'): False,
                          loc('untitled-2.py'): False}
edit.file-encoding = {loc('untitled-2.py'): 'cp936'}
guimgr.overall-gui-state = {'windowing-policy': 'combined-window',
                            'windows': [{'name': 'Yw7ztSPomdRASSRVcGobFKbLMX'\
        'XNf5sV',
        'size-state': '',
        'type': 'dock',
        'view': {'area': 'tall',
                 'constraint': None,
                 'current_pages': [0,
                                   0],
                 'full-screen': False,
                 'notebook_display': 'normal',
                 'notebook_percent': 0.25,
                 'override_title': None,
                 'pagelist': [('project',
                               'tall',
                               0,
                               {'tree-state': {'file-sort-method': 'by name',
        'list-files-first': False,
        'tree-states': {'deep': {'expanded-nodes': [],
                                 'selected-nodes': [(25,)],
                                 'top-node': (9,)}},
        'tree-style': 'deep'}}),
                              ('browser',
                               'tall',
                               0,
                               {'all_tree_states': {loc('Trigger/trig_draft_init.py'): {'e'\
        'xpanded-nodes': [],
        'selected-nodes': [],
        'top-node': [('function def',
                      loc('Trigger/trig_draft_init.py'),
                      'AddVitesseAddIns')]},
        loc('kcs_ex_struct_test.py'): {'expanded-nodes': [],
                                       'selected-nodes': [[('function def',
        loc('kcs_ex_struct_test.py'),
        'SelectPartReturnModel')]],
                                       'top-node': [('generic attribute',
        loc('kcs_ex_struct_test.py'),
        'CANCEL')]},
        loc('test.py'): {'expanded-nodes': [],
                         'selected-nodes': [],
                         'top-node': None},
        loc('untitled-2.py'): {'expanded-nodes': [],
                               'selected-nodes': [[('generic attribute',
        loc('untitled-2.py'),
        'a')]],
                               'top-node': [('generic attribute',
        loc('untitled-2.py'),
        'a')]},
        loc('wingdbstub.py'): {'expanded-nodes': [],
                               'selected-nodes': [],
                               'top-node': [('generic attribute',
        loc('wingdbstub.py'),
        'actual_winghome')]},
        loc('unknown:untitled-2.py'): {'expanded-nodes': [],
                                       'selected-nodes': [],
                                       'top-node': None},
        loc('unknown:untitled-1.py'): {'expanded-nodes': [],
                                       'selected-nodes': [],
                                       'top-node': None}},
                                'browse_mode': u'Current Module',
                                'follow-selection': False,
                                'sort_mode': 'Alphabetically',
                                'visibility_options': {u'Derived Classes': False,
        u'Imported': False,
        u'Modules': True}}),
                              ('snippets',
                               'tall',
                               0,
                               {'tree-states': {'__all__': [],
        u'c': [],
        u'django': [],
        u'html': [],
        u'py': [(u'ddcomp',
                 u'all',
                 u'')]}}),
                              ('source-assistant',
                               'tall',
                               2,
                               {}),
                              ('debug-stack',
                               'tall',
                               1,
                               {'codeline-mode': 'below'}),
                              ('indent',
                               'tall',
                               2,
                               {})],
                 'primary_view_state': {'area': 'wide',
        'constraint': None,
        'current_pages': [5,
                          0],
        'notebook_display': 'normal',
        'notebook_percent': 0.30000000000000004,
        'override_title': None,
        'pagelist': [('batch-search',
                      'wide',
                      0,
                      {'fScope': {'fFileSetName': 'All Source Files',
                                  'fLocation': None,
                                  'fRecursive': True,
                                  'fType': 'project-files'},
                       'fSearchSpec': {'fEndPos': None,
                                       'fIncludeLinenos': True,
                                       'fInterpretBackslashes': False,
                                       'fMatchCase': False,
                                       'fOmitBinary': True,
                                       'fRegexFlags': 42,
                                       'fReplaceText': '',
                                       'fReverse': False,
                                       'fSearchText': '',
                                       'fStartPos': 0,
                                       'fStyle': 'text',
                                       'fWholeWords': False,
                                       'fWrap': True},
                       'fUIOptions': {'fAutoBackground': True,
                                      'fFilePrefix': 'short-file',
                                      'fFindAfterReplace': True,
                                      'fInSelection': False,
                                      'fIncremental': True,
                                      'fReplaceOnDisk': False,
                                      'fShowFirstMatch': False,
                                      'fShowLineno': True,
                                      'fShowReplaceWidgets': False},
                       'replace-entry-expanded': False,
                       'search-entry-expanded': False}),
                     ('interactive-search',
                      'wide',
                      0,
                      {'fScope': {'fFileSetName': 'All Source Files',
                                  'fLocation': None,
                                  'fRecursive': True,
                                  'fType': 'project-files'},
                       'fSearchSpec': {'fEndPos': None,
                                       'fIncludeLinenos': True,
                                       'fInterpretBackslashes': False,
                                       'fMatchCase': False,
                                       'fOmitBinary': True,
                                       'fRegexFlags': 42,
                                       'fReplaceText': '',
                                       'fReverse': False,
                                       'fSearchText': u'debugger',
                                       'fStartPos': 0,
                                       'fStyle': 'text',
                                       'fWholeWords': False,
                                       'fWrap': True},
                       'fUIOptions': {'fAutoBackground': True,
                                      'fFilePrefix': 'short-file',
                                      'fFindAfterReplace': True,
                                      'fInSelection': False,
                                      'fIncremental': True,
                                      'fReplaceOnDisk': False,
                                      'fShowFirstMatch': False,
                                      'fShowLineno': True,
                                      'fShowReplaceWidgets': False}}),
                     ('debug-data',
                      'wide',
                      0,
                      {}),
                     ('debug-exceptions',
                      'wide',
                      0,
                      {}),
                     ('debug-breakpoints',
                      'wide',
                      0,
                      {'tree-state': []}),
                     ('testing',
                      'wide',
                      0,
                      {'added-files': [loc('test.py')],
                       'filter': u'',
                       'recent-filters': None,
                       'sort-order': 'alpha',
                       'tree-state': {'expanded-nodes': [(0,)],
                                      'selected-nodes': [(0,
        0)],
                                      'top-node': (0,)}}),
                     ('uses',
                      'wide',
                      0,
                      {}),
                     ('debug-io',
                      'wide',
                      1,
                      {}),
                     ('debug-watch',
                      'wide',
                      1,
                      {'node-states': [('eval',
        'wingdbstub'),
                                       ('eval',
        u'a')],
                       'tree-state': {'expanded-nodes': [],
                                      'selected-nodes': [(1,)],
                                      'top-node': (0,)}}),
                     ('debug-modules',
                      'wide',
                      1,
                      {}),
                     ('python-shell',
                      'wide',
                      2,
                      {'active-range': (None,
        -1,
        -1),
                       'attrib-starts': [],
                       'first-line': 0L,
                       'folded-linenos': [],
                       'history': {},
                       'launch-id': None,
                       'sel-line': 2L,
                       'sel-line-start': 115L,
                       'selection_end': 147L,
                       'selection_start': 147L,
                       'zoom': 0L}),
                     ('bookmarks',
                      'wide',
                      1,
                      {}),
                     ('messages',
                      'wide',
                      2,
                      {'current-domain': 0}),
                     ('os-command',
                      'wide',
                      1,
                      {'last-percent': 0.8,
                       'toolbox-percent': 1.0,
                       'toolbox-tree-sel': 'cmd-DIQRgVJaranAGbXU'})],
        'primary_view_state': {'editor_states': ({'bookmarks': ([[loc('kcs_ex_struct_test.py'),
        {'attrib-starts': [('run',
                            178)],
         'first-line': 177L,
         'folded-linenos': [],
         'sel-line': 199L,
         'sel-line-start': 6999L,
         'selection_end': 7024L,
         'selection_start': 7024L,
         'zoom': 0L},
        1735570898.287],
        [loc('kcs_ex_struct_test.py'),
         {'attrib-starts': [],
          'first-line': 12L,
          'folded-linenos': [],
          'sel-line': 32L,
          'sel-line-start': 836L,
          'selection_end': 862L,
          'selection_start': 862L,
          'zoom': 0L},
         1735571207.586],
        [loc('test.py'),
         {'attrib-starts': [],
          'first-line': 6L,
          'folded-linenos': [],
          'sel-line': 17L,
          'sel-line-start': 466L,
          'selection_end': 478L,
          'selection_start': 478L,
          'zoom': 0L},
         1735571672.367],
        (loc('kcs_ex_struct_test.py'),
         {'attrib-starts': [],
          'first-line': 12L,
          'folded-linenos': [],
          'sel-line': 32L,
          'sel-line-start': 836L,
          'selection_end': 862L,
          'selection_start': 862L,
          'zoom': 0L},
         1735571673.04),
        (loc('test.py'),
         {'attrib-starts': [],
          'first-line': 6L,
          'folded-linenos': [],
          'sel-line': 17L,
          'sel-line-start': 466L,
          'selection_end': 478L,
          'selection_start': 478L,
          'zoom': 0L},
         1735571685.434),
        (loc('test.py'),
         {'attrib-starts': [],
          'first-line': 1L,
          'folded-linenos': [],
          'sel-line': 17L,
          'sel-line-start': 466L,
          'selection_end': 478L,
          'selection_start': 478L,
          'zoom': 0L},
         1735571687.372),
        [loc('test.py'),
         {'attrib-starts': [],
          'first-line': 1L,
          'folded-linenos': [],
          'sel-line': 10L,
          'sel-line-start': 214L,
          'selection_end': 262L,
          'selection_start': 262L,
          'zoom': 0L},
         1735572635.341],
        [loc('unknown:untitled-1.py'),
         {'attrib-starts': [],
          'first-line': 0L,
          'folded-linenos': [],
          'sel-line': 0L,
          'sel-line-start': 0L,
          'selection_end': 0L,
          'selection_start': 0L,
          'zoom': 0L},
         1735572636.803],
        [loc('untitled-2.py'),
         {'attrib-starts': [],
          'first-line': 0L,
          'folded-linenos': [],
          'sel-line': 0L,
          'sel-line-start': 0L,
          'selection_end': 7L,
          'selection_start': 7L,
          'zoom': 0L},
         1735572663.264],
        [loc('untitled-2.py'),
         {'attrib-starts': [],
          'first-line': 0L,
          'folded-linenos': [],
          'sel-line': 2L,
          'sel-line-start': 11L,
          'selection_end': 18L,
          'selection_start': 18L,
          'zoom': 0L},
         1735572730.908],
        [loc('Trigger/trig_draft_init.py'),
         {'attrib-starts': [],
          'first-line': 0L,
          'folded-linenos': [],
          'sel-line': 0L,
          'sel-line-start': 0L,
          'selection_end': 0L,
          'selection_start': 0L,
          'zoom': 0L},
         1735572730.913],
        [loc('Trigger/trig_draft_init.py'),
         {'attrib-starts': [('AddVitesseAddIns',
                             54)],
          'first-line': 44L,
          'folded-linenos': [],
          'sel-line': 56L,
          'sel-line-start': 1607L,
          'selection_end': 1657L,
          'selection_start': 1656L,
          'zoom': 0L},
         1735572731.165],
        [loc('Trigger/trig_draft_init.py'),
         {'attrib-starts': [('AddVitesseAddIns',
                             54)],
          'first-line': 44L,
          'folded-linenos': [],
          'sel-line': 56L,
          'sel-line-start': 1607L,
          'selection_end': 1673L,
          'selection_start': 1672L,
          'zoom': 0L},
         1735572736.751],
        [loc('untitled-2.py'),
         {'attrib-starts': [],
          'first-line': 0L,
          'folded-linenos': [],
          'sel-line': 2L,
          'sel-line-start': 11L,
          'selection_end': 18L,
          'selection_start': 18L,
          'zoom': 0L},
         1735572736.754],
        [loc('untitled-2.py'),
         {'attrib-starts': [],
          'first-line': 0L,
          'folded-linenos': [],
          'sel-line': 0L,
          'sel-line-start': 0L,
          'selection_end': 1L,
          'selection_start': 0L,
          'zoom': 0L},
         1735572737.516],
        [loc('untitled-2.py'),
         {'attrib-starts': [],
          'first-line': 0L,
          'folded-linenos': [],
          'sel-line': 2L,
          'sel-line-start': 11L,
          'selection_end': 18L,
          'selection_start': 18L,
          'zoom': 0L},
         1735572769.257],
        [loc('untitled-2.py'),
         {'attrib-starts': [],
          'first-line': 0L,
          'folded-linenos': [],
          'sel-line': 2L,
          'sel-line-start': 11L,
          'selection_end': 18L,
          'selection_start': 17L,
          'zoom': 0L},
         1735573145.209],
        [loc('test.py'),
         {'attrib-starts': [],
          'first-line': 1L,
          'folded-linenos': [],
          'sel-line': 10L,
          'sel-line-start': 214L,
          'selection_end': 262L,
          'selection_start': 262L,
          'zoom': 0L},
         1735573145.221],
        [loc('test.py'),
         {'attrib-starts': [],
          'first-line': 1L,
          'folded-linenos': [],
          'sel-line': 12L,
          'sel-line-start': 336L,
          'selection_end': 336L,
          'selection_start': 336L,
          'zoom': 0L},
         1735573170.803],
        [loc('kcs_ex_struct_test.py'),
         {'attrib-starts': [],
          'first-line': 6L,
          'folded-linenos': [],
          'sel-line': 32L,
          'sel-line-start': 836L,
          'selection_end': 862L,
          'selection_start': 862L,
          'zoom': 0L},
         1735573174.962]],
        20),
        'current-loc': loc('test.py'),
        'editor-state-list': [(loc('kcs_ex_struct_test.py'),
                               {'attrib-starts': [],
                                'first-line': 6L,
                                'folded-linenos': [],
                                'sel-line': 32L,
                                'sel-line-start': 836L,
                                'selection_end': 862L,
                                'selection_start': 862L,
                                'zoom': 0L}),
                              (loc('test.py'),
                               {'attrib-starts': [],
                                'first-line': 6L,
                                'folded-linenos': [],
                                'sel-line': 27L,
                                'sel-line-start': 526L,
                                'selection_end': 537L,
                                'selection_start': 537L,
                                'zoom': 0L}),
                              (loc('untitled-2.py'),
                               {'attrib-starts': [],
                                'first-line': 0L,
                                'folded-linenos': [],
                                'sel-line': 2L,
                                'sel-line-start': 11L,
                                'selection_end': 18L,
                                'selection_start': 17L,
                                'zoom': 0L}),
                              (loc('wingdbstub.py'),
                               {'attrib-starts': [],
                                'first-line': 248L,
                                'folded-linenos': [],
                                'sel-line': 248L,
                                'sel-line-start': 10074L,
                                'selection_end': 10074L,
                                'selection_start': 10074L,
                                'zoom': 0L})],
        'has-focus': False,
        'locked': False},
        [loc('kcs_ex_struct_test.py'),
         loc('test.py'),
         loc('unknown:untitled-1.py'),
         loc('untitled-2.py'),
         loc('wingdbstub.py')]),
                               'open_files': [u'wingdbstub.py',
        u'untitled-2.py',
        u'kcs_ex_struct_test.py',
        u'test.py']},
        'saved_notebook_display': None,
        'split_percents': {0: 0.5},
        'splits': 2,
        'tab_location': 'top',
        'user_data': {}},
                 'saved_notebook_display': None,
                 'split_percents': {0: 0.5},
                 'splits': 2,
                 'tab_location': 'left',
                 'user_data': {}},
        'window-alloc': (0,
                         -1,
                         1911,
                         1020)}]}
guimgr.recent-documents = [loc('test.py'),
                           loc('kcs_ex_struct_test.py'),
                           loc('untitled-2.py'),
                           loc('unknown:untitled-1.py'),
                           loc('wingdbstub.py')]
proj.build-cmd = {None: ('default',
                         None)}
proj.env-vars = {None: ('default',
                        [u''])}
proj.pyexec = {None: ('custom',
                      u'c:\\Python27\\python.exe')}
proj.pypath = {None: ('custom',
                      [u'c:\\Python27'])}
proj.vcs-system-config = ('prefs',
                          {'bzr': {'versioncontrol.bzr.active': 'active-if-p'\
        'roject-dir',
                                   'versioncontrol.bzr.executable': u'bzr'},
                           'cvs': {'versioncontrol.cvs.active': 'active-if-p'\
        'roject-dir',
                                   'versioncontrol.cvs.executable': u'cvs',
                                   'versioncontrol.cvs.extra-global-args': '-'\
        'z3'},
                           'git': {'versioncontrol.git.active': 'active-if-p'\
        'roject-dir',
                                   'versioncontrol.git.executable': u'git',
                                   'versioncontrol.git.use-porcelain': True},
                           'hg': {'versioncontrol.hg.active': 'active-if-pro'\
        'ject-dir',
                                  'versioncontrol.hg.dont-find-unregistered': True,
                                  'versioncontrol.hg.executable': u'hg',
                                  'versioncontrol.hg.extra-global-args': '--'\
        'encoding=utf8'},
                           'perforce': {'versioncontrol.perforce.active': 'n'\
        'ot-active',
        'versioncontrol.perforce.dont-find-unregistered': True,
        'versioncontrol.perforce.executable': u'p4',
        'versioncontrol.perforce.extra-global-args': ''},
                           'svn': {'versioncontrol.svn.active': 'active-if-p'\
        'roject-dir',
                                   'versioncontrol.svn.executable': u'svn',
                                   'versioncontrol.svn.extra-global-args': '',
                                   'versioncontrol.svn.svnadmin-executable': u'svnadmin'}})
testing.stored-results = (1,
                          [],
                          {})
