#
#      NAME:
#
#          trig_cable_cway_comp_intermediate.py
#
#      PURPOSE:
#
#          Vitesse trigger for checking names of cable way components. Implemented as
#          Post- and Pre- triggers which will be called automatically when components
#          for straight parts are selected by user.
#          To activate the name checking, the following must be set up:
#          SBB_TRIGDIR:
#                Must point to the directory where triggers are located
#          kcs_ex_trig_cable_cway_comp_intermediate.py:
#                Rename to trig_cable_cway_comp_intermediate.py
#          kcs_ex_cway_09_rulesfile.py:
#                Naming rules
#          kcs_ex_cway_09_preferences.py:
#                Name and location of rules file
#                (Def. of type code group for structure comp.)
#                (Rule names)
#

import kcs_util
import kcs_ui
import kcs_ex_cway_09_compcheck


#-----------------------------------------------------------------------------
def pre(*args):                             # pre trigger definition

#
#  Convert arguments
#
    arguments = []
    for argument in args:
        arguments.append(argument)

#
#  Check names
#
    if kcs_ex_cway_09_compcheck.CompCheck(arguments[0]) == 0 and \
       kcs_ex_cway_09_compcheck.CompCheck(arguments[1]) == 0:
       return kcs_util.trigger_ok()
    else:
       return kcs_util.trigger_abort()

#----------------------------------------------------------------------------
def post(*args):                            # post trigger definition
    result = kcs_util.trigger_ok()          # ok value
    return result;
