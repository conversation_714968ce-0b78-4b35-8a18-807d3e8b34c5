import kcs_att
import kcs_ui
import kcs_util
import KcsModel
import kcs_att_SYSTEM

def pre(*args):
#
# Create a list for model classes.
#
   list = []

#
# Prompt user for filter value
#
   res = kcs_ui.string_req("System attribute value to filter for:", "")
   if res[0] == kcs_util.ok():
      text = res[1]

#
# For each model object - iterate through all attributes.
#
   for i in range(len(args[0])):
      found = 0
      att = kcs_att.attribute_first_get(args[0][i])
      while att and not found:
#
# Check if user defined attribute is of requested type...
#
         found = kcs_att.attribute_is(att,kcs_att_SYSTEM.name_SYSTEM_SYSTEM_PURPOSE())
         if not found:
            att = kcs_att.attribute_next_get()

#
# ...if it is, check if its first value corresponds to the value entered - if so append to result
#
      if found:
         txt = kcs_att.string_get(att,0)
         if txt == text:
            list.append(args[0][i])
#
# Create the return list.
#
   result = []
#
# First, append the proper return code.
#
   result.append(kcs_util.trigger_ok())
#
# Then, append the list of Model classes.
#
   result.append(list)
   return result
