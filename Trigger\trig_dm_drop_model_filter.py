import kcs_util
import KcsModel

# The Pre trigger is executed for the node from where a drag & drop operation starts.
# The Post trigger is executed for each node included in a drag & drop operation.
#

def pre(*args):
#
#  Argument
#      - object (not valid for nodes repesenting other than model objects e.g. "Drawings", "References" etc.) 	
#      - parentObject (in case "object" not is a named model object but a folder like e.g. "plane panels" etc.)
#      - filterName
#
#      I.e. the number of arguments can vary between 1 and 3.
#
#  Return
#      - OK/abort
#      - newFilterName (optional, makes it possible to change current filer name)
#
#      I.e the number of return values can vary between 1 and 2
#
#
#
   if len(args)==3:
      object = args[0]
      parentObject = args[1]
      filterName = args[2]
      print object
      print parentObject
      print filterName
   elif len(args)==2:
      object = args[0]
      filterName = args[1]
      print object
      print filterName
   else:
      filterName = args[0]
      print filterName
#
#  Here you can add customized functions, widgets etc and e.g. store away in a file information
#  extracted based on "object" ("parentObject" and "filterName")
#
#   newFilterName = "NEWCURRFILT"
   result = []
   result.append(kcs_util.trigger_ok())
#   result.append(newFilterName)
   return result

def post(*args):
#
#  Argument
#      - object
#      - filterName
#
#  Return
#      - OK/abort
#
   object = args[0]
   filterName=args[1]
   print object
#
#  Here you can add customized functions for matching of "object" with information stored in a
#  file generated by the pre part above and/or in a file representing current filter selected by the
#  trigger connected to the Filter Active function.
#
   result = kcs_util.trigger_ok()
   return result
