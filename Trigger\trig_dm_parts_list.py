import kcs_util
import shelve
import KcsModel

#-------------------------------------------------------------------------------------------------------------
def pre(*args):                             # pre trigger definition
    d = shelve.open("c:\\temp\\partslist")
    i = 0
    for tbm in args:
        i = i + 1
        key = "Mod"+str(i)
        d[key]= tbm    
    d.close

#-------------------------------------------------------------------------------------------------------------
def post(*args):                            # post trigger definition
    d = shelve.open("c:\\temp\\partslist")
    for key in d.keys():
        tbm = d[key]
        print tbm    
    d.close
    kcs_util.exit_program()    

## Start of main body
if __name__ == '__main__':
    post() 
