
import kcs_dex
import kcs_util
import types

#---------------------------------------------------------------------------------
#
#      NAME:
#
#          aadAssDataExtraction.py
#
#      PURPOSE:
#
#               This file contains class which used for Data Extraction
#
#
#
#---------------------------------------------------------------------------------

class DataExtraction:


    #----------------------------------------------------------------------
    #  method
    #           __init__
    #  purpose
    #           constructor
    #  parameter
    #           string   statements of Data Extraction
    #  output
    #           none
    #----------------------------------------------------------------------
    def __init__(self, inStat=''):
       #--------------------------------------------------------------
       # Data Extraction
       #----------------
       # DataNumer  : number of output
       # DataType : type of output
       # DataResult : list of output
       #--------------------------------------------------------------
       self.DataNumber=0
       self.Datatype=0
       self.DataResult=[]
       self.DataStat=inStat
       if len(inStat)>0:
           self.ExtractData(self.DataStat)


    #---------------------------------------------------------------------
    #  method
    #             _repr_
    #  Purpose
    #             print the the result of data extraction
    #  Parameter
    #             none
    #----------------------------------------------------------------------


    def __repr__(self):

        resStr = 'Statement= ' + self.DataStat + '\n' +\
        'Number of data extracted = ' + str(self.DataNumber) + '\n' +\
        'Result type = ' + str(self.DataType) + '\n'

        for i in range(len(self.DataResult)):
            resStr = resStr + '\t' + str(self.DataResult[i]) + '\n'

        return resStr



    #----------------------------------------------------------------------
    # method
    #           ExtractData
    # Purpose
    #           Data extraction with kcs_dex
    # Parameter
    #      1)String   statment for extraction
    #----------------------------------------------------------------------

    def ExtractData(self,Stat):

        self.DataNumber=0
        self.DataType=0
        self.DataResult=[]
        self.DataStat=Stat


        try:
           res = kcs_dex.extract(self.DataStat)
           if res == kcs_util.success():
              type=1
              while (type>=0):
                  type = kcs_dex.next_result()
                  if type == 1:
                     self.DataNumber=self.DataNumber+1
                     self.DataResult.append(kcs_dex.get_int())
                  elif type == 2:
                     self.DataNumber=self.DataNumber+1
                     self.DataResult.append(kcs_dex.get_real())
                  elif type == 3:
                     self.DataResult.append(kcs_dex.get_string())
                     self.DataNumber=self.DataNumber+1
                  elif type == 4:
                     self.DataResult.append(kcs_dex.get_reavec3d())
                     self.DataNumber=self.DataNumber+1
                  elif type == 5:
                     self.DataResult.append(kcs_dex.get_box())
                     self.DataNumber=self.DataNumber+1
                  elif type == 6:
                     self.DataResult.append(kcs_dex.get_reavec2d())
                     self.DataNumber=self.DataNumber+1
                  elif type >10:
                     for j in range(type-10):
                       self.DataResult.append(kcs_dex.get_indexedreal(j))
                       self.DataNumber=self.DataNumber+1
                  if type>0:
                     self.DataType=type

        except:
           print 'Extract ' + str(res)
           print 'Statement=', self.DataStat
           print 'Data Extraction Error....', kcs_dex.error

    def ExtractDataFromDexInt(self, Stat, dex):
        self.DataNumber=0
        self.DataType=0
        self.DataResult=[]
        self.DataStat=Stat
        try:
           dex.DoDataExtraction(Stat)
           while 1:
              value = dex.GetValue()
              if type(value) == type(False) and value==False:
                 break
              if value != None:
                 self.DataNumber=self.DataNumber+1
                 self.DataResult.append(value)
        except Exception,e:
           print 'Statement=', self.DataStat
           print 'Data Extraction Error....', str(e)
