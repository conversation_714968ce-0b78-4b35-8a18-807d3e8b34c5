
import kcs_ui
import kcs_util
import kcs_draft
import KcsAssembly
import KcsLinetype
import string
import KcsPoint2D
import KcsContour2D
import aadAssUtil
import KcsModel
import aadAssExtractData
import aadAssLblPositioner
import aadAssemblyModel



#================================================================================================#
# Name :
#       aadAssDrawing.py
#
#
# Purpose :
#       This program serves as a class for holding the data about the drawings
#
#
# This File Contains the Class That holds the Drawing Related Data and Has  Function to Deal with #
#                             Drawing                                                            #
#================================================================================================#

DATABANK = 'SB_ASSINSTR'
PROJ=0
RPARENT=1
CURRENT=2
DESCRIPTION=3
PLANNING_UNIT=4
TYPE=5
WORK_LOCATION=6
DESTINATION=7
PLAN_START_DATE=8
PLAN_END_DATE=9
PLAN_DURATION=10
DWG_FRM_TEMPLATE="ASSY01"
NLABEL=25 # Constant That Determines the no. of Labels to be Displayed in a Drawing
COST_CODE=13


class CDwg:
    def __init__(self,name,job,modlist,AssData,viewname,res_name,viewlist,vent=0):
        self.Name=name # Name of the Drawing
        self.Job=job    #job Description
        self.mod_list=modlist # Modellist
        self.vent=vent            # A Flag to Differentiate between vent and pipe
        self.SubDwgLst=[]        # List for storing the names of the Sub Drawings
        self.view_name=viewname # Name of the View
        self.res_name=res_name # Name of the View Area Restriction Defined.
        self.AssData=[]        # Single field data list
        self.PartsList=[]    # Parts List
        self.LblList=[] # List for position numbers of the label
        self.viewlist=viewlist # List of View Objects.
        self.__InitData(AssData)







# Function Which Initializes the List Holding the Single Value Fields #
#=====================================================================#
    def __InitData(self,AssData):
        date=aadAssUtil.GetDate()
        for cnt in range(201,219,1):
            if cnt in (203,205):
                continue
            elif cnt==215:
                self.AssData.append([cnt,str(self.Name)])
            elif cnt==216:
                self.AssData.append([cnt,str(date)])
            else:
                self.AssData.append([cnt,""])
        self.AssData[0][1]=AssData[PROJ] # rule 201
        self.AssData[1][1]=AssData[CURRENT] # rule 202
        self.AssData[2][1]=AssData[RPARENT] #  rule 204
        self.AssData[3][1]=AssData[DESCRIPTION]  #rule 206
        self.AssData[4][1]=AssData[CURRENT]+"-"+str(self.Name[3]) # rule 207
        self.AssData[5][1]=self.Job #rule 208
        self.AssData[6][1]=AssData[PLANNING_UNIT]   #rule 209
        self.AssData[7][1]=AssData[WORK_LOCATION]# rule 210
        self.AssData[8][1]=AssData[DESTINATION]#rule 211
        self.AssData[9][1]=AssData[PLAN_START_DATE]    #rule 212
        self.AssData[10][1]=AssData[PLAN_END_DATE]    #rule 213
        self.AssData[11][1]=AssData[PLAN_DURATION]#rule 214
        self.AssData[14][1]=AssData[TYPE]#rule 217
        self.AssData[15][1]=AssData[COST_CODE]#rule 218
        self.__CreateSubDwgLst() # Creates Sub Drawings
        if len(self.mod_list)<=0:
            self.FillFields()






# Function Creates a new Drawing . If Drawing with same name exists then is deleted #
#===================================================================================#
    def DwgNew(self,flag=0):
        try:
            if kcs_draft.dwg_exist(self.Name, DATABANK):
               kcs_draft.dwg_delete(self.Name, DATABANK)
        except Exception,e:
            print e

        if flag==0:
            try:
                kcs_draft.dwg_save_as(self.Name, DATABANK)
            except:
                print "Module : DwgNew :"
                print kcs_draft.error
        else:
            try:
                if kcs_draft.dwg_current():
                    kcs_draft.dwg_save_as(self.Name, DATABANK)
                else:
                    kcs_draft.dwg_new(self.Name,DWG_FRM_TEMPLATE, DATABANK)
                    kcs_draft.dwg_save_as(self.Name, DATABANK)
                    #kcs_draft.dwg_save_as(self.Name, DATABANK)
            except:
                print "Module : DwgNew :"
                print kcs_draft.error




# Function that opens an Existing Drawing  #
#===================================================================================#
    def DwgOpen(self):
        try:
            kcs_draft.dwg_open(self.Name, DATABANK)
        except:
            print "Module :DwgOpen "+str(kcs_draft.error)

# Function that Saves the current Drawing #
# ===========================================#

    def DwgSave(self):
        try:
            kcs_draft.dwg_save_as(self.Name,DATABANK)
        except:
            print "Module : DwgSave "+str(kcs_draft.error)



# Function That Closes the Current Drawing #
# ===========================================#
    def DwgClose(self):
        try:
            kcs_draft.dwg_close()
        except:
            print "Module DwgClose "+str(kcs_draft.error)




# Function Used to Fill Data in Single Fields in the Drawing #
#============================================================#
    def FillFields(self):
        for cnt in range(len(self.AssData)):
            text=str(self.AssData[cnt][1])
            rule=self.AssData[cnt][0]
            try:
                if len(text)>0:
                    kcs_draft.rule_text_new(text,rule)
            except:
                print "Module :FillFields "+str(kcs_draft.error)




# Function that Fills Parts List #
#================================#
    def FillPartsList(self):
        if len(self.PartsList)<=0:
            try:
                self.__ExtractData()
            except Exception,e:
                print " aadAssDrawing, FillPartsList: %s"%e
        if len(self.PartsList)<=0:
            return
        lblpos=aadAssLblPositioner.LblPositioner(self.LblList,self.view_name,self.res_name)
        ind_list=lblpos.DrawLabels()
        self.__PopulatePartsList(ind_list)
        kcs_draft.dwg_save()#_as(self.Name, DATABANK)
        kcs_draft.dwg_close()
        for obj in self.SubDwgLst:
            kcs_draft.dwg_open(obj, DATABANK)
            ind_list=lblpos.DrawLabels()
            self.__PopulatePartsList(ind_list)
            kcs_draft.dwg_save()#_as(obj, DATABANK)
            kcs_draft.dwg_close()
        kcs_draft.dwg_open(self.Name, DATABANK)



# Function That Highlights the Models in the Model list #
#=======================================================#
    def HighlightModel(self):
        self.ChangeLineType(self.viewlist,self.mod_list)


# Function Extracts Data for all the models that exist #
#======================================================#
    def __ExtractData(self,flag=0):
        type=""
        lastmodel=None
        if flag==0:
            if len(self.mod_list)>0:
                for mod in self.mod_list:
                    type=mod.GetType()
                    if type=="struct":
                        if lastmodel==None:
                            lastmodel=mod
                            self.__SetStructData(lastmodel)
                        elif lastmodel and (lastmodel.GetName()<>mod.GetName() or lastmodel.GetPartId()<>mod.GetPartId()):
                            lastmodel=mod
                            self.__SetStructData(lastmodel)
                    elif type=="pipe" and self.vent==0:
                        self.__SetPipeData(mod)
                    elif type=="pipe spool" and self.vent==0:
                        self.__SetPipSpoolData(mod)
                    elif type=="pipe" and self.vent==1:
                        self.__SetVentData(mod)
                    elif type=="ventilation" and self.vent==1:
                        if mod.GetPartId()>0:
                            self.__SetVentSpoolData(mod)
                        else:
                            self.__SetVentData(mod)
                    elif type=="vent spool":
                        self.__SetVentSpoolData(mod)
                    elif type=="pipe spool" and self.vent==1:
                        self.__SetVentSpoolData(mod)
                    elif type=="equipment":
                        self.__SetEquipmentData(mod)
                    elif type=="cable way":
                        self.__SetCableData(mod)
                    #elif type=="penetration":
                    #    self.__SetPntrData(mod)
                    elif type=="curved panel":
                        self.__SetCurvedPanelData(mod)
                    elif type=="plane panel":
                        self.__SetPlanePanelData(mod)

                self.__SetIndexPartsList()
        else:
            if len(self.mod_list)>0:
                for mod in self.mod_list:
                    self.__SetKIData(mod)




# Creates the List of names of the Sub Drawings #
#===============================================#
    def __CreateSubDwgLst(self):
        nSubDwg=0
        lastmodel=None
        structlist=[]
        nModels=0
        model=None
        if len(self.mod_list)>0:
            model=self.mod_list[0]
        else:
            return 0
        if isinstance(model,KcsModel.Model) and model and model.GetType()=="struct":
           for mod in self.mod_list:
               if lastmodel==None:
                  lastmodel=mod
                  structlist.append(mod)
               elif lastmodel and lastmodel.GetName()!=mod.GetName():
                  lastmodel=mod
                  structlist.append(mod)
           nModels=len(structlist)-NLABEL
           nSubDwg=(nModels/NLABEL)
        else:
           nModels=len(self.mod_list)-NLABEL
           nSubDwg=(nModels/NLABEL)
        if (nModels % NLABEL)>0:
            nSubDwg=nSubDwg+1
        if nSubDwg>0:
            for cnt in range(1,nSubDwg+1,1):
                self.SubDwgLst.append(self.Name+"_"+str(cnt))
            return 1
        else:
            return 0



# Function Sets the Indexes for the Parts #
#=========================================#
    def __SetIndexPartsList(self):
        if len(self.PartsList)>0:
            for i in range(len(self.PartsList)):
                self.PartsList[i][0]=i
                self.LblList[i][0]=i







#  Function Sets Data for Structures      #
#=========================================#
    def __SetStructData(self,mod):
        lst=[]
        vid=""
        aadAssExtractData.GetData(mod.GetType(),mod.GetName(),lst, mod.GetPartId())
        for cnt in lst:
            vid=kcs_draft.view_identify(self.view_name)
            point=KcsPoint2D.Point2D()
            pp=kcs_draft.point_transform(vid,cnt[1],point)
            self.LblList.append([0,point,0])
            if mod.GetPartId() <> 0:
                self.PartsList.append([0,"STRUCTURE PART",cnt[0],cnt[2],cnt[3],cnt[4]])
            else:
                self.PartsList.append([0,"STRUCTURE",cnt[0],cnt[2],cnt[3],cnt[4]])




#    Function Sets Data for Pipe Parts     #
#=========================================#
    def __SetPipeData(self,mod):
        lst=[]
        aadAssExtractData.GetData(mod.GetType(),mod.GetName(),lst,mod.PartId)
        for cnt in lst:
            vid=kcs_draft.view_identify(self.view_name)
            point=KcsPoint2D.Point2D()
            pp=kcs_draft.point_transform(vid,cnt[1],point)
            self.LblList.append([0,point,0])
            self.PartsList.append([0,"PIPE",cnt[0],cnt[2],cnt[3],cnt[4]])

# Function that sets the Pipe Spool Data #
#=========================================#
    def __SetPipSpoolData(self,mod):
        lst=[]
        aadAssExtractData.GetData(mod.GetType(),mod.GetName(),lst, mod.PartId)
        for cnt in lst:
            try:
                vid=kcs_draft.view_identify(self.view_name)
                point=KcsPoint2D.Point2D()
                pp=kcs_draft.point_transform(vid,cnt[1],point)
                self.LblList.append([0,point,0])
                self.PartsList.append([0,"PIPE SPOOL",cnt[0],cnt[2],cnt[3],cnt[4]])
            except Exception,e:
                print e


# Function that sets the Equipment Data #
#=========================================#
    def __SetEquipmentData(self,mod):
        lst=[]
        ViewValid = 0
        aadAssExtractData.GetData(mod.GetType(),mod.GetName(),lst)
        try:
            for cnt in lst:
                try:
                    vid=kcs_draft.view_identify(self.view_name)
                    point=KcsPoint2D.Point2D()
                    pp=kcs_draft.point_transform(vid,cnt[1],point)
                    ViewValid = 1
                except TBException,e:
                   print "aadAssDrawing: ", e
                   ViewValid = 0

                if (cnt[1].X==0 and cnt[1].Y==0 and cnt[1].Z==0):
                   ppoint=KcsPoint2D.Point2D()
                   self.__GetScreenPoint(mod,ppoint)
                   if (ppoint.X!=0 and ppoint.Y!=0) or not ViewValid:
                       self.LblList.append([0,ppoint,0])
                       self.PartsList.append([0,"EQUIPMENT",cnt[0],cnt[2],cnt[3],cnt[4]])
                   else:
                       self.LblList.append([0,None,0])
                       self.PartsList.append([0,"EQUIPMENT",cnt[0],cnt[2],cnt[3],"-1"])


                else:
                   self.LblList.append([0,point,0])
                   self.PartsList.append([0,"EQUIPMENT",cnt[0],cnt[2],cnt[3],cnt[4]])
        except Exception,f:
            print "aadAssDrawing: ", e
        except:
            print "aadAssDrawing: Fatal Error!"


# Function that sets the cable data       #
#=========================================#
    def __SetCableData(self,mod):
        lst=[]
        aadAssExtractData.GetData(mod.GetType(),mod.GetName(),lst,mod.PartId)
        for cnt in lst:
            vid=kcs_draft.view_identify(self.view_name)
            point=KcsPoint2D.Point2D()
            pp=kcs_draft.point_transform(vid,cnt[1],point)
            self.LblList.append([0,point,0])
            self.PartsList.append([0,"CABLETRAY",cnt[0],cnt[2],cnt[3],cnt[4]])


# Function that sets the cable penetration data       #
#=========================================#
    def __SetPntrData(self,mod):
        lst=[]
        aadAssExtractData.GetData(mod.GetType(),mod.GetName(),lst,mod.PartId)
        for cnt in lst:
            vid=kcs_draft.view_identify(self.view_name)
            point=KcsPoint2D.Point2D()
            pp=kcs_draft.point_transform(vid,cnt[1],point)
            self.LblList.append([0,point,0])
            self.PartsList.append([0,"PENETRATION",cnt[0],cnt[2],cnt[3],cnt[4]])


# Function that sets the Curved Panel Data #
#==========================================#
    def __SetCurvedPanelData(self,mod):
        lst=[]
        obj_lst=""
        handle=""
        phandle=""
        segment=""
        point=""
        aadAssExtractData.GetData(mod.GetType(),mod.GetName(),lst,mod.PartId)
        if len(lst)>0:
           obj_lst=kcs_draft.model_handle_get(mod)
           if len(obj_lst)>0:
              for obj in obj_lst:
                  name=kcs_draft.subpicture_name_get(obj)
                  phandle=kcs_draft.element_parent_get(obj)
                  phandle=kcs_draft.element_parent_get(phandle)
                  name=kcs_draft.subpicture_name_get(phandle)
                  if string.upper(name)==string.upper(self.view_name):
                     break
              try:
                  handle=kcs_draft.element_child_first_get(obj)
              except:
                  print "Module : __SetCurvedPanelData \n Error : "+str(kcs_draft.error)
              while 1:
                   try:
                       if kcs_draft.element_is_contour(handle):
                            pp=KcsPoint2D.Point2D()
                            cont=KcsContour2D.Contour2D(pp)
                            cont=kcs_draft.contour_properties_get(handle,cont)
                            segment=cont.Contour[0]
                            point=segment[0]
                            break
                       handle=kcs_draft.element_sibling_next_get(handle)
                   except:
                       break
        self.LblList.append([0,point,0])
        self.PartsList.append([0,lst[0][5],lst[0][0],lst[0][2],lst[0][3],lst[0][4]])



# Function that sets the Plane Panel Data #
#=========================================#

    def __SetPlanePanelData(self,mod):
        lst=[]
        aadAssExtractData.GetHullData(mod,lst)
        for cnt in lst:
            vid=kcs_draft.view_identify(self.view_name)
            point=KcsPoint2D.Point2D()
            if cnt[1] <> None:
                try:
                    pp=kcs_draft.point_transform(vid,cnt[1],point)
                except TBException, e:
                    print e
                self.LblList.append([0,point,0])
            else:
                self.LblList.append([0,None,0])
            self.PartsList.append([0,cnt[len(cnt)-1],cnt[0],cnt[2],cnt[3],cnt[4]])


# Function that Sets the Vent Part Data #
#========================================#
    def __SetVentData(self,mod):
        lst=[]
        if mod.PartId>0:
            aadAssExtractData.GetData("vent",mod.GetName(),lst, -mod.PartId)
        else:
            aadAssExtractData.GetData("vent",mod.GetName(),lst, mod.PartId)
        for cnt in lst:
            vid=kcs_draft.view_identify(self.view_name)
            point=KcsPoint2D.Point2D()
            pp=kcs_draft.point_transform(vid,cnt[1],point)
            self.LblList.append([0,point,0])
            self.PartsList.append([0,"VENT",cnt[0],cnt[2],cnt[3],cnt[4]])



# Function that sets the Vent Spool Data #
#========================================#
    def __SetVentSpoolData(self,mod):
        lst=[]
        try:
            aadAssExtractData.GetData("ventilation",mod.GetName(),lst,mod.GetPartId())
        except Exception, e:
            print "Error in __SetVentSpoolData:%s"%e
        for cnt in lst:
            vid=kcs_draft.view_identify(self.view_name)
            point=KcsPoint2D.Point2D()
            pp=kcs_draft.point_transform(vid,cnt[1],point)
            self.LblList.append([0,point,0])
            self.PartsList.append([0,"VENT SPOOL",cnt[0],cnt[2],cnt[3],cnt[4]])

# Function that sets the Key In Item  Data #
#========================================#
    def __SetKIData(self,mod):
        lst=[]
        aadAssExtractData.GetData(mod.GetItemType(),mod.GetName(),lst)
        for cnt in lst:
            self.PartsList.append([0,mod.GetItemType(),cnt[0],cnt[2],cnt[3],cnt[4]])



# Function that sets the Key In Component or Parts List #
#=======================================================#
    def FillKIPartsList(self):
        if len(self.PartsList)<=0:
            self.__ExtractData(1)
        if len(self.PartsList)<=0:
            return
        cntr=0
        cntr=self.__PopulateKIPartsList(cntr)
        kcs_draft.dwg_save_as(self.Name, DATABANK)
        kcs_draft.dwg_close()
        for obj in self.SubDwgLst:
            kcs_draft.dwg_open(obj, DATABANK)
            cntr=self.__PopulateKIPartsList(cntr)
            kcs_draft.dwg_save_as(obj, DATABANK)
            kcs_draft.dwg_close()
        kcs_draft.dwg_open(self.Name, DATABANK)



# Function to Create Sub drawings if any #
#========================================#
    def CreateSubDwg(self):
        name=""
        kcs_draft.dwg_save()#_as(self.Name, DATABANK)
        if len(self.SubDwgLst)>0:
            for obj in self.SubDwgLst:
                try:
                    if kcs_draft.dwg_exist(str(obj), DATABANK):
                        kcs_draft.dwg_delete(str(obj), DATABANK)
##                    kcs_draft.dwg_save_as(str(obj), DATABANK)
                    try:
                        kcs_draft.rule_text_new(str(obj),215)
                    except:
                        print str(kcs_draft.error)
                    kcs_draft.dwg_save_as(str(obj), DATABANK)
                except:
                    print kcs_draft.error
            try:
                kcs_draft.dwg_close()
                kcs_draft.dwg_open(self.Name, DATABANK)
            except:
                print kcs_draft.error
        else:
            return



# Function to Populate the Parts List Grid
#=========================================#
    def __PopulatePartsList(self,ind_list):
        pos_no=""
        type=""
        name=""
        mat_no=""
        ac_code=""
        status=""
        qty=""
        for cnt in range(len(ind_list)):
            pos_no=pos_no+str(ind_list[cnt][1])+"\n"
            name=name+str(self.PartsList[ind_list[cnt][0]][2])+"\n"
            ac_code=ac_code+str(self.PartsList[ind_list[cnt][0]][3])+"\n"
            qty=qty+"1\n"
            status=status+str(self.PartsList[ind_list[cnt][0]][5])+"\n"
            mat_no=mat_no+str(self.PartsList[ind_list[cnt][0]][4])+"\n"
            type=type+str(self.PartsList[ind_list[cnt][0]][1])+"\n"
        try:
            kcs_draft.rule_text_new(pos_no,1)
            kcs_draft.rule_text_new(name,3)
            kcs_draft.rule_text_new(type,2)
            kcs_draft.rule_text_new(qty,4)
            kcs_draft.rule_text_new(ac_code,5)
            kcs_draft.rule_text_new(mat_no,6)
            kcs_draft.rule_text_new(status,7)
        except:
            print "Module : FillPartsList \n Error :"+kcs_draft.error




# Function to Populate the Parts List Grid for Key In Items #
#===========================================================#
    def __PopulateKIPartsList(self,cntr=0):
        pos_no=""
        type=""
        name=""
        mat_no=""
        ac_code=""
        status=""
        qty=""
        for cnt in range(len(self.PartsList)):
            cntr=cntr+1
            pos_no=pos_no+str(cntr)+"\n"
            name=name+str(self.PartsList[cnt][2])+"\n"
            ac_code=ac_code+str(self.PartsList[cnt][3])+"\n"
            qty=qty+"1\n"
            status=status+str(self.PartsList[cnt][5])+"\n"
            mat_no=mat_no+str(self.PartsList[cnt][4])+"\n"
            type=type+str(self.PartsList[cnt][1])+"\n"
            if cntr%NLABEL==0:
                break
        try:
            kcs_draft.rule_text_new(pos_no,1)
            kcs_draft.rule_text_new(name,3)
            kcs_draft.rule_text_new(type,2)
            kcs_draft.rule_text_new(qty,4)
            kcs_draft.rule_text_new(ac_code,5)
            kcs_draft.rule_text_new(mat_no,6)
            kcs_draft.rule_text_new(status,7)
        except:
            print "Module : FillPartsList \n Error :"+kcs_draft.error
        return cntr






#=========================================================#
# Function That Changes the Line type of Selected  Item   #
#========================================================#
    def ChangeLineType(self,viewlist,mod_list):
        nInd=1
        name=""
        flag=0
        vlist=[]
        vhandle=kcs_draft.element_child_first_get()
        for obj in viewlist:
            vlist.append(obj.Name)
        while nInd:
              try:
                  name=kcs_draft.subpicture_name_get(vhandle)
                  if name in vlist:
                      flag=1
                  else:
                      flag=0
                  self.GetSubView(vhandle,mod_list,flag)
                  vhandle=kcs_draft.element_sibling_next_get(vhandle)
                  nInd=nInd+1
              except:
                  nInd=0



#=========================================================#
# Function That Gets the Sub Views in the Current Drawing #
#========================================================#
    def GetSubView(self,handle,mod_list,flag=0):
        import KcsLinetype
        import KcsColour
        import string
        newstr=""
        col=KcsColour.Colour("Blue")
        nInd=1
        name=""
        viewname=""
        lin=KcsLinetype.Linetype("SolidXWide")
        try:
            viewhandle=kcs_draft.element_child_first_get(handle)
        except:
            return
        while nInd:
               try:
                    if kcs_draft.element_is_subview(viewhandle):
                       try:
                           name=kcs_draft.subpicture_name_get(viewhandle)
                           newstr=name
                           newstr=string.replace(name,"=","-")
                           newstr=string.replace(newstr,"#","-")
                           if flag==1:
                               for obj in mod_list:
                                   if (string.find(obj.Name, newstr) > -1):
                                        kcs_draft.element_linetype_set(viewhandle,lin)
                                        kcs_draft.element_colour_set(viewhandle,col)
                                        break
                       except:
                           print kcs_draft.error
                    viewhandle=kcs_draft.element_sibling_next_get(viewhandle)
                    nInd=nInd+1
               except:
                     nInd=0







#================================================================================================#
#        This function traverses the Elements                                                    #
#================================================================================================#
    def __GetElements(self,componenthandle,point,flag):

       try:
          elementhandle = kcs_draft.element_child_first_get(componenthandle)
          nIndex = 1
          while nIndex:
             try:
                if kcs_draft.element_is_contour(elementhandle):
                    if flag==1:
                       pp=KcsPoint2D.Point2D()
                       cont=KcsContour2D.Contour2D(pp)
                       cont=kcs_draft.contour_properties_get(elementhandle,cont)
                       segments=cont.Contour[0:10]
                       for seg in segments:
                           if len(seg)>0:
                               ppoint=KcsPoint2D.Point2D()
                               ppoint=seg[0]
                               point.X=ppoint.X
                               point.Y=ppoint.Y
                               flag=-1
                               nIndex=0
                               break
                elementhandle = kcs_draft.element_sibling_next_get(elementhandle)
             except:
                nIndex = 0
       except:
          print 'component has no elements'







#================================================================================================#
#        This function traverses the Components                                                  #
#================================================================================================#
    def __GetComponents(self,subviewhandle,point,flag):
       import KcsElementHandle
       componenthandle = KcsElementHandle.ElementHandle()
       try:
          componenthandle = kcs_draft.element_child_first_get(subviewhandle)   # get first component item
          nIndex = 1

          while nIndex:
             try:
                # first get component elements info to another table
                if flag==1:
                    self.__GetElements(componenthandle,point,flag)
                if flag==-1:
                    nIndex=0
                # now get component name
                name = kcs_draft.subpicture_name_get(componenthandle)

                # and create information about component

                nIndex = nIndex+1
                componenthandle = kcs_draft.element_sibling_next_get(componenthandle)   # get next sibling item

             except:

                nIndex = 0
       except:
          print 'exception: ', kcs_draft.error



#================================================================================================#
#        This function traverses the sub views                                                   #
#================================================================================================#
    def __GetSubviews(self,viewhandle, mod,point,flag):
       subviewhandle = 0
       name = ''
       try:
          subviewhandle = kcs_draft.element_child_first_get(viewhandle)        # get first subview item
          nIndex = 1
          while nIndex:
             try:
                name = kcs_draft.subpicture_name_get(subviewhandle)
                name=string.replace(name,"=","-")
                name=string.replace(name,"#","-")
                if name==mod.GetName():
                    flag=1
                    self.__GetComponents(subviewhandle,point,flag)
                if flag==-1:
                    nIndex=0

                nIndex = nIndex+1
                subviewhandle = kcs_draft.element_sibling_next_get(subviewhandle)       # get next sibling item
                flag=1
             except:
                   nIndex = 0
       except:
          print 'exception: ', kcs_draft.error





#================================================================================================#
#            This function is used if no Point for Labeling is got from the databank             #
#================================================================================================#
    def __GetScreenPoint(self,mod,point):
        nInd=1
        name=""
        tree=[]
        flag=0
        vlist=[]
        try:
            vhandle=kcs_draft.element_child_first_get()
            while nInd:
                  try:
                      name=kcs_draft.subpicture_name_get(vhandle)
                      if name==self.view_name:
                          flag=1
                          self.__GetSubviews(vhandle,mod,point,flag)
                      if flag==-1:
                          return
                      nInd=nInd+1
                      vhandle=kcs_draft.element_sibling_next_get(vhandle)
                      flag=0
                  except:
                      nInd=0
        except:
            print 'Exception'
            print kcs_draft.error
