
import math
import string

import kcs_ui
import kcs_util
import KcsModel
import KcsPoint2D
import KcsPoint3D
import KcsVector3D
import KcsTransformation3D

import aadAssUtil
import aadAssDataExtraction



#----------------------------------------------------------------------------------------------------#
#   Name :   aadAssExtractData.py
#
#
#   Purpose :
#               This file is to provide with functions that Extract Data              #
#               for Different Installation Jobs                                                      #
#----------------------------------------------------------------------------------------------------#






# The main function which decides which function to call according to the type of model    #
#----------------------------------------------------------------------------------------------------#
def GetHullData(model, out_data):
    st=GetModelName(model.GetName())
    GetPlanePanelData(st,model.GetPartId(),out_data, model)

def GetData(type,nam,out_data,part_id=0):
#suport for pipe spools(just in case)
    if type=='ventilation' and part_id > 0:
         name = GetVentSpoolName(nam, part_id)
         type = 'vent spool'

    if type=='pipe' and part_id > 0:
         name = GetSpoolName(nam, part_id)
         type = 'pipe spool'

    if type=="struct":
         st=GetModelName(nam)
         GetStructData(st, part_id, out_data)
    elif type=="pipe":
         st=GetModelName(nam)
         GetPipeData(st,part_id,out_data)
    elif type=="pipe spool":
         st=GetModelName(nam)
         lst = st.split('-')
         tmp = lst[0] + '-' + lst[1]
         tmp_data = []
         GetPipeData(tmp,-abs(part_id),out_data)
         for data in out_data:
             data[0] = st
    elif type=="vent spool":
         st=GetModelName(nam)
         lst = st.split('-')
         tmp = lst[0] + '-' + lst[1]
         GetVentData(tmp,-abs(part_id),out_data)
         for data in out_data:
             data[0] = st
    elif type=="cable way":
         st=GetModelName(nam)
         GetCableData(st,part_id,out_data)
    elif type=="equipment":
         st=GetModelName(nam)
         GetEquipmentData(st,out_data)
    elif type=="curved panel":
         st=GetModelName(nam)
         GetCurvedPanelData(st,part_id,out_data)
    elif type=="plane panel":
         st=GetModelName(nam)
         GetPlanePanelData(st,part_id,out_data)
    elif type=="vent":
         st=GetModelName(nam)
         GetVentData(st,part_id,out_data)
    elif type=="ventilation":
        st=GetModelName(nam)
        GetVentSpoolData1(st,out_data)
    elif type=='key-in component':
       st=GetModelName(nam)
       GetKICData(st,out_data)
    elif type=='key-in part':
       st=GetModelName(nam)
       GetKIPData(st,out_data)
    #elif type=="penetration":
    #     st=GetModelName(nam)
    #     GetPenetrationData(st,part_id,out_data)

#----------------------------------------------------------------------------------------------------#
# This Function Removes the project name from the model name if its present
def GetModelName(nam):
    pnam=kcs_util.TB_environment_get("SB_PROJ")
    pnam=string.upper(pnam)+"-"
    st=nam
    st1=""
    if string.find(nam,pnam)>-1:
       n=string.find(st,"-")
       st1=st[n+1:]
       return st1
    else:
       return st



#----------------------------------------------------------------------------------------------------#
#========================================================================#
#           Retrives Data from a Structure                               #
#========================================================================#

def GetStructData(nam,Part_Id,out_data):
    st="STRUCT.ITEM('"+nam+"')"
    result=[]
    result1=[]
    status=""
    tdm_data=""
    dt=aadAssDataExtraction.DataExtraction(st+".NAME")
    tdm_data=aadAssDataExtraction.DataExtraction(st+".TDM_INFORMATION.STATUS_MANUFACT")
    if len(tdm_data.DataResult)>0:
       status=tdm_data.DataResult[0]
    if len(dt.DataResult)>0:
        nam=dt.DataResult[0]

        #check if this is the struct module or part:
        if(Part_Id <> 0):
            #get part
            nGroup_data = aadAssDataExtraction.DataExtraction(st + ".NGROUP")
            nGroup = 0
            if len(nGroup_data.DataResult)>0:
                nGroup = nGroup_data.DataResult[0] + 1;
            for nGr in range(1,nGroup,1):
                nParts_data = aadAssDataExtraction.DataExtraction(st + ".GROUP("+str(nGr)+").NPART")
                nParts = 0
                if len(nParts_data.DataResult) > 0:
                    nParts = nParts_data.DataResult[0] + 1
                for nPart in range(1, nParts, 1):
                    Part_ST = st + ".GROUP("+str(nGr)+").PART("+str(nPart)+")"
                    nPart_data = aadAssDataExtraction.DataExtraction(Part_ST+".PART_ID")
                    if len(nPart_data.DataResult) >0:
                        if nPart_data.DataResult[0] == Part_Id:
                            #This is the part :)
                            dt = aadAssDataExtraction.DataExtraction(Part_ST + ".COG")
                            if len(dt.DataResult)>0:
                                pnt_lst=dt.DataResult[0]
                                pnt=KcsPoint3D.Point3D(pnt_lst[0],pnt_lst[1],pnt_lst[2])
                                nam = nam + '-PAR'+str(Part_Id)
                                out_data.append([nam,pnt,"MF","",status])
        else:
            dt=aadAssDataExtraction.DataExtraction(st+".COG")
            if len(dt.DataResult)>0:
              pnt_lst=dt.DataResult[0]
              pnt=KcsPoint3D.Point3D(0,0,0)
              pnt.SetCoordinates(pnt_lst[0],pnt_lst[1],pnt_lst[2])
              out_data.append([nam,pnt,"MF","",status])
            else:
              dt=aadAssDataExtraction.DataExtraction("STRUCT.ITEM('"+nam+"').GROUP(1).PART(1).COG")
              if len(dt.DataResult)>0:
                 pnt_lst=dt.DataResult[0]
                 pnt=KcsPoint3D.Point3D(0,0,0)
                 pnt.SetCoordinates(pnt_lst[0],pnt_lst[1],pnt_lst[2])
                 out_data.append([nam,pnt,"MF","",status])


#----------------------------------------------------------------------#
# This Function gets Data About the Pipe part for the Part Id Supplied #
#----------------------------------------------------------------------#
def GetPipeData(nam,part_id,out_data):
    proj=""
    comp_name=""
    ac_code=""
    mat_no=""
    status=""
    tdm_data=""
    proj=string.upper(kcs_util.TB_environment_get("SB_PROJ"))
    st="PIPE('"+proj+"').PIPMODEL('"+string.upper(nam)+"').PART("+str(part_id)+")"
    stmt="PIPE('"+proj+"').PIPMODEL('"+string.upper(nam)+"').TDM_INFORMATION.STATUS_MATERIAL"
    pip_data=aadAssDataExtraction.DataExtraction(st+".COMP_NAME")
    tdm_data=aadAssDataExtraction.DataExtraction(stmt)
    if len(tdm_data.DataResult)>0:
       status=tdm_data.DataResult[0]
    if len(pip_data.DataResult)>0:
       comp_name=pip_data.DataResult[0]
       pip_data1=aadAssDataExtraction.DataExtraction(st+".NODE(0).POINT")
       if len(pip_data1.DataResult)>0:
          pnt_lst=pip_data1.DataResult[0]
          point=KcsPoint3D.Point3D(pnt_lst[0],pnt_lst[1],pnt_lst[2])
          stmt="COMPONENT('"+string.upper(comp_name)+"')"
          pip_data2=aadAssDataExtraction.DataExtraction(stmt+".MATERIAL")
          if len(pip_data2.DataResult)>0:
             mat_no=pip_data2.DataResult[0]
          stmt="COMPONENT('"+string.upper(comp_name)+"')"
          pip_data3=aadAssDataExtraction.DataExtraction(stmt+".MAN_PROPERTY.ACQUISITION_CODE")
          if len(pip_data3.DataResult)>0:
             ac_code=pip_data3.DataResult[0]
       if part_id<0:
          comp_name=nam+str(part_id)
       else:
          comp_name=nam+"-"+str(part_id)
       out_data.append([comp_name,point,ac_code,mat_no,status])

#----------------------------------------------------------------------#
# This Function gets Data About the Vent part for the Part Id Supplied #
#----------------------------------------------------------------------#
def GetVentData(nam,part_id,out_data):
    proj=""
    comp_name=""
    ac_code=""
    mat_no=""
    status=""
    tdm_data=""
    proj=string.upper(kcs_util.TB_environment_get("SB_PROJ"))
    st="VENT('"+proj+"').VENTMODEL('"+string.upper(nam)+"').PART("+str(part_id)+")"
    stmt="VENT('"+proj+"').VENTMODEL('"+string.upper(nam)+"').TDM_INFORMATION.STATUS_MATERIAL"
    pip_data=aadAssDataExtraction.DataExtraction(st+".COMP_NAME")
    tdm_data=aadAssDataExtraction.DataExtraction(stmt)
    if len(tdm_data.DataResult)>0:
       status=tdm_data.DataResult[0]
    if len(pip_data.DataResult)>0:
       comp_name=pip_data.DataResult[0]
       pip_data1=aadAssDataExtraction.DataExtraction(st+".NODE(0).POINT")
       if len(pip_data1.DataResult)>0:
          pnt_lst=pip_data1.DataResult[0]
          point=KcsPoint3D.Point3D(pnt_lst[0],pnt_lst[1],pnt_lst[2])
          stmt="COMPONENT('"+string.upper(comp_name)+"')"
          pip_data2=aadAssDataExtraction.DataExtraction(stmt+".MATERIAL")
          if len(pip_data2.DataResult)>0:
             mat_no=pip_data2.DataResult[0]
          stmt="COMPONENT('"+string.upper(comp_name)+"')"
          pip_data3=aadAssDataExtraction.DataExtraction(stmt+".MAN_PROPERTY.ACQUISITION_CODE")
          if len(pip_data3.DataResult)>0:
             ac_code=pip_data3.DataResult[0]
       if part_id<0:
          comp_name=nam+str(part_id)
       else:
          comp_name=nam+"-"+str(part_id)
       out_data.append([comp_name,point,ac_code,mat_no,status])


#----------------------------------------------------------------------------------------------------#
#========================================================================#
#           Retrives Data from a Pipe Spool                              #
#========================================================================#
def GetPipeSpoolData(nam,out_data):
    st="PIPE.PIPSPOOL('"+nam+"')"
    pip_data=aadAssDataExtraction.DataExtraction(st+".NMTRL")
    part_id=[]
    branch_id=[]
    status=""
    npart=0
    nbranch=0
    if len(pip_data.DataResult)>0:
        nmtrl=pip_data.DataResult[0]
        for cnt in range(1,nmtrl+1,1):
            pip_data1=aadAssDataExtraction.DataExtraction(st+".MTRL("+str(cnt)+").NPART")
            if len(pip_data1.DataResult)>0:
                npart=pip_data1.DataResult[0]
                for ncnt in range(1,npart+1):
                    pip_data2=aadAssDataExtraction.DataExtraction(st+".MTRL("+str(cnt)+").PART_ID("+str(ncnt)+")") # Retriving the part id
                    if len(pip_data2.DataResult)>0:
                        part_id.append([pip_data2.DataResult[0],0])
        pip_data3=aadAssDataExtraction.DataExtraction(st+".NBRANCH")   #  Retriving the number of branches
        if len(pip_data3.DataResult)>0:
            nbranch=pip_data3.DataResult[0]
            for bcnt in range(1,nbranch+1,1):
                for npr in range(len(part_id)):
                    if part_id[npr][1]==0:
                        pip_data4=aadAssDataExtraction.DataExtraction(st+".BRANCH("+str(bcnt)+").PART("+str(part_id[npr][0])+").COMP_NAME")
                        if len(pip_data4.DataResult)>0:
                           comp_name=pip_data4.DataResult[0]
                           part_id[npr][1]=1
                           ##kcs_ui.message_confirm(comp_name)
                           pip_data5=aadAssDataExtraction.DataExtraction(st+".BRANCH("+str(bcnt)+").PART("+str(part_id[npr][0])+").NNODE")
                           if len(pip_data5.DataResult)>0:
                               nnode=pip_data5.DataResult[0]
                               ##kcs_ui.message_confirm(str(nnode))
                               ##for nncnt in range(nnode+1):
                               ##pip_data6=aadAssDataExtraction.DataExtraction(st+".BRANCH("+str(bcnt)+").PART("+str(part_id[npr][0])+").NODPOINT("+str(nncnt)+")")
                               pip_data6=aadAssDataExtraction.DataExtraction(st+".BRANCH("+str(bcnt)+").PART("+str(part_id[npr][0])+").NODPOINT(0)")
                               if len(pip_data6.DataResult)>0:
                                  pnt_lst=pip_data6.DataResult[0]
                                  pnt=KcsPoint3D.Point3D(pnt_lst[0],pnt_lst[1],pnt_lst[2])
                                  ##kcs_ui.message_confirm(str(pnt.X))
                               stmt="COMPONENT('"+comp_name+"').MATERIAL"
                               pip_data7=aadAssDataExtraction.DataExtraction(stmt)
                               mat_no=0
                               if len(pip_data7.DataResult)>0:
                                   mat_no=pip_data7.DataResult[0]
                               stmt="COMPONENT('"+comp_name+"').MAN_PROPERTY.ACQUISITION_CODE"
                               pip_data8=aadAssDataExtraction.DataExtraction(stmt)
                               ac_code=0
                               if len(pip_data8.DataResult)>0:
                                   ac_code=pip_data8.DataResult[0]
                               out_data.append([comp_name,pnt,ac_code,mat_no])
                               ##data=[]
                               ##data.append(comp_name)
                               ##data.append(pnt)
                               ##data.append(mat_no)
                               ##data.append(ac_code)
                               ##out_data.append(data)





#----------------------------------------------------------------------------------------------------#
#========================================================================#
#           Retrives Data from a Vent Spool                              #
#========================================================================#
def GetVentSpoolData(nam,out_data):
    st="VENT.PIPSPOOL('"+nam+"')"
    pip_data=aadAssDataExtraction.DataExtraction(st+".NMTRL")
    part_id=[]
    branch_id=[]
    npart=0
    status=""
    nbranch=0
    if len(pip_data.DataResult)>0:
        nmtrl=pip_data.DataResult[0]
        for cnt in range(1,nmtrl+1,1):
            pip_data1=aadAssDataExtraction.DataExtraction(st+".MTRL("+str(cnt)+").NPART")
            if len(pip_data1.DataResult)>0:
                npart=pip_data1.DataResult[0]
                for ncnt in range(1,npart+1):
                    pip_data2=aadAssDataExtraction.DataExtraction(st+".MTRL("+str(cnt)+").PART_ID("+str(ncnt)+")") # Retriving the part id
                    if len(pip_data2.DataResult)>0:
                        part_id.append([pip_data2.DataResult[0],0])
        pip_data3=aadAssDataExtraction.DataExtraction(st+".NBRANCH")   #  Retriving the number of branches
        if len(pip_data3.DataResult)>0:
            nbranch=pip_data3.DataResult[0]
            for bcnt in range(1,nbranch+1,1):
                for npr in range(len(part_id)):
                    if part_id[npr][1]==0:
                        pip_data4=aadAssDataExtraction.DataExtraction(st+".BRANCH("+str(bcnt)+").PART("+str(part_id[npr][0])+").COMP_NAME")
                        if len(pip_data4.DataResult)>0:
                           comp_name=pip_data4.DataResult[0]
                           part_id[npr][1]=1
                           ##kcs_ui.message_confirm(comp_name)
                           pip_data5=aadAssDataExtraction.DataExtraction(st+".BRANCH("+str(bcnt)+").PART("+str(part_id[npr][0])+").NNODE")
                           if len(pip_data5.DataResult)>0:
                               nnode=pip_data5.DataResult[0]
                               ##kcs_ui.message_confirm(str(nnode))
                               ##for nncnt in range(nnode+1):
                               ##pip_data6=aadAssDataExtraction.DataExtraction(st+".BRANCH("+str(bcnt)+").PART("+str(part_id[npr][0])+").NODPOINT("+str(nncnt)+")")
                               pip_data6=aadAssDataExtraction.DataExtraction(st+".BRANCH("+str(bcnt)+").PART("+str(part_id[npr][0])+").NODPOINT(0)")
                               if len(pip_data6.DataResult)>0:
                                  pnt_lst=pip_data6.DataResult[0]
                                  pnt=KcsPoint3D.Point3D(pnt_lst[0],pnt_lst[1],pnt_lst[2])
                                  ##kcs_ui.message_confirm(str(pnt.X))
                               stmt="COMPONENT('"+comp_name+"').MATERIAL"
                               pip_data7=aadAssDataExtraction.DataExtraction(stmt)
                               mat_no=0
                               if len(pip_data7.DataResult)>0:
                                   mat_no=pip_data7.DataResult[0]
                               stmt="COMPONENT('"+comp_name+"').MAN_PROPERTY.ACQUISITION_CODE"
                               pip_data8=aadAssDataExtraction.DataExtraction(stmt)
                               ac_code=0
                               if len(pip_data8.DataResult)>0:
                                   ac_code=pip_data8.DataResult[0]
                               out_data.append([comp_name,pnt,ac_code,mat_no])
                               ##data=[]
                               ##data.append(comp_name)
                               ##data.append(pnt)
                               ##data.append(mat_no)
                               ##data.append(ac_code)




#----------------------------------------------------------------------------------------------------#
#========================================================================#
#           Retrives Data from a Equipment                               #
#========================================================================#
def GetEquipmentData(nam,out_data):
    pnam=kcs_util.TB_environment_get("SB_PROJ")
    pnam=string.upper(pnam)
    st="EQUIPMENT('"+pnam+"').ITEM('"+nam+"')"
    tdm_data=""
    comp_name=""
    cab_name=""
    cab_flag=0
    status=""
    pipe_flag=0
    eqp_name=[]
    mat_no=0
    ac_code=0
    ncab=0
    npipconn=0
    eqp_data=aadAssDataExtraction.DataExtraction(st+".COMP_NAME")
    tdm_data=aadAssDataExtraction.DataExtraction(st+".TDM_INFORMATION.STATUS_MATERIAL")
    if len(tdm_data.DataResult)>0:
       status=tdm_data.DataResult[0]
    if len(eqp_data.DataResult)>0:
        comp_name=eqp_data.DataResult[0] # Name of the Equipment Component
        eqp_data1=aadAssDataExtraction.DataExtraction(st+".ELECTRIC.NCABLE") # Check if Cables are attached
        if len(eqp_data1.DataResult)>0:
           ncab=eqp_data1.DataResult[0]
           for ncnt in range(1,ncab+1,1):
               eqp_data2=aadAssDataExtraction.DataExtraction(st+".ELECTRIC.CABLE("+str(ncnt)+").NAME")
               if len(eqp_data2.DataResult)>0:
                  cab_name=eqp_data2.DataResult[0]
                  break
           stmt="CABLE.CAB_MOD('"+cab_name+"').CONNECTION(1).EQUIPMENT"
           eqp_data3=aadAssDataExtraction.DataExtraction(stmt)

           if len(eqp_data3.DataResult)>0:
              eqp_name.append(eqp_data3.DataResult[0])

           stmt="CABLE.CAB_MOD('"+cab_name+"').CONNECTION(2).EQUIPMENT"
           eqp_data4=aadAssDataExtraction.DataExtraction(stmt)

           if len(eqp_data4.DataResult)>0:
              eqp_name.append(eqp_data4.DataResult[0])

           for nnam in range(len(eqp_name)):
               if string.strip(string.upper(eqp_name[nnam]))==string.strip(string.upper(nam)):
                  stmt="CABLE.CAB_MOD('"+cab_name+"').CONNECTION("+str(nnam+1)+").POINT"
                  eqp_data5=aadAssDataExtraction.DataExtraction(stmt)
                  if len(eqp_data5.DataResult)>0:
                      pnt_lst=eqp_data5.DataResult[0]
                      pnt_cab=KcsPoint3D.Point3D(0,0,0)
                      pnt_cab.SetCoordinates(pnt_lst[0],pnt_lst[1],pnt_lst[2])
                      cab_flag=1
                      break

        eqp_data6=aadAssDataExtraction.DataExtraction(st+".PIPE.NCONNECTION") # Check if Pipes are attached
        if len(eqp_data6.DataResult)>0:
           npipconn=eqp_data6.DataResult[0]
           for npconn in range(1,npipconn+1):
               eqp_data7=aadAssDataExtraction.DataExtraction(st+".PIPE.CONNECTION("+str(npconn)+").POINT") # gets the ref point
               if len(eqp_data7.DataResult)>0:
                  pnt_lst=eqp_data7.DataResult[0]
                  pnt_pipe=KcsPoint3D.Point3D(0,0,0)
                  pnt_pipe.SetCoordinates(pnt_lst[0],pnt_lst[1],pnt_lst[2])
                  pipe_flag=1
                  break
        stmt="COMPONENT('"+comp_name+"').MATERIAL"
        eqp_data8=aadAssDataExtraction.DataExtraction(stmt) # gets the material no
        if len(eqp_data8.DataResult)>0:
           mat_no=eqp_data8.DataResult[0]
        stmt="COMPONENT('"+comp_name+"').MAN_PROPERTY.ACQUISITION_CODE"
        eqp_data9=aadAssDataExtraction.DataExtraction(stmt) # gets the material no
        if len(eqp_data9.DataResult)>0:
           ac_code=eqp_data9.DataResult[0]
        if cab_flag==1:
           comp_name=nam
           out_data.append([comp_name,pnt_cab,ac_code,mat_no,status])
        elif pipe_flag==1:
           comp_name=nam
           out_data.append([comp_name,pnt_pipe,ac_code,mat_no,status])
        else: # Added for handling the case where the databank doesnt contain a point to be plotted hence can be handled by CDrawing Object
           comp_name=nam
           pnt=KcsPoint3D.Point3D(0,0,0)
           out_data.append([comp_name,pnt,ac_code,mat_no,status])



# Function that extracts the Plane Panel Data using  Data extraction #
#----------------------------------------------------------------------------------------------------#
def GetPlanePanelData(nam, partID,out_data, model):
    names=[]
    tdm_data=""
    comp_name=""
    ac_code="MF"
    mat_no=""
    status=""
    mat=KcsTransformation3D.Transformation3D()
    pnt=KcsPoint3D.Point3D(0.0,0.0,0.0)

    st="HULL.PANEL('"+nam+"')"
    hull_dat=aadAssDataExtraction.DataExtraction(st+".TRANSFORMATION_MATRIX")
    tdm_data=aadAssDataExtraction.DataExtraction(st+".TDM_INFORMATION.STATUS_MANUFACT")
    if len(hull_dat.DataResult)>0:
       mat=KcsTransformation3D.Transformation3D()
       M = [[0,0,0,0],[0,0,0,0],[0,0,0,0],[0,0,0,0]]
       for i in range(4):
           for j in range (4):
               M[i][j] = hull_dat.DataResult[i*4 + j]
       mat.SetByRow(1,M)
    hull_da=aadAssDataExtraction.DataExtraction(st+".BOUNDARY(1:999).REFLECT")
    if len(tdm_data.DataResult)>0:
          status=tdm_data.DataResult[0]
    if hull_da.DataNumber>0:
       for i in range(0,hull_da.DataNumber,1):
            if hull_da.DataResult[i]==1:
                test=KcsTransformation3D.Transformation3D()
                test.ReflectY()
##                mat.Combine(test)
                break
    nSymetry = __GetPanelSymetry(model)
    if(model.GetPartType() == "plate"):
        hull_data=aadAssDataExtraction.DataExtraction(st+".NPLATE") # gets the no. of plates in the plane panel
        if len(hull_data.DataResult)>0:
           npl=hull_data.DataResult[0]
           for npla in range(1,npl+1,1):
               hull_data3 = aadAssDataExtraction.DataExtraction(st+".PLATE("+str(npla)+").COMP_ID")
               if len(hull_data3.DataResult)>0:
                   if hull_data3.DataResult[0] == partID:
                       hull_data1=aadAssDataExtraction.DataExtraction(st+".PLATE("+str(npla)+").PART_ID.LONG")
                       if len(hull_data1.DataResult)>0:
                           comp_name = __GetHullPartName(string.split(hull_data1.DataResult[0],'@@'), nSymetry, model.ReflCode)

                       hull_data2=aadAssDataExtraction.DataExtraction(st+".PLATE("+str(npla)+").COG")
                       if hull_data2.DataNumber>0:
                          n=hull_data2.DataNumber
                          for cnt in range(n):
                              if len(hull_data2.DataResult[cnt])>0:
                                 pnt_lst=hull_data2.DataResult[cnt]
                                 if model.ReflCode:
                                     if nSymetry == 0 or nSymetry == 1 or nSymetry == 3 or nSymetry == 12 or nSymetry == 10:
                                         pnt=KcsPoint3D.Point3D(pnt_lst[0],-pnt_lst[1],pnt_lst[2])
                                     else:
                                         pnt=KcsPoint3D.Point3D(pnt_lst[0],pnt_lst[1],pnt_lst[2])
                                 else:
                                     if nSymetry == 10 or nSymetry == 11 or nSymetry == 2:
                                         pnt=KcsPoint3D.Point3D(pnt_lst[0],-pnt_lst[1],pnt_lst[2])
                                     else:
                                         pnt=KcsPoint3D.Point3D(pnt_lst[0],pnt_lst[1],pnt_lst[2])
                                 break

                       out_data.append([comp_name,pnt,ac_code,mat_no,status,'PLATE'])
                       return
    elif(model.GetPartType() == "stiffener"):
        hull_data=aadAssDataExtraction.DataExtraction(st+".NSTIFFENER") # gets the no. of stiffeners in a plane panel
        if len(hull_data.DataResult)>0:
           nstiff=hull_data.DataResult[0]
           for nst in range(1,nstiff+1,1):
               hull_data3 = aadAssDataExtraction.DataExtraction(st+".STIFFENER("+str(nst)+").COMP_ID")
               if len(hull_data3.DataResult)>0:
                   if hull_data3.DataResult[0] == partID:
                       hull_data1 = aadAssDataExtraction.DataExtraction(st + ".STIFFENER("+str(nst)+").PROFILE.PART_ID.LONG")
                       if len(hull_data1.DataResult) > 0:
                           comp_name= __GetHullPartName(string.split(hull_data1.DataResult[0], '@@'), nSymetry, model.ReflCode)

                       hull_data2=aadAssDataExtraction.DataExtraction(st+".STIFFENER("+str(nst)+").END(1).POINT")
                       if len(hull_data2.DataResult)>0:
                          pnt_lst=hull_data2.DataResult[0]
                          pnt=KcsPoint3D.Point3D(pnt_lst[0],pnt_lst[1],0.0)
                          if model.ReflCode:
                              mat.ReflectY()
                          pnt.Transform(mat)
                       out_data.append([comp_name,pnt,ac_code,mat_no,status, 'STIFFENER'])
                       return

    elif(model.GetPartType() == "bracket"):
        hull_data=aadAssDataExtraction.DataExtraction(st+".NBRACKET") # gets the no. of brackets in a plane panel
        if len(hull_data.DataResult)>0:
            nbract=hull_data.DataResult[0]
            for brkt in range(1,nbract+1,1):
                hull_data3 = aadAssDataExtraction.DataExtraction(st+".BRACKET("+str(brkt)+").COMP_ID")
                if len(hull_data3.DataResult) > 0:
                    if abs(hull_data3.DataResult[0]) == abs(partID):
                        comp_name=""

                        hull_data1=aadAssDataExtraction.DataExtraction(st+".BRACKET("+str(brkt)+").PART_ID.LONG")
                        if len(hull_data1.DataResult)>0:
                           comp_name = __GetHullPartName(string.split(str(hull_data1.DataResult[0]), '@@'), nSymetry, model.ReflCode)

                        hull_data2=aadAssDataExtraction.DataExtraction(st+".BRACKET("+str(brkt)+").COG")
                        if len(hull_data2.DataResult)>0:
                            pnt_lst=hull_data2.DataResult[0]
                            if model.ReflCode:
                                if nSymetry == 0 or nSymetry == 1 or nSymetry == 3 or nSymetry == 12 or nSymetry == 10:
                                    pnt=KcsPoint3D.Point3D(pnt_lst[0],-pnt_lst[1],pnt_lst[2])
                                else:
                                    pnt=KcsPoint3D.Point3D(pnt_lst[0],pnt_lst[1],pnt_lst[2])
                            else:
                                if nSymetry == 10 or nSymetry == 11 or nSymetry == 2:
                                    pnt=KcsPoint3D.Point3D(pnt_lst[0],-pnt_lst[1],pnt_lst[2])
                                else:
                                    pnt=KcsPoint3D.Point3D(pnt_lst[0],pnt_lst[1],pnt_lst[2])
                        out_data.append([comp_name,pnt,ac_code,mat_no,status,'BRACKET'])
                        return

    elif(model.GetPartType() == "pillar"):
        hull_data=aadAssDataExtraction.DataExtraction(st+".NPILLAR") # gets the no. of stiffeners in a plane panel
        if len(hull_data.DataResult)>0:
           npill=hull_data.DataResult[0]
           for pillar in range(1,npill+1,1):
               hull_data3 = aadAssDataExtraction.DataExtraction(st+".PILLAR("+str(pillar)+").COMP_ID")
               if len(hull_data3.DataResult) > 0 :
                   if hull_data3.DataResult[0] == partID:
                       hull_data1=aadAssDataExtraction.DataExtraction(st+".PILLAR("+str(pillar)+").PROFILE.PART_ID.LONG")
                       if len(hull_data1.DataResult) > 0:
                           comp_name = __GetHullPartName(string.split(str(hull_data1.DataResult[0]), '@@'), nSymetry, model.ReflCode)

                       hull_data2=aadAssDataExtraction.DataExtraction(st+".PILLAR("+str(pillar)+").END(1).POINT")
                       if len(hull_data2.DataResult)>0:
                          pnt_lst=hull_data2.DataResult[0]
                          pnt=KcsPoint3D.Point3D(pnt_lst[0],pnt_lst[1],0.0)
                          if model.ReflCode:
                              if nSymetry == 0 or nSymetry == 1 or nSymetry == 3 or nSymetry == 12 or nSymetry == 10:
                                  mat.ReflectY()
                          else:
                              if nSymetry == 10 or nSymetry == 11 or nSymetry == 2:
                                  mat.ReflectY()
                          pnt.Transform(mat)
                       out_data.append([comp_name,pnt,ac_code,mat_no,status, 'PILLAR'])
                       return

    elif(model.GetPartType() == "flange"):
        hull_data=aadAssDataExtraction.DataExtraction(st+".NFLANGE") # gets the no. of stiffeners in a plane panel
        if len(hull_data.DataResult)>0:
           nflange=hull_data.DataResult[0]
           for flange in range(1,nflange+1,1):
               hull_data3 = aadAssDataExtraction.DataExtraction(st+".FLANGE("+str(flange)+").COMP_ID")
               if len(hull_data3.DataResult) > 0:
                   if hull_data3.DataResult[0] == partID:
                       comp_name=""

                       hull_data1=aadAssDataExtraction.DataExtraction(st+".FLANGE("+str(flange)+").PROFILE.PART_ID.LONG")
                       if len(hull_data1.DataResult)>0:
                          comp_name = __GetHullPartName(string.split(str(hull_data1.DataResult[0]), '@@'), nSymetry, model.ReflCode)

                       hull_data2=aadAssDataExtraction.DataExtraction(st+".FLANGE("+str(flange)+").END(1).POINT")
                       if len(hull_data2.DataResult)>0:
                          pnt_lst=hull_data2.DataResult[0]
                          pnt=KcsPoint3D.Point3D(pnt_lst[0],pnt_lst[1],0.0)
                          if model.ReflCode:
                              if nSymetry == 0 or nSymetry == 1 or nSymetry == 3 or nSymetry == 12 or nSymetry == 10:
                                  mat.ReflectY()
                          else:
                              if nSymetry == 10 or nSymetry == 11 or nSymetry == 2:
                                  mat.ReflectY()
                          pnt.Transform(mat)

                       out_data.append([comp_name,pnt,ac_code,mat_no,status, 'FLANGE'])
                       return

    elif(model.GetPartType() == "cutout" or model.GetPartType() == "clip"):
        pnt = None
        comp_name = ''
        if( hasattr(model, "AaidClipName") and hasattr(model, "AaidCOG")):
                out_data.append([model.AaidClipName,model.AaidCOG,ac_code,mat_no,status, 'CLIP'])
                return
        else:
            for index in range(1,4,1):
                hull_data1=aadAssDataExtraction.DataExtraction(st+".CUTOUT("+str(model.GetPartId())+").CLIP("+ str(index) +").PART_ID.LONG")
                if len(hull_data1.DataResult) > 0:
                    comp_name = __GetHullPartName(string.split(str(hull_data1.DataResult[0]), '@@'), nSymetry, model.ReflCode)

                hull_data2 = aadAssDataExtraction.DataExtraction(st+".CUTOUT("+str(model.GetPartId())+").CLIP("+ str(index) +").COG")
                if len(hull_data2.DataResult) > 0:
                    pnt_lst=hull_data2.DataResult[0]
                    if model.ReflCode:
                        if nSymetry == 0 or nSymetry == 1 or nSymetry == 3 or nSymetry == 12 or nSymetry == 10:
                            pnt=KcsPoint3D.Point3D(pnt_lst[0],-pnt_lst[1],pnt_lst[2])
                        else:
                            pnt=KcsPoint3D.Point3D(pnt_lst[0],pnt_lst[1],pnt_lst[2])
                    else:
                        if nSymetry == 10 or nSymetry == 11 or nSymetry == 2:
                            pnt=KcsPoint3D.Point3D(pnt_lst[0],-pnt_lst[1],pnt_lst[2])
                        else:
                            pnt=KcsPoint3D.Point3D(pnt_lst[0],pnt_lst[1],pnt_lst[2])
                if(comp_name <> ''):
                    out_data.append([comp_name,pnt,ac_code,mat_no,status, 'CLIP'])
        return

#========================================================================#
#           Retrives Data from a clip\cutout models                      #
#========================================================================#
def GetClipData(model, out_data):
    if(model.GetPartType() == "cutout" or model.GetPartType() == "clip"):
        st="HULL.PANEL('"+model.GetName()+"')"
        comp_name = ''
        pnt = None
        nSymetry = __GetPanelSymetry(model)
        for index in range(1,4,1):
            hull_data1=aadAssDataExtraction.DataExtraction(st+".CUTOUT("+str(model.GetPartId())+").CLIP("+ str(index) +").PART_ID.LONG")
            if len(hull_data1.DataResult) > 0:
                comp_name = __GetHullPartName(string.split(str(hull_data1.DataResult[0]), '@@'), nSymetry, model.ReflCode)
                tmpModel = KcsModel.Model(model.GetType(), model.GetName())
                tmpModel.SetPartId(model.GetPartId())
                tmpModel.SetPartType(model.GetPartType())
                tmpModel.SetReflCode(model.GetReflCode())
                tmpModel.AaidClipName = comp_name
                hull_data2 = aadAssDataExtraction.DataExtraction(st+".CUTOUT("+str(model.GetPartId())+").CLIP("+ str(index) +").COG")
                if len(hull_data2.DataResult) > 0:
                    pnt_lst=hull_data2.DataResult[0]
                    if model.ReflCode:
                        if nSymetry == 0 or nSymetry == 1 or nSymetry == 3 or nSymetry == 12 or nSymetry == 10:
                            pnt=KcsPoint3D.Point3D(pnt_lst[0],-pnt_lst[1],pnt_lst[2])
                        else:
                            pnt=KcsPoint3D.Point3D(pnt_lst[0],pnt_lst[1],pnt_lst[2])
                    else:
                        if nSymetry == 10 or nSymetry == 11 or nSymetry == 2:
                            pnt=KcsPoint3D.Point3D(pnt_lst[0],-pnt_lst[1],pnt_lst[2])
                        else:
                            pnt=KcsPoint3D.Point3D(pnt_lst[0],pnt_lst[1],pnt_lst[2])
                tmpModel.AaidCOG = pnt
                out_data.append(tmpModel)
    return

#----------------------------------------------------------------------------------------------------#
#========================================================================#
#           Retrives Data from a Cable Way                               #
#========================================================================#
def GetCableData1(nam,out_data):
    pnam=kcs_util.TB_environment_get("SB_PROJ")
    pnam=string.upper(pnam)
    st="CABLE('"+pnam+"').CWAY('"+nam+"')"
    mat_no=""
    ac_code=""
    status=""
    tdm_data=""
    cab_data=aadAssDataExtraction.DataExtraction(st+".MAX_PARTID") # gets the no. of parts in the cable way
    tdm_data=aadAssDataExtraction.DataExtraction(st+".TDM_INFORMATION.STATUS_MATERIAL") # gets the no. of parts in the cable way
    npart=0
    dt=[]
    if len(cab_data.DataResult)>0:
       npart=cab_data.DataResult[0]
       if len(tdm_data.DataResult)>0:
          status=tdm_data.DataResult[0]
       for npt in range(1,npart+1,1):
           cab_data1=aadAssDataExtraction.DataExtraction(st+".PART("+str(npt)+").MAN_PROPERTIES.POSITION_NUMBER") # gets the comp_name
           if len(cab_data1.DataResult)>0:
              comp_name=cab_data1.DataResult[0]
              ##cab_data2=aadAssDataExtraction.DataExtraction(st+".PART("+str(npt)+").COG") # gets the COG value
              cab_data2=aadAssDataExtraction.DataExtraction(st+".PART("+str(npt)+").NCONN") # gets the Number of Connections
              if len(cab_data2.DataResult)>0:
                 ncon=cab_data2.DataResult[0]
                 for ncntr in range(1,ncon+1,1):
                    cab_data3=aadAssDataExtraction.DataExtraction(st+".PART("+str(npt)+").CONN("+str(ncntr)+").POINT")
                    if len(cab_data3.DataResult)>0:
                       pnt_lst=cab_data3.DataResult[0]
                       cab_pnt=KcsPoint3D.Point3D(0,0,0)
                       cab_pnt.SetCoordinates(pnt_lst[0],pnt_lst[1],pnt_lst[2])
                       break
                 cab_datax=aadAssDataExtraction.DataExtraction(st+".PART("+str(npt)+").COMP_NAME") # gets the comp_name
                 if len(cab_datax.DataResult)>0:
                    comp_nam1=cab_datax.DataResult[0]
                    stmt="COMPONENT('"+comp_nam1+"').MATERIAL"
                    cab_data4=aadAssDataExtraction.DataExtraction(stmt) # gets the Material Number
                    if len(cab_data4.DataResult)>0:
                       mat_no=cab_data4.DataResult[0]
                    stmt="COMPONENT('"+comp_nam1+"').MAN_PROPERTY.ACQUISITION_CODE"
                    cab_data5=aadAssDataExtraction.DataExtraction(stmt) # gets the Material Number
                    if len(cab_data5.DataResult)>0:
                       ac_code=cab_data5.DataResult[0]
                 cnam=nam+"-"+string.upper(str(comp_name))
                 out_data.append([cnam,cab_pnt,ac_code,mat_no,status])
                 mat_no=""
                 ac_code=""





#  Function for pipe spool data #
#-----------------------------------------------------------------------------------------#
def GetPipeSpoolData1(nam,out_data):
    import string
    proj=""
    tdm_data=""
    proj=string.upper(str(kcs_util.TB_environment_get("SB_PROJ")))
    st="PIPE('"+str(proj)+"').PIPSPOOL('"+nam+"')"
    #pip_data=aadAssDataExtraction.DataExtraction(st+".GEN_PROPERTY.MTRL_CODE")
    part_id=[]
    branch_id=[]
    npart=0
    status=""
    tdm_data=aadAssDataExtraction.DataExtraction(st+".TDM_INFORMATION.STATUS_ASSEMBLY")
    nbranch=0
    flag=0
    if len(tdm_data.DataResult) > 0:
        status=tdm_data.DataResult[0]
    #if len(pip_data.DataResult) > 0:
    mat_no = 0
    pip_data1=aadAssDataExtraction.DataExtraction(st+".BRANCH(0:999).PART("+str(part_id)+").NODPOINT(0)")
    if len(pip_data1.DataResult)>0:
        pnt_lst=pip_data1.DataResult[0]
        pnt=KcsPoint3D.Point3D(pnt_lst[0],pnt_lst[1],pnt_lst[2])

    out_data.append([nam, pnt, ac_code, mat_no, status])


# Function to get the Vent Spool Data #
#=====================================#
def GetVentSpoolData1(nam,out_data):
    st="VENT.PIPSPOOL('"+nam+"')"
    pip_data=aadAssDataExtraction.DataExtraction(st+".NMTRL")
    part_id=[]
    branch_id=[]
    npart=0
    nbranch=0
    status=""
    tdm_data=""
    tdm_data=aadAssDataExtraction.DataExtraction(st+".TDM_INFORMATION.STATUS_ASSEMBLY")
    flag=0
    if len(pip_data.DataResult)>0:
        nmtrl=pip_data.DataResult[0]
        if len(tdm_data.DataResult)>0:
           status=tdm_data.DataResult[0]
        for cnt in range(1,nmtrl+1,1):
            pip_data1=aadAssDataExtraction.DataExtraction(st+".MTRL("+str(cnt)+").NPART")
            if len(pip_data1.DataResult)>0:
                npart=pip_data1.DataResult[0]
                for ncnt in range(1,npart+1):
                    pip_data2=aadAssDataExtraction.DataExtraction(st+".MTRL("+str(cnt)+").PART_ID("+str(ncnt)+")") # Retriving the part id
                    if len(pip_data2.DataResult)>0:
                        part_id.append([pip_data2.DataResult[0],0])
        pip_data3=aadAssDataExtraction.DataExtraction(st+".NBRANCH")   #  Retriving the number of branches
        if len(pip_data3.DataResult)>0:
            nbranch=pip_data3.DataResult[0]
            for bcnt in range(1,nbranch+1,1):
                if flag==1:
                   break
                for npr in range(len(part_id)):
                    if part_id[npr][1]==0:
                        pip_data4=aadAssDataExtraction.DataExtraction(st+".BRANCH("+str(bcnt)+").PART("+str(part_id[npr][0])+").COMP_NAME")
                        if len(pip_data4.DataResult)>0:
                           comp_name=pip_data4.DataResult[0]
                           part_id[npr][1]=1
                           pip_data5=aadAssDataExtraction.DataExtraction(st+".BRANCH("+str(bcnt)+").PART("+str(part_id[npr][0])+").NNODE")
                           if len(pip_data5.DataResult)>0:
                               nnode=pip_data5.DataResult[0]
                               ##kcs_ui.message_confirm(str(nnode))
                               ##for nncnt in range(nnode+1):
                               ##pip_data6=aadAssDataExtraction.DataExtraction(st+".BRANCH("+str(bcnt)+").PART("+str(part_id[npr][0])+").NODPOINT("+str(nncnt)+")")
                               pip_data6=aadAssDataExtraction.DataExtraction(st+".BRANCH("+str(bcnt)+").PART("+str(part_id[npr][0])+").NODPOINT(0)")
                               if len(pip_data6.DataResult)>0:
                                  flag=1
                                  pnt_lst=pip_data6.DataResult[0]
                                  pnt=KcsPoint3D.Point3D(pnt_lst[0],pnt_lst[1],pnt_lst[2])
                                  ##kcs_ui.message_confirm(str(pnt.X))
                               stmt="COMPONENT('"+comp_name+"').MATERIAL"
                               pip_data7=aadAssDataExtraction.DataExtraction(stmt)
                               mat_no=0
                               if len(pip_data7.DataResult)>0:
                                   mat_no=pip_data7.DataResult[0]
                               stmt="COMPONENT('"+comp_name+"').MAN_PROPERTY.ACQUISITION_CODE"
                               pip_data8=aadAssDataExtraction.DataExtraction(stmt)
                               ac_code=0
                               if len(pip_data8.DataResult)>0:
                                   ac_code=pip_data8.DataResult[0]
                               if flag==1:
                                  ##comp_name=nam
                                  cmp_nm=nam
                                  ac_code="MF"
                                  mat_no=""
                                  out_data.append([cmp_nm,pnt,ac_code,mat_no,status])
                                  break



# Function to get the Curved Panel Data #
#=======================================#
def GetCurvedPanelData(nam,part_id,out_data):
    try:
        import KcsModel
        import KcsContour2D

        objPart = [];
        comp_name=""
        handle=""
        mat_no=""
        status=""
        ac_code="MF"
        part_lst=[]
        lst=""
        st="HULL.CPANEL('"+nam+"').PLATE(1:999).COMP_ID"
        st1="HULL.CPANEL('"+nam+"').PLATE(1:999).MNAME"
        st2="HULL.CPANEL('"+nam+"').STIFFENER(1:999).COMP_ID"
        st3="HULL.CPANEL('"+nam+"').STIFFENER(1:999).NAME"

        from win32com.client import gencache
        from win32com.client import Dispatch
        gencache.EnsureModule('{FEE7B652-EF27-11D1-A709-0000F87A9053}', 0, 1, 0)
        dex = Dispatch("TBDex.TBDex.1")

        data2=aadAssDataExtraction.DataExtraction()
        data2.ExtractDataFromDexInt(st2, dex) # Gets Stiffeners Part ID

        data=aadAssDataExtraction.DataExtraction(st) # Gets Plates Part ID
        data1=aadAssDataExtraction.DataExtraction(st1) # Gets Plates Name

        data3=aadAssDataExtraction.DataExtraction("") # Gets Stiffeners Name
        data3.ExtractDataFromDexInt(st3, dex)

        if data.DataNumber>0 and data1.DataNumber>0:
          for cntr in range(data.DataNumber):
             part_lst.append([data.DataResult[cntr],data1.DataResult[cntr], "PLATE"])

        if data2.DataNumber>0 and data3.DataNumber>0:
          for cntr in range(data2.DataNumber):
             part_lst.append([data2.DataResult[cntr],data3.DataResult[cntr], "STIFFENER"])

        for obj in part_lst:
          if obj[0]==part_id:
             objPart = obj
             break

        pnt=KcsPoint2D.Point2D()
        if len(objPart) > 0:
             out_data.append([objPart[1],pnt,ac_code,mat_no,status, objPart[2]])

    except Exception, e:
        print " aadAssExtractData.GetCurvedPanelData:" + str(e)


# Function for getting the Penetration Data #
#===========================================#
def GetPenetrationData(nam,part_id,out_data):
   comp_name=""
   ac_code=""
   mat_no=""
   status=""
   tdm_data=""
   point=KcsPoint3D.Point3D()
   proj=string.upper(str(kcs_util.TB_environment_get("SB_PROJ")))
   st="CABLE('"+str(proj)+"').CWAY('"+string.upper(nam)+"')"
   stmt="CABLE('"+str(proj)+"').CWAY('"+string.upper(nam)+"').PNTR("+str(part_id)+").NAME"
   stmt1="CABLE('"+str(proj)+"').CWAY('"+string.upper(nam)+"').PNTR("+str(part_id)+").POINT"
   data1=aadAssDataExtraction.DataExtraction(stmt)
   data2=aadAssDataExtraction.DataExtraction(stmt1)
   tdm_data=aadAssDataExtraction.DataExtraction(st+".TDM_INFORMATION.STATUS_MATERIAL")
   if len(tdm_data.DataResult)>0:
      status=tdm_data.DataResult[0]
   if len(data1.DataResult)>0 and len(data2.DataResult)>0:
      comp_name=nam+"-"+str(data1.DataResult[0])
      pnt_lst=data2.DataResult[0]
      point=KcsPoint3D.Point3D(pnt_lst[0],pnt_lst[1],pnt_lst[2])
      out_data.append([comp_name,point,ac_code,mat_no,status])



# Function to get the Cable Way Data  #
#=====================================#
def GetCableData(nam,part_id,out_data):
   comp_name=""
   name=""
   ac_code=""
   mat_no=""
   status=""
   point=KcsPoint3D.Point3D()
   proj=string.upper(str(kcs_util.TB_environment_get("SB_PROJ")))
   stmt="CABLE('"+str(proj)+"').CWAY('"+str(nam)+"')"
   data=aadAssDataExtraction.DataExtraction(stmt+".PART("+str(part_id)+").MAN_PROPERTIES.POSITION_NUMBER")
   data1=aadAssDataExtraction.DataExtraction(stmt+".PART("+str(part_id)+").CONN(1:999).POINT")
   tdm_data=""
   tdm_data=aadAssDataExtraction.DataExtraction(stmt+".TDM_INFORMATION.STATUS_MATERIAL")
   if len(tdm_data.DataResult)>0:
      status=tdm_data.DataResult[0]
   if len(data.DataResult)>0:
      name=data.DataResult[0]
   if data1.DataNumber>0:
      for cnt in range(data1.DataNumber):
         if len(data1.DataResult[cnt])>0:
            pnt_lst=data1.DataResult[cnt]
            point=KcsPoint3D.Point3D(pnt_lst[0],pnt_lst[1],pnt_lst[2])
            break
      cab_datax=aadAssDataExtraction.DataExtraction(stmt+".PART("+str(part_id)+").COMP_NAME") # gets the comp_name
      if len(cab_datax.DataResult)>0:
         comp_nam1=cab_datax.DataResult[0]
         stmt1="COMPONENT('"+comp_nam1+"').MATERIAL"
         cab_data4=aadAssDataExtraction.DataExtraction(stmt1) # gets the Material Number
         if len(cab_data4.DataResult)>0:
            mat_no=cab_data4.DataResult[0]
            stmt2="COMPONENT('"+comp_nam1+"').MAN_PROPERTY.ACQUISITION_CODE"
            cab_data5=aadAssDataExtraction.DataExtraction(stmt2) # gets the Material Number
            if len(cab_data5.DataResult)>0:
                ac_code=cab_data5.DataResult[0]
      if len(name) > 0:
          comp_name=nam+"-"+string.upper(str(name))
      else:
          comp_name=nam+"-PAR"+str(part_id);

      out_data.append([comp_name,point,ac_code,mat_no,status])





# Function to get the Key in Component Data #
#===========================================#
def GetKICData(nam,out_data):
   comp_name=""
   ac_code=""
   mat_no=""
   point=KcsPoint3D.Point3D()
   status=""
   stmt="COMPONENT('"+string.upper(str(nam))+"')"
   data=aadAssDataExtraction.DataExtraction(stmt+".NAME")
   if len(data.DataResult)>0:
      data1=aadAssDataExtraction.DataExtraction(stmt+".MATERIAL")
      if len(data1.DataResult)>0:
          mat_no=data1.DataResult[0]
      data1=aadAssDataExtraction.DataExtraction(stmt+".MAN_PROPERTY.ACQUISITION_CODE")
      if len(data1.DataResult)>0:
         ac_code=data1.DataResult[0]
   comp_name=nam
   out_data.append([comp_name,point,ac_code,mat_no,status])



# Function to get the Key in Part Data #
#===========================================#
def GetKIPData(nam,out_data):
   comp_name=""
   ac_code=""
   mat_no=""
   point=KcsPoint3D.Point3D()
   status=""
   comp_name=nam
   out_data.append([comp_name,point,ac_code,mat_no,status])

#----------------------------------------------------------------------#
# This Function gets spool name given SpoolId and pipe name#
#----------------------------------------------------------------------#

def GetSpoolName(name, part_id):
    pipename = GetModelName(name)
    proj = string.upper(kcs_util.TB_environment_get("SB_PROJ"))
    datastat1="PIP('" + proj + "').PIPM('"+string.upper(pipename) +"').PART(" + str(-abs(part_id)) + ").SPOOL_NAME"
    try:
        ex_data=aadAssDataExtraction.DataExtraction( datastat1 )
        if len(ex_data.DataResult) > 0 :
            return proj +'-'+ pipename + '-' +  ex_data.DataResult[0]
        else:
            return None
    except Exception,e:
        print "aadAssDataExtraction - GetSpoolName: ", str(e)

def GetVentSpoolName(name, part_id):
    pipename = GetModelName(name)
    proj = string.upper(kcs_util.TB_environment_get("SB_PROJ"))
    names = pipename.split("-")
    pipename = ""
    for item in range(0, len(names)):
        if names[item].find("SPOOL") == -1:
            pipename += names[item]
        pipename+= "-"
    pipename = pipename.rstrip(" -")

    datastat1="vent('" + proj + "').ventmodel('"+string.upper(pipename) +"').PART(" + str(-abs(part_id)) + ").SPOOL_NAME"
    try:
        ex_data=aadAssDataExtraction.DataExtraction( datastat1 )
        if len(ex_data.DataResult) > 0 :
            return proj +'-'+ pipename + '-' +  ex_data.DataResult[0]
        else:
            return None
    except Exception,e:
        print "aadAssDataExtraction - GetVentSpoolName: ", str(e)

#----------------------------------------------------------------------#
# The following function gets the ship side valid for part given as
# model
# Input:
#   KcsModel.Model
#
# Result:
#   0 - Port Side
#   1 - Starboard Side
#   2 - Both Sides
#   3 - the centre of the ship(???)
#----------------------------------------------------------------------#
def __GetPanelSymetry(model):
    "__GetPanelSymetry(KcsModel.Model) -> int"
    resSide = -4
    try:
        if not isinstance(model, KcsModel.Model):
            return -2
        if model.GetType() <> "plane panel":
            return -3
        statment = "HULL.PANEL('"+ model.GetName()+"').SYMMETRY"
        data=aadAssDataExtraction.DataExtraction( statment )
        if len(data.DataResult) > 0:
            resSide = data.DataResult[0]
    except Exception,e:
        print "aadAssExtractData, __GetPanelSymetry: %s"%str(e)

    return resSide

def __GetHullPartName(names, nSymmetry, nReflCode):
    strName = ''
    #panel valid for PS and SB but stored on PS
    if nSymmetry == 0:
       if nReflCode == 1:
           if len(names) > 1:
               strName = names[1]
           else:
               strName = names[0]
       else:
           strName = names[0]

    #panel valid for PS only, stored on PS
    elif nSymmetry == 1:
       strName = names[0]

    #panel valid for PS only, stored on SB
    elif nSymmetry == 11:
       if nReflCode == 1:
           strName = names[0]
       else:
           if len(names) > 1:
               strName = names[1]
           else:
               strName = names[0]

    #panel valid for SB only , stored on PS
    elif nSymmetry == 2:
       if nReflCode == 1:
           if len(names) > 1:
               strName = names[1]
           else:
               strName = names[0]
       else:
           strName = names[0]

    #panel valid for SB only, stored on SB
    elif nSymmetry == 12:
           strName = names[0]

    #panel valid for PS and SB stored on SB
    elif nSymmetry == 10:
       if nReflCode == 0:
           if len(names) > 1:
               strName = names[1]
           else:
               strName = names[0]

       else:
           strName = names[0]
    #panels in the centre line plane or panel extending over the center line,
    #having components on both sides of the centre line
    else:
       if nReflCode == 0:
           strName = names[0]
       elif len(names) > 1:
           strName = names[1]
       else:
           strName = names[0]

    return strName
