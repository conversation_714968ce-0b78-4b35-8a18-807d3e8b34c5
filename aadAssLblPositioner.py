
import kcs_draft
import KcsVector3D
import kcs_ui
import K<PERSON><PERSON><PERSON><PERSON>gle2D
import aadAssUtil
import aadAssVec2D
import KcsPolygon2D
import KcsPoint2D
import KcsPoint3D

#---------------------------------------------------------------------------------
#
#      NAME:
#
#          aadAssLblPositioner.py
#
#      PURPOSE:
#
#               This program serves to Automatically position the labels on the Drawing
#
#
#
#---------------------------------------------------------------------------------




# Please Avoid Changing the names of the class Attributes . Attributes and functions can be added

NLABEL=25 # Constant to determine No. of Labels to be displayed in a Drawing

class LblPositioner:
    def __init__(self,list,view_name,res_name,cntr=0):
        self.pnt_list=list
        self.final_list=""
        self.view_name=view_name
        self.res_name=res_name
        self.cntr=cntr


# Function that sets the initial positions of the labels #
    def SetInitPos(self):
        self.final_list=self.MakeBoundList()





# Function that sets the final positions of the labels #
    def Arrange(self):
        bool=0
        flag=0
        iind=0
        jind=0
        for i in range(len(self.final_list)-1):
            for j in range(len(self.final_list)):
                if len(self.final_list[i]) > 1 and len(self.final_list[j]) > 1:
                    iind=self.final_list[i][0]
                    jind=self.final_list[j][0]
                    inpt=KcsPoint2D.Point2D()
                    if (self.pnt_list[iind][1] <> None) and (self.pnt_list[jind][1] <> None):
                        p1=KcsPoint2D.Point2D(self.pnt_list[iind][1].X,self.pnt_list[iind][1].Y)
                        p3=KcsPoint2D.Point2D(self.pnt_list[jind][1].X,self.pnt_list[jind][1].Y)
                        try:
                            if self.IsIntersecting(inpt,p1,self.final_list[i][1],p3,self.final_list[j][1])==1:
                                self.final_list[i][0]=jind
                                self.final_list[j][0]=iind
                                bool=1
                        except Exception,e:
                            print "Error: ",e
        return bool



# Function that Plots the  labels #
    def DrawLabels(self):
        ind_list=[]
        #cntr=0
        flag=0
        xx=1
        self.SetInitPos()
        while xx!=0:
            xx=self.Arrange()
        if len(self.final_list)>0:
            for cnt in self.final_list:
               if cnt[1] == None :
                   self.cntr=self.cntr+1
                   for ctr in range(len(self.pnt_list)):
                       if self.pnt_list[ctr][0]==cnt[0]:
                           self.pnt_list[ctr][2]=1
                           break
                   ind_list.append([self.pnt_list[ctr][0],self.cntr])
                   if self.cntr%NLABEL==0:
                           break
               else:
                   for ctr in range(len(self.pnt_list)):
                       if self.pnt_list[ctr][0]==cnt[0]:
                           flag=1
                           break
                   if flag==1:
                       self.pnt_list[ctr][2]=1
                       if(self.pnt_list[ctr][1].X <> 0) and (self.pnt_list[ctr][1].Y <> 0):
                           poly=KcsPolygon2D.Polygon2D(self.pnt_list[ctr][1])
                           poly.AddPoint(cnt[1])
                           poly.AddPoint(cnt[2])
                           self.cntr=self.cntr+1
                           try:
                               kcs_draft.posno_height_set(8)
                               kcs_draft.posno_new(str(self.cntr),poly)
                           except TBException, e:
                               print "aadAssLblPositioner: Method: DrawLabels: ",e
                       ind_list.append([self.pnt_list[ctr][0],self.cntr])
                       if self.cntr%NLABEL==0:
                           break
               flag=0
        return ind_list




# Function that Checks if the lines connecting the labels intersect #
    def IsIntersecting(self,inpt,p1,p2,p3,p4):
        bool=0
        vec1=aadAssVec2D.AssVec2D()
        vec1.SetFromPoints(p1,p2)
        vecDir=aadAssVec2D.AssVec2D()
        vecDir.SetFromPoints(p1,p2)
        vec2=aadAssVec2D.AssVec2D()
        vec2.SetFromPoints(p3,p4)
        if not vec1.IsParallel(vec2):
            PointVec=aadAssVec2D.AssVec2D()
            PointVec.SetFromPoints(p1,p3)
            cross=vec1.CrossProduct(vec2)
            if(abs(cross)>1.0e-8):
               dp=PointVec.DotProduct(vec2.GetNormal())
               vecDir.BlankProduct(dp)
               alongline=vecDir.DivideByScalar(cross)
               inpt.X=p1.X-alongline.X
               inpt.Y=p1.Y-alongline.Y
               if (self.IsOnLine(inpt,p1,p2) and self.IsOnLine(inpt,p3,p4)): # This Funciton is used to check is the Intersecting point lies on the line
                  bool=1
        return bool



# Function that Gets the Distance #
    def Distance(self,p1,p2,intpt):
        temp=aadAssVec2D.AssVec2D()
        temp.SetFromPoints(p1,p2)
        norm=temp.GetNormal()
        vec=aadAssVec2D.AssVec2D()
        vec.SetFromPoints(p1,intpt)
        return(vec.DotProduct(norm))



# Function that Checks if a point is on a line #
    def IsOnLine(self,inpt,p1,p2):
        bool=0
        vec1=aadAssVec2D.AssVec2D()
        vec1.SetFromPoints(p1,p2)
        if abs(self.Distance(p1,p2,inpt))<0.01:
            len=vec1.Length()
            vstart=aadAssVec2D.AssVec2D()
            vend=aadAssVec2D.AssVec2D()
            vstart.SetFromPoints(p1,inpt)
            vend.SetFromPoints(p2,inpt)
            if vstart.Length()<len and vend.Length()<len:
                bool=1
        return bool



# Function that creates a list of points and allocates the Initial positions #
    def MakeBoundList(self):
        pt_list=[]
        rect=KcsRectangle2D.Rectangle2D()
        try:
            vid_res=kcs_draft.view_identify(self.res_name)
            rect=kcs_draft.view_restriction_area_get(vid_res)
        except TBException, e:
            print "addAssLblPositioner:  ", e
            print kcs_draft.error
        p1=KcsPoint2D.Point2D(rect.Corner1.X,rect.Corner1.Y)
        p2=KcsPoint2D.Point2D(rect.Corner2.X,rect.Corner2.Y)
        view_rect=KcsRectangle2D.Rectangle2D()
        try:
            vid=kcs_draft.view_identify(self.view_name)
            view_rect=kcs_draft.element_extent_get(vid) # Gets the Extents of a specified view
        except TBException, e:
            print "addAssLblPositioner:  ", e
            print kcs_draft.error

        pnt=aadAssUtil.GetCenter(view_rect.Corner1,view_rect.Corner2)
        lbcor=[]
        rbcor=[]
        ltcor=[]
        rtcor=[]
        for cnt in range(p1.X+5,pnt.X,15):
           po=KcsPoint2D.Point2D(cnt,p1.Y)
           po1=KcsPoint2D.Point2D(cnt,p1.Y-5)
           lbcor.append([0,po,po1])
        for cnt in range(p1.Y+5,pnt.Y,15):
           po=KcsPoint2D.Point2D(p1.X,cnt)
           po1=KcsPoint2D.Point2D(p1.X-5,cnt)
           lbcor.append([0,po,po1])
        for cnt in range(pnt.X+5,p2.X-5,15):
           po=KcsPoint2D.Point2D(cnt,p1.Y)
           po1=KcsPoint2D.Point2D(cnt,p1.Y-5)
           rbcor.append([0,po,po1])
        for cnt in range(p1.Y+5,pnt.Y,15):
           po=KcsPoint2D.Point2D(p2.X,cnt)
           po1=KcsPoint2D.Point2D(p2.X+5,cnt)
           rbcor.append([0,po,po1])
        for cnt in range(pnt.Y+10,p2.Y,15):
           po=KcsPoint2D.Point2D(p1.X,cnt)
           po1=KcsPoint2D.Point2D(p1.X-5,cnt)
           ltcor.append([0,po,po1])
        for cnt in range(p1.X+5,pnt.X,15):
           po=KcsPoint2D.Point2D(cnt,p2.Y)
           po1=KcsPoint2D.Point2D(cnt,p2.Y+5)
           ltcor.append([0,po,po1])
        for cnt in range(pnt.X+5,p2.X,15):
           po=KcsPoint2D.Point2D(cnt,p2.Y)
           po1=KcsPoint2D.Point2D(cnt,p2.Y+5)
           rtcor.append([0,po,po1])
        for cnt in range(pnt.Y+10,p2.Y,15):
           po=KcsPoint2D.Point2D(p2.X,cnt)
           po1=KcsPoint2D.Point2D(p2.X+5,cnt)
           rtcor.append([0,po,po1])
        for i in range(len(self.pnt_list)):
           if self.pnt_list[i][2]==0:
               if (self.pnt_list[i][1] <> None):
                   lst_pt=KcsPoint2D.Point2D(self.pnt_list[i][1].X,self.pnt_list[i][1].Y)
                   if len(pt_list)==NLABEL:
                       break
                   if lst_pt.X<pnt.X and lst_pt.Y<pnt.Y:
                      ind=self.GetPoint(lst_pt,lbcor)
                      if ind!=-1 :
                         pt_list.append([self.pnt_list[i][0],lbcor[ind][1],lbcor[ind][2]])
                      else:
                         ind=self.GetPoint(lst_pt,ltcor)
                         if ind!=-1 :
                            pt_list.append([self.pnt_list[i][0],ltcor[ind][1],ltcor[ind][2]])
                         else:
                            ind=self.GetPoint(lst_pt,rbcor)
                            if ind!=-1 :
                               pt_list.append([self.pnt_list[i][0],rbcor[ind][1],rbcor[ind][2]])
                            else:
                               ind=self.GetPoint(lst_pt,rtcor)
                               if ind!=-1 :
                                  pt_list.append([self.pnt_list[i][0],rtcor[ind][1],rtcor[ind][2]])
                   elif lst_pt.X>pnt.X and lst_pt.Y<pnt.Y:
                        ind=self.GetPoint(lst_pt,rbcor)
                        if ind!=-1 :
                           pt_list.append([self.pnt_list[i][0],rbcor[ind][1],rbcor[ind][2]])
                        else:
                           ind=self.GetPoint(lst_pt,rtcor)
                           if ind!=-1 :
                              pt_list.append([self.pnt_list[i][0],rtcor[ind][1],rtcor[ind][2]])
                           else:
                              ind=self.GetPoint(lst_pt,lbcor)
                              if ind!=-1 :
                                 pt_list.append([self.pnt_list[i][0],lbcor[ind][1],lbcor[ind][2]])
                              else:
                                 ind=self.GetPoint(lst_pt,ltcor)
                                 if ind!=-1 :
                                    pt_list.append([self.pnt_list[i][0],ltcor[ind][1],ltcor[ind][2]])
                   elif lst_pt.X<pnt.X and lst_pt.Y>pnt.Y:
                        ind=self.GetPoint(lst_pt,ltcor)
                        if ind!=-1 :
                           pt_list.append([self.pnt_list[i][0],ltcor[ind][1],ltcor[ind][2]])
                        else:
                           ind=self.GetPoint(lst_pt,rtcor)
                           if ind!=-1 :
                              pt_list.append([self.pnt_list[i][0],rtcor[ind][1],rtcor[ind][2]])
                           else:
                              ind=self.GetPoint(lst_pt,lbcor)
                              if ind!=-1 :
                                 pt_list.append([self.pnt_list[i][0],lbcor[ind][1],lbcor[ind][2]])
                              else:
                                 ind=self.GetPoint(lst_pt,rbcor)
                                 if ind!=-1 :
                                    pt_list.append([self.pnt_list[i][0],rbcor[ind][1],rbcor[ind][2]])
                   elif lst_pt.X>=pnt.X and lst_pt.Y>=pnt.Y:
                        ind=self.GetPoint(lst_pt,rtcor)
                        if ind!=-1 :
                           pt_list.append([self.pnt_list[i][0],rtcor[ind][1],rtcor[ind][2]])
                        else:
                           ind=self.GetPoint(lst_pt,rbcor)
                           if ind!=-1 :
                              pt_list.append([self.pnt_list[i][0],rbcor[ind][1],rbcor[ind][2]])
                           else:
                              ind=self.GetPoint(lst_pt,ltcor)
                              if ind!=-1 :
                                 pt_list.append([self.pnt_list[i][0],ltcor[ind][1],ltcor[ind][2]])
                              else:
                                 ind=self.GetPoint(lst_pt,lbcor)
                                 if ind!=-1 :
                                    pt_list.append([self.pnt_list[i][0],lbcor[ind][1],lbcor[ind][2]])
               else:
                   lst_pt = None
                   if len(pt_list)==NLABEL:
                       break
                   pt_list.append([self.pnt_list[i][0], None, None])

        return pt_list





# Function that checks the point which is min distant from the point to be labelled#
    def GetPoint(self,pt,lst):
        cnt=0
        min_dist=[]
        temp=0
        for i in range(len(lst)):
           if lst[i][0]==0:
              if cnt==0:
                 temp=pt.DistanceToPoint(lst[i][1])
                 min_dist.append([temp,i])
                 cnt==1
              else:
                 if temp>pt.DistanceToPoint(lst[i][1]):
                    temp=pt.DistanceToPoint(lst[i][1])
                    min_dist[0][0]=temp
                    min_dist[0][1]=i
        if len(min_dist)>0:
           x=min_dist[0][1]
           lst[x][0]=1
           return x
        else:
           return -1
