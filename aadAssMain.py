
import kcs_draft
import aadAssemblyModel
import kcs_assembly
import aadAssView
import KcsDocumentReference
import aadAssDrawing
import aadAssUtil
import aadAssLblPositioner
import aadAssVec2D
import KcsStringlist
import aadAssExtractData
import KcsModelDrawAssyCriteria
import kcs_ui
import kcs_util
import KcsVector3D
import KcsPoint2D
import string
import math

DATABANK = 'SB_ASSINSTR'
#---------------------------------------------------------------------------------
#
#      NAME:
#
#          aadAssMain.py
#
#      PURPOSE:
#
#               This program serve to create automatic assembly drawings
#
#
#
#---------------------------------------------------------------------------------






# Constants #
HULL=0
STRUCT=1
EQUIPMENT=2
PIPE=3
VENT=4
CABLE=5
MISC=6
ASS=7
DWG_FRM_TEMPLATE="ASSY01"

# Predefined Lists of Drawing Names and Jobs #
Dwg_Names=["_A01","_A02","_A03","_A04","_A05","_A06","_A07","_A00"]
JobList=["Install Structural Steel","Install OutFit Steel","Install Equipment","Install Pipe Systems","Install Vent Systems","Install Electrical Systems","Install Misc. material","Assembly"]


#================================================= #
# Main Function for creating Assembly Drawings     #
# Input : Object of the ModelDrawAssyCriteria Class#
#=================================================#
def AssemblyMain(object=None):
    MODELS_EXIST=0
    KEYIN_EXIST=0
    assname=None
    obj=""
    res=None
    viewlist=[]
    res_view=[]
    models_net=[]
    models_key_in=[]
    models_normal=[]
    dwglist=[]
    KIdwglist=[]
    netlist=[]
    if object==None:
        res=kcs_ui.string_req("Enter The Name of an Assembly","Unknown")
        if res[0]==kcs_util.ok():
           assname=string.upper(res[1])
        else:
           print "No Assembly Name Entered "
    else:
        obj=object
        assname=string.upper(obj.GetAssemblyName())

    if kcs_assembly.assembly_exist("-"+assname):
        try:
            res=kcs_assembly.assembly_sub_get("-"+assname)
        except:
            print "Module : AssemblyMain \n Error : "
            print kcs_assembly.error
        try:
            models_key_in=kcs_assembly.assembly_keyin_ref_get("-"+assname)
            KEYIN_EXIST=(len(models_key_in)>0)
            models_normal=kcs_assembly.assembly_model_ref_get("-"+assname)
            MODELS_EXIST=(len(models_normal)>0)
            models_net=models_normal+models_key_in
        except:
            print "Module : AssemblyMain \n Error : "
            print kcs_assembly.error
        if len(res)>0 or len(models_net)>0:
              if kcs_draft.dwg_current()==1:
                  kcs_draft.dwg_close()
              try:
                  kcs_draft.dwg_new("UNTIT",DWG_FRM_TEMPLATE, DATABANK)
              except:
                  print "Module : AssemblyMain \n Error :"
                  print kcs_draft.error
                  return
              res_view=GetViewAreaNames()   # Gets the Names of the View areas Defined
              if len(res_view)<=0:
                 print "Error: View Area Restrictions Not Defined in Form"
                 kcs_draft.dwg_close()
                 return
              ass=aadAssemblyModel.CAss(assname,1)
              #GetDwgNames(ass)
              if MODELS_EXIST or ((not MODELS_EXIST) and (not KEYIN_EXIST)):
                  DrawAssemblies(ass,viewlist,res_view)
                  DrawModels(ass,viewlist,res_view)
                  ChangeLineType(viewlist)
                  dwglist=CreateDwgList(ass,viewlist,res_view)
                  CreateAssDrawings(dwglist)
              if KEYIN_EXIST:
                 KIdwglist=CreateKIDwgList(ass)
                 CreateKIAssDrawings(KIdwglist)
              netlist=dwglist+KIdwglist
              AddDwgRef(netlist,assname)
        else:
              print "Info : Sub Assembly and parts do not exits"
    else:
              print "Info :Assembly non existent"




# =====================================#
# Function to Draw  Models of Assembly #
# =====================================#
def DrawModels(ass,viewlist,res_view):
    if ass.Parts_Exist>0:
        if len(viewlist)<=0:
            CreateViewList(viewlist,res_view)
        for view in viewlist:
            try:
                view.OrientView(ass.Orientation) # Orient the View as per the Orientation Assembly Orientation .
                model = KcsModelDrawAssyCriteria.ModelDrawAssyCriteria(ass.GetName())
                kcs_draft.model_draw(model,view.GetViewId())
                view.PositionView()
                view.ScaleView()
            except TBException, e:
                print "aadAssMain(DrawModels): ", e
            except Exception, e:
                print "aadAssMain(DrawModels): ", e




#=================================================#
# Function to create View List for the Drawings   #
#=================================================#
def CreateViewList(viewlist,res_view):
    U_ELE=KcsVector3D.Vector3D(0,0,0)
    V_ELE=KcsVector3D.Vector3D(0,0,0)
    U_ISO=KcsVector3D.Vector3D((1/math.sqrt(2)),(-1/math.sqrt(2)),0)
    V_ISO=KcsVector3D.Vector3D((1/math.sqrt(6)),(1/math.sqrt(6)),(2/math.sqrt(6)))
    view1=aadAssView.CView("ISO_VIEW","ISO",res_view[0],U_ISO,V_ISO)
    U_PLAN=KcsVector3D.Vector3D(1,0,0)
    V_PLAN=KcsVector3D.Vector3D(0,1,0)
    view2=aadAssView.CView("PLAN_VIEW","PLAN",res_view[1],U_PLAN,V_PLAN)
    st=KcsStringlist.Stringlist("U,W")
    st.AddString("-U,W")
    st.AddString("V,W")
    st.AddString("-V,W")
    res=kcs_ui.choice_select("Side Elevation View","Select an Orientation",st)
    if res[0]==271:
       if res[1]==1: ## u,w
          U_ELE.SetComponents(1,0,0)
          V_ELE.SetComponents(0,0,1)
       elif res[1]==2: ## -u,w
          U_ELE.SetComponents(-1,0,0)
          V_ELE.SetComponents(0,0,1)
       elif res[1]==3: ## v,w
          U_ELE.SetComponents(0,1,0)
          V_ELE.SetComponents(0,0,1)
       elif res[1]==4: ## -v,w
          U_ELE.SetComponents(-1,0,0)
          V_ELE.SetComponents(0,0,1)
    else:
       U_ELE.SetComponents(1,0,0)
       V_ELE.SetComponents(0,1,0)
    view3=aadAssView.CView("ELE_VIEW","ELE",res_view[2],U_ELE,V_ELE)
    viewlist.append(view1)
    viewlist.append(view2)
    viewlist.append(view3)
    for view in viewlist:
        view.CreateView()



#=========================================#
# Function to Draw the Sub Assemblies     #
#=========================================#
def DrawAssemblies(ass,viewlist,res_view):
    subasslist=None
    assembly=None
    subass=None
    try:
        assembly=KcsModelDrawAssyCriteria.ModelDrawAssyCriteria(ass.GetName())
    except:
        print "Module : DrawAssemblies \n Error : "
        print KcsModelDrawAssyCriteria.error

    if ass.SubAss_Exist<=0:
        return
    if len(viewlist)<=0:
       CreateViewList(viewlist,res_view)
    for view in viewlist:
        try:
            kcs_draft.model_draw(assembly,view.GetViewId())
        except:
            print "Module : DrawAssemblies \n Error : "
            print kcs_draft.error
    for view in viewlist:
        view.OrientView(ass.Orientation) # Orient the View as per the Orientation Assembly Orientation .
        view.PositionView()
        view.ScaleView()
        view.RemoveLines()





#===================================================================#
# Function to creates the Drawing lists containing Drawing Objects  #
#===================================================================#
def CreateDwgList(ass,viewlist,res_view):
    hulllist=ass.GetHullList()
    pipelist=ass.GetPipeList()
    cablelist=ass.GetCableList()
    ventlist=ass.GetVentList()
    equipmentlist=ass.GetEquipmentList()
    structlist=ass.GetStructList()
    dwglist=[]
    if len(hulllist)>0:
        dwg=aadAssDrawing.CDwg(Dwg_Names[HULL]+ass.GetIntName(),JobList[HULL],hulllist,ass.PropList,viewlist[0].Name,res_view[0],viewlist)
        dwg.DwgNew()
        dwglist.append(dwg)
    if len(pipelist)>0:
        dwg=aadAssDrawing.CDwg(Dwg_Names[PIPE]+ass.GetIntName(),JobList[PIPE],pipelist,ass.PropList,viewlist[0].Name,res_view[0],viewlist)
        dwg.DwgNew()
        dwglist.append(dwg)
    if len(cablelist)>0:
        dwg=aadAssDrawing.CDwg(Dwg_Names[CABLE]+ass.GetIntName(),JobList[CABLE],cablelist,ass.PropList,viewlist[0].Name,res_view[0],viewlist)
        dwg.DwgNew()
        dwglist.append(dwg)
    if len(ventlist)>0:
        dwg=aadAssDrawing.CDwg(Dwg_Names[VENT]+ass.GetIntName(),JobList[VENT],ventlist,ass.PropList,viewlist[0].Name,res_view[0],viewlist,1)
        dwg.DwgNew()
        dwglist.append(dwg)
    if len(equipmentlist)>0:
        dwg=aadAssDrawing.CDwg(Dwg_Names[EQUIPMENT]+ass.GetIntName(),JobList[EQUIPMENT],equipmentlist,ass.PropList,viewlist[0].Name,res_view[0],viewlist)
        dwg.DwgNew()
        dwglist.append(dwg)
    if len(structlist)>0:
        dwg=aadAssDrawing.CDwg(Dwg_Names[STRUCT]+ass.GetIntName(),JobList[STRUCT],structlist,ass.PropList,viewlist[0].Name,res_view[0],viewlist)
        dwg.DwgNew()
        dwglist.append(dwg)
    if ass.Parts_Exist<=0:
        dwg=aadAssDrawing.CDwg("_A00"+ass.GetIntName(),"Assembly Installation",[],ass.PropList,viewlist[0].Name,res_view[0],viewlist)
        dwg.DwgNew()
        dwglist.append(dwg)

    kcs_draft.dwg_close()
    return dwglist




#=================================================================================#
# Function to creates the Key In Parts Drawing lists containing Drawing Objects   #
#   New Function Added For Key In Items                                           #
#=================================================================================#
def CreateKIDwgList(ass):
    KIlist=[]
    dwglist=[]
    KIlist=ass.GetKIList()
    if len(KIlist)>0:
        dwg=aadAssDrawing.CDwg(Dwg_Names[MISC]+ass.GetIntName(),JobList[MISC],KIlist,ass.PropList,"","",[])
        dwg.DwgNew(1)
        dwglist.append(dwg)
        dwg.FillFields()
        dwg.CreateSubDwg()
        dwg.FillKIPartsList()
        dwg.DwgSave()
        dwg.DwgClose()
    kcs_draft.dwg_close()
    return dwglist






#==================================================================#
# Function that iterates thro all the drawings and Fills the Data #
#==================================================================#
def CreateAssDrawings(dwglist):
        for dwg in dwglist:
            dwg.DwgOpen()
            dwg.FillFields()
            dwg.HighlightModel()
            dwg.CreateSubDwg()
            dwg.FillPartsList()
            dwg.DwgSave()
            dwg.DwgClose()





#==================================================================#
# Function that iterates thro all the drawings and Fills the Data  #
#   New Function Added For Key In Items                            #
#==================================================================#
def CreateKIAssDrawings(dwglist):
        for dwg in dwglist:
            dwg.DwgOpen()
            dwg.FillFields()
            dwg.CreateSubDwg()
            dwg.FillKIPartsList()
            dwg.DwgSave()
            dwg.DwgClose()



#==================================================================#
# Function That Changes the Line type of the Models in the Drawing #
#==================================================================#
def ChangeLineType(viewlist):
    nInd=1
    name=""
    flag=0
    vhandle=kcs_draft.element_child_first_get()
    vlist=[]
    for obj in viewlist:
        vlist.append(obj.Name)
    while nInd:
          try:
              name=kcs_draft.subpicture_name_get(vhandle)
              if name in vlist:
                  flag=1
                  GetSubView(vhandle,flag)
              else:
                  flag=0

              vhandle=kcs_draft.element_sibling_next_get(vhandle)
              nInd=nInd+1
          except:
              nInd=0



#=========================================================#
# Function That Gets the Sub Views in the Current Drawing #
#========================================================#
def GetSubView(handle,flag=0):
    import KcsLinetype
    nInd=1
    name=""
    viewname=""
    lin=KcsLinetype.Linetype("Dashed")
    try:
        viewhandle=kcs_draft.element_child_first_get(handle)
    except:
        return
    while nInd:
           try:
                if kcs_draft.element_is_subview(viewhandle):
                   try:
                       name=kcs_draft.subpicture_name_get(viewhandle)
                       if flag==1:
                           kcs_draft.element_linetype_set(viewhandle,lin)
                   except:
                       print kcs_draft.error
                viewhandle=kcs_draft.element_sibling_next_get(viewhandle)
                nInd=nInd+1
           except:
               nInd=0






#===============================================================#
# Function Used to show the names of the Drawings to be created #
#===============================================================#
def GetDwgNames(ass):
    nam_lst=[]
    st=""
    if ass.Parts_Exist<=0:
        nam_lst.append(string.upper(Dwg_Names[ASS]+ass.GetIntName()))
    if len(ass.StructList)>0:
        nam_lst.append(string.upper(Dwg_Names[STRUCT]+ass.GetIntName()))
    if len(ass.PipList)>0:
        nam_lst.append(string.upper(Dwg_Names[PIPE]+ass.GetIntName()))
    if len(ass.EquipList)>0:
        nam_lst.append(string.upper(Dwg_Names[EQUIPMENT]+ass.GetIntName()))
    if len(ass.CabList)>0:
        nam_lst.append(string.upper(Dwg_Names[CABLE]+ass.GetIntName()))
    if len(ass.VentList)>0:
        nam_lst.append(string.upper(Dwg_Names[VENT]+ass.GetIntName()))
    if len(ass.HpList)>0:
        nam_lst.append(string.upper(Dwg_Names[HULL]+ass.GetIntName()))
    if len(ass.KIList)>0:
        nam_lst.append(string.upper(Dwg_Names[MISC]+ass.GetIntName()))
    if len(nam_lst)>0:
        st=st+"Following Assembly Drawings will be created :\n"
        for i in range(len(nam_lst)):
            if i==0:
                st=st+nam_lst[i]+"\n"
            else:
                st=st+nam_lst[i]+"\n"
        kcs_ui.message_confirm(st)





#===================================================#
# Function that returns the view area names defined #
#===================================================#
def GetViewAreaNames():
    name=""
    area_lst=[]
    view=None
    cntr=0
    try:
        view=kcs_draft.element_child_first_get()
    except:
        return area_lst
    while 1:
        try:
            cntr=cntr+1
            name=kcs_draft.subpicture_name_get(view)
            if len(name)>0 and cntr>1 and cntr<=4:
                area_lst.append(name)
        except:
            print kcs_draft.error
        try:
            view=kcs_draft.element_sibling_next_get(view)
        except:
            break
    return area_lst




#====================================================================================#
# Function that Adds References of Drawings Created to the Respective Assembly      #
#===================================================================================#
def AddDwgRef(dwglist,assname):
    subdwglst=[]
    list=[]
    dwg_exist = 0
    for obj in dwglist:
        subdwglst.append(obj.Name)
        if len(obj.SubDwgLst)>0:
            for ob in obj.SubDwgLst:
                subdwglst.append(ob)
    try:
        kcs_assembly.assembly_activate("-"+assname)
        list=kcs_assembly.document_reference_get()
        if len(list)>0:
            kcs_assembly.assembly_save()
            kcs_assembly.assembly_activate("-"+assname)
        for obj in list:
             if obj.GetPurpose() == kcs_draft.kcsDWGTYPE_ASS_INSTR:
                 kcs_assembly.document_reference_remove(obj)
                 try:
                    for doc in subdwglst:
                       if doc == obj.GetDocument():
                          dwg_exist = 1
                    if not dwg_exist:
                       kcs_draft.dwg_delete(obj.GetDocument(), kcs_draft.kcsDWGTYPE_ASS_INSTR)
                 except TBException,e:
                    print e

        for obj in subdwglst:
            doc=KcsDocumentReference.DocumentReference()
            doc.SetType("drawing")
            doc.SetPurpose(kcs_draft.kcsDWGTYPE_ASS_INSTR)
            doc.SetDocument(obj)


            kcs_assembly.document_reference_add(doc)
        kcs_assembly.assembly_save()
    except:
        kcs_assembly.assembly_cancel()
        print "Module : AddDwgRef \n Error: "+kcs_assembly.error

#===================================================#
# The Main for creating Automatic Assembly Drawings #
#===================================================#

if __name__ == "__main__":
   import kcs_dex
   result = kcs_dex.extract("vent.help")
   from sys import argv
   try:
       assembly_name=kcs_assembly.assembly_path_name_get(argv[1])
       assembly_name=assembly_name[1:len(assembly_name)]
       assembly=KcsModelDrawAssyCriteria.ModelDrawAssyCriteria(assembly_name)
       AssemblyMain(assembly)
       kcs_util.exit_program()
   except Exception, e:
       print "AssMain: ",e
