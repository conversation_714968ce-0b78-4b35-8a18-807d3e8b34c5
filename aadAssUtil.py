import string
import kcs_draft
import operator
import time
import math
import KcsPoint2D
import KcsPoint3D
import kcs_ui
import kcs_util



#---------------------------------------------------------------------------------
#
#      NAME:
#
#          AssUtil.py
#
#      PURPOSE:
#
#               This File contains utility functions
#
#
#
#---------------------------------------------------------------------------------


class AssException:
    def __init__(self,Module="",Error=""):
        self.Module=Module
        self.Error=Error


    def __repr__(self):
        return "Module : "+self.Module+"\n Error : "+self.Error





# Calculates the difference between two days and returns back the days in between                   #
#----------------------------------------------------------------------------------------------------#
def DateDiff(date1,date2):
    ONE_DAY=60*60*24
    days=0
    if len(date1)>=3 and len(date2)>=3:
        dat1=time.mktime(date1)
        dat2=time.mktime(date2)
        diff=(dat2-dat1)
        days=(abs(diff)/ONE_DAY)
    return days



#----------------------------------------------------------------------------------------------------#
# function returns the current Date
def GetDate():
    date=time.strftime("%d %b %Y ", time.gmtime(time.time()))
    return date

#----------------------------------------------------------------------------------------------------#
# Split the Model name in Different parts Assuming that the delimiter is '-'
def SplitModelName(nam,out_str):
    pnam=kcs_util.TB_environment_get("SB_PROJ")
    pnam=string.upper(pnam)
    st=nam
    st1=""
    cnt=0
    start=0
    for i in range(len(st)):
        n=string.find(st,"-")
        if n>-1:
           cnt=cnt+1
           st1=st[start:n]
           out_str.append(st1)
           st=st[n+1:]
        else:
           out_str.append(st)
           break

#----------------------------------------------------------------------------------------------------#
# This Function Removes the project name from the model name if its present
def GetModelName(nam):
    pnam=kcs_util.TB_environment_get("SB_PROJ")
    pnam=string.upper(pnam)
    st=nam
    st1=""
    if string.find(nam,pnam)>-1:
       n=string.find(st,"-")
       st1=st[n+1:]
       return st1
    else:
       return st



#----------------------------------------------------------------------------------------------------#
# A Utility function to get the center of two points
def GetCenter(p1,p2):
   cent=KcsPoint2D.Point2D(0,0)
   x=p1.X+(abs(p1.X-p2.X)/2)
   y=p1.Y+(abs(p1.Y-p2.Y)/2)
   cent.SetCoordinates(x,y)
   return cent


#----------------------------------------------------------------------------------------------------#
# Checks for a Duplicate Value in the List
def Chk_Dup(lst,st):
    flag=0
    if len(lst)>0:
        for i in range(len(lst)):
            if string.find(lst[i],st)!=-1:
               flag=1
               break
    return flag




#----------------------------------------------------------------------------------------------------#
# Converts list of 3D points to 2D by transforming them on the basis of the view handle passed
def ConvertPointsTo2D(view_id,pnt_lst):
   pntlst=[]
   for i in range(len(pnt_lst)):
      pt2D=KcsPoint2D.Point2D(0,0)
      pt3D=KcsPoint3D.Point3D(0,0,0)
      pt3D.SetCoordinates(pnt_lst[i][1][0],pnt_lst[i][1][1],pnt_lst[i][1][2])
      pp=kcs_draft.point_transform(view_id,pt3D,pt2D)
      pntlst.append([pnt_lst[i][0],pt2D.X,pt2D.Y,pnt_lst[i][2],pnt_lst[i][3]])
   return pntlst

