import KcsVector2D
import math
import aadAssUtil
#---------------------------------------------------------------------------------
#
#      NAME:
#
#          AssVec2D.py
#
#      PURPOSE:
#
#               This file contains class which can represent a Vector in 2D with some Extra functionality
#
#
#
#---------------------------------------------------------------------------------

# A Vector class derived from KcsVector2D with Additional methods #
# New methods Added
# GetNormal() --- gets Normal to a Vector
# CrossProduct() --- Computes the Cross Product of self and another vector
# IsParallel() ---- Checks if a vector is Parallel to another
# DividebyScalar() ---- Function that Divides a Vector by a Scalar to give another Vector


class AssVec2D(KcsVector2D.Vector2D):
    def __init__(self,X=-32000.0,Y=-32000.0):
        KcsVector2D.Vector2D.__init__(self,X,Y)


    def GetNormal(self):
        temp=KcsVector2D.Vector2D()
        temp.X=-self.Y
        temp.Y=self.X
        return temp


    def CrossProduct(self,vec):
        return (self.X*vec.Y-self.Y*vec.X)

    def IsParallel(self,vec):
        cosval=self.CrossProduct(vec)
        return (abs(cosval)<=1.0e-8)

    def DivideByScalar(self,scal):
        temp=KcsVector2D.Vector2D()
        temp.X=(self.X/scal)
        temp.Y=(self.Y/scal)
        return temp
