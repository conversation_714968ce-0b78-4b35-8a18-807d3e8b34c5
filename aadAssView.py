import kcs_draft
import aadAssUtil
import math
import kcs_ui
import K<PERSON><PERSON><PERSON>2D
import KcsPoint3D
import K<PERSON><PERSON>ransformation3D
import KcsRectangle2D
import KcsVector2D
import KcsVector3D



#---------------------------------------------------------------------------------
#
#      NAME:
#
#          aadAssView.py
#
#      PURPOSE:
#
#               This program serve to create views in the Drawing form
#
#
#
#---------------------------------------------------------------------------------

# The Following  File Contains class and member functions for  View Objects in Auto Assembly Drawings #
# The class Contains
# View name:- self.Name
# Type of view :- self.Type
# Handle to the View :- self.View_Id
# View vectors :- self.U , self.V , self.W
# Scale Factor :- self.ScaleFactor

# The Following are the view restrictions for the each and every view #
# These are Currently Hard Coded but will change when the Views are Created while Defining the Drawing Form #

#P1_ISO=KcsPoint2D.Point2D(74.9701843262+30,121.455833435+30)
#P2_ISO=KcsPoint2D.Point2D(299.428070068-30,258.914520264-30)
#P1_ELE=KcsPoint2D.Point2D(182.869293213+10,17.0568141937+10)
#P2_ELE=KcsPoint2D.Point2D(299.448181152-10,122.035827637-10)
#P1_PLAN=KcsPoint2D.Point2D(74.97+10,17.05+10)
#P2_PLAN=KcsPoint2D.Point2D(182.84-10,122.03-10)


class CView:
    def __init__(self,name="",type="",res_name="",u=0,v=0):
        self.Name=name
        self.view_res_name=res_name
        self.Type=type
        self.View_Id=0
        self.U=u
        self.V=v
        self.W=self.__CrossProduct(self.U,self.V)
        self.ScaleFactor=0
        #vid_res=kcs_draft.view_identify(self.view_res_name)
        #rect=KcsRectangle2D.Rectangle2D()
        #rect=kcs_draft.view_restriction_area_get(vid_res)
        #if self.Type=="ISO":
        #    self.P1_ISO=KcsPoint2D.Point2D(rect.Corner1.X+30,rect.Corner1.Y+30)
        #    self.P2_ISO=KcsPoint2D.Point2D(rect.Corner2.X-30,rect.Corner2.Y-30)
        #elif self.Type=="ELE":
        #    self.P1_ELE=KcsPoint2D.Point2D(rect.Corner1.X+10,rect.Corner1.Y+10)
        #    self.P2_ELE=KcsPoint2D.Point2D(rect.Corner2.X-10,rect.Corner2.Y-10)
        #elif self.Type=="PLAN":
        #    self.P1_PLAN=KcsPoint2D.Point2D(rect.Corner1.X+10,rect.Corner1.Y+10)
        #    self.P2_PLAN=KcsPoint2D.Point2D(rect.Corner2.X-10,rect.Corner2.Y-10)




# Function that uses the the supplied two vectors and calculates the third #
# Parameters : two vectors representing a view plane #
#===================================================================================#
    def __CrossProduct(self,vec1,vec2):
        vec3=KcsVector3D.Vector3D()
        vec3.X=((vec1.Y*vec2.Z)-(vec2.Y*vec1.Z))
        vec3.Y=((vec1.X*vec2.Z)-(vec2.X*vec1.Z))
        vec3.Z=((vec1.X*vec2.Y)-(vec2.X*vec1.Y))
        return vec3





# Function that Calculates the Scale Factor#
# Parameters None #
#===================================================================================#
    def __CalcScaleFactor(self):
        view_rect=KcsRectangle2D.Rectangle2D()
        view_rect=kcs_draft.element_extent_get(self.View_Id)
        vht=abs(view_rect.Corner1.Y-view_rect.Corner2.Y)
        vwdt=abs(view_rect.Corner1.X-view_rect.Corner2.X)
        vid_res=kcs_draft.view_identify(self.view_res_name)
        rect=KcsRectangle2D.Rectangle2D()
        rect=kcs_draft.view_restriction_area_get(vid_res)
        aht=abs(rect.Corner1.Y-rect.Corner2.Y)
        awdt=abs(rect.Corner1.X-rect.Corner2.X)
        #if self.Type=="ISO":
        #    aht=abs(self.P1_ISO.Y-self.P2_ISO.Y)
        #    awdt=abs(self.P1_ISO.X-self.P2_ISO.X)
        #elif self.Type=="ELE":
        #    aht=abs(self.P1_ELE.Y-self.P2_ELE.Y)
        #    awdt=abs(self.P1_ELE.X-self.P2_ELE.X)
        #elif self.Type=="PLAN":
        #    aht=abs(self.P1_PLAN.Y-self.P2_PLAN.Y)
        #    awdt=abs(self.P1_PLAN.X-self.P2_PLAN.X)
        if (awdt/aht)>(vwdt/vht):
            scale_factor=abs(aht/vht)
        else:
            scale_factor=abs(awdt/vwdt)
        self.ScaleFactor=scale_factor




# Function that Creates a new view #
# Parameters are the name and the type of the view #
#===================================================================================#
    def CreateView(self,name="",type=""):
        if len(name)>0:
            self.Name=name
        if len(type)>0:
            self.Type=type
        if self.Type=="ISO":
           try:
               self.View_Id=kcs_draft.view_new(self.Name,self.U,self.V)
           except:
               print "Module : CreateView \n Error : "+kcs_draft.error
        elif self.Type=="PLAN":
           try:
               self.View_Id=kcs_draft.view_new(self.Name,self.U,self.V)
           except:
               print "Module : CreateView \n Error : "+kcs_draft.error
        elif self.Type=="ELE":
           try:
               self.View_Id=kcs_draft.view_new(self.Name,self.U,self.V)
           except:
               print "Module : CreateView \n Error : "+kcs_draft.error
        else:
           try:
               self.View_Id=kcs_draft.view_new(self.Name,self.U,self.V)
           except:
               print "Module : CreateView \n Error : "+kcs_draft.error


# Function that Sets Vector Planes for the View Creation #
# Parameters are two vectors for making a view plane #
#===================================================================================#
    def SetViewVector(self,u,v):
        self.U=u
        self.V=v
        self.W=self.__CrossProduct(self.U,self.V)



# Function that Positions the view  #
# Parameters None #
#===================================================================================#
    def PositionView(self):
        view_rect=KcsRectangle2D.Rectangle2D()
        view_rect=kcs_draft.element_extent_get(self.View_Id)
        acenter=self.__CalcViewAreaCenter()
        vcenter=aadAssUtil.GetCenter(view_rect.Corner1,view_rect.Corner2)
        dv=KcsVector2D.Vector2D((acenter.X-vcenter.X),(acenter.Y-vcenter.Y))
        try:
            kcs_draft.view_move(self.View_Id,dv)
        except:
            print "Module : PostionView \n Error :"+kcs_draft.error


# Function that Scales the Created View Based on the calculated Scale Factor  #
# Parameters None #
#===================================================================================#
    def ScaleView(self):
        center=self.__CalcViewAreaCenter()
        self.__CalcScaleFactor()
        try:
            kcs_draft.view_scale(self.View_Id,self.ScaleFactor,center)
        except:
            print "Module : ScaleView \n Error :"+kcs_draft.error
            kcs_ui.message_noconfirm("Module : ScaleView \n Error :"+kcs_draft.error)




# Function that returns the handle to the Created View #
# Parameters None #
#===================================================================================#
    def GetViewId(self):
        return self.View_Id





# Function that Calculates the View Area Center #
# Parameters None #
#===================================================================================#
    def __CalcViewAreaCenter(self):
        #if self.Type=="ISO":
        #    center=AssUtil.GetCenter(self.P1_ISO,self.P2_ISO)
        #elif self.Type=="ELE":
        #    center=AssUtil.GetCenter(self.P1_ELE,self.P2_ELE)
        #elif self.Type=="PLAN":
        #    center=AssUtil.GetCenter(self.P1_PLAN,self.P2_PLAN)
        vid_res=kcs_draft.view_identify(self.view_res_name)
        rect=KcsRectangle2D.Rectangle2D()
        rect=kcs_draft.view_restriction_area_get(vid_res)
        center=aadAssUtil.GetCenter(rect.Corner1,rect.Corner2)
        return center



# Function that Removes the Hidden Lines in the View #
# Parameters None #
#===================================================================================#
    def  RemoveLines(self):
        try:
            kcs_draft.view_hl_remove(self.View_Id)
        except:
            print "Module : RemoveLines \n Error : "+kcs_draft.error




# Function used to Orient a particular view according to the Transformation Matrix Specified #
# Input:
#      Object of Transformation3D
#============================================================================================#
    def OrientView(self,AssOrientation):
        trans3D=KcsTransformation3D.Transformation3D()
        lst=[]
        M=[[0,0,0,0],[0,0,0,0],[0,0,0,0],[0,0,0,0]]
        try:
            kcs_draft.view_projection_get(self.View_Id,trans3D) # Gets the View Projection for the View
            trans3D.Invert()
            trans3D.Combine(AssOrientation)
            trans3D.GetByRow(type,M)
            for obj in range(len(M)):
                data=M[obj]
                vec=KcsVector3D.Vector3D(data[0],data[1],data[2])
                lst.append(vec)
        except:
            print "Module : OrientView \n Error :"+kcs_draft.error
        try:
            kcs_draft.view_projection_set(self.View_Id,lst[0],lst[1]) # Sets the View Projection after changing.
        except:
            print "Module : OrientView \n Error : "+kcs_draft.error





