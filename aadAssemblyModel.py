
import KcsModel
import KcsCommonProperties
import kcs_ui
import kcs_draft
import aadAssUtil
import kcs_assembly
import KcsAssembly
import KcsTransformation3D
import KcsDate
import kcs_util
import kcs_model
import string
import aadAssExtractData
#---------------------------------------------------------------------------------
#
#      NAME:
#
#          aadAssemblyModel.py
#
#      PURPOSE:
#
#               This program serve to store the Assembly Related Data
#
#
#
#---------------------------------------------------------------------------------


# The Value of Attribute Super Denotes whether the Assembly is Super or Sub Assembly #
# if its 1 its a Super Assembly else if 0 sub assembly #
class CAss:

    delimit=["=","#"]

    def __init__(self,name="",Super=1):
        self.__Name=string.upper(name)
        self.Super=Super
        intname=""
        if len(name)>0:
            try:
                intname=kcs_assembly.assembly_internal_name_get("-"+self.__Name)
            except:
                print "Error in getting internal name "+kcs_assembly.error
        self.PropList=[]
        self.__IntName=string.upper(intname)
        self.__SubAss=[]  # Sub Assembly list
        self.Parts_Exist=0
        self.SubAss_Exist=0
        self.PipList=[] # pipe  model list
        self.StructList=[] # Struct model list
        self.EquipList=[] # Equipment list
        self.VentList=[] #  Vent  model list
        self.CabList=[]   # Cable Model List
        self.HpList=[]    # Hull Panel Model List
        self.KIList=[]   # Key In Components List
        self.Orientation = KcsTransformation3D.Transformation3D() # Orientation Matrix for assembly
        if self.Super==1:
            self.__SetSubAssList()
            self.__SetAssPropList()
            self.__SetModelList()
            self.__SetKIList()
            self.__SortHullModels()







# This Function Sets the Assembly Property List with Properties #
#===============================================================#
    def __SetAssPropList(self):
        Description=""
        PlanningUnit=""
        OrientationMatrix=KcsTransformation3D.Transformation3D()
        dat1=""
        dat2=""
        Type=""
        WorkingLocation=""
        Destination=""
        PlannedStartDate=""
        PlannedEndDate=""
        PlannedDuration=""
        CostCode = self.__GetCostCode()
        days=""
        ActualStartDate=""
        ActualEndDate=""
        object=None
        proj=""
        proj=kcs_util.TB_environment_get("SB_PROJ")
        self.PropList.append(string.upper(proj))
        self.PropList.append(string.upper(self.GetRootParent()))
        self.PropList.append(string.upper(self.__RemovePathFromName()))
        try:
           object=kcs_assembly.assembly_properties_get("-"+self.__Name)
        except:
           print "Module : __SetAssPropList \n Error : "+kcs_assembly.error
        if object:
           Description=str(object.GetDescription())
           PlanningUnit=str(object.GetPlanningUnit())
           Type=str(object.GetType())
           WorkingLocation=str(object.GetWorkingLocation())
           Destination=str(object.GetDestination())
           dat1=object.GetPlannedStartDate()
           dat2=object.GetPlannedEndDate()
           self.Orientation = object.GetOrientationMatrix()
           if dat1 and dat2:
               date1=dat1.GetDate()
               date2=dat2.GetDate()
               startdate=[date1[0],date1[1],date1[2],0,0,0,0,0,0]
               enddate=[date2[0],date2[1],date2[2],0,0,0,0,0,0]
               PlannedStartDate=str(date1[0])+"-"+str(date1[1])+"-"+str(date1[2])
               PlannedEndDate=str(date2[0])+"-"+str(date2[1])+"-"+str(date2[2])
               days=aadAssUtil.DateDiff(startdate,enddate)
               PlannedDuration=str(days)
           ActualStartDate=str(object.GetActualStartDate())
           ActualEndDate=str(object.GetActualEndDate())
        self.PropList.append(str(Description))
        self.PropList.append(str(PlanningUnit))
        self.PropList.append(str(Type))
        self.PropList.append(str(WorkingLocation))
        self.PropList.append(str(Destination))
        self.PropList.append(str(PlannedStartDate))
        self.PropList.append(str(PlannedEndDate))
        self.PropList.append(str(PlannedDuration))
        self.PropList.append(str(ActualStartDate))
        self.PropList.append(str(ActualEndDate))
        self.PropList.append(str(CostCode))



# A utility function that removes the full path and just gives the name of node #
#==========================================================================#
    def __RemovePathFromName(self):
        name=self.__Name
        names=""
        names=string.split(name,"-")
        if len(names)>0:
            name=names[len(names)-1]
        return name









# This Function Creates a List of SubAssemblies #
#===============================================#
    def __SetSubAssList(self):
        subass=""
        st=""
        try:
            subass=kcs_assembly.assembly_sub_get("-"+self.__Name)
        except:
            print "Module : __SetSubAssList \n Error : "+kcs_assembly.error
        if len(subass)>0:
            self.SubAss_Exist=len(subass)
            for sass in subass:
                st=sass[0]
                #self.__SubAss.append(CAss(st[1:len(st)],0))
                self.__SubAss.append(st[1:len(st)])




# This Function Converts the Assembly Convention Model Names to Ordinary Model Names #
#====================================================================================#
    def __GetIntModelName(self,mname):
        names=None
        name=""
        for d in CAss.delimit:
            if string.find(mname,d)!=-1:
                names=string.split(mname,d)
                break
        if names:
            for n in range(len(names)-1):
                if n==0:
                   name=name+names[n]+"-"
                else:
                   name=name+names[n]+"-"
            name=name+names[len(names)-1]
        else:
            name=mname
        return name





# Check if the Model Belongs to a Pipe Spool or Vent Spool #
#==========================================================#
    def __CheckVentSpool(self,name):
        import aadAssDataExtraction
        proj=""
        ind=-1
        newname=""
        proj=string.upper(kcs_util.TB_environment_get("SB_PROJ"))
        ind=string.find(name,string.upper(proj))
        if ind>-1:
            newname=string.strip(name[ind+len(proj)+1:])
        else:
            newname=string.strip(name)
        stmt="VENT('"+string.upper(proj)+"').PIPSPOOL('"+string.upper(newname)+"'*).NAME"
        data=aadAssDataExtraction.DataExtraction(stmt)
        if data.DataNumber>0:
            return 1
        else:
            return 0


# Checks if the Data is for a Vent or Pipe part #
    def __CheckVent(self,name):
        import aadAssDataExtraction
        proj=""
        ind=-1
        newname=""
        proj=string.upper(kcs_util.TB_environment_get("SB_PROJ"))
        ind=string.find(name,string.upper(proj))
        if ind>-1:
            newname=string.strip(name[ind+len(proj)+1:])
        else:
            newname=string.strip(name)
        stmt="VENT('"+string.upper(proj)+"').VENTMODEL('"+string.upper(newname)+"'*).NAME"
        data=aadAssDataExtraction.DataExtraction(stmt)
        if data.DataNumber>0:
            return 1
        else:
            return 0




#================================================#
# Creates a Model List for the current assembly #
#==============================================#
    def __SetModelList(self):
        usedIDs = []
        models=""
        try:
            models=kcs_assembly.assembly_model_ref_get("-"+self.__Name)
        except:
            print "Module : __SetModelList \n Error :"+kcs_assembly.error
        name=""
        type=""
        lastmodel=None
        flag=0
        if len(models)>0:
            self.Parts_Exist=len(models)
            for mod in models:
                name=self.__GetIntModelName(mod.GetName())
                type=mod.GetType()
                if type == 'pipe' and mod.PartId > 0:
                    spname = aadAssExtractData.GetSpoolName(name, mod.PartId)
                    if spname:
                        type = 'pipe spool'
                        name  = spname

                if type=='ventilation' and mod.PartId > 0:
                    spname = aadAssExtractData.GetVentSpoolName(name, mod.PartId)
                    if spname:
                        type = 'vent spool'
                        name  = spname

                model=KcsModel.Model(type,name)
                model.PartId = mod.PartId
                model.ReflCode = mod.ReflCode
                if type=="pipe" or type=='ventilation':
                    if self.__CheckVent(model.GetName()):
                        self.VentList.append(model)
                    else:
                        self.PipList.append(model)
                elif type=="pipe spool":
                    self.PipList.append(model)
                elif type=="vent spool":
                    self.VentList.append(model)
                elif type=="equipment":
                    self.EquipList.append(model)
                elif type=="struct":
                    self.StructList.append(model)
                elif type=="plane panel" or type=="curved panel":
                    if(mod.GetPartType() == "cutout" or mod.GetPartType() == "clip"):
                        lst = []
                        aadAssExtractData.GetClipData(mod, lst)
                        for item in lst:
                            self.HpList.append(item)
                    else:
                        self.HpList.append(mod)
                elif type=="cable way":
                    self.CabList.append(model)


#=======================================================================#
# Creates a Key In components and parts  List for the current assembly #
#======================================================================#
    def __SetKIList(self):
        keyins=""
        try:
            keyins=kcs_assembly.assembly_keyin_ref_get("-"+self.__Name)
        except:
            print "Module : __SetKIList \n Error :"+str(kcs_assembly.error)
        if len(keyins)>0:
            self.Parts_Exist=self.Parts_Exist+len(keyins)
            for keyin in keyins:
                self.KIList.append(keyin)






# Returns Pipe List for Assembly #
#================================#
    def GetPipeList(self):
        return self.PipList


# Returns Equipment List for Assembly #
#=====================================#
    def GetEquipmentList(self):
        return self.EquipList


# Returns Cable List for Assembly #
#=================================#
    def GetCableList(self):
        return self.CabList



# Returns Hull List for Assembly #
#================================#
    def GetHullList(self):
        return self.HpList


# Returns Struct List for Assembly #
#================================#
    def GetStructList(self):
        return self.StructList


# Returns Vent Spool List for Assembly #
#================================#
    def GetVentList(self):
        return self.VentList


# Returns Key-in parts  List for Assembly #
#=========================================#
    def GetKIList(self):
        return self.KIList





# Returns the name of the Assembly #
#==================================#
    def GetName(self):
        return self.__Name


# Returns the Internal name of the Assembly #
#==================================#
    def GetIntName(self):
        return self.__IntName






# This Function returns the Path Name of the Parent of the current Assembly #
# ========================================================================= #
    def GetParent(self):
        pname=""
        try:
            res=kcs_assembly.assembly_parent_get("-"+self.__Name)
            pname=res[0]
        except:
            print " Module : GetParent \n Error : "+kcs_assembly.error
        return pname





# This Function returns the Internal Name of the Parent of the current Assembly #
# ============================================================================= #
    def GetParentInt(self):
        pname=""
        try:
            res=kcs_assembly.assembly_parent_get("-"+self.__Name)
            pname=res[1]
        except:
            print " Module : GetParentInt \n Error : "+kcs_assembly.error
        return pname

# This Function returns the top most root parent of the current assembly #
#========================================================================#
    def GetRootParent(self):
        rpname=""
        pname=""
        status=1
        name=""
        names=""
        pos=-1
        cname=self.__Name
        # The Code Below is for kcs_assembly library which return entire path of the assembly without the current assembly
        try:
            names=kcs_assembly.assembly_parent_get("-"+cname)
        except:
            print "Module : GetRootParent \n Error : "+kcs_assembly.error
        if len(names)>0:
            name=names[0][1:]
        pos=string.find(name,"-")
        if pos>0:
            rpname=name[:pos]
        else:
            rpname=name
        # The Code Below is for kcs_assembly library which return immidiate parent
        #while status:
        #    try:
        #        pname=kcs_assembly.assembly_parent_get("-"+cname)
        #        cname=pname[0]
        #        rpname=pname[0]
        #    except:
        #        print " Module : GetRootParent \n Error : "+kcs_assembly.error
        #        status=0
        return rpname






# This function returns the List of Sub Assemblies for the Current Assembly #
# ==========================================================================#
    def GetSubAssemblies(self):
        return self.__SubAss

# This function sorts the huyll models so that those models with out an     #
# orientation point go on the end of list
# ==========================================================================#
    def __SortHullModels(self):
        CplList = []
        HullList = []
        CPLbgn = []
        CPLend = []
        for item in self.HpList:
            if (item.GetPartType() == 'cutout' or item.GetPartType() == 'clip'):
                CplList.append(item)
            else:
                HullList.append(item)
        self.HpList =  []
        self.HpList = HullList
        for model in CplList:
            if hasattr(model, "AaidCOG"):
                if model.AaidCOG == None :
                    CPLend.append(model)
                else:
                    CPLbgn.append(model)
        self.HpList += CPLbgn
        self.HpList += CPLend

    def __GetCostCode(self):
        try:
            m = KcsModel.Model('assembly',self.__Name)
            tdm   = KcsCommonProperties.CommonProperties()
            if(tdm):
                return tdm.GetCostCode()
        except Exception,e:
            print "aadAssemblyModel: __GetCostCode Error: " + str(e)
        return ''
