MACRO,DEX;
   DEC000000000000LARE,EQNAME,STRING;
   ASSIGN,J,1;
   ASSIGN,EMPTY_OBJ,'          ';
   RANGE,RNG/FILENAME='USER5:[CTSNYH.SZ]RANGE.DAT';
   RANGE,NDR/FILENAME='USER5:[CTSNYH.SZ]NDRAW.DAT';
   ASSIGN,STR,
	'EQUIP.ITEM(RNG).DRAWING(NDR).USAGECODE';
EXTRACTION,E,STR;
   ASSIGN,STR,'EQUIP.ITEM(RNG).DRAWING(NDR).POSITION';
   EXTRACTION,F,STR;
   ASSIGN,STR,'EQUIP.ITEM(RNG).DRAWING(NDR).NAME';
   EXTRACTION,G,STR;
   ASSIGN,HEAD,
	' OBJECT   I    USAGE   NAME     POSNO';
ASSIGN,EMPTY,'             ';
TABLE,TAB/ARGUMENTS=(HEAD)
   /FORMAT=(-60,)
   /NOAPPEND;
TABLE,TAB/ARGUMENTS=(EMPTY)
   /FORMAT=(-60,);
ASSIGN,EMPTY,'';
   GET/RANGE=(PROJECT,E);
!
!  Loop over different projects
!
   LOOP,PROJ,PROJECT;
      GET/RANGE=(RNG2,E,PROJ);
!
!     Loop over different objects
!
      LOOP,EQNAME,RNG2;
         ASSIGN,OBJNAME,EQNAME;
         GET/RANGE=(NDR2,E,PROJ,EQNAME);
!
!        Loop over different drawings
!
         LOOP,I,NDR2;
	    GET/EXTRACT=(USAGE,E,PROJ,EQNAME,I,)
      /EXTRACT=(NAME,G,PROJ,EQNAME,I,)
      /EXTRACT=(POSNO,F,PROJ,EQNAME,I,);
   IF,J == 1;
      TABLE,TAB
      /ARGUMENTS=(EQNAME,I,USAGE,NAME,POSNO)
      /FORMAT=(-10,,1,0,8,0,10,,10,);
      ASSIGN,J,2;
   ELSE;
      TABLE,TAB
      /ARGUMENTS=(OBJNAME,I,USAGE,NAME,POSNO)
      /FORMAT=(-10,,1,0,8,0,10,,10,);
   ENDIF;
   ASSIGN,OBJNAME,EMPTY_OBJ;
ENDLOOP;
ENDLOOP;
ENDLOOP;
PRESENT,TAB;
ENDMACRO;