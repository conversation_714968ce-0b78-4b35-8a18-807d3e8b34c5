#-*- encoding:mbcs -*-
# import sys   #reload()之前必须要引入模
# reload(sys)
# sys.setdefaultencoding("mbcs")
import KcsCaptureRegion2D
import KcsRectangle2D

import wingdbstub

import kcs_model

import kcs_ui
import kcs_util
import KcsStringlist
import kcs_struct
import KcsContour2D
import kcs_draft
import KcsModel
import KcsPoint2D
import KcsRline2D
import sys
import kcs_ui
import kcs_util
import kcs_draft
from KcsPoint2D               import Point2D
from KcsCursorType            import CursorType, CursorTypes
from KcsStat_point2D_req      import Stat_point2D_req, Point2dDefModes
import KcsSymbol
import KcsTransformation2D
import kcs_ic
from KcsButtonState           import ButtonState

import kcs_dex

# 网络请求与json处理
import urllib2
import simplejson as json

serverAddress = "http://*************:9999/"

kcs_ui.message_noconfirm("Sssssst" )

OK = kcs_util.ok()
CANCEL  = kcs_util.cancel()


import time
import socket
import datetime
import traceback 
import fnmatch

#-------------------------------------------------------------------------
# 工具方法
#-------------------------------------------------------------------------
def point2D_reqGetStatus():
    status = Stat_point2D_req()
    status.SetDefMode('ModeCursor')
    curType = CursorType()
    curType.SetCrossHair()
    status.SetCursorType(curType)
    helpPoint = KcsPoint2D.Point2D(0.0, 0.0)
    status.SetHelpPoint(helpPoint)
    return status

def point2D_reqGetButton():
    buttons = ButtonState()
    buttons.LockEnabled = 0
    buttons.OptionsEnabled = 1
    buttons.LockChecked = None
    return buttons

#-------------------------------------------------------------------------
# 获取坐标附件的钢结构件
#-------------------------------------------------------------------------
def getNearByStructureByPoint(pt, model):
    model.SetType("struct")
    kcs_draft.model_identify(pt, model)

#-------------------------------------------------------------------------
# 抽取数据
#-------------------------------------------------------------------------
def dex_extract(extract_query):
    """封装kcs_dex.extract的数据提取公共方法
    
    Args:
        extract_query (str): 数据提取语句
        
    Returns:
        list: 包含字典格式的结果列表，每个字典包含：
            - data_type: 数据类型代码
            - value: 对应类型的值
            - vector_size: 向量维度（仅vector_nD类型需要）
        
    Raises:
        kcs_ArgumentError: 无效的提取语句
    """
    try:
        kcs_dex.extract(extract_query)
    except:
        kcs_ui.message_noconfirm(kcs_struct.error)
    
    results = []
    result_type = kcs_dex.next_result()
    
    while result_type >= 0:
        value = None
        vector_size = None
        
        # 根据文档定义处理所有数据类型
        if result_type == 0:    # 空树部分
            value = "Empty tree part"
        elif result_type == 1:  # 整型
            value = kcs_dex.get_int()
        elif result_type == 2:  # 实数值
            value = kcs_dex.get_real()
        elif result_type == 3:  # 字符串
            value = kcs_dex.get_string()
        elif result_type == 4:  # 3D向量
            value = kcs_dex.get_reavec3d()
        elif result_type == 5:  # 盒子类型
            value = kcs_dex.get_box()
        elif result_type == 6:  # 2D向量
            value = kcs_dex.get_reavec2d()
        elif result_type > 10:  # nD向量
            vector_size = result_type - 10
            value = [kcs_dex.get_indexedreal(i) for i in range(vector_size)]
        else:                   # 未定义类型
            value = "Unsupported data type: {result_type}"
        
        results.append({
            'data_type': result_type,
            'value': value,
            'vector_size': vector_size  # 仅vector_nD有效
        })
        result_type = kcs_dex.next_result()
    
    return results

#-------------------------------------------------------------------------
# 获取结构件全部信息
#-------------------------------------------------------------------------
def get_structure_info(structure_name):
    """
    通过structure_name获取结构件全部信息
    """
    # 返回结构格式为json字符串格式，包括以下信息
    # 1. 结构件名称
    # 2. component名称
    # 3. component类型
    # 4. component的volume名称
    # 5. volume的连接点坐标
    # 6. 结构件的坐标
    # 7. 结构件的旋转
    # 8. 结构件的路由
    # 9. 结构件所属Module名称
    info = {}
    info['structureName'] = structure_name
    info['componentName'] = dex_extract("STRU.ITEM('" + structure_name + "').GRO(1).PART_ID(1).COMP_N")[0].get('value')    
    info['volumeName'] = dex_extract("COMP('" + info['componentName'] + "').VOL(1).NAME")[0].get('value')
    info['poi'] = dex_extract("STR.ITEM('" + structure_name + "').GRO(1).PART_ID(1).POI")[0].get('value')
    info['rot'] = dex_extract("STR.ITEM('" + structure_name + "').GRO(1).PART_ID(1).ROT")[0].get('value')
    info['rou'] = dex_extract("STR.ITEM('" + structure_name + "').GRO(1).PART_ID(1).ROU")[0].get('value')
    info['cog'] = dex_extract("STR.ITEM('" + structure_name + "').GRO(1).PART_ID(1).COG")[0].get('value')
    return info

#-------------------------------------------------------------------------
# 获取所有Structure数据
#-------------------------------------------------------------------------
def get_all_structure_info(module, structure_name):
    """ 
    获取所有匹配指定名称的Structure数据（支持*通配符）
    :param module: 模块名称
    :param structure_name: 结构件名称（支持*通配符）
    :return: 包含所有匹配结构件信息的列表
    """
    import fnmatch  # 导入通配符匹配模块
    
    # 获取总数量
    total_count = dex_extract("STRU.MODULE('" + module + "').NITEM")[0].get('value')
    kcs_ui.message_noconfirm("模块 '" + module + "' 中共有 " + str(total_count) + " 个结构件")
    
    results = []  # 存储匹配结果
    match_count = 0  # 匹配计数器
    
    # 遍历所有结构件
    for i in range(1, total_count + 1):
        # 获取当前结构件名称
        current_structure_name = dex_extract("STRU.MODULE('" + module + "').ITEM(" + str(i) + ").NAME")[0].get('value')
        
        # 检查名称是否匹配（支持通配符*）
        if fnmatch.fnmatch(current_structure_name, structure_name):
            # 获取当前结构件的完整信息[1](@ref)[2](@ref)
            try:
                info = get_structure_info(current_structure_name)  # 修正参数传递
                results.append(info)
            except Exception, e:
                error_msg = "?? Exception occurred:\n"
                error_msg += "? Type: " + str(type(e)) + "\n"
                error_msg += "? Message: " + str(e) + "\n"
                kcs_ui.message_noconfirm(error_msg)
                kcs_ui.message_noconfirm(current_structure_name + " 的结构信息获取失败！！！")
                # match_count += 1
                continue
            match_count += 1
            kcs_ui.message_noconfirm("已获取 " + current_structure_name + " 的结构信息")
    
    kcs_ui.message_noconfirm("共找到 " + str(match_count) + " 个匹配 '" + structure_name + "' 的结构件")
    return results


