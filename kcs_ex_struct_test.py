#-*- encoding:mbcs -*-
# import sys   #reload()之前必须要引入模
# reload(sys)
# sys.setdefaultencoding("mbcs")
import KcsCaptureRegion2D
import KcsRectangle2D

import wingdbstub

import kcs_model

import kcs_ui
import kcs_util
import KcsStringlist
import kcs_struct
import KcsContour2D
import kcs_draft
import KcsModel
import KcsPoint2D
import KcsRline2D
import sys
import kcs_ui
import kcs_util
import kcs_draft
from KcsPoint2D               import Point2D
from KcsCursorType            import CursorType, CursorTypes
from KcsStat_point2D_req      import Stat_point2D_req, Point2dDefModes
import KcsSymbol
import KcsTransformation2D
import kcs_ic
from KcsButtonState           import ButtonState

import kcs_dex

# 网络请求与json处理
import urllib2
import simplejson as json
from kcs_ex_struct_draw import CreateNode

serverAddress = "http://*************:9999/"

kcs_ui.message_noconfirm("Sssssst" )

OK = kcs_util.ok()
CANCEL  = kcs_util.cancel()
#------------------------------------------------------------------------------------
#   配置点位请求
#------------------------------------------------------------------------------------
def CreatePoint2DReqStatus(defmode="ModeEvent", cursortype="CrossHair", highlight=None):
   status = Stat_point2D_req()
   status.SetDefMode(defmode)

   startpoint = Point2D()
   CurType = CursorType()
   if cursortype == 'CrossHair':
      CurType.SetCrossHair()
   elif cursortype == 'RubberBand':
      CurType.SetRubberBand(startpoint)
   elif cursortype == 'RubberRectangle':
      CurType.SetRubberRectangle(startpoint)
   elif cursortype == 'RubberCircle':
      CurType.SetRubberCircle(startpoint)
   elif cursortype == 'DragCursor':
      CurType.SetDragCursor( highlight, startpoint )

   status.SetCursorType(CurType)


   return status

#------------------------------------------------------------------------------------
#   Select structure part
#------------------------------------------------------------------------------------
def SelectPart():
   pt = KcsPoint2D.Point2D()
   kcs_ui.message_noconfirm('2.1')
   statusD = CreatePoint2DReqStatus()
   kcs_ui.message_noconfirm('2')
   kcs_ui.point2D_req("Indicate cableway part", pt, statusD)

   model = KcsModel.Model()
   # kcs_draft.model_properties_get(handle, model)
   # kcs_ui.message_debug("Detected %s ' %s' " % (model.Type, model.Name))
   # kcs_ui.message_noconfirm("Detected %s ' %s' " % (model.Type, model.Name))
   try:
      kcs_draft.model_identify(pt, model)
      if model.Type == "struct":
         kcs_ui.message_debug("Detected Type is %s , name is %s, partId is %s " % (model.Type, model.Name, model.PartId))
         kcs_ui.message_noconfirm(
            "Detected Type is %s , name is %s, partId is %s " % (model.Type, model.Name, model.PartId))
         # print model.Name, model.PartId
         # return model.PartId
         return pt
      else:
         # print "Selected model is not struct"
         kcs_ui.message_noconfirm("Selected model is not struct" )
         raise 0
   except:
      print kcs_ui.error
      print sys.exc_info()[1]


#------------------------------------------------------------------------------------
#   Select structure part and return model and point
#------------------------------------------------------------------------------------
def SelectPartReturnModel():
   pt = KcsPoint2D.Point2D()
   kcs_ui.message_noconfirm('2.1')
   statusD = CreatePoint2DReqStatus()
   kcs_ui.message_noconfirm('2')
   kcs_ui.point2D_req("Indicate cableway part", pt, statusD)

   model = KcsModel.Model()
   # kcs_draft.model_properties_get(handle, model)
   # kcs_ui.message_debug("Detected %s ' %s' " % (model.Type, model.Name))
   # kcs_ui.message_noconfirm("Detected %s ' %s' " % (model.Type, model.Name))
   try:
      kcs_draft.model_identify(pt, model)
      if model.Type == "struct":
         kcs_ui.message_debug("Detected Type is %s , name is %s, partId is %s " % (model.Type, model.Name, model.PartId))
         kcs_ui.message_noconfirm(
            "Detected Type is %s , name is %s, partId is %s " % (model.Type, model.Name, model.PartId))
         # print model.Name, model.PartId
         # return model.PartId
         return model, pt
      else:
         # print "Selected model is not struct"
         kcs_ui.message_noconfirm("Selected model is not struct" )
         raise 0
   except:
      print kcs_ui.error
      print sys.exc_info()[1]

#-------------------------------------------------------------------------
# 工具方法
#-------------------------------------------------------------------------
def point2D_reqGetStatus():
    status = Stat_point2D_req()
    status.SetDefMode('ModeCursor')
    curType = CursorType()
    curType.SetCrossHair()
    status.SetCursorType(curType)
    helpPoint = KcsPoint2D.Point2D(0.0, 0.0)
    status.SetHelpPoint(helpPoint)
    return status

def point2D_reqGetButton():
    buttons = ButtonState()
    buttons.LockEnabled = 0
    buttons.OptionsEnabled = 1
    buttons.LockChecked = None
    return buttons

# 获取坐标附件的钢结构件
def getNearByStructureByPoint(pt, model):
    model.SetType("struct")
    kcs_draft.model_identify(pt, model)

#抽取数据
def dex_extract(extract_query):
    """封装kcs_dex.extract的数据提取公共方法
    
    Args:
        extract_query (str): 数据提取语句
        
    Returns:
        list: 包含字典格式的结果列表，每个字典包含：
            - data_type: 数据类型代码
            - value: 对应类型的值
            - vector_size: 向量维度（仅vector_nD类型需要）
        
    Raises:
        kcs_ArgumentError: 无效的提取语句
    """
    try:
        kcs_dex.extract(extract_query)
    except:
        kcs_ui.message_noconfirm(kcs_struct.error)
    
    results = []
    result_type = kcs_dex.next_result()
    
    while result_type >= 0:
        value = None
        vector_size = None
        
        # 根据文档定义处理所有数据类型
        if result_type == 0:    # 空树部分
            value = "Empty tree part"
        elif result_type == 1:  # 整型
            value = kcs_dex.get_int()
        elif result_type == 2:  # 实数值
            value = kcs_dex.get_real()
        elif result_type == 3:  # 字符串
            value = kcs_dex.get_string()
        elif result_type == 4:  # 3D向量
            value = kcs_dex.get_reavec3d()
        elif result_type == 5:  # 盒子类型
            value = kcs_dex.get_box()
        elif result_type == 6:  # 2D向量
            value = kcs_dex.get_reavec2d()
        elif result_type > 10:  # nD向量
            vector_size = result_type - 10
            value = [kcs_dex.get_indexedreal(i) for i in range(vector_size)]
        else:                   # 未定义类型
            value = "Unsupported data type: {result_type}"
        
        results.append({
            'data_type': result_type,
            'value': value,
            'vector_size': vector_size  # 仅vector_nD有效
        })
        result_type = kcs_dex.next_result()
    
    return results

#获取全部信息
def get_all_info(structure_name):
    """
    通过structure_name获取结构件全部信息
    """
    # 返回结构格式为json字符串格式，包括以下信息
    # 1. 结构件名称
    # 2. component名称
    # 3. component类型
    # 4. component的volume名称
    # 5. volume的连接点坐标
    # 6. 结构件的坐标
    # 7. 结构件的旋转
    # 8. 结构件的路由
    # 9. 结构件所属Module名称
    info = {}
    info['structureName'] = structure_name
    info['componentName'] = dex_extract("STRU.ITEM('" + structure_name + "').GRO(1).PART_ID(1).COMP_N")[0].get('value')    
    info['volumeName'] = dex_extract("COMP('" + info['componentName'] + "').VOL(1).NAME")[0].get('value')
    info['poi'] = dex_extract("STR.ITEM('" + structure_name + "').GRO(1).PART_ID(1).POI")[0].get('value')
    info['rot'] = dex_extract("STR.ITEM('" + structure_name + "').GRO(1).PART_ID(1).ROT")[0].get('value')
    info['rou'] = dex_extract("STR.ITEM('" + structure_name + "').GRO(1).PART_ID(1).ROU")[0].get('value')

    return info

#获取所有Structure数据
def GetAllStructureData():
    """
    获取所有Structure数据
    """
    # 获取总数量
    total_count = dex_extract("STRU.MODULE('ECA').NITEM")[0].get('value')
    kcs_ui.message_noconfirm("总数量: " + str(total_count))
    # 获取所有Structure数据
    for i in range(1, total_count + 1):
        # 获取结构件名称
        structure_name = dex_extract("STRU.MODULE('*').ITEM(" + str(i) + ").NAME")[0].get('value')
        # 获取结构件全部信息
        info = get_all_info(structure_name)
        kcs_ui.message_noconfirm(info)


#-------------------------------------------------------------------------
# 创建路径
#-------------------------------------------------------------------------
def CreateRouter():
   try:
      kcs_ui.message_noconfirm('1')
      id1 = SelectPart()
      id2 = SelectPart()
      kcs_ui.message_noconfirm('1.1')
      kcs_ui.message_noconfirm(id1.X)
      sp = KcsPoint2D.Point2D(id1.X, id1.Y)
      ep = KcsPoint2D.Point2D(id2.X, id2.Y)
      kcs_ui.message_noconfirm('4')
      line = KcsRline2D.Rline2D(sp, ep)
      kcs_ui.message_noconfirm(kcs_draft.dwg_current())

      kcs_draft.line_new(line)

   except:
      kcs_ui.message_noconfirm(kcs_struct.error)
      print kcs_struct.error


def PresentTransformation(transf):
   try:
      type = 0
      matrix = [ [0,0,0], [0,0,0], [0,0,0] ]
      transf.GetByRow(type, matrix)

      properties = KcsStringlist.Stringlist('----------------- Element transformation -----------------------')
      properties.AddString('')
      properties.AddString(str(matrix[0]))
      properties.AddString(str(matrix[1]))
      properties.AddString(str(matrix[2]))
      properties.AddString('')
      properties.AddString('Transformation Type: ' + str(type))
      properties.AddString('')
      properties.AddString('decompose:')
      properties.AddString('scale: '+ str(transf.GetScale()))
      properties.AddString('XY shear: ' + str(transf.GetXYShear()))
      properties.AddString('XY translation: ' + str(transf.GetTranslation()))
      properties.AddString('rotation: ' + str(transf.GetRotation()))
      properties.AddString('reflection: ' + str(transf.GetReflection()))
   except:
      print sys.exc_info()[1]
      print KcsStringlist.error

   try:
      kcs_ui.string_select('View Projection', '', '', properties)
   except:
      print sys.exc_info()[1]
      print kcs_ui.error

   return


#-------------------------------------------------------------------------
# 网络请求 - 上行, 数据传送云
#-------------------------------------------------------------------------
def DataUpload():
   global serverAddress
   try:
      kcs_ui.message_noconfirm('begin to upload data')
      model, pt = SelectPartReturnModel()
      eqType = ""
      kcs_ui.message_confirm("Model data extracting ... ' %s'! " % model.GetName())
      if model.Name.startswith("EQ"):
        web_url = serverAddress + "erupt-api/data/modify/Struct"
        eqType = "EQ"
      else:
        web_url = serverAddress + "erupt-api/data/modify/Struct"
        eqType = "Channel"
      # 数据抽取
      #dexStr = "STRU.ITEM('" + ModelName +  "').GROUP(1)."
      
      data = {
         "name": model.Name,
         "partId": model.PartId,
         "partType": model.PartType,
         "type": model.Type,
         "eqType": eqType,
         "coordinateX": pt.X,
         "coordinateY": pt.Y
      }
      
      # 获取模型坐标属
    #   ehandle = kcs_draft.element_identify(model.Name)
    #   transf = KcsTransformation2D.Transformation2D()
    #   kcs_draft.element_transformation_get(ehandle, transf)
    #   PresentTransformation(transf)
      
      # dataList = []
      # dataList.append(data1)
      # dataList = json.dumps(dataList)
      # projNo = "H1550"
      # blockNo = "107"
      # data = {
      #    "name": projNo,
      #    "type": blockNo,
      #    "data": dataList
      # }
      data = json.dumps(data)
      #
      headers = {'Content-Type': 'application/json', 'APPID': 'SuperAppId', 'erupt': 'Struct'}
      req = urllib2.Request(web_url, data, headers)
      response = urllib2.urlopen(req).read()
      response = json.loads(response)
    #   print response["message"]

   except:
      kcs_ui.message_noconfirm(ex)
      kcs_ui.message_noconfirm(kcs_struct.error)


def drawLineFromPoints(points):
    """
    Draws lines connecting a series of points.
    This function takes a list of points, where each point is a dictionary with 'X' and 'Y' coordinates,
    and draws lines connecting these points in sequence.
    Args:
        points (list of dict): A list of dictionaries, each containing 'X' and 'Y' keys representing the coordinates of a point.
    Returns:
        None
    Raises:
        None
    Example:
        points = [{'X': 0, 'Y': 0}, {'X': 1, 'Y': 1}, {'X': 2, 'Y': 2}]
        drawLineFromPoints(points)
    """
    try:
        if not points:
            kcs_ui.message_noconfirm('No points provided.')
            return

        previous_point = None
        for point in points:
            current_point = KcsPoint2D.Point2D(point['X'], point['Y'])
            if previous_point is not None:
                line = KcsRline2D.Rline2D(previous_point, current_point)
                kcs_draft.line_new(line)
            previous_point = current_point

        kcs_ui.message_noconfirm('Lines drawn successfully.')

    except:
        kcs_ui.message_noconfirm(kcs_struct.error)
        # print kcs_struct.error

def GetDataAndDrawLine():
    try:
        # Select start and end parts
        start_model, start_pt = SelectPartReturnModel()
        if not start_model.Name.startswith("EQ"):
            kcs_ui.message_noconfirm("Please select a valid equipment starting with 'EQ' for the start part.")
            return

        end_model, end_pt = SelectPartReturnModel()
        if not end_model.Name.startswith("EQ"):
            kcs_ui.message_noconfirm("Please select a valid equipment starting with 'EQ' for the end part.")
            return

        # Prepare data with start and end equipment names
        data = {
            "startEquipment": start_model.Name,
            "endEquipment": end_model.Name
        }

        web_url = serverAddress + "rest/getPoints"
        data = json.dumps(data)
        headers = {'Content-Type': 'application/json', 'APPID': 'SuperAppId', 'erupt': 'Struct'}
        req = urllib2.Request(web_url, data, headers)
        response = urllib2.urlopen(req).read()
        kcs_ui.message_noconfirm("Response received from server: " + response)
        points = json.loads(response)
        
        if points:
            drawLineFromPoints(points)
        else:
            kcs_ui.message_noconfirm('No points received from server.')

    except:
        kcs_ui.message_confirm('Error in getting points: ' + kcs_struct.error  )
        # print 'Error in getting points:', ex
       
      
#-------------------------------------------------------------------------
# 网络请求 - 下行，接收云端数
#-------------------------------------------------------------------------
def DataDownStream():
   try:
      kcs_ui.message_noconfirm('begin to get data')
      #model = SelectPartReturnModel()
      #kcs_ui.message_confirm("Model data extracting ... ' %s'! " % model.GetName())
      # web_url = "http://************:9999/erupt-api/data/modify/Struct"
      web_url = serverAddress + "erupt-api/data/table/Struct"
      data = {"pageIndex":1,"pageSize":10,"sort":None,"condition":[]}

      data = json.dumps(data)
      #
      headers = {'Content-Type': 'application/json', 'APPID': 'SuperAppId', 'erupt': 'Struct'}
      req = urllib2.Request(web_url, data, headers)
      response = urllib2.urlopen(req).read()
      response = json.loads(response)
      kcs_ui.message_noconfirm("below is response.get list")
      # kcs_ui.message_noconfirm(response)
      UnhighlightEntities()
      kcs_ui.message_noconfirm("will into for")
      for element in response.get("list"):
         kcs_ui.message_noconfirm("show if highlist")
         kcs_ui.message_noconfirm(element.get("ifHighlisht"))
         if element.get("ifHighlisht") == "??":
            elem = kcs_draft.element_identify(element.get("name"))
            kcs_draft.element_highlight(elem)
            # kcs_ui.message_confirm("高亮" + element.get("name"))
            
      
   except:
      kcs_ui.message_noconfirm(kcs_struct.error)
      print kcs_struct.error      


def GetEntityHandles(elementkind, region):             # function calls corresponding capture function
    handles = []                                       # results list

    # call corresponding function
    if elementkind == 1:
        handles = kcs_draft.view_capture(region)       # views
    elif elementkind == 2:
        handles = kcs_draft.subview_capture(region)    # subviews
    elif elementkind == 3:
        handles = kcs_draft.component_capture(region)  # component
    elif elementkind == 4:
        handles = kcs_draft.model_capture(region)      # models
    elif elementkind == 5:
        handles = kcs_draft.dim_capture(region)        # dimensions
    elif elementkind == 6:
        handles = kcs_draft.note_capture(region)       # notes
    elif elementkind == 7:
        handles = kcs_draft.posno_capture(region)      # position numbers
    elif elementkind == 8:
        handles = kcs_draft.hatch_capture(region)      # hatches
    elif elementkind == 9:
        handles = kcs_draft.text_capture(region)       # texts
    elif elementkind == 10:
        handles = kcs_draft.symbol_capture(region)     # symbols
    elif elementkind == 11:
        handles = kcs_draft.contour_capture(region)    # contours
    elif elementkind == 12:
        handles = kcs_draft.point_capture(region)      # points
    elif elementkind == 13:
        handles = kcs_draft.geometry_capture(region)   # geometries

    return handles

# 传入两个模型，将两个模型之间自动创建连线
def drawLineAuto(id1, id2):
    try:
        sp = KcsPoint2D.Point2D(id1.X, id1.Y)
        ep = KcsPoint2D.Point2D(id2.X, id2.Y)
        line = KcsRline2D.Rline2D(sp, ep)
        kcs_draft.line_new(line)

    except:
        kcs_ui.message_noconfirm(kcs_struct.error)
        print kcs_struct.error


def FindEntities(elementkind, region):                  # function gets entities handles and reports it on screen
    try:                                                # if no entities found reports a message kcs_NotFound

        kcs_draft.highlight_off(0)                      # highlight off all highlighted entities

        handles = GetEntityHandles(elementkind, region) # get entity handles
        nSize = handles[0]

        # report number of captured entities
        kcs_ui.message_noconfirm('Captured entities:' + str(nSize))

        if nSize > 200:
            kcs_ui.message_noconfirm('Too many entities to highlight!');
        else:
            handles = handles[1:]
            if elementkind == 4:
                # previousHandle = None
                for nHandle in handles:
                    kcs_draft.element_highlight(nHandle)        # highlight founded entities
                    # if previousHandle != None:
                    #     drawLineAuto(previousHandle, nHandle)
                    # previousHandle = nHandle
            elif elementkind == 10:
                previous2DPoint = None
                current2DPoint = None
                for nHandle in handles:
                    kcs_draft.element_highlight(nHandle)  # highlight founded entities
                    if current2DPoint is not None:
                       previous2DPoint = current2DPoint
                    current2DPoint = kcs_draft.symbol_properties_get(nHandle, KcsSymbol.Symbol())
                    if previous2DPoint is not None:
                        drawLineAuto(previous2DPoint.GetPosition(), current2DPoint.GetPosition())


    except:
        print kcs_draft.error                           # print exception

        if kcs_draft.error == 'kcs_NotFound':
            kcs_ui.message_noconfirm('kcs_NotFound')

        return

def DefineRectangle():
   point1 = KcsPoint2D.Point2D()
   point2 = KcsPoint2D.Point2D()
   resp, point1 = kcs_ui.point2D_req('First corner', point1)
   if resp == kcs_util.ok():
      status = Stat_point2D_req()
      status.SetDefMode('ModeCursor')

      CurType = CursorType()
      CurType.SetRubberRectangle(point1)
      status.SetCursorType(CurType)
      status.SetHelpPoint(point1)

      resp, point2 = kcs_ui.point2D_req('Second corner', point2, status)

      if resp == kcs_util.ok():
         # region = KcsCaptureRegion2D.CaptureRegion2D()
         return 1, KcsRectangle2D.Rectangle2D(point1, point2)
      else:
         return 0, None
   else:
      return 0, None


# 范围选择模型，并使所选模型高
def RegionFromRectangle(elementkind, Inside, Cut):              # select region by rectangle
    try:
        region = KcsCaptureRegion2D.CaptureRegion2D()
        res, shape = DefineRectangle()
        region.SetRectangle(shape)

        if Inside == 0:
            region.SetOutside()
        else:
            region.SetInside()

        if Cut == 0:
            region.SetNoCut()
        else:
            region.SetCut()

        FindEntities(elementkind, region)

    except:
        print 'Unexpected error:', sys.exc_info()[0]

    return


def UnhighlightEntities():
   try:
      kcs_draft.highlight_off(0)  # Turn off all highlighted entities
      kcs_ui.message_noconfirm('All entities have been unhighlighted.')
   except:
      print kcs_draft.error
      kcs_ui.message_noconfirm('Error in unhighlighting entities.')
#
# # 范围选择模型，并使所选模型高
# def RegionFromRectangle(elementkind, Inside, Cut):              # select region by rectangle
#     point1 = KcsPoint2D.Point2D()
#     point2 = KcsPoint2D.Point2D()
#
#     while 1:
#         try:
#             resp = kcs_ui.point2D_req('Indicate first corner of rectangle', point1)  # request user for first corner
#             if resp[0] != kcs_util.ok():
#                 kcs_draft.highlight_off(0)                      # highlight off all highlighted entities
#                 return
#             resp = kcs_ui.point2D_req('Indicate second corner of rectangle', point2) # request user for second corner
#             if resp[0] != kcs_util.ok():
#                 kcs_draft.highlight_off(0)                      # highlight off all highlighted entities
#                 return
#         except:
#             print kcs_ui.error
#             return
#
#         try:
#             region = KcsCaptureRegion2D.CaptureRegion2D()
#             rectangle = KcsRectangle2D.Rectangle2D(point1, point2)
#             region.SetRectangle(rectangle)
#
#             if Inside==0:
#                 region.SetOutside()
#             else:
#                 region.SetInside()
#
#             if Cut==0:
#                 region.SetNoCut()
#             else:
#                 region.SetCut()
#
#             FindEntities(elementkind, region)
#
#         except:
#             print 'Unexpected error:', sys.exc_info()[0]
#
#     return


def GetInsideOption(Inside):                                # get Inside option
    actions = KcsStringlist.Stringlist('Inside Contour')
    actions.AddString('Outside Contour')
    res = kcs_ui.choice_select('Get Inside/Outside', 'Select method', actions)
    if res[0]==kcs_util.ok():
        if res[1] == 1:
            return 1
        else:
            return 0

    return Inside


def GetCutOption(Cut):                                      # get Cut option
    actions = KcsStringlist.Stringlist('Cut')
    actions.AddString('No cut')
    res = kcs_ui.choice_select('Get Cut/No cut', 'Select method', actions)
    if res[0]==kcs_util.ok():
        if res[1] == 1:
            return 1
        else:
            return 0

    return Cut


def GetRegionSelectionMethod(elementkind, Inside, Cut):               # function gets selection method and selects entities
    try:
        RegionFromRectangle(elementkind, Inside, Cut)
    except:
        print kcs_ui.error

    return
########################################################################

import asyncore,socket

# class MyServer(asyncore.dispatcher):
#     def __init__(self, host, port):
#         asyncore.dispatcher.__init__(self)
#         self.create_socket(socket.AF_INET, socket.SOCK_STREAM)  # 创建 Socket
#         self.bind((host, port))  # 绑定地址
#         self.listen(5)  # 监听端口
#         kcs_ui.message_noconfirm("listen")
    
#     def handle_accept(self):
#         kcs_ui.message_noconfirm("handle_accept")
#         client, addr = self.accept()
#         kcs_ui.message_noconfirm("Accepted connection from %s" % (addr))


class RequestHandler(asyncore.dispatcher_with_send):
    """处理客户端请求并返回内容"""
    def handle_read(self):
        # 接收客户端数据（最大1024字节）
        request_data = self.recv(1024)
        if request_data:
            kcs_ui.message_noconfirm("收到请求: %s" % (request_data.decode('ascii')))
            
            # 处理请求并准备响应（此处原样返回请求内容）
            response = "已收到您的请求: %s" % (request_data.decode('ascii'))
            
            # 发送响应给客户端
            self.send(response.encode('ascii'))
        else:
            # 客户端断开连接
            self.close()

class AsyncServer(asyncore.dispatcher):
    """异步Socket服务器"""
    def __init__(self, host, port):
        asyncore.dispatcher.__init__(self)
        self.create_socket(socket.AF_INET, socket.SOCK_STREAM)
        self.set_reuse_addr()  # 允许地址重用[14](@ref)
        self.bind((host, port))
        self.listen(5)  # 最大排队连接数[5](@ref)
        kcs_ui.message_noconfirm("服务器启动在 %s:%s" % (host,port))

    def handle_accept(self):
        # 接受新客户端连接
        client_sock, client_addr = self.accept()
        kcs_ui.message_noconfirm("新客户端连接: %s:%s" % (client_addr[0],client_addr[1]))
        
        # 为每个客户端创建独立的请求处理器
        RequestHandler(client_sock)


def create_menu():
    Inside = 1
    Cut = 0
    #-------------------------------------------------------------------------
    # Create main menu
    #-------------------------------------------------------------------------
    main = KcsStringlist.Stringlist('手工创建路径')
    main.AddString('捕捉元素并上传到云端')
    main.AddString("范围选择并高亮")
    main.AddString("范围选择并自动连线")
    main.AddString("接收云端数据高亮")
    main.AddString("取消高亮")
    main.AddString("选择两个设备，AI自动连线")
    main.AddString("手工创建节点（指定struct，自动在struct两个端点创建节点）")
    main.AddString("获取所有Structure数据")
    
    next = 1

    while next:
        (status, index) = kcs_ui.choice_select("Main",'Operation on structure', main)
        if status == OK:
            if index == 1:
                CreateRouter()
            elif index == 2:
                DataUpload()
            elif index == 3:
                GetRegionSelectionMethod(4, Inside, Cut)
            elif index == 4:
                GetRegionSelectionMethod(10, Inside, Cut)
            elif index == 5:
                DataDownStream()
            elif index == 6:
                UnhighlightEntities()
            elif index == 7:
                GetDataAndDrawLine()
            elif index == 8:
                CreateNode()
            elif index == 9:
                GetAllStructureData()

        else:
            next = 0
            print "User interrupted!"


def run():

    # # 启动交互界面
    create_menu()
    
    
   #-------------------------------------------------------------------------
