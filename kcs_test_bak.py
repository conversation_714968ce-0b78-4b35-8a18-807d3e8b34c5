#-*- encoding:mbcs -*-
# import sys   #reload()之前必须要引入模
# reload(sys)
# sys.setdefaultencoding("mbcs")
import KcsCaptureRegion2D
import KcsRectangle2D

import wingdbstub

import kcs_model

import kcs_ui
import kcs_util
import KcsStringlist
import kcs_struct
import KcsContour2D
import kcs_draft
import KcsModel
import KcsPoint2D
import KcsRline2D
import sys
import kcs_ui
import kcs_util
import kcs_draft
from KcsPoint2D               import Point2D
from KcsCursorType            import CursorType, CursorTypes
from KcsStat_point2D_req      import Stat_point2D_req, Point2dDefModes
import KcsSymbol
import KcsTransformation2D
import kcs_ic
from KcsButtonState           import ButtonState
import KcsColour
import kcs_dex

# 网络请求与json处理
import urllib2
import simplejson as json
from kcs_ex_struct_http_util import data_upload_common, get_http_token

serverAddress = "http://*************:9999/"

kcs_ui.message_noconfirm("Sssssst" )

OK = kcs_util.ok()
CANCEL  = kcs_util.cancel()


from kcs_ex_struct_extract import get_structure_info 
import KcsVector3D
import kcs_att

import kcs_ui
from KcsPoint3D import Point3D
from KcsStat_point3D_req import Stat_point3D_req
import KcsLinetype
import KcsPoint3D

#
# 获取token
#
token = get_http_token()

#-------------------------------------------------------------------------
# 工具方法
#-------------------------------------------------------------------------
def point2D_reqGetStatus():
    status = Stat_point2D_req()
    status.SetDefMode('ModeCursor')
    curType = CursorType()
    curType.SetCrossHair()
    status.SetCursorType(curType)
    helpPoint = KcsPoint2D.Point2D(0.0, 0.0)
    status.SetHelpPoint(helpPoint)
    return status

def point2D_reqGetButton():
    buttons = ButtonState()
    buttons.LockEnabled = 0
    buttons.OptionsEnabled = 1
    buttons.LockChecked = None
    return buttons

# 获取坐标附近的钢结构件
def get_near_by_struct(pt, model):
    model.SetType("struct")
    kcs_draft.model_identify(pt, model)

# 根据一个中心点，返回其X形状的四个顶点
def generate_x_vertices(center_point, size=1.0):
    cx, cy = center_point.GetX(), center_point.GetY()

    offset = size * 0.70710678118

    top_left = (cx - offset, cy + offset)
    top_right = (cx + offset, cy + offset)
    bottom_left = (cx - offset, cy - offset)
    bottom_right = (cx + offset, cy - offset)

    return (top_left, top_right, bottom_left, bottom_right)

# 创建一个X标识
def createXIdentity(verticesX):
    '''
    返回X标识的两条线的句柄
    '''
    global line_handles
    # 创建 Point2D 对象
    top_left = Point2D(verticesX[0][0], verticesX[0][1])
    top_right = Point2D(verticesX[1][0], verticesX[1][1])
    bottom_left = Point2D(verticesX[2][0], verticesX[2][1])
    bottom_right = Point2D(verticesX[3][0], verticesX[3][1])
    # 创建两条交叉直线
    line1 = KcsRline2D.Rline2D(top_left, bottom_right)  # 左上 → 右下
    line2 = KcsRline2D.Rline2D(top_right, bottom_left)  # 右上 → 左下
    # 绘制直线
    try:
        line1_handel = kcs_draft.line_new(line1)._ElementHandle__handle
        line2_handel = kcs_draft.line_new(line2)._ElementHandle__handle
        return [line1_handel, line2_handel]
    except Exception, e:
        kcs_ui.message_noconfirm("绘图失败:" + str(e))

#-------------------------------------------------------------------------



def placeNode(nodeId, module, nodeComponent, startPoint, endPoint):
    """
    放置一个用于标识节点的模型, 参数为放置节点标识的3维坐标点
    注意：
    1. nodeId如果超过了22个字符会被截取

    """
    try:
        # 检查nodeId长度，截取前22个字符
        nodeId = nodeId[:22]
        # kcs_struct.struct_cancel()
        colour = KcsColour.Colour('BLUE')
        kcs_struct.struct_new(nodeId, module, colour)
        rotation    = KcsVector3D.Vector3D(0.0, 1.0, 0.0)
        profilePart = kcs_struct.profile_new_2point3D(nodeComponent, startPoint, endPoint, rotation)
        kcs_util.coord_to_pos(1, startPoint.GetX())        
        save = kcs_struct.struct_save()
        # 显示以上创建的model
        model = KcsModel.Model("struct", nodeId)
        kcs_att.model_save(model)  
        kcs_util.coord_to_pos(1, startPoint.GetX())              
        kcs_draft.model_draw(model)
    except Exception, e:
        kcs_struct.struct_cancel()
        kcs_ui.message_noconfirm(str(e))

def placeRoute(routeId, module, routeComponent, startPoint, endPoint):
    """
    放置一个用于通道连接的模型, 参数为连接的起始和结束坐标
    该方法会创建通道
    注意：
    1. routeId如果超过了22个字符会被截取
    """
    try:
        # 检查routeId长度，截取前22个字符
        routeId = routeId[:22]
        # kcs_struct.struct_cancel()
        colour = KcsColour.Colour('BLUE')
        kcs_struct.struct_new(routeId, module, colour)
        rotation    = KcsVector3D.Vector3D(0.0, 1.0, 0.0)
        profilePart = kcs_struct.profile_new_2point3D(routeComponent, startPoint, endPoint, rotation)
        kcs_util.coord_to_pos(1, startPoint.GetX())
        save = kcs_struct.struct_save()
        # 显示以上创建的model
        model = KcsModel.Model("struct", routeId)
        kcs_att.model_save(model)  
        kcs_util.coord_to_pos(1, startPoint.GetX())              
        kcs_draft.model_draw(model)
    except Exception, e:
        kcs_struct.struct_cancel()
        kcs_ui.message_noconfirm(str(e))

def drawRelation(startStructCogPoint, endStructCogPoint):
    """
    绘制关系线，参数为两个结构的重心坐标。该方法在两个坐标点上标记红叉，并在两个红叉间画上红线
    参数：
    两个坐标点
    返回：
    返回一组handles，该handles为线的句柄，可以用来删除该线，返回值格式为：
    [startHandle1, startHandle2, endHandle1, endHandle2, lineHandle]
    """
    # 定义返回对象
    returnHandles = []
    global line_handles
    # 设置绘图颜色为红色
    colour = KcsColour.Colour('RED')
    kcs_draft.colour_set(colour)
    # 设置线条样式
    lin=KcsLinetype.Linetype("SolidXWide")
    kcs_draft.linetype_set(lin)
    # 获取绘图中第一个视图的句柄
    handler = kcs_draft.element_child_first_get()
    # 设置当前视图
    kcs_draft.subpicture_current_set(handler)
    # 在以上视图中，将重心三维坐标,在指定视图中，转换为二维坐标
    # 1. 绘制起点的红叉
    startPoint2D=KcsPoint2D.Point2D()
    kcs_draft.point_transform(handler, startStructCogPoint, startPoint2D)
    # 获取以重心为中心的X形状四个顶点，传入的参数为二维重心点
    verticesX = generate_x_vertices(startPoint2D)
    startHandles = createXIdentity(verticesX)
    returnHandles.extend(startHandles)
    # 2. 绘制终点的红叉
    endPoint2D=KcsPoint2D.Point2D()
    kcs_draft.point_transform(handler, endStructCogPoint, endPoint2D)
    # 获取以重心为中心的X形状四个顶点，传入的参数为二维重心点
    verticesX = generate_x_vertices(endPoint2D)
    endHandles = createXIdentity(verticesX)
    returnHandles.extend(endHandles)    
    # 3. 绘制连线
    kcs_ui.message_noconfirm('绘制连线XXXXXXXXXXXX')
    line = KcsRline2D.Rline2D(startPoint2D, endPoint2D)  
    kcs_ui.message_noconfirm('当前' + str(line))
    line_handle = kcs_draft.line_new(line)._ElementHandle__handle
    returnHandles.append(line_handle)
    return returnHandles
    
    # body = {
    #             "handle": line_handle
    #         }        
    # data_upload_common('erupt-api/data/modify/Line', body, 'Line')
    

def createRelationRun(startStruct, endStruct):
    """
    创建路线关系的入口方法，返回所绘制的路线的句柄，结构为：
    [startHandle1, startHandle2, endHandle1, endHandle2, lineHandle]
    参数：
    startStruct: 起点结构件的ID
    endStruct: 终点结构件的ID
    """
    # 获取起点结构件的重心坐标
    startStructCog = get_structure_info(startStruct)['cog']
    # 获取终点结构件的重心坐标
    endStructCog = get_structure_info(endStruct)['cog']
    # 创建路线
    return drawRelation(KcsPoint3D.Point3D(startStructCog[0],startStructCog[1],startStructCog[2]), KcsPoint3D.Point3D(endStructCog[0],endStructCog[1],endStructCog[2]))


def CreateNode():
    """
    创建节点
    参数：
    无
    返回：
    无
    """
    point1 = KcsPoint2D.Point2D()
    resp, point1 = kcs_ui.point2D_req('选择节点坐标',point1, point2D_reqGetStatus(), point2D_reqGetButton() )

    if resp == kcs_util.ok():
        kcs_ui.message_noconfirm(point1)
        st = KcsModel.Model()
        #根据所选坐标点，获取附近结构件
        get_near_by_struct(point1, st)
        # 获取结构件的全部信息
        structure_info = get_structure_info(st.Name)
        # 获取结构件的重心
        cog = structure_info['cog']
        pointCog=KcsPoint3D.Point3D(cog[0],cog[1],cog[2])
        # 设置绘图颜色为红色
        colour = KcsColour.Colour('RED')
        kcs_draft.colour_set(colour)
        # 设置线条样式
        lin=KcsLinetype.Linetype("SolidXWide")
        kcs_draft.linetype_set(lin)
        # 获取绘图中第一个视图的句柄
        handler = kcs_draft.element_child_first_get()
        # 设置当前视图
        kcs_draft.subpicture_current_set(handler)
        # 在以上视图中，将重心三维坐标,在指定视图中，转换为二维坐标
        point2D=KcsPoint2D.Point2D()
        kcs_draft.point_transform(handler, pointCog, point2D)
        # 获取元素句柄的二维变换矩阵:transf
        transf = KcsTransformation2D.Transformation2D()
        transf.IdentityTransf()           
        kcs_draft.element_transformation_get(handler, transf)
        # 获取以重心为中心的X形状四个顶点，传入的参数为二维重心点
        verticesX = generate_x_vertices(point2D)
        createXIdentity(verticesX)
        return 1, None


#-------------------------------------------------------------------------------------
# 六、 以下为移除所有边的代码
# def removeAllLines(handles):
# kcs_draft.element_delete(461)
# for handle in range(, 461):
#     kcs_draft.element_delete(handle)
    


# 五、 以下为测试显示路线图的代码
# 显示图的原理为画红叉和画线，根据输入的struct，结合struct的路线关系，标记红叉并划线
# 第一步，框选struct
def GetEntityHandles(elementkind, region):             # function calls corresponding capture function
    handles = []                                       # results list

    # call corresponding function
    if elementkind == 1:
        handles = kcs_draft.view_capture(region)       # views
    elif elementkind == 2:
        handles = kcs_draft.subview_capture(region)    # subviews
    elif elementkind == 3:
        handles = kcs_draft.component_capture(region)  # component
    elif elementkind == 4:
        handles = kcs_draft.model_capture(region)      # models
    elif elementkind == 5:
        handles = kcs_draft.dim_capture(region)        # dimensions
    elif elementkind == 6:
        handles = kcs_draft.note_capture(region)       # notes
    elif elementkind == 7:
        handles = kcs_draft.posno_capture(region)      # position numbers
    elif elementkind == 8:
        handles = kcs_draft.hatch_capture(region)      # hatches
    elif elementkind == 9:
        handles = kcs_draft.text_capture(region)       # texts
    elif elementkind == 10:
        handles = kcs_draft.symbol_capture(region)     # symbols
    elif elementkind == 11:
        handles = kcs_draft.contour_capture(region)    # contours
    elif elementkind == 12:
        handles = kcs_draft.point_capture(region)      # points
    elif elementkind == 13:
        handles = kcs_draft.geometry_capture(region)   # geometries

    return handles

def FindEntities(elementkind, region):                  # function gets entities handles and reports it on screen
    try:                                                # if no entities found reports a message kcs_NotFound

        kcs_draft.highlight_off(0)                      # highlight off all highlighted entities

        handles = GetEntityHandles(elementkind, region) # get entity handles
        nSize = handles[0]
        return handles

        # report number of captured entities
        kcs_ui.message_noconfirm('Captured entities:' + str(nSize))

    except:
        print kcs_draft.error                           # print exception

        if kcs_draft.error == 'kcs_NotFound':
            kcs_ui.message_noconfirm('kcs_NotFound')

        return

def RegionFromRectangle(elementkind, Inside, Cut):              # select region by rectangle
    try:
        region = KcsCaptureRegion2D.CaptureRegion2D()
        res, shape = DefineRectangle()
        region.SetRectangle(shape)
        if Inside == 0:
            region.SetOutside()
        else:
            region.SetInside()
        if Cut == 0:
            region.SetNoCut()
        else:
            region.SetCut()
        return FindEntities(elementkind, region)
    except:
        print 'Unexpected error:', sys.exc_info()[0]

    return

# handles = RegionFromRectangle(4,1,0)
# modelNameList = []
# #遍历handles，通过model_properties_get获取model，并获取到Name，放入到modelNameList中
# for i in range(0, len(handles)):
#     mod=KcsModel.Model()
#     model=kcs_draft.model_properties_get(i,mod)
#     modelNameList.append(model.Name)


# 三、以下为测试在struct的重心绘制红叉
# CreateNode()

# 四、以下为生成路线关系的测试代码
line_handles = []
point1 = KcsPoint2D.Point2D()
point2 = KcsPoint2D.Point2D()
startStruct = KcsModel.Model()
endStruct = KcsModel.Model()
resp, point1 = kcs_ui.point2D_req('选择起始节点坐标',point1, point2D_reqGetStatus(), point2D_reqGetButton() )
if resp == kcs_util.ok():
    kcs_ui.message_noconfirm(point1)
    #根据所选坐标点，获取附近结构件
    get_near_by_struct(point1, startStruct)
    resp, point2 = kcs_ui.point2D_req('选择结束节点坐标',point2, point2D_reqGetStatus(), point2D_reqGetButton() )
    if resp == kcs_util.ok():
        kcs_ui.message_noconfirm(point1)
        get_near_by_struct(point2, endStruct)
        line_handles = createRelationRun(startStruct.Name, endStruct.Name)
        # 将选择的起点和终点struct, 以及连线句柄发送到处理端，记录关系
        body = {
            "structCodeA": startStruct.Name,
            "structCodeB": endStruct.Name,
            "lineHandles": line_handles
        }        
        # result = data_upload_common('erupt-api/data/modify/StructureRelationship', body, 'StructureRelationship')
        result = data_upload_common('rest/createStructRelation', body, '')
        # 
        # result 的格式为{
        #"structCodeAExistFlag": true,
        #"structCodeBExistFlag": true
        #}，判断structCodeAExistFlag和structCodeBExistFlag为true时，删除line_handles
        if result['structCodeAExistFlag'] :
            kcs_draft.element_delete(line_handles[0])
            kcs_draft.element_delete(line_handles[1])
        if result['structCodeBExistFlag'] :
            kcs_draft.element_delete(line_handles[2])
            kcs_draft.element_delete(line_handles[3])
        if result['relationLineExistsFlag']:
            kcs_draft.element_delete(line_handles[4])
                
        
        

# 一、以下为生成节点的测试代码
# V1 - 已测试可行
# pnt3D1 = KcsPoint3D.Point3D()
# import time
# res = kcs_ui.point3D_req("Define 3D point",pnt3D1)
# if res[0] == kcs_util.ok():
#     pnt3D2 = KcsPoint3D.Point3D(pnt3D1.GetX() +125, pnt3D1.GetY(), pnt3D1.GetZ())
#     int_timestamp = int(time.time())
#     # 转换为字符串
#     int_timestamp_str = str(int_timestamp)
#     placeNode("ECA-TESTNODE-" + int_timestamp_str, "ECA", 'SQU#125', pnt3D1, pnt3D2)
# else:
#    print 'User interrupted'

# # V2 - 定位托架、扁条及其他结构件
# # 创建初始点对象
# initial_point = Point2D(0.0, 0.0)

# # 配置光标类型为十字准线
# cursor_type = CursorType()
# cursor_type.SetCrossHair()

# # 配置点定义模式为光标位置
# status = Stat_point2D_req()
# status.SetCursorType(cursor_type)
# status.SetDefMode('ModeCursor')  # 设置为光标模式

# # 配置按钮状态（禁用锁定，启用选项）
# buttons = ButtonState()
# buttons.LockEnabled = 0    # 禁用锁定按钮
# buttons.OptionsEnabled = 1  # 启用选项按钮
# buttons.LockChecked = None  # 无选中锁定

# # 调用点请求函数
# response, result_point = kcs_ui.point2D_req(
#     '选择点',          # GBK解码后的消息文本
#     initial_point,     # 初始点坐标
#     status,            # 点定义模式和光标配置
#     buttons            # 按钮状态配置
# )


# import time
# res = kcs_ui.point3D_req("Define 3D point",pnt3D1)
# if response == kcs_util.ok():    
#     int_timestamp = int(time.time())
#     # 转换为字符串
#     int_timestamp_str = str(int_timestamp)
#     placeNode("ECA-TESTNODE-" + int_timestamp_str, "ECA", 'SQU#125', pnt3D1, pnt3D2)
# else:
#    print 'User interrupted'   


# 二、以下为生成通道的测试代码
# pnt3D1 = KcsPoint3D.Point3D()
# pnt3D2 = KcsPoint3D.Point3D()
# res = kcs_ui.point3D_req("Define 3D point",pnt3D1)
# if res[0] == kcs_util.ok():
#     resp, pnt3D2 = kcs_ui.point3D_req("Define 3D point",pnt3D2)
#     if resp == kcs_util.ok():
#         int_timestamp = int(time.time())
#         # 转换为字符串
#         int_timestamp_str = str(int_timestamp)
#         placeRoute("ECA-TESTROUTE-" + int_timestamp_str, "ECA", 'O#1', pnt3D1, pnt3D2)
# else:
#    print 'User interrupted'

