MACRO,LIST;
  GET
   /POINT_2D=('Give upper left corner of frame',FRM);
  ASSIGN,X,FRM/XCOORD;
  ASSIGN,Y,FRM/YCOORD;
  ASSIGN,TXH,3.5;
  ASSIGN,ILSP,1.5*TXH;
  ASSIGN,DX,45*TXH;
!
! Give number of lines
!
  ASSIGN,N,14;
  ASSIGN,DY,(N+3)*ILSP;
!
! Create frame
!
  POINT_2D,P1,X+DX,Y;
  POINT_2D,P2,X+DX,Y-DY;
  POINT_2D,P3,X,Y-DY;
  CONTOUR,CNT,FRM/LINEEND=P1/LINEEND=P2
		 /LINEEND=P3/LINEEND=FRM;
PRESENT,CNT;
!
! Create vertical lines between the rows
!
POINT_2D,P4,X+6*TXH,Y;
POINT_2D,P5,X+6*TXH,Y-DY;
LINE,L1,P4/LINEEND=P5;
PRESENT,L1;
POINT_2D,P6,X+11*TXH,Y;
POINT_2D,P7,X+11*TXH,Y-DY;
LINE,L2,P6/LINEEND=P7;
PRESENT,L2;
POINT_2D,P8,X+19*TXH,Y;
POINT_2D,P9,X+19*TXH,Y-DY;
LINE,L3,P8/LINEEND=P9;
PRESENT,L3;
POINT_2D,P10,X+25*TXH,Y;
POINT_2D,P11,X+25*TXH,Y-DY;
LINE,L4,P10/LINEEND=P11;
PRESENT,L4;
POINT_2D,P12,X+33*TXH,Y;
POINT_2D,P13,X+33*TXH,Y-DY;
LINE,L5,P12/LINEEND=P13;
PRESENT,L5;
!
! Put a heading
!
ASSIGN,DY,ILSP;
POINT_2D,TXP,X,Y-DY;
TEXT,TXT,TXP/TEXTLINE=
'   DN  NAME   DIA     TH    QUA      MAT_NO';
PRESENT,TXT;
POINT_2D,P14,X,Y-DY-ILSP;
POINT_2D,P15,X+DX,Y-DY-ILSP;
LINE,L6,P14/LINEEND=P15;
PRESENT,L6;
!
! Put textfile
!
POINT_2D,TXP,X,Y-DY-2*ILSP;
TEXTFILE,TXF,'STEEL.DAT'/POSLINES=(TXP,1,N);
PRESENT,TXF;
ENDMACRO;