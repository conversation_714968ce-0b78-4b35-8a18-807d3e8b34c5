MACRO,PUMP;
  NAME,'MOORINGPUMP'/VOLUME=(3000,3000);
  GET/DECIMAL  =('Angle for pressure side: ',ANG1)
     /DECIMAL  =('Angle for suction side: ',ANG2)
     /POINT_3D =('Start point: ',STP)
     /DISTANCE =('Pump length: ',LEN)
     /DISTANCE =('Pump height: ',DIA);
  ASSIGN,X,STP/XCOORD;
  ASSIGN,Y,STP/YCOORD;
  ASSIGN,Z,STP/ZCOORD;
  ASSIGN,L,LEN%1250.0;
  ASSIGN,R,DIA%400.0%2;
  ASSIGN,L1,100*L;
  ASSIGN,L2,405*L;
  ASSIGN,L3,50*L;
  ASSIGN,L4,15*L;
  ASSIGN,L5,270*L;
  ASSIGN,L6,L4;
  ASSIGN,L7,L4;
  ASSIGN,L8,305*L;
  ASSIGN,L9,L4;
  ASSIGN,L10,25*L;
  ASSIGN,A11,35*L;
  ASSIGN,L12,330*L;
  ASSIGN,H12,10%330.0*L12;
  ASSIGN,W12,80%330.0*L12;
  ASSIGN,L13,L12;
  ASSIGN,H13,H12;
  ASSIGN,W13,W12;
  ASSIGN,L14,75*L;
  ASSIGN,L15,L14;
  ASSIGN,L16,L4;
  ASSIGN,L17,L4;
  ASSIGN,L18,L4;
  ASSIGN,L19,L4;
  ASSIGN,L20,125*L;
  ASSIGN,L21,L4;
  ASSIGN,L22,L4;
  ASSIGN,L23,L3;
  ASSIGN,L24,L3;
  ASSIGN,L25,L4;
  ASSIGN,L26,L20;
  ASSIGN,L27,L4;
  ASSIGN,L28,L4;
  ASSIGN,L29,L14;
  ASSIGN,L30,L4;
  ASSIGN,R1A,300*R;
  ASSIGN,R1B,350*R;
  ASSIGN,R2,R1B;
  ASSIGN,R3,R1A;
  ASSIGN,R4,400*R;
  ASSIGN,R5,375*R;
  ASSIGN,R7,R1A;
  ASSIGN,R8,225*R;
  ASSIGN,R9,R8;
  ASSIGN,R10,150*R;
  ASSIGN,R11,R10;
  ASSIGN,R14A,75*R;
  ASSIGN,R14B,100*R;
  ASSIGN,R15A,R14A;
  ASSIGN,R15B,R14B;
  ASSIGN,R16,200*R;
  ASSIGN,R17,R16;
  ASSIGN,R18,R16;
  ASSIGN,R19,R16;
  ASSIGN,R20,60*R;
  ASSIGN,R21,R10;
  ASSIGN,R22,R10;
  ASSIGN,R23A,10*R;
  ASSIGN,R23B,125*R;
  ASSIGN,R24A,R23A;
  ASSIGN,R24B,R23B;
  ASSIGN,R26,R20;
  ASSIGN,R25,R10;
  ASSIGN,R27,R10;
  ASSIGN,R28,R10;
  ASSIGN,R29,R10;
  ASSIGN,R30,R10;
  ASSIGN,XE1,X+L1;
  ASSIGN,YE1,Y;
  ASSIGN,ZE1,Z;
  VECTOR_3D,VEC1,1,0,0;
  POINT_3D,PE1,XE1,YE1,ZE1;
  CONE,CON1,R1A,R1B/COORDCONE=(P,PE1);
  COLOUR,'RED';
  PRESENT,CON1;
  ASSIGN,XE2,L2+XE1;
  POINT_3D,PE2,XE2,YE1,ZE1;
  CYLINDER,CYL1,R2/COORDCYL=(PE1,PE2);
  PRESENT,CYL1;
  ASSIGN,XE3,L3+XE2;
  POINT_3D,PE3,XE3,YE1,ZE1;
  CYLINDER,CYL2,R3/COORDCYL=(PE2,PE3);
  PRESENT,CYL2;
  ASSIGN,XE4,L4+XE3;
  POINT_3D,PE4,XE4,YE1,ZE1;
  ASSIGN,R42,R4%2;
  POINT_2D,PC1,R4,R42;
  POINT_2D,PC2,R4,-R42;
  POINT_2D,PC3,-R4,-R42;
  POINT_2D,PC4,-R4,R42;
  POINT_2D,PC5,0,1.5*R4;
  POINT_3D,PGC1,XE3,YE1,ZE1-R42;
  POINT_3D,PGC2,XE3,YE1+1,ZE1-R42;
  POINT_3D,PGC3,XE3,YE1,ZE1+1-R42;
  CONTOUR,CNT1,PC1
    /LINEEND=PC2
    /LINEEND=PC3
    /LINEEND=PC4
    /ARCMIDPNT=(PC5,PC1);
  GENERALCYLINDER,GCL1,CNT1,L4,PGC1,PGC2,PGC3;
  COLOUR,'GREEN';
  PRESENT,GCL1;
  ASSIGN,XE5,L5+XE4;
  POINT_3D,PE5,XE5,YE1,ZE1;
  CYLINDER,CYL3,R5/COORDCYL=(PE4,PE5);
  COLOUR,'MAGENTA';
  PRESENT,CYL3;
  ASSIGN,XE6,L6+XE5;
  POINT_3D,PE6,XE6,YE1,ZE1;
  POINT_3D,PGC4,XE5,YE1,ZE1-R42;
  POINT_3D,PGC5,XE5,YE1+1,ZE1-R42;
  POINT_3D,PGC6,XE5,YE1,ZE1+1-R42;
  GENERALCYLINDER,GCL2,CNT1,L6,PGC4,PGC5,PGC6;
  COLOUR,'GREEN';
  PRESENT,GCL2;
  POINT_3D,PPA1,XE3-L12%22,YE1-R4+(W12%2-0.2*W12),
	ZE1-R4-H12%2;
POINT_3D,PPA2,XE3-L12%22+L12,YE1-R4+(W12%2-0.2*W12),
ZE1-R4-H12%2;
POINT_3D,PPA3,XE3-L12%22+L12,YE1-R4+(W12-0.2*W12),
ZE1-R4;
PARALLELEPIPED,PAR1/CENLINPARA=(PPA1,PPA2,PPA3);
PRESENT,PAR1;
  POINT_3D,PPA4,XE3-L13%22,YE1+R4-(W13%2-0.2*W13),
	ZE1-R4-H13%2;
POINT_3D,PPA5,XE3-L13%22+L13,YE1+R4-(W13%2-0.2*W13),
ZE1-R4-H13%2;
POINT_3D,PPA6,XE3-L13%22+L13,YE1+R4+0.2*W13,
ZE1-R4;
PARALLELEPIPED,PAR2/CENLINPARA=(PPA4,PPA5,PPA6);
PRESENT,PAR2;
  ASSIGN,XE7,L7+XE6;
  POINT_3D,PE7,XE7,YE1,ZE1;
  CYLINDER,CYL4,R7/COORDCYL=(PE6,PE7);
  COLOUR,'CYAN';
  PRESENT,CYL4;
  ASSIGN,XE8,L8+XE7;
  POINT_3D,PE8,XE8,YE1,ZE1;
  CYLINDER,CYL5,R8/COORDCYL=(PE7,PE8);
  PRESENT,CYL5;
  ASSIGN,XE9,L9+XE8;
  POINT_3D,PE9,XE9,YE1,ZE1;
  CYLINDER,CYL6,R9/COORDCYL=(PE8,PE9);
  PRESENT,CYL6;
  ASSIGN,XE10,L10+XE9;
  POINT_3D,PE10,XE10,YE1,ZE1;
  CYLINDER,CYL7,R10/COORDCYL=(PE9,PE10);
  COLOUR,'MAGENTA';
  PRESENT,CYL7;
  ASSIGN,XE11,A11+XE10;
  POINT_3D,PE11,XE11,YE1,ZE1;
  SPHERESEG,SPH1,R11/COORDSEG=(PE10,PE11);
  PRESENT,SPH1;
  ASSIGN,XE14,XE8-170*L;
  ASSIGN,YS14,YE1-R8;
  ASSIGN,YE14,YS14-L14;
  POINT_3D,PS14,XE14,YS14,ZE1;
  POINT_3D,PE14,XE14,YE14,ZE1;
  CONE,CON2,R14B,R14A/COORDCONE=(PS14,PE14);
  COLOUR,'YELLOW';
  PRESENT,CON2;
  ASSIGN,XE16,XE14;
  ASSIGN,YS16,YE14;
  ASSIGN,YE16,YE14-L14;
  POINT_3D,PS16,XE16,YS16,ZE1;
  POINT_3D,PE16,XE16,YE16,ZE1;
  CYLINDER,CYL8,R16/COORDCYL=(PS16,PE16);
  COLOUR,'CYAN';
  PRESENT,CYL8;
  ASSIGN,XE18,XE16;
  ASSIGN,YS18,YE16;
  ASSIGN,YE18,YE16-L16;
  POINT_3D,PS18,XE18,YS18,ZE1;
  POINT_3D,PE18,XE18,YE18,ZE1;
  CYLINDER,CYL9,R18/COORDCYL=(PS18,PE18);
  PRESENT,CYL9;
  ASSIGN,COS1,COSD(ANG1);
  ASSIGN,SIN1,SIND(ANG1);
  ASSIGN,XS20,XE18;
  ASSIGN,XE20,XE18+SIN1*L20;;
  ASSIGN,YS20,YE18;
  ASSIGN,YE20,YE18-L20;
  ASSIGN,ZE20,ZE1+COS1*L20;
  ASSIGN,XM20,XS20+(1-COSD(45))*SIN1*L20;
  ASSIGN,YM20,YE18-COSD(45)*L20;
  ASSIGN,ZM20,ZE1+(1-COSD(45))*COS1*L20;
  POINT_3D,PS20,XS20,YS20,ZE1;
  POINT_3D,PM20,XM20,YM20,ZM20;
  POINT_3D,PE20,XE20,YE20,ZE20;
  TOROID,TOR1,R20/COORDTOR=(PS20,PM20,PE20);
  PRESENT,TOR1;
  ASSIGN,XS21,XE20;
  ASSIGN,XE21,XE20+SIN1*L21;
  ASSIGN,YS21,YE20;
  ASSIGN,YE21,YE20;
  ASSIGN,ZS21,ZE20;
  ASSIGN,ZE21,ZS21+COS1*L21;
  POINT_3D,PS21,XS21,YS21,ZS21;
  POINT_3D,PE21,XE21,YE21,ZE21;
  CYLINDER,CYL21,R21/COORDCYL=(PS21,PE21);
  COLOUR,'RED';
  PRESENT,CYL21;
  ASSIGN,XS22,XE21;
  ASSIGN,XE22,XE21+SIN1*L22;
  ASSIGN,YS22,YE21;
  ASSIGN,YE22,YE21;
  ASSIGN,ZS22,ZE21;
  ASSIGN,ZE22,ZS22+COS1*L22;
  POINT_3D,PS22,XS22,YS22,ZS22;
  POINT_3D,PE22,XE22,YE22,ZE22;
  CYLINDER,CYL22,R22/COORDCYL=(PS22,PE22);
  PRESENT,CYL22;
  ASSIGN,XS23,XE22;
  ASSIGN,XE23,XE22+SIN1*L23;
  ASSIGN,YS23,YE22;
  ASSIGN,YE23,YE22;
  ASSIGN,ZS23,ZE22;
  ASSIGN,ZE23,ZS23+COS1*L23;
  POINT_3D,PS23,XS23,YS23,ZS23;
  POINT_3D,PE23,XE23,YE23,ZE23;
  CONE,CON23,R23B,R23A/COORDCONE=(PS23,PE23);
  COLOUR,'WHITE';
  PRESENT,CON23;
  ASSIGN,XS24,XE23;
  ASSIGN,XE24,XE23+SIN1*L24;
  ASSIGN,YS24,YE23;
  ASSIGN,YE24,YE23;
  ASSIGN,ZS24,ZE23;
  ASSIGN,ZE24,ZS24+COS1*L24;
  POINT_3D,PS24,XS24,YS24,ZS24;
  POINT_3D,PE24,XE24,YE24,ZE24;
  CONE,CON24,R24A,R24B/COORDCONE=(PS24,PE24);
  PRESENT,CON24;
  ASSIGN,XS25,XE24;
  ASSIGN,XE25,XE24+SIN1*L25;
  ASSIGN,YS25,YE24;
  ASSIGN,YE25,YE24;
  ASSIGN,ZS25,ZE24;
  ASSIGN,ZE25,ZS25+COS1*L25;
  POINT_3D,PS25,XS25,YS25,ZS25;
  POINT_3D,PE25,XE25,YE25,ZE25;
  CYLINDER,CYL25,R25/COORDCYL=(PS25,PE25);
  COLOUR,'CYAN';
  PRESENT,CYL25;
  ASSIGN,XE15,XE14;
  ASSIGN,YS15,YE1+R8;
  ASSIGN,YE15,YS15+L15;
  POINT_3D,PS15,XE15,YS15,ZE1;
  POINT_3D,PE15,XE15,YE15,ZE1;
  CONE,CON3,R15B,R15A/COORDCONE=(PS15,PE15);
  COLOUR,'YELLOW';
  PRESENT,CON3;
  ASSIGN,XE17,XE15;
  ASSIGN,YS17,YE15;
  ASSIGN,YE17,YE15+L17;
  POINT_3D,PS17,XE17,YS17,ZE1;
  POINT_3D,PE17,XE17,YE17,ZE1;
  CYLINDER,CYL10,R17/COORDCYL=(PS17,PE17);
  COLOUR,'CYAN';
  PRESENT,CYL10;
  ASSIGN,XE19,XE17;
  ASSIGN,YS19,YE17;
  ASSIGN,YE19,YE17+L19;
  POINT_3D,PS19,XE19,YS19,ZE1;
  POINT_3D,PE19,XE19,YE19,ZE1;
  CYLINDER,CYL11,R19/COORDCYL=(PS19,PE19);
  PRESENT,CYL11;
  ASSIGN,COS2,COSD(ANG2);
  ASSIGN,SIN2,SIND(ANG2);
  ASSIGN,XS26,XE19;
  ASSIGN,XE26,XE19+SIN2*L26;
  ASSIGN,YS26,YE19;
  ASSIGN,YE26,YE19+L26;
  ASSIGN,ZE26,ZE1+COS2*L26;
  ASSIGN,XM26,XS26+(1-COSD(45))*SIN2*L26;
  ASSIGN,YM26,YE19+COSD(45)*L26;
  ASSIGN,ZM26,ZE1+(1-COSD(45))*COS2*L26;
  POINT_3D,PS26,XS26,YS26,ZE1;
  POINT_3D,PM26,XM26,YM26,ZM26;
  POINT_3D,PE26,XE26,YE26,ZE26;
  TOROID,TOR2,R26/COORDTOR=(PS26,PM26,PE26);
  PRESENT,TOR2;
  ASSIGN,XS27,XE26;
  ASSIGN,XE27,XE26+SIN2*L27;
  ASSIGN,YS27,YE26;
  ASSIGN,YE27,YE26;
  ASSIGN,ZS27,ZE26;
  ASSIGN,ZE27,ZS27+COS2*L27;
  POINT_3D,PS27,XS27,YS27,ZS27;
  POINT_3D,PE27,XE27,YE27,ZE27;
  CYLINDER,CYL27,R27/COORDCYL=(PS27,PE27);
  COLOUR,'RED';
  PRESENT,CYL27;
  ASSIGN,XS28,XE27;
  ASSIGN,XE28,XE27+SIN2*L28;
  ASSIGN,YS28,YE27;
  ASSIGN,YE28,YE27;
  ASSIGN,ZS28,ZE27;
  ASSIGN,ZE28,ZS28+COS2*L28;
  POINT_3D,PS28,XS28,YS28,ZS28;
  POINT_3D,PE28,XE28,YE28,ZE28;
  CYLINDER,CYL28,R28/COORDCYL=(PS28,PE28);
  PRESENT,CYL28;
  ASSIGN,XS29,XE28;
  ASSIGN,XE29,XE28+SIN2*L29;
  ASSIGN,YS29,YE28;
  ASSIGN,YE29,YE28;
  ASSIGN,ZS29,ZE28;
  ASSIGN,ZE29,ZS29+COS2*L29;
  POINT_3D,PS29,XS29,YS29,ZS29;
  POINT_3D,PE29,XE29,YE29,ZE29;
  CYLINDER,CYL29,R29/COORDCYL=(PS29,PE29);
  COLOUR,'WHITE';
  PRESENT,CYL29;
  ASSIGN,XS30,XE29;
  ASSIGN,XE30,XE29+SIN2*L30;
  ASSIGN,YS30,YE29;
  ASSIGN,YE30,YE29;
  ASSIGN,ZS30,ZE29;
  ASSIGN,ZE30,ZS30+COS2*L30;
  POINT_3D,PS30,XS30,YS30,ZS30;
  POINT_3D,PE30,XE30,YE30,ZE30;
  CYLINDER,CYL30,R30/COORDCYL=(PS30,PE30);
  COLOUR,'RED';
  PRESENT,CYL30;
  COLOUR,'GREEN';
ENDMACRO;