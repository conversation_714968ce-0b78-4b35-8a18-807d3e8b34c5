<?xml version="1.0" standalone="yes"?>
<tribon-help>
	<book Name="Vitesse.chm" ID="VITESSE">
	</book>
	<book Name="Assembly_Planning.chm" ID="TBASSP">
	</book>
	<book Name="Basic_Design.chm" ID="TBBASICD">
	</book>
	<book Name="Basic.chm" ID="BASIC">
	</book>
	<book Name="Cable.chm" ID="TBCABLEMOD">
		<match Key="18273">
			<htmlindex>Cable_op_cable3.html#141327</htmlindex>
			<tiptext>New</tiptext>
		</match>
		<match Key="18274">
			<htmlindex>Cable_op_cable4.html#141358</htmlindex>
			<tiptext>Activate</tiptext>
		</match>
		<match Key="18272">
			<htmlindex>Cable_op_cable5.html#141401</htmlindex>
			<tiptext>Save</tiptext>
		</match>
		<match Key="18275">
			<htmlindex>Cable_op_cable6.html#141538</htmlindex>
			<tiptext>Cancel</tiptext>
		</match>
		<match Key="18211">
			<htmlindex>Cable_op_cable7.html#141568</htmlindex>
			<tiptext>Delete</tiptext>
		</match>
		<match Key="18299">
			<htmlindex>Cable_op_cable8.html#141603</htmlindex>
			<tiptext>List activated</tiptext>
		</match>
		<match Key="18200">
			<htmlindex>Cable_op_cable9.html#150504</htmlindex>
			<tiptext>Model route</tiptext>
		</match>
		<match Key="18200">
			<htmlindex>Cable_op_cable10.html#150590</htmlindex>
			<tiptext>Model route</tiptext>
		</match>
		<match Key="18200">
			<htmlindex>Cable_op_cable11.html#150644</htmlindex>
			<tiptext>Model route</tiptext>
		</match>
		<match Key="18200">
			<htmlindex>Cable_op_cable12.html#150675</htmlindex>
			<tiptext>Model route</tiptext>
		</match>
		<match Key="18200">
			<htmlindex>Cable_op_cable13.html#150706</htmlindex>
			<tiptext>Model route</tiptext>
		</match>
		<match Key="18200">
			<htmlindex>Cable_op_cable14.html#151243</htmlindex>
			<tiptext>Model route</tiptext>
		</match>
		<match Key="18200">
			<htmlindex>Cable_op_cable15.html#151266</htmlindex>
			<tiptext>Model route</tiptext>
		</match>
		<match Key="18200">
			<htmlindex>Cable_op_cable16.html#151320</htmlindex>
			<tiptext>Model route</tiptext>
		</match>
		<match Key="18224">
			<htmlindex>Cable_op_cable17.html#151296</htmlindex>
			<tiptext>Model new corner</tiptext>
		</match>
		<match Key="18299">
			<htmlindex>Cable_op_cable18.html#150550</htmlindex>
			<tiptext>Model delete corner</tiptext>
		</match>
		<match Key="18225">
			<htmlindex>Cable_op_cable19.html#141797</htmlindex>
			<tiptext>Model move corner</tiptext>
		</match>
		<match Key="18299">
			<htmlindex>Cable_op_cable20.html#145629</htmlindex>
			<tiptext>Model display corner</tiptext>
		</match>
		<match Key="18230">
			<htmlindex>Cable_op_cable21.html#145655</htmlindex>
			<tiptext>Model break connection</tiptext>
		</match>
		<match Key="18232">
			<htmlindex>Cable_op_cable21.html#145655</htmlindex>
			<tiptext>Model break connection</tiptext>
		</match>
		<match Key="18232">
			<htmlindex>Cable_op_cable22.html#145678</htmlindex>
			<tiptext>Model delete cable route</tiptext>
		</match>
		<match Key="18299">
			<htmlindex>Cable_op_cable23.html#141884</htmlindex>
			<tiptext>Model list cable on CW</tiptext>
		</match>
		<match Key="18321">
			<htmlindex>Cable_op_cable24.html#141921</htmlindex>
			<tiptext>Material straight part</tiptext>
		</match>
		<match Key="18323">
			<htmlindex>Cable_op_cable25.html#151383</htmlindex>
			<tiptext>Material bent part</tiptext>
		</match>
		<match Key="18323">
			<htmlindex>Cable_op_cable26.html#151433</htmlindex>
			<tiptext>Material bent part</tiptext>
		</match>
		<match Key="18324">
			<htmlindex>Cable_op_cable27.html#151407</htmlindex>
			<tiptext>Material remove all</tiptext>
		</match>
		<match Key="18325">
			<htmlindex>Cable_op_cable28.html#142017</htmlindex>
			<tiptext>Material rotate part</tiptext>
		</match>
		<match Key="18325">
			<htmlindex>Cable_op_cable29.html#142049</htmlindex>
			<tiptext>Material rotate branch</tiptext>
		</match>
		<match Key="18324">
			<htmlindex>Cable_op_cable30.html#142076</htmlindex>
			<tiptext>Material remove all</tiptext>
		</match>
		<match Key="18299">
			<htmlindex>Cable_op_cable31.html#142108</htmlindex>
			<tiptext>Material calculate used area</tiptext>
		</match>
		<match Key="18219">
			<htmlindex>Cable_op_cable32.html#142172</htmlindex>
			<tiptext>Interference exclude</tiptext>
		</match>
		<match Key="18219">
			<htmlindex>Cable_op_cable33.html#142205</htmlindex>
			<tiptext>Interference permit</tiptext>
		</match>
		<match Key="18247">
			<htmlindex>Cable_op_cable34.html#142270</htmlindex>
			<tiptext>Production information assembly model</tiptext>
		</match>
		<match Key="18247">
			<htmlindex>Cable_op_cable35.html#142304</htmlindex>
			<tiptext>Production information assembly part</tiptext>
		</match>
		<match Key="18247">
			<htmlindex>Cable_op_cable36.html#142340</htmlindex>
			<tiptext>Production information position number</tiptext>
		</match>
		<match Key="18247">
			<htmlindex>Cable_op_cable37.html#142369</htmlindex>
			<tiptext>Production information plan unit</tiptext>
		</match>
		<match Key="18299">
			<htmlindex>Cable_op_cable38.html#142437</htmlindex>
			<tiptext>Ready</tiptext>
		</match>
		<match Key="18210">
			<htmlindex>Cable_op_cable39.html#142475</htmlindex>
			<tiptext>Connect</tiptext>
		</match>
		<match Key="18111">
			<htmlindex>Cable_op_cable40.html#142513</htmlindex>
			<tiptext>Check connection</tiptext>
		</match>
		<match Key="18299">
			<htmlindex>Cable_op_cable41.html#142547</htmlindex>
			<tiptext>Display connection</tiptext>
		</match>
		<match Key="18442">
			<htmlindex>Cable_op_cable42.html#142576</htmlindex>
			<tiptext>Reroute cable</tiptext>
		</match>
		<match Key="18252">
			<htmlindex>Cable_op_cable43.html#142607</htmlindex>
			<tiptext>Group move</tiptext>
		</match>
		<match Key="18251">
			<htmlindex>Cable_op_cable44.html#142652</htmlindex>
			<tiptext>Group duplicate</tiptext>
		</match>
		<match Key="18264">
			<htmlindex>Cable_op_cable45.html#152305</htmlindex>
			<tiptext>Group rename</tiptext>
		</match>
		<match Key="18269">
			<htmlindex>Cable_op_cable46.html#152328</htmlindex>
			<tiptext>Group colour</tiptext>
		</match>
		<match Key="18102">
			<htmlindex>Cable_op_cable50.html#152269</htmlindex>
			<tiptext>Fill level</tiptext>
		</match>
		<match Key="18201">
			<htmlindex>Cable_op_cable51.html#142804</htmlindex>
			<tiptext>Dump</tiptext>
		</match>
		<match Key="18301">
			<htmlindex>Cable_op_cable53.html#142886</htmlindex>
			<tiptext>Real penetration new</tiptext>
		</match>
		<match Key="18302">
			<htmlindex>Cable_op_cable54.html#142923</htmlindex>
			<tiptext>Real penetration delete</tiptext>
		</match>
		<match Key="18308">
			<htmlindex>Cable_op_cable55.html#142960</htmlindex>
			<tiptext>Real penetration Move</tiptext>
		</match>
		<match Key="18315">
			<htmlindex>Cable_op_cable56.html#142991</htmlindex>
			<tiptext>Real penetration rename</tiptext>
		</match>
		<match Key="18300">
			<htmlindex>Cable_op_cable57.html#143022</htmlindex>
			<tiptext>Real penetration display</tiptext>
		</match>
		<match Key="18304">
			<htmlindex>Cable_op_cable58.html#143053</htmlindex>
			<tiptext>Real penetration change data</tiptext>
		</match>
		<match Key="18305">
			<htmlindex>Cable_op_cable59.html#143090</htmlindex>
			<tiptext>Real penetration new block</tiptext>
		</match>
		<match Key="18307">
			<htmlindex>Cable_op_cable60.html#143130</htmlindex>
			<tiptext>Real penetration delete block</tiptext>
		</match>
		<match Key="18300">
			<htmlindex>Cable_op_cable61.html#143176</htmlindex>
			<tiptext>Real penetration assembly</tiptext>
		</match>
		<match Key="18301">
			<htmlindex>Cable_op_cable62.html#143217</htmlindex>
			<tiptext>Imaginary penetration new</tiptext>
		</match>
		<match Key="18302">
			<htmlindex>Cable_op_cable63.html#143247</htmlindex>
			<tiptext>Imaginary penetration delete</tiptext>
		</match>
		<match Key="18315">
			<htmlindex>Cable_op_cable64.html#143289</htmlindex>
			<tiptext>Imaginary penetration rename</tiptext>
		</match>
		<match Key="18300">
			<htmlindex>Cable_op_cable65.html#143331</htmlindex>
			<tiptext>Imaginary penetration display</tiptext>
		</match>
		<match Key="18491">
			<htmlindex>Cable_op_cable67.html#143442</htmlindex>
			<tiptext>New</tiptext>
		</match>
		<match Key="18493">
			<htmlindex>Cable_op_cable68.html#143480</htmlindex>
			<tiptext>Activate by name</tiptext>
		</match>
		<match Key="18493">
			<htmlindex>Cable_op_cable69.html#143517</htmlindex>
			<tiptext>Activate by indicating CW</tiptext>
		</match>
		<match Key="18493">
			<htmlindex>Cable_op_cable70.html#143556</htmlindex>
			<tiptext>Actrvate by indicating real penetration</tiptext>
		</match>
		<match Key="18493">
			<htmlindex>Cable_op_cable71.html#143588</htmlindex>
			<tiptext>Activate by indicating imaginary penetration</tiptext>
		</match>
		<match Key="18490">
			<htmlindex>Cable_op_cable72.html#143626</htmlindex>
			<tiptext>Save</tiptext>
		</match>
		<match Key="18494">
			<htmlindex>Cable_op_cable73.html#143667</htmlindex>
			<tiptext>Cancel</tiptext>
		</match>
		<match Key="18495">
			<htmlindex>Cable_op_cable74.html#143704</htmlindex>
			<tiptext>Delete</tiptext>
		</match>
		<match Key="18499">
			<htmlindex>Cable_op_cable75.html#143749</htmlindex>
			<tiptext>List Activated</tiptext>
		</match>
		<match Key="18402">
			<htmlindex>Cable_op_cable76.html#143786</htmlindex>
			<tiptext>Route manual</tiptext>
		</match>
		<match Key="18409">
			<htmlindex>Cable_op_cable77.html#143821</htmlindex>
			<tiptext>Route automatic</tiptext>
		</match>
		<match Key="18409">
			<htmlindex>Cable_op_cable78.html#143853</htmlindex>
			<tiptext>Route intermediate</tiptext>
		</match>
		<match Key="18438">
			<htmlindex>Cable_op_cable79.html#143945</htmlindex>
			<tiptext>Route copy by name</tiptext>
		</match>
		<match Key="18438">
			<htmlindex>Cable_op_cable80.html#143979</htmlindex>
			<tiptext>Route copy by indicating CW</tiptext>
		</match>
		<match Key="18438">
			<htmlindex>Cable_op_cable81.html#144004</htmlindex>
			<tiptext>Route copy by indicating real penetration</tiptext>
		</match>
		<match Key="18438">
			<htmlindex>Cable_op_cable82.html#144049</htmlindex>
			<tiptext>Route copy by indicating imaginary penetration</tiptext>
		</match>
		<match Key="18437">
			<htmlindex>Cable_op_cable83.html#144087</htmlindex>
			<tiptext>Route cut</tiptext>
		</match>
		<match Key="18499">
			<htmlindex>Cable_op_cable84.html#144128</htmlindex>
			<tiptext>Route delete</tiptext>
		</match>
		<match Key="18499">
			<htmlindex>Cable_op_cable85.html#144165</htmlindex>
			<tiptext>Route cableway select picture</tiptext>
		</match>
		<match Key="18411">
			<htmlindex>Cable_op_cable86.html#144217</htmlindex>
			<tiptext>Route cableway select by name</tiptext>
		</match>
		<match Key="18413">
			<htmlindex>Cable_op_cable87.html#144258</htmlindex>
			<tiptext>Route cableway select route object</tiptext>
		</match>
		<match Key="18425">
			<htmlindex>Cable_op_cable88.html#144297</htmlindex>
			<tiptext>Connect end 1</tiptext>
		</match>
		<match Key="18425">
			<htmlindex>Cable_op_cable89.html#144350</htmlindex>
			<tiptext>Connect end 2</tiptext>
		</match>
		<match Key="18426">
			<htmlindex>Cable_op_cable90.html#144378</htmlindex>
			<tiptext>Disconnect end 1</tiptext>
		</match>
		<match Key="18426">
			<htmlindex>Cable_op_cable91.html#144410</htmlindex>
			<tiptext>Disconnect end 2</tiptext>
		</match>
		<match Key="18426">
			<htmlindex>Cable_op_cable92.html#144435</htmlindex>
			<tiptext>Disconnect indicate</tiptext>
		</match>
		<match Key="18492">
			<htmlindex>Cable_op_cable93.html#144510</htmlindex>
			<tiptext>Change data</tiptext>
		</match>
		<match Key="18415">
			<htmlindex>Cable_op_cable94.html#144552</htmlindex>
			<tiptext>Production information new mark point</tiptext>
		</match>
		<match Key="18415">
			<htmlindex>Cable_op_cable95.html#144597</htmlindex>
			<tiptext>Production information delete markpoint</tiptext>
		</match>
		<match Key="18416">
			<htmlindex>Cable_op_cable96.html#144629</htmlindex>
			<tiptext>Production information new group</tiptext>
		</match>
		<match Key="18416">
			<htmlindex>Cable_op_cable97.html#144675</htmlindex>
			<tiptext>Production information delete group</tiptext>
		</match>
		<match Key="18417">
			<htmlindex>Cable_op_cable98.html#144713</htmlindex>
			<tiptext>Production information display group</tiptext>
		</match>
		<match Key="18499">
			<htmlindex>Cable_op_cable99.html#144754</htmlindex>
			<tiptext>Ready</tiptext>
		</match>
		<match Key="18447">
			<htmlindex>Cable_op_cable100.html#144802</htmlindex>
			<tiptext>Update background</tiptext>
		</match>
		<match Key="18447">
			<htmlindex>Cable_op_cable101.html#144839</htmlindex>
			<tiptext>CW net background</tiptext>
		</match>
		<match Key="18141">
			<htmlindex>Cable_op_cable102.html#144967</htmlindex>
			<tiptext>Power calculate</tiptext>
		</match>
		<match Key="18141">
			<htmlindex>Cable_op_cable103.html#144989</htmlindex>
			<tiptext>Dimension</tiptext>
		</match>
		<match Key="18400">
			<htmlindex>Cable_op_cable104.html#145014</htmlindex>
			<tiptext>Check cableway</tiptext>
		</match>
		<match Key="18400">
			<htmlindex>Cable_op_cable105.html#152939</htmlindex>
			<tiptext>Check cableway</tiptext>
		</match>
		<match Key="18330">
			<htmlindex>Cable_op_cable106.html#145133</htmlindex>
			<tiptext>Check real penetration</tiptext>
		</match>
		<match Key="18330">
			<htmlindex>Cable_op_cable107.html#145157</htmlindex>
			<tiptext>Check imaginary penetration</tiptext>
		</match>
		<match Key="18330">
			<htmlindex>Cable_op_cable109.html#152477</htmlindex>
			<tiptext>Check imaginary penetration</tiptext>
		</match>
		<match Key="18330">
			<htmlindex>Cable_op_cable110.html#152508</htmlindex>
			<tiptext>Check imaginary penetration</tiptext>
		</match>
		<match Key="18330">
			<htmlindex>Cable_op_cable111.html#152540</htmlindex>
			<tiptext>Check imaginary penetration</tiptext>
		</match>
		<match Key="18447">
			<htmlindex>Cable_op_cable112.html#145184</htmlindex>
			<tiptext>List background</tiptext>
		</match>
		<match Key="18151">
			<htmlindex>Cable_op_cable113.html#145229</htmlindex>
			<tiptext>Dump</tiptext>
		</match>
	</book>
	<book Name="Calc.chm" ID="TBCALC">
		<match Key="IDH_ROOTFOLDER_NODE">
			<htmlindex>Calc_op5.html#141432</htmlindex>
			<tiptext>Calculations</tiptext>
		</match>
		<match Key="IDH_PREFERENCES">
			<htmlindex>Calc_op6.html#151781</htmlindex>
			<tiptext>Preferences</tiptext>
		</match>
		<match Key="IDH_OPTIONS">
			<htmlindex>Calc_op7.html#196398</htmlindex>
			<tiptext>Options</tiptext>
		</match>
		<match Key="IDH_NOTE_NODE">
			<htmlindex>Calc_op8.html#219707</htmlindex>
			<tiptext>Note</tiptext>
		</match>
		<match Key="IDH_EDITNOTE">
			<htmlindex>Calc_op9.html#219714</htmlindex>
			<tiptext>Note Dialog</tiptext>
		</match>
		<match Key="IDH_GENERAL_NODE">
			<htmlindex>Calc_op10.html#141477</htmlindex>
			<tiptext>General Particulars</tiptext>
		</match>
		<match Key="IDH_SHIPDATA_NODE">
			<htmlindex>Calc_op11.html#206550</htmlindex>
			<tiptext>Ship Data Setup</tiptext>
		</match>
		<match Key="IDH_GENERALPAGE">
			<htmlindex>Calc_op11.html#149527</htmlindex>
			<tiptext>Ship Data - General Sheet</tiptext>
		</match>
		<match Key="IDH_MAINPARTICULARS">
			<htmlindex>Calc_op11.html#149527</htmlindex>
			<tiptext>Ship Data - Main Particulars</tiptext>
		</match>
		<match Key="IDH_MAINDIMENSIONS">
			<htmlindex>Calc_op11.html#141523</htmlindex>
			<tiptext>Ship Data - Main Dimensions</tiptext>
		</match>
		<match Key="IDH_DRAFT_MARKS">
			<htmlindex>Calc_op11.html#167842</htmlindex>
			<tiptext>Ship Data - Draft Marks</tiptext>
		</match>
		<match Key="IDH_STABILITYPAGE">
			<htmlindex>Calc_op11.html#141534</htmlindex>
			<tiptext>Ship Data - Stability</tiptext>
		</match>
		<match Key="IDH_AXISPAGE">
			<htmlindex>Calc_op11.html#168667</htmlindex>
			<tiptext>Ship Data - Axis</tiptext>
		</match>
		<match Key="IDH_WAVESPAGE">
			<htmlindex>Calc_op11.html#149475</htmlindex>
			<tiptext>Ship Data - Waves</tiptext>
		</match>
		<match Key="IDH_FRAME_TABLE">
			<htmlindex>Calc_op11.html#149494</htmlindex>
			<tiptext>Ship Data - Frame Table</tiptext>
		</match>
		<match Key="IDH_WAVESDLG">
			<htmlindex>Calc_op11.html#219155</htmlindex>
			<tiptext>Axis Shift and Waves dialog</tiptext>
		</match>
		<match Key="IDH_TANKPLAN_NODE">
			<htmlindex>Calc_op12.html#149492</htmlindex>
			<tiptext>Tankplan</tiptext>
		</match>
		<match Key="IDH_TANKPLANPAGE1">
			<htmlindex>Calc_op12.html#141586</htmlindex>
			<tiptext>Tank Plan - Tank Plan</tiptext>
		</match>
		<match Key="IDH_TANKPLANPAGE2">
			<htmlindex>Calc_op12.html#214321</htmlindex>
			<tiptext>Tank Plan - Compartments</tiptext>
		</match>
		<match Key="IDH_CATEGORIES">
			<htmlindex>Calc_op12.html#150150</htmlindex>
			<tiptext>Tank Plan - Compartment Categories</tiptext>
		</match>
		<match Key="IDH_COMPARTMENT_EXTENTS">
			<htmlindex>Calc_op12.html#150155</htmlindex>
			<tiptext>Tank Plan - Compartment Extents</tiptext>
		</match>
		<match Key="IDH_CONTENT_TYPE">
			<htmlindex>Calc_op12.html#150155</htmlindex>
			<tiptext>Tank Plan - Content Types</tiptext>
		</match>
		<match Key="IDH_OPENINGS_NODE">
			<htmlindex>Calc_op13.html#141628</htmlindex>
			<tiptext>Other Data</tiptext>
		</match>
		<match Key="IDH_TITLEPAGE">
			<htmlindex>Calc_op13.html#141628</htmlindex>
			<tiptext>Other Data</tiptext>
		</match>
		<match Key="IDH_DECKEDGEPAGE">
			<htmlindex>Calc_op13.html#141636</htmlindex>
			<tiptext>Deck Edge Points</tiptext>
		</match>
		<match Key="IDH_PROFILEPAGE">
			<htmlindex>Calc_op13.html#141644</htmlindex>
			<tiptext>Profile Points</tiptext>
		</match>
		<match Key="IDH_UNPROTECTEDPAGE">
			<htmlindex>Calc_op13.html#141650</htmlindex>
			<tiptext>Unprotected Openings</tiptext>
		</match>
		<match Key="IDH_PROTECTEDPAGE">
			<htmlindex>Calc_op13.html#141662</htmlindex>
			<tiptext>Protected Openings</tiptext>
		</match>
		<match Key="IDH_SPILLOUTPAGE">
			<htmlindex>Calc_op13.html#214466</htmlindex>
			<tiptext>Spillout Points</tiptext>
		</match>
		<match Key="IDH_CONNECTED_COMPARTMENTSPAGE">
			<htmlindex>Calc_op13.html#215119</htmlindex>
			<tiptext>Connected Compartments</tiptext>
		</match>
		<match Key="IDH_BULKHEADS">
			<htmlindex>Calc_op13.html#215278</htmlindex>
			<tiptext>BM and SF Limits</tiptext>
		</match>
		<match Key="IDH_WINDAGE_AREA_PG">
			<htmlindex>Calc_op13.html#150258</htmlindex>
			<tiptext>Windage Areas</tiptext>
		</match>
		<match Key="IDH_LIGHTWEIGHT_NODE">
			<htmlindex>Calc_op14.html#202008</htmlindex>
			<tiptext>Lightweight Definition Overview</tiptext>
		</match>
		<match Key="IDH_LIGHTSHIPWEIGHT">
			<htmlindex>Calc_op14.html#202012</htmlindex>
			<tiptext>Lightweight Setup</tiptext>
		</match>
		<match Key="IDH_CONTAINERS_NODE">
			<htmlindex>Calc_op15.html#202023</htmlindex>
			<tiptext>Container Loading Introduction</tiptext>
		</match>
		<match Key="IDH_CONTAINER_BAYDEF">
			<htmlindex>Calc_op15.html#202026</htmlindex>
			<tiptext>Container Bay Definition</tiptext>
		</match>
		<match Key="IDH_CONTAINER_BAY_GENERAL">
			<htmlindex>Calc_op15.html#202030</htmlindex>
			<tiptext>Container Bay Definition</tiptext>
		</match>
		<match Key="IDH_CONTAINER_BAYDEF1">
			<htmlindex>Calc_op15.html#202032</htmlindex>
			<tiptext>Editing a Container Bay Definition</tiptext>
		</match>
		<match Key="IDH_CONTAINER_BAYDEF2">
			<htmlindex>Calc_op15.html#202041</htmlindex>
			<tiptext>Vertical Distance</tiptext>
		</match>
		<match Key="IDH_CONTAINER_BAYDEF3">
			<htmlindex>Calc_op15.html#202041</htmlindex>
			<tiptext>Transverse Distance</tiptext>
		</match>
		<match Key="IDH_CONTAINER_TYPES">
			<htmlindex>Calc_op15.html#202044</htmlindex>
			<tiptext>Container Types</tiptext>
		</match>
		<match Key="IDH_CONTAINER_LOADING">
			<htmlindex>Calc_op15.html#202060</htmlindex>
			<tiptext>Container Set Definition</tiptext>
		</match>
		<match Key="IDH_CONTLOAD">
			<htmlindex>Calc_op15.html#202064</htmlindex>
			<tiptext>Editing Container Sets</tiptext>
		</match>
		<match Key="IDH_CONTHATCHPAGE">
			<htmlindex>Calc_op15.html#202069</htmlindex>
			<tiptext>Container Hatch dimensions</tiptext>
		</match>
		<match Key="IDH_VISIBILITY_CHECK_NODE">
			<htmlindex>Calc_op16.html#208307</htmlindex>
			<tiptext>Visibility Check</tiptext>
		</match>
		<match Key="IDH_VIS_MAIN_PG">
			<htmlindex>Calc_op16.html#208309</htmlindex>
			<tiptext>General page</tiptext>
		</match>
		<match Key="IDH_VISIBILITY_CHECK_PG">
			<htmlindex>Calc_op16.html#208318</htmlindex>
			<tiptext>Visibility Check page</tiptext>
		</match>
		<match Key="IDH_TANK_CALIBRATIONS_NODE">
			<htmlindex>Calc_op17.html#141671</htmlindex>
			<tiptext>Tank Calibrations</tiptext>
		</match>
		<match Key="IDH_TANKCALSPAGE1">
			<htmlindex>Calc_op18.html#141676</htmlindex>
			<tiptext>Compartments to Calibrate</tiptext>
		</match>
		<match Key="IDH_TANKCALSPAGE2">
			<htmlindex>Calc_op19.html#141684</htmlindex>
			<tiptext>Default data setup</tiptext>
		</match>
		<match Key="IDH_TANK_NODE">
			<htmlindex>Calc_op19.html#141695</htmlindex>
			<tiptext>Calibrating individual compartments</tiptext>
		</match>
		<match Key="IDH_TANKCALS">
			<htmlindex>Calc_op19.html#141695</htmlindex>
			<tiptext>Calibrating individual compartments</tiptext>
		</match>
		<match Key="IDH_TABLEFORMAT">
			<htmlindex>Calc_op19.html#141710</htmlindex>
			<tiptext>Tabular output format</tiptext>
		</match>
		<match Key="IDH_SENSOR">
			<htmlindex>Calc_op20.html#141715</htmlindex>
			<tiptext>Sensor Types</tiptext>
		</match>
		<match Key="IDH_HOLDS_NODE">
			<htmlindex>Calc_op21.html#150384</htmlindex>
			<tiptext>Grain Calibrations</tiptext>
		</match>
		<match Key="IDH_GRAINCALSPAGE1">
			<htmlindex>Calc_op21.html#212209</htmlindex>
			<tiptext>Compartments page</tiptext>
		</match>
		<match Key="IDH_GRAINCALSPAGE2">
			<htmlindex>Calc_op21.html#212216</htmlindex>
			<tiptext>Default Data page</tiptext>
		</match>
		<match Key="IDH_GRAINPAGE1">
			<htmlindex>Calc_op21.html#212231</htmlindex>
			<tiptext>Compartments page</tiptext>
		</match>
		<match Key="IDH_GRAINPAGE2">
			<htmlindex>Calc_op21.html#212246</htmlindex>
			<tiptext>Filled Hold page</tiptext>
		</match>
		<match Key="IDH_GRAINPAGE3">
			<htmlindex>Calc_op21.html#212253</htmlindex>
			<tiptext>Hatch and girders page</tiptext>
		</match>
		<match Key="IDH_PERMISSIBLE_NODE">
			<htmlindex>Calc_op22.html#202513</htmlindex>
			<tiptext>Permissible Grain Moments</tiptext>
		</match>
		<match Key="IDH_PERMISSIBLE_GRAIN">
			<htmlindex>Calc_op22.html#210117</htmlindex>
			<tiptext>Input dialog</tiptext>
		</match>
		<match Key="IDH_FORM_CALCULATIONS_NODE">
			<htmlindex>Calc_op23.html#141749</htmlindex>
			<tiptext>Form Calculations</tiptext>
		</match>
		<match Key="IDH_HYDROSTATICS_NODE">
			<htmlindex>Calc_op24.html#141753</htmlindex>
			<tiptext>Hydrostatics Introduction</tiptext>
		</match>
		<match Key="IDH_HYDROS">
			<htmlindex>Calc_op24.html#141760</htmlindex>
			<tiptext>Calculating Hydrostatics</tiptext>
		</match>
		<match Key="IDH_DWTSCALE_NODE">
			<htmlindex>Calc_op25.html#141778</htmlindex>
			<tiptext>Deadweight Scale Introduction</tiptext>
		</match>
		<match Key="IDH_DEADWEIGHT">
			<htmlindex>Calc_op25.html#141791</htmlindex>
			<tiptext>Calculating a Deadweight Scale</tiptext>
		</match>
		<match Key="IDH_DEADWEIGHTFORMAT">
			<htmlindex>Calc_op25.html#200039</htmlindex>
			<tiptext>Modifying a Deadweight Scale</tiptext>
		</match>
		<match Key="IDH_CROSS_NODE">
			<htmlindex>Calc_op26.html#141798</htmlindex>
			<tiptext>Cross Curves Introduction</tiptext>
		</match>
		<match Key="IDH_CROSS_CURVES">
			<htmlindex>Calc_op26.html#211958</htmlindex>
			<tiptext>Calculating Cross Curves</tiptext>
		</match>
		<match Key="IDH_SAC_NODE">
			<htmlindex>Calc_op27.html#141818</htmlindex>
			<tiptext>Sectional Area Curves Introduction</tiptext>
		</match>
		<match Key="IDH_SAC">
			<htmlindex>Calc_op27.html#141826</htmlindex>
			<tiptext>Calculating Sectional Area Curves</tiptext>
		</match>
		<match Key="IDH_TRIMTABLE_NODE">
			<htmlindex>Calc_op28.html#169492</htmlindex>
			<tiptext>Trim Table</tiptext>
		</match>
		<match Key="IDH_TTGENERALPAGE">
			<htmlindex>Calc_op28.html#206751</htmlindex>
			<tiptext>General page</tiptext>
		</match>
		<match Key="IDH_TTCOMPARTMENTPAGE">
			<htmlindex>Calc_op28.html#206760</htmlindex>
			<tiptext>Compartments page</tiptext>
		</match>
		<match Key="IDH_STABILITY_NODE">
			<htmlindex>Calc_op29.html#169534</htmlindex>
			<tiptext>Stability Calculations</tiptext>
		</match>
		<match Key="IDH_DAMAGE_NODE">
			<htmlindex>Calc_op30.html#169443</htmlindex>
			<tiptext>Damage Scenarios</tiptext>
		</match>
		<match Key="IDH_DAMAGE">
			<htmlindex>Calc_op30.html#141871</htmlindex>
			<tiptext>Creating Damage Scenarios</tiptext>
		</match>
		<match Key="IDH_DETERMINISTIC_NODE">
			<htmlindex>Calc_op31.html#142032</htmlindex>
			<tiptext>Deterministic Stability Introduction</tiptext>
		</match>
		<match Key="IDH_DETERMINISTIC">
			<htmlindex>Calc_op31.html#142043</htmlindex>
			<tiptext>Calculating Deterministic Stability</tiptext>
		</match>
		<match Key="IDH_PROBABILISTIC_NODE">
			<htmlindex>Calc_op32.html#142060</htmlindex>
			<tiptext>Probabilistic Stability Introduction</tiptext>
		</match>
		<match Key="IDH_PROBCOMP">
			<htmlindex>Calc_op34.html#221215</htmlindex>
			<tiptext>Compartments</tiptext>
		</match>
		<match Key="IDH_PROB_SETUP_NODE">
			<htmlindex>Calc_op34.html#222169</htmlindex>
			<tiptext>Probabilistic Setup</tiptext>
		</match>
		<match Key="IDH_PROBSTABPAGE">
			<htmlindex>Calc_op34.html#222010</htmlindex>
			<tiptext>Probabilistic</tiptext>
		</match>
		<match Key="IDH_TANKPLANPAGE3">
			<htmlindex>Calc_op34.html#211425</htmlindex>
			<tiptext>Surfaces</tiptext>
		</match>
		<match Key="Compartment ExtentsIDH_COMPARTMENT_EXTENTS">
			<htmlindex>Calc_op34.html#211413</htmlindex>
			<tiptext>Compartment ExtentsIDH_COMPARTMENT_EXTENTS</tiptext>
		</match>
		<match Key="IDH_DAMAGE_GEN_MAIN">
			<htmlindex>Calc_op34.html#224202</htmlindex>
			<tiptext>General Page</tiptext>
		</match>
		<match Key="IDH_DAMAGE_GEN_COMP">
			<htmlindex>Calc_op34.html#222657</htmlindex>
			<tiptext>Compartments Page</tiptext>
		</match>
		<match Key="IDH_PROB_ASSESS_NODE">
			<htmlindex>Calc_op34.html#206480</htmlindex>
			<tiptext>Probabilistic Assessment</tiptext>
		</match>
		<match Key="IDH_PROBMAIN">
			<htmlindex>Calc_op34.html#211460</htmlindex>
			<tiptext>Probabilistic General page</tiptext>
		</match>
		<match Key="IDH_PROBDAMAGEPAGE">
			<htmlindex>Calc_op34.html#211466</htmlindex>
			<tiptext>Damage and Waves page</tiptext>
		</match>
		<match Key="IDH_LOADING_CONDITIONS_NODE">
			<htmlindex>Calc_op35.html#142184</htmlindex>
			<tiptext>Loading Condition Introduction</tiptext>
		</match>
		<match Key="IDH_LOADING_NODE">
			<htmlindex>Calc_op36.html#142324</htmlindex>
			<tiptext>Loading Condition Calculation</tiptext>
		</match>
		<match Key="IDH_LOADING">
			<htmlindex>Calc_op36.html#149590</htmlindex>
			<tiptext>Loading Condition Calculation Setup</tiptext>
		</match>
		<match Key="IDH_LOADINGPAGE">
			<htmlindex>Calc_op36.html#149590</htmlindex>
			<tiptext>Loading Condition Calculation Setup</tiptext>
		</match>
		<match Key="IDH_FIXEDWEIGHT">
			<htmlindex>Calc_op36.html#142348</htmlindex>
			<tiptext>Loading Condition - Fixed Weights</tiptext>
		</match>
		<match Key="IDH_LOADINGSINGLE">
			<htmlindex>Calc_op36.html#142359</htmlindex>
			<tiptext>Loading Condition - Load Groups</tiptext>
		</match>
		<match Key="IDH_DAMAGEPAGE">
			<htmlindex>Calc_op36.html#142374</htmlindex>
			<tiptext>Selecting Damage and Waves for Loading</tiptext>
		</match>
		<match Key="IDH_AUTOLOADPAGE">
			<htmlindex>Calc_op36.html#142383</htmlindex>
			<tiptext>Automatic Loading</tiptext>
		</match>
		<match Key="IDH_STAB_NODE">
			<htmlindex>Calc_op37.html#158471</htmlindex>
			<tiptext>Summary Table</tiptext>
		</match>
		<match Key="IDH_STABPAGE2">
			<htmlindex>Calc_op37.html#158471</htmlindex>
			<tiptext>Summary Table</tiptext>
		</match>
		<match Key="IDH_STABPAGE1">
			<htmlindex>Calc_op38.html#142419</htmlindex>
			<tiptext>Selecting Loading Conditions</tiptext>
		</match>
		<match Key="IDH_LOADSEQ_NODE">
			<htmlindex>Calc_op39.html#204674</htmlindex>
			<tiptext>Loading/Discharging Sequence</tiptext>
		</match>
		<match Key="IDH_LOADSEQMAINPAGE">
			<htmlindex>Calc_op39.html#204677</htmlindex>
			<tiptext>General page</tiptext>
		</match>
		<match Key="IDH_LOADSEQSTAGEPAGE">
			<htmlindex>Calc_op39.html#204687</htmlindex>
			<tiptext>Loading Stages</tiptext>
		</match>
		<match Key="IDH_LOADSEQSTAGE">
			<htmlindex>Calc_op39.html#204694</htmlindex>
			<tiptext>Loading/Discharging Stage dialog</tiptext>
		</match>
		<match Key="IDH_CFLOOD_NODE">
			<htmlindex>Calc_op40.html#215535</htmlindex>
			<tiptext>Continuous Flooding</tiptext>
		</match>
		<match Key="IDH_CFLOOD_MAINPAGE">
			<htmlindex>Calc_op40.html#215552</htmlindex>
			<tiptext>Continuous Flooding - General Page</tiptext>
		</match>
		<match Key="IDH_CFLOOD_OPENINGSPAGE">
			<htmlindex>Calc_op40.html#215563</htmlindex>
			<tiptext>Continuous Flooding - Openings Page</tiptext>
		</match>
		<match Key="IDH_CFLOOD_PUMPSPAGE">
			<htmlindex>Calc_op40.html#215592</htmlindex>
			<tiptext>Continuous Flooding - Pumps Page</tiptext>
		</match>
		<match Key="IDH_REGULATIONS_NODE">
			<htmlindex>Calc_op41.html#142423</htmlindex>
			<tiptext>Regulations</tiptext>
		</match>
		<match Key="IDH_TONNAGE_NODE">
			<htmlindex>Calc_op42.html#142427</htmlindex>
			<tiptext>Tonnage - Introduction</tiptext>
		</match>
		<match Key="IDH_TONNAGE">
			<htmlindex>Calc_op42.html#142432</htmlindex>
			<tiptext>Tonnage - Setup</tiptext>
		</match>
		<match Key="IDH_FREEBOARD_NODE">
			<htmlindex>Calc_op43.html#142446</htmlindex>
			<tiptext>Freeboard Introduction</tiptext>
		</match>
		<match Key="IDH_FR_GENERAL">
			<htmlindex>Calc_op43.html#142456</htmlindex>
			<tiptext>General</tiptext>
		</match>
		<match Key="IDH_FR_BRANDFORE">
			<htmlindex>Calc_op43.html#238306</htmlindex>
			<tiptext>Superstructure</tiptext>
		</match>
		<match Key="IDH_FR_SHEAR">
			<htmlindex>Calc_op43.html#238407</htmlindex>
			<tiptext>Freeboard Deck</tiptext>
		</match>
		<match Key="IDH_FLOOD_NODE">
			<htmlindex>Calc_op44.html#142592</htmlindex>
			<tiptext>Floodable Length</tiptext>
		</match>
		<match Key="IDH_FLOOD">
			<htmlindex>Calc_op44.html#203390</htmlindex>
			<tiptext>General Page</tiptext>
		</match>
		<match Key="IDH_COS_PAGE">
			<htmlindex>Calc_op44.html#203401</htmlindex>
			<tiptext>Criterion of Service</tiptext>
		</match>
		<match Key="IDH_EQUIPMENT_NUMBER_NODE">
			<htmlindex>Calc_op45.html#206665</htmlindex>
			<tiptext>Equipment Number</tiptext>
		</match>
		<match Key="IDH_EQUIPMENT_NUMBER">
			<htmlindex>Calc_op45.html#212044</htmlindex>
			<tiptext>Input dialog</tiptext>
		</match>
		<match Key="IDH_MISCELLANEOUS_NODE">
			<htmlindex>Calc_op46.html#203373</htmlindex>
			<tiptext>Miscellaneous Calculations</tiptext>
		</match>
		<match Key="IDH_LAUNCH_NODE">
			<htmlindex>Calc_op47.html#153169</htmlindex>
			<tiptext>Launching Calculations Introduction</tiptext>
		</match>
		<match Key="IDH_LAUNCH">
			<htmlindex>Calc_op47.html#142600</htmlindex>
			<tiptext>Entering Data for Launching Calulations</tiptext>
		</match>
		<match Key="IDH_INCLINE_NODE">
			<htmlindex>Calc_op48.html#224731</htmlindex>
			<tiptext>Inclining Experiment</tiptext>
		</match>
		<match Key="IDH_INCLINEPAGE1">
			<htmlindex>Calc_op48.html#147810</htmlindex>
			<tiptext>Enter General Description Data</tiptext>
		</match>
		<match Key="IDH_INCLINEPAGE2">
			<htmlindex>Calc_op48.html#142692</htmlindex>
			<tiptext>Enter ship drafts</tiptext>
		</match>
		<match Key="IDH_INCLINEPAGE3">
			<htmlindex>Calc_op48.html#142696</htmlindex>
			<tiptext>Enter pendulum information</tiptext>
		</match>
		<match Key="IDH_INCLINEPAGE4">
			<htmlindex>Calc_op48.html#142700</htmlindex>
			<tiptext>Enter experiment readings</tiptext>
		</match>
		<match Key="IDH_INCLINEPAGE5">
			<htmlindex>Calc_op48.html#142704</htmlindex>
			<tiptext>Input information on weights to add</tiptext>
		</match>
		<match Key="IDH_INCLINEPAGE6">
			<htmlindex>Calc_op48.html#142710</htmlindex>
			<tiptext>Input information on weights to come off</tiptext>
		</match>
		<match Key="IDH_INCLINEPAGE8">
			<htmlindex>Calc_op48.html#142714</htmlindex>
			<tiptext>Input tank contents data</tiptext>
		</match>
		<match Key="IDH_REPORT_EDITOR_SHEET">
			<htmlindex>Calc_op49.html#204498</htmlindex>
			<tiptext>Report Editing</tiptext>
		</match>
		<match Key="IDH_REPORT_EDITOR_GENERAL_PG">
			<htmlindex>Calc_op50.html#204506</htmlindex>
			<tiptext>Report Order Page</tiptext>
		</match>
		<match Key="IDH_REPORT_EDITOR_TABLES_PG">
			<htmlindex>Calc_op54.html#204532</htmlindex>
			<tiptext>Table Page</tiptext>
		</match>
		<match Key="IDH_REPORT_EDITOR_GRAPHS_PG">
			<htmlindex>Calc_op57.html#204552</htmlindex>
			<tiptext>Graphs Page</tiptext>
		</match>
		<match Key="IDH_REPORT_EDITOR_DIAGRAM_PG">
			<htmlindex>Calc_op60.html#204569</htmlindex>
			<tiptext>Diagram Page</tiptext>
		</match>
		<match Key="IDH_CRITERIA_A">
			<htmlindex>Calc_op63.html#210607</htmlindex>
			<tiptext>Sets</tiptext>
		</match>
		<match Key="IDH_CRITERIA_B">
			<htmlindex>Calc_op64.html#210618</htmlindex>
			<tiptext>Edit</tiptext>
		</match>
		<match Key="IDH_TID_GEOMETRY_XML_FILE">
			<htmlindex>Calc_op70.html#172397</htmlindex>
			<tiptext>TID Geometry XML File format</tiptext>
		</match>
	</book>
	<book Name="Compartment.chm" ID="TBCOMPART">
		<match Key="HIDD_ENVELOPE_DEFINITION">
			<htmlindex>Compartment_op2.html#1011905</htmlindex>
			<tiptext>Envelope Definition</tiptext>
		</match>
		<match Key="HIDD_ENVELOPE_GENERAL_SHEET">
			<htmlindex>Compartment_op6.html#1012091</htmlindex>
			<tiptext>Envelope General</tiptext>
		</match>
		<match Key="HIDD_ENVELOPE_PROFILE_PAGE">
			<htmlindex>Compartment_op7.html#1012166</htmlindex>
			<tiptext>Profile Points</tiptext>
		</match>
		<match Key="HIDD_ENVELOPE_DECK_EDGE_PAGE">
			<htmlindex>Compartment_op8.html#1012202</htmlindex>
			<tiptext>Deck Edge Points</tiptext>
		</match>
		<match Key="HIDD_INTERNAL_SURFACE_EDITOR_SHEET">
			<htmlindex>Compartment_op9.html#1012095</htmlindex>
			<tiptext>Internal Surface Editor</tiptext>
		</match>
		<match Key="HIDD_INT_SURFACE_GEOMETRY_PAGE">
			<htmlindex>Compartment_op10.html#1006218</htmlindex>
			<tiptext>Geometry Page</tiptext>
		</match>
		<match Key="HIDD_INT_SURFACE_CONSTRAINTS_PAGE">
			<htmlindex>Compartment_op13.html#1004264</htmlindex>
			<tiptext>Constraints Page</tiptext>
		</match>
		<match Key="HIDD_INT_SURFACE_CORRUGATION_PAGE">
			<htmlindex>Compartment_op14.html#1004356</htmlindex>
			<tiptext>Corrugation Page</tiptext>
		</match>
		<match Key="HIDD_INT_SURFACE_STOOLS_PAGE">
			<htmlindex>Compartment_op15.html#1004361</htmlindex>
			<tiptext>Stools Page</tiptext>
		</match>
		<match Key="HIDD_STOOL_PROFILE_EDITOR">
			<htmlindex>Compartment_op16.html#1013867</htmlindex>
			<tiptext>Stool Profile</tiptext>
		</match>
		<match Key="HIDD_COMPARTMENTS_GENERAL_SHEET">
			<htmlindex>Compartment_op17.html#1012246</htmlindex>
			<tiptext>Compartments General</tiptext>
		</match>
		<match Key="HIDD_COMPARTMENTS_CONTENTS_PAGE">
			<htmlindex>Compartment_op18.html#1019573</htmlindex>
			<tiptext>Compartment Contents</tiptext>
		</match>
		<match Key="HIDD_COMPARTMENTS_CATEGORIES_PAGE">
			<htmlindex>Compartment_op19.html#1012264</htmlindex>
			<tiptext>Compartment Categories</tiptext>
		</match>
		<match Key="HIDD_COMPARTMENT_SHEET">
			<htmlindex>Compartment_op20.html#1004380</htmlindex>
			<tiptext>Compartment Editor Sheet</tiptext>
		</match>
		<match Key="HIDD_COMPARTMENT_BASIC_PAGE">
			<htmlindex>Compartment_op21.html#1004392</htmlindex>
			<tiptext>Basic Page</tiptext>
		</match>
		<match Key="HIDD_COMPARTMENT_MERGED_PAGE">
			<htmlindex>Compartment_op25.html#1004398</htmlindex>
			<tiptext>Merged Page</tiptext>
		</match>
		<match Key="HIDD_CORRUGATIONS">
			<htmlindex>Compartment_op29.html#1007665</htmlindex>
			<tiptext>Corrugation Profile</tiptext>
		</match>
		<match Key="HIDD_SAC_CURVE">
			<htmlindex>Compartment_op30.html#1004409</htmlindex>
			<tiptext>Sectional Area Curve</tiptext>
		</match>
		<match Key="HIDD_COMPARTMENTATION_REPORT">
			<htmlindex>Compartment_op31.html#1011858</htmlindex>
			<tiptext>Compartment Reports</tiptext>
		</match>
		<match Key="HIDD_OBJECT_CONVERSION">
			<htmlindex>Compartment_op32.html#1004440</htmlindex>
			<tiptext>Object Conversion</tiptext>
		</match>
		<match Key="HIDD_CONTAINERS">
			<htmlindex>Compartment_op33.html#1009319</htmlindex>
			<tiptext>Containers Dialog</tiptext>
		</match>
		<match Key="HIDD_BAY_DEFINITION_SHEET">
			<htmlindex>Compartment_op37.html#1009328</htmlindex>
			<tiptext>Bay Definition Sheet</tiptext>
		</match>
		<match Key="HIDD_BAY_DEFINITION_BASIC_PAGE">
			<htmlindex>Compartment_op39.html#1009588</htmlindex>
			<tiptext>Basic page</tiptext>
		</match>
		<match Key="HIDD_BAY_DEFINITION_CONTAINER_PAGE">
			<htmlindex>Compartment_op40.html#1009967</htmlindex>
			<tiptext>Container Positions Page</tiptext>
		</match>
		<match Key="HIDD_BAY_DEFINITION_SIGHTLINE_PAGE">
			<htmlindex>Compartment_op45.html#1010319</htmlindex>
			<tiptext>Sight line page</tiptext>
		</match>
		<match Key="HIDD_HATCHES">
			<htmlindex>Compartment_op50.html#1010382</htmlindex>
			<tiptext>Hatches Dialog</tiptext>
		</match>
		<match Key="HIDD_CONTAINER_LOADING_REPORT">
			<htmlindex>Compartment_op56.html#1010513</htmlindex>
			<tiptext>Container Arrangement Report</tiptext>
		</match>
		<match Key="HIDD_ANALYSIS_WEIGHTS">
			<htmlindex>Compartment_op57.html#1010255</htmlindex>
			<tiptext>Weight Analysis</tiptext>
		</match>
		<match Key="HIDD_ENVELOPE_PROFILE_PAGE">
			<htmlindex>Compartment_414.html#1026765</htmlindex>
			<tiptext>Profile Points</tiptext>
		</match>
		<match Key="HIDD_ENVELOPE_DECK_EDGE_PAGE">
			<htmlindex>Compartment_415.html#1026786</htmlindex>
			<tiptext>Deck Edge Points</tiptext>
		</match>
		<match Key="HIDD_STOOL_PROFILE_EDITOR">
			<htmlindex>Compartment_423.html#1027620</htmlindex>
			<tiptext>Stool Profile</tiptext>
		</match>
		<match Key="HIDD_COMMANDS">
			<htmlindex>Compartment_62.html#145742</htmlindex>
			<tiptext>Compartment Commands</tiptext>
		</match>
	</book>
	<book Name="Components.chm" ID="TBCOMP">
		<match Key="TBCOMP_ViewsAndTools">
			<htmlindex>Components_op2.html#143437</htmlindex>
			<tiptext>Views and Tools</tiptext>
		</match>
		<match Key="TNCOMP_UiOverview">
			<htmlindex>Components_op3.html#141149</htmlindex>
			<tiptext>User Interface Overview</tiptext>
		</match>
		<match Key="TBCOMP_TreeTool">
			<htmlindex>Components_op4.html#141239</htmlindex>
			<tiptext>Navigation Tree Tool</tiptext>
		</match>
		<match Key="TBCOMP_SearchTool">
			<htmlindex>Components_op5.html#141264</htmlindex>
			<tiptext>Search Tool</tiptext>
		</match>
		<match Key="TBCOMP_PropertyTool">
			<htmlindex>Components_op6.html#141293</htmlindex>
			<tiptext>Property Tool</tiptext>
		</match>
		<match Key="TBCOMP_ComponentListTool">
			<htmlindex>Components_op7.html#141318</htmlindex>
			<tiptext>Component List Tool</tiptext>
		</match>
		<match Key="TBCOMP_FilterTool">
			<htmlindex>Components_op8.html#141343</htmlindex>
			<tiptext>Filter Tool</tiptext>
		</match>
		<match Key="TBCOMP_OptionsTool">
			<htmlindex>Components_op9.html#141372</htmlindex>
			<tiptext>Options Tool</tiptext>
		</match>
		<match Key="TBCOMP_OptionsTool">
			<htmlindex>Components_op10.html#142385</htmlindex>
			<tiptext>Options Tool</tiptext>
		</match>
	</book>
	<book Name="Curved_Modelling.chm" ID="TBCHULLM">
		<match Key="9720">
			<htmlindex>Curved_Modelling_op3.html#144314</htmlindex>
			<tiptext>Create or modify a seam or a butt</tiptext>
		</match>
		<match Key="9725">
			<htmlindex>Curved_Modelling_op4.html#144340</htmlindex>
			<tiptext>Create or modify a shell plate</tiptext>
		</match>
		<match Key="9734">
			<htmlindex>Curved_Modelling_op5.html#144366</htmlindex>
			<tiptext>Create or modify a shell profile</tiptext>
		</match>
		<match Key="9735">
			<htmlindex>Curved_Modelling_op6.html#144389</htmlindex>
			<tiptext>Split shell stiffener</tiptext>
		</match>
		<match Key="9736">
			<htmlindex>Curved_Modelling_op7.html#144412</htmlindex>
			<tiptext>Combine shell stiffeners</tiptext>
		</match>
		<match Key="9751">
			<htmlindex>Curved_Modelling_op8.html#144435</htmlindex>
			<tiptext>Store shell stiffeners on profile data bank</tiptext>
		</match>
		<match Key="9755">
			<htmlindex>Curved_Modelling_op9.html#144461</htmlindex>
			<tiptext>Create or modify a hole in a shell profile</tiptext>
		</match>
		<match Key="9755">
			<htmlindex>Curved_Modelling_op10.html#144487</htmlindex>
			<tiptext>Create or modify a notch in a shell profile</tiptext>
		</match>
		<match Key="9755">
			<htmlindex>Curved_Modelling_op11.html#144513</htmlindex>
			<tiptext>Create or model a cutout in a shell profile</tiptext>
		</match>
		<match Key="9755">
			<htmlindex>Curved_Modelling_op12.html#144539</htmlindex>
			<tiptext>Create or modify a marking in a shell profile</tiptext>
		</match>
		<match Key="9760">
			<htmlindex>Curved_Modelling_op13.html#144565</htmlindex>
			<tiptext>Create or modify a curved panel</tiptext>
		</match>
		<match Key="9728">
			<htmlindex>Curved_Modelling_op14.html#144592</htmlindex>
			<tiptext>Create or modify a hull curve</tiptext>
		</match>
		<match Key="9745">
			<htmlindex>Curved_Modelling_op15.html#144618</htmlindex>
			<tiptext>Create or modify a storable plane</tiptext>
		</match>
		<match Key="9727">
			<htmlindex>Curved_Modelling_op16.html#144641</htmlindex>
			<tiptext>Delete a model object from the data bank</tiptext>
		</match>
		<match Key="9724">
			<htmlindex>Curved_Modelling_op17.html#144664</htmlindex>
			<tiptext>Recreate model objects</tiptext>
		</match>
		<match Key="9729">
			<htmlindex>Curved_Modelling_op18.html#144721</htmlindex>
			<tiptext>Modify an object</tiptext>
		</match>
		<match Key="9731">
			<htmlindex>Curved_Modelling_op20.html#144748</htmlindex>
			<tiptext>Create or modify a point defined by three co-ordinates</tiptext>
		</match>
		<match Key="9732">
			<htmlindex>Curved_Modelling_op21.html#144773</htmlindex>
			<tiptext>Create or modify a point on the surface</tiptext>
		</match>
		<match Key="9733">
			<htmlindex>Curved_Modelling_op22.html#144798</htmlindex>
			<tiptext>Create or modify a point in the intersection between a curve and a plane</tiptext>
		</match>
		<match Key="9711">
			<htmlindex>Curved_Modelling_op23.html#144823</htmlindex>
			<tiptext>Create or modify a principal plane</tiptext>
		</match>
		<match Key="9713">
			<htmlindex>Curved_Modelling_op24.html#144848</htmlindex>
			<tiptext>Create or modify a plane defined by three points</tiptext>
		</match>
		<match Key="9712">
			<htmlindex>Curved_Modelling_op25.html#144873</htmlindex>
			<tiptext>Create or modify a plane defined by two points and a principal axis</tiptext>
		</match>
		<match Key="9714">
			<htmlindex>Curved_Modelling_op26.html#144898</htmlindex>
			<tiptext>Create or modify a plane defined by two points and an angle aginst a principal axis</tiptext>
		</match>
		<match Key="9726">
			<htmlindex>Curved_Modelling_op27.html#144923</htmlindex>
			<tiptext>Create or modify a general cylinder</tiptext>
		</match>
		<match Key="9717">
			<htmlindex>Curved_Modelling_op28.html#144948</htmlindex>
			<tiptext>Create or modify a shell curve defined by the intersection between a plane and a surface</tiptext>
		</match>
		<match Key="9718">
			<htmlindex>Curved_Modelling_op29.html#144973</htmlindex>
			<tiptext>Create or modify a shell curve defined by the intersection between a general cylinder and a surface</tiptext>
		</match>
		<match Key="9719">
			<htmlindex>Curved_Modelling_op30.html#144998</htmlindex>
			<tiptext>Create or modify a parallel shell curve</tiptext>
		</match>
		<match Key="9715">
			<htmlindex>Curved_Modelling_op31.html#145023</htmlindex>
			<tiptext>Create or modify a shell curve defined as the combination of two other curves</tiptext>
		</match>
		<match Key="9715">
			<htmlindex>Curved_Modelling_op32.html#145048</htmlindex>
			<tiptext>Create or modify a shell curve defined as the combination of two other curves</tiptext>
		</match>
		<match Key="9704">
			<htmlindex>Curved_Modelling_op34.html#141201</htmlindex>
			<tiptext>Select default surface</tiptext>
		</match>
		<match Key="9705">
			<htmlindex>Curved_Modelling_op35.html#141223</htmlindex>
			<tiptext>Set default box</tiptext>
		</match>
		<match Key="9707">
			<htmlindex>Curved_Modelling_op37.html#141478</htmlindex>
			<tiptext>Create shell expansion view</tiptext>
		</match>
		<match Key="8740">
			<htmlindex>Curved_Modelling_op38.html#141511</htmlindex>
			<tiptext>Create bodyplan view</tiptext>
		</match>
		<match Key="9703">
			<htmlindex>Curved_Modelling_op39.html#141534</htmlindex>
			<tiptext>Create developed plate view</tiptext>
		</match>
		<match Key="9706">
			<htmlindex>Curved_Modelling_op40.html#144273</htmlindex>
			<tiptext>Create plate template view</tiptext>
		</match>
		<match Key="9708">
			<htmlindex>Curved_Modelling_op40.html#144273</htmlindex>
			<tiptext>Create shell profile view</tiptext>
		</match>
		<match Key="9420">
			<htmlindex>Curved_Modelling_op41.html#141578</htmlindex>
			<tiptext>Create curved panel view</tiptext>
		</match>
		<match Key="14066">
			<htmlindex>Curved_Modelling_op42.html#141622</htmlindex>
			<tiptext>Recreate view</tiptext>
		</match>
		<match Key="14066">
			<htmlindex>Curved_Modelling_op43.html#143982</htmlindex>
			<tiptext>Recreate view</tiptext>
		</match>
		<match Key="9770">
			<htmlindex>Curved_Modelling_op45.html#145093</htmlindex>
			<tiptext>Select object in drawing</tiptext>
		</match>
		<match Key="9770">
			<htmlindex>Curved_Modelling_op46.html#145125</htmlindex>
			<tiptext>Advanced object selection</tiptext>
		</match>
		<match Key="9723">
			<htmlindex>Curved_Modelling_op47.html#145148</htmlindex>
			<tiptext>Store selected objects</tiptext>
		</match>
		<match Key="9723">
			<htmlindex>Curved_Modelling_op48.html#145171</htmlindex>
			<tiptext>Store selected objects</tiptext>
		</match>
		<match Key="9770">
			<htmlindex>Curved_Modelling_op49.html#145196</htmlindex>
			<tiptext>Remove objects from selection</tiptext>
		</match>
		<match Key="9770">
			<htmlindex>Curved_Modelling_op50.html#145219</htmlindex>
			<tiptext>Remove all objects from selection</tiptext>
		</match>
		<match Key="9750">
			<htmlindex>Curved_Modelling_op51.html#145242</htmlindex>
			<tiptext>List selected objects</tiptext>
		</match>
	</book>
	<book Name="Design_Manager.chm" ID="TBDESM">
	</book>
	<book Name="Diagrams.chm" ID="TBDIAGRAM">
	</book>
	<book Name="Drafting.chm" ID="TBDRAFT">
		<match Key="10301">
			<htmlindex>Drafting_op3.html#161288</htmlindex>
			<tiptext>File new</tiptext>
		</match>
		<match Key="10308">
			<htmlindex>Drafting_op4.html#161305</htmlindex>
			<tiptext>File open</tiptext>
		</match>
		<match Key="10309">
			<htmlindex>Drafting_op6.html#221955</htmlindex>
			<tiptext>Save</tiptext>
		</match>
		<match Key="10326">
			<htmlindex>Drafting_op7.html#222277</htmlindex>
			<tiptext>Save as</tiptext>
		</match>
		<match Key="10225">
			<htmlindex>Drafting_op14.html#222086</htmlindex>
			<tiptext>Subdrawing</tiptext>
		</match>
		<match Key="10240">
			<htmlindex>Drafting_op18.html#169595</htmlindex>
			<tiptext>Define drawing reference</tiptext>
		</match>
		<match Key="10241">
			<htmlindex>Drafting_op19.html#169634</htmlindex>
			<tiptext>Collapse drawing reference</tiptext>
		</match>
		<match Key="10242">
			<htmlindex>Drafting_op20.html#170255</htmlindex>
			<tiptext>Expand drawing reference</tiptext>
		</match>
		<match Key="10243">
			<htmlindex>Drafting_op21.html#172388</htmlindex>
			<tiptext>Exchange drawing reference</tiptext>
		</match>
		<match Key="10244">
			<htmlindex>Drafting_op22.html#172408</htmlindex>
			<tiptext>Dissolve drawing reference</tiptext>
		</match>
		<match Key="10245">
			<htmlindex>Drafting_op23.html#172426</htmlindex>
			<tiptext>Show drawing reference</tiptext>
		</match>
		<match Key="10106">
			<htmlindex>Drafting_op41.html#179176</htmlindex>
			<tiptext>Edit delete</tiptext>
		</match>
		<match Key="10108">
			<htmlindex>Drafting_op41.html#179176</htmlindex>
			<tiptext>Edit delete</tiptext>
		</match>
		<match Key="10107">
			<htmlindex>Drafting_op43.html#173888</htmlindex>
			<tiptext>Delete subpicture</tiptext>
		</match>
		<match Key="10190">
			<htmlindex>Drafting_op46.html#174186</htmlindex>
			<tiptext>Dimension note</tiptext>
		</match>
		<match Key="10192">
			<htmlindex>Drafting_op47.html#174305</htmlindex>
			<tiptext>Dimension position number</tiptext>
		</match>
		<match Key="10170">
			<htmlindex>Drafting_op58.html#179629</htmlindex>
			<tiptext>Edit text</tiptext>
		</match>
		<match Key="11122">
			<htmlindex>Drafting_op74.html#189101</htmlindex>
			<tiptext>View layer</tiptext>
		</match>
		<match Key="3801">
			<htmlindex>Drafting_op78.html#174963</htmlindex>
			<tiptext>Define grid</tiptext>
		</match>
		<match Key="10744">
			<htmlindex>Drafting_op103.html#301874</htmlindex>
			<tiptext>Ellipse andpoints and data</tiptext>
		</match>
		<match Key="10745">
			<htmlindex>Drafting_op103.html#301874</htmlindex>
			<tiptext>Segment slopes and point data</tiptext>
		</match>
		<match Key="10076">
			<htmlindex>Drafting_op122.html#246055</htmlindex>
			<tiptext>Spline</tiptext>
		</match>
		<match Key="10078">
			<htmlindex>Drafting_op124.html#246238</htmlindex>
			<tiptext>Rectangle</tiptext>
		</match>
		<match Key="10078">
			<htmlindex>Drafting_op125.html#246327</htmlindex>
			<tiptext>Square</tiptext>
		</match>
		<match Key="10085">
			<htmlindex>Drafting_op126.html#246353</htmlindex>
			<tiptext>Parallel curve</tiptext>
		</match>
		<match Key="10086">
			<htmlindex>Drafting_op127.html#246385</htmlindex>
			<tiptext>Fillet</tiptext>
		</match>
		<match Key="10421">
			<htmlindex>Drafting_op128.html#253901</htmlindex>
			<tiptext>Hatch pattern</tiptext>
		</match>
		<match Key="10176">
			<htmlindex>Drafting_op130.html#254062</htmlindex>
			<tiptext>Text</tiptext>
		</match>
		<match Key="10182">
			<htmlindex>Drafting_op131.html#275336</htmlindex>
			<tiptext>Text file</tiptext>
		</match>
		<match Key="10183">
			<htmlindex>Drafting_op132.html#246827</htmlindex>
			<tiptext>Underline</tiptext>
		</match>
		<match Key="10151">
			<htmlindex>Drafting_op133.html#246851</htmlindex>
			<tiptext>Symbol</tiptext>
		</match>
		<match Key="10427">
			<htmlindex>Drafting_op148.html#306787</htmlindex>
			<tiptext>Format hatch pattern</tiptext>
		</match>
		<match Key="10261">
			<htmlindex>Drafting_op149.html#306834</htmlindex>
			<tiptext>Format defaults</tiptext>
		</match>
		<match Key="10229">
			<htmlindex>Drafting_op189.html#194000</htmlindex>
			<tiptext>Exchange subpicture</tiptext>
		</match>
		<match Key="10229">
			<htmlindex>Drafting_op190.html#251733</htmlindex>
			<tiptext>Exchange subpicture</tiptext>
		</match>
		<match Key="10552">
			<htmlindex>Drafting_op193.html#194128</htmlindex>
			<tiptext>Split</tiptext>
		</match>
		<match Key="10403">
			<htmlindex>Drafting_op248.html#194891</htmlindex>
			<tiptext>Dimension Linear</tiptext>
		</match>
		<match Key="10406">
			<htmlindex>Drafting_op249.html#195555</htmlindex>
			<tiptext>Dimension radius</tiptext>
		</match>
		<match Key="10415">
			<htmlindex>Drafting_op250.html#195869</htmlindex>
			<tiptext>Dimension diameter</tiptext>
		</match>
		<match Key="10405">
			<htmlindex>Drafting_op251.html#178289</htmlindex>
			<tiptext>Dimension angle</tiptext>
		</match>
		<match Key="10407">
			<htmlindex>Drafting_op252.html#178465</htmlindex>
			<tiptext>Dimension curve length</tiptext>
		</match>
		<match Key="10407">
			<htmlindex>Drafting_op253.html#259571</htmlindex>
			<tiptext>Dimension curve length</tiptext>
		</match>
		<match Key="10407">
			<htmlindex>Drafting_op254.html#259616</htmlindex>
			<tiptext>Dimension curve length</tiptext>
		</match>
		<match Key="10410">
			<htmlindex>Drafting_op255.html#196248</htmlindex>
			<tiptext>Dimension area</tiptext>
		</match>
		<match Key="10410">
			<htmlindex>Drafting_op256.html#259864</htmlindex>
			<tiptext>Dimension area</tiptext>
		</match>
		<match Key="10410">
			<htmlindex>Drafting_op257.html#259992</htmlindex>
			<tiptext>Dimension area</tiptext>
		</match>
		<match Key="10410">
			<htmlindex>Drafting_op259.html#260127</htmlindex>
			<tiptext>Dimension area</tiptext>
		</match>
		<match Key="10410">
			<htmlindex>Drafting_op260.html#260229</htmlindex>
			<tiptext>Dimension area</tiptext>
		</match>
		<match Key="10410">
			<htmlindex>Drafting_op261.html#260339</htmlindex>
			<tiptext>Dimension area</tiptext>
		</match>
		<match Key="10410">
			<htmlindex>Drafting_op262.html#260528</htmlindex>
			<tiptext>Dimension area</tiptext>
		</match>
		<match Key="10410">
			<htmlindex>Drafting_op262.html#260706</htmlindex>
			<tiptext>Dimension area</tiptext>
		</match>
		<match Key="10190">
			<htmlindex>Drafting_op276.html#262059</htmlindex>
			<tiptext>Dimension note</tiptext>
		</match>
		<match Key="10190">
			<htmlindex>Drafting_op277.html#207203</htmlindex>
			<tiptext>Dimension note</tiptext>
		</match>
		<match Key="10190">
			<htmlindex>Drafting_op278.html#275533</htmlindex>
			<tiptext>Dimension note</tiptext>
		</match>
		<match Key="10190">
			<htmlindex>Drafting_op279.html#275624</htmlindex>
			<tiptext>Dimension note</tiptext>
		</match>
		<match Key="10190">
			<htmlindex>Drafting_op280.html#275718</htmlindex>
			<tiptext>Dimension note</tiptext>
		</match>
		<match Key="10190">
			<htmlindex>Drafting_op281.html#275643</htmlindex>
			<tiptext>Dimension note</tiptext>
		</match>
		<match Key="10190">
			<htmlindex>Drafting_op282.html#262288</htmlindex>
			<tiptext>Dimension note</tiptext>
		</match>
		<match Key="10190">
			<htmlindex>Drafting_op283.html#262315</htmlindex>
			<tiptext>Dimension note</tiptext>
		</match>
		<match Key="10190">
			<htmlindex>Drafting_op284.html#262342</htmlindex>
			<tiptext>Dimension note</tiptext>
		</match>
		<match Key="10190">
			<htmlindex>Drafting_op285.html#262369</htmlindex>
			<tiptext>Dimension note</tiptext>
		</match>
		<match Key="10190">
			<htmlindex>Drafting_op287.html#262402</htmlindex>
			<tiptext>Dimension note</tiptext>
		</match>
		<match Key="10190">
			<htmlindex>Drafting_op289.html#262444</htmlindex>
			<tiptext>Dimension note</tiptext>
		</match>
		<match Key="10190">
			<htmlindex>Drafting_op290.html#262759</htmlindex>
			<tiptext>Dimension note</tiptext>
		</match>
		<match Key="10192">
			<htmlindex>Drafting_op295.html#315801</htmlindex>
			<tiptext>Dimension position number</tiptext>
		</match>
		<match Key="10178">
			<htmlindex>Drafting_op297.html#196533</htmlindex>
			<tiptext>Modify  move text</tiptext>
		</match>
		<match Key="10159">
			<htmlindex>Drafting_op298.html#196611</htmlindex>
			<tiptext>Modify move symbol</tiptext>
		</match>
		<match Key="10159">
			<htmlindex>Drafting_op299.html#263324</htmlindex>
			<tiptext>Modify move symbol</tiptext>
		</match>
		<match Key="10159">
			<htmlindex>Drafting_op300.html#263432</htmlindex>
			<tiptext>Modify move symbol</tiptext>
		</match>
		<match Key="10159">
			<htmlindex>Drafting_op301.html#263468</htmlindex>
			<tiptext>Modify move symbol</tiptext>
		</match>
		<match Key="10159">
			<htmlindex>Drafting_op302.html#263592</htmlindex>
			<tiptext>Modify move symbol</tiptext>
		</match>
		<match Key="10159">
			<htmlindex>Drafting_op303.html#263611</htmlindex>
			<tiptext>Modify move symbol</tiptext>
		</match>
		<match Key="10159">
			<htmlindex>Drafting_op304.html#264052</htmlindex>
			<tiptext>Modify move symbol</tiptext>
		</match>
		<match Key="10121">
			<htmlindex>Drafting_op306.html#279033</htmlindex>
			<tiptext>Transform geometry</tiptext>
		</match>
		<match Key="10122">
			<htmlindex>Drafting_op307.html#279254</htmlindex>
			<tiptext>Transform subpicture</tiptext>
		</match>
		<match Key="10124">
			<htmlindex>Drafting_op308.html#178713</htmlindex>
			<tiptext>Modify move geometry</tiptext>
		</match>
		<match Key="10102">
			<htmlindex>Drafting_op309.html#178735</htmlindex>
			<tiptext>Trim to entity</tiptext>
		</match>
		<match Key="10104">
			<htmlindex>Drafting_op310.html#198859</htmlindex>
			<tiptext>Trim by length</tiptext>
		</match>
		<match Key="10103">
			<htmlindex>Drafting_op311.html#198959</htmlindex>
			<tiptext>Trim gap</tiptext>
		</match>
		<match Key="10105">
			<htmlindex>Drafting_op312.html#199046</htmlindex>
			<tiptext>Trim cutout</tiptext>
		</match>
		<match Key="10086">
			<htmlindex>Drafting_op313.html#199144</htmlindex>
			<tiptext>Trim fillet</tiptext>
		</match>
		<match Key="10136">
			<htmlindex>Drafting_op314.html#199246</htmlindex>
			<tiptext>Stretch</tiptext>
		</match>
		<match Key="10077">
			<htmlindex>Drafting_op315.html#207075</htmlindex>
			<tiptext>Chain</tiptext>
		</match>
		<match Key="10149">
			<htmlindex>Drafting_op316.html#207095</htmlindex>
			<tiptext>Dechain</tiptext>
		</match>
		<match Key="10409">
			<htmlindex>Drafting_op320.html#207046</htmlindex>
			<tiptext>Modify dimension</tiptext>
		</match>
	</book>
	<book Name="Equipment.chm" ID="TBEQUIP">
		<match Key="TBEQUIP_ViewsAndTools">
			<htmlindex>Equipment_op2.html#146806</htmlindex>
			<tiptext>Views and tools</tiptext>
		</match>
		<match Key="TBEQUIP_UiOverview">
			<htmlindex>Equipment_op3.html#146662</htmlindex>
			<tiptext>User Interface Overview</tiptext>
		</match>
		<match Key="TBEQUIP_TreeTool">
			<htmlindex>Equipment_op4.html#140831</htmlindex>
			<tiptext>Navigation Tree Tool</tiptext>
		</match>
		<match Key="TBEQUIP_SearchTool">
			<htmlindex>Equipment_op5.html#146222</htmlindex>
			<tiptext>Search Tool</tiptext>
		</match>
		<match Key="TBEQUIP_PropertyTool">
			<htmlindex>Equipment_op6.html#146254</htmlindex>
			<tiptext>Property Tool</tiptext>
		</match>
		<match Key="TBEQUIP_ComponentListTool">
			<htmlindex>Equipment_op7.html#146270</htmlindex>
			<tiptext>Component List Tool</tiptext>
		</match>
		<match Key="TBEQUIP_FilterTool">
			<htmlindex>Equipment_op8.html#146327</htmlindex>
			<tiptext>Filter Tool</tiptext>
		</match>
		<match Key="TBEQUIP_OptionsTool">
			<htmlindex>Equipment_op9.html#146491</htmlindex>
			<tiptext>OptionsTool</tiptext>
		</match>
		<match Key="TBEQUIP_OptionsTool">
			<htmlindex>Equipment_op10.html#148488</htmlindex>
			<tiptext>OptionsTool</tiptext>
		</match>
	</book>
	<book Name="Form.chm" ID="TBFORM">
		<match Key="HIDD_NEWSHIP">
			<htmlindex>Form_op4.html#142495</htmlindex>
			<tiptext>New Dialogue</tiptext>
		</match>
		<match Key="HIDD_HULLDES_TYPE">
			<htmlindex>Form_op5.html#142511</htmlindex>
			<tiptext>HullDes Dialogue</tiptext>
		</match>
		<match Key="HIDD_HDES_SHIPGEN">
			<htmlindex>Form_op6.html#142524</htmlindex>
			<tiptext>General Particulars Dialogue</tiptext>
		</match>
		<match Key="HIDD_HULLSHAPE">
			<htmlindex>Form_op7.html#142527</htmlindex>
			<tiptext>Hull Shape Parameters</tiptext>
		</match>
		<match Key="HIDD_CM">
			<htmlindex>Form_op8.html#155063</htmlindex>
			<tiptext>Midship Coefficent Dialogue</tiptext>
		</match>
		<match Key="HIDD_HDES_MIDSHIP_SECTION">
			<htmlindex>Form_op9.html#142550</htmlindex>
			<tiptext>Midship Section Dialogue</tiptext>
		</match>
		<match Key="HIDD_HDES_DEFAULTS">
			<htmlindex>Form_op10.html#142566</htmlindex>
			<tiptext>Further Hullform Shape Parameters Dialogue</tiptext>
		</match>
		<match Key="HIDD_HULLDES_MAIN">
			<htmlindex>Form_op12.html#142582</htmlindex>
			<tiptext>Parameter Input Dialogue</tiptext>
		</match>
		<match Key="HIDD_HDES_FOR_BODY">
			<htmlindex>Form_op13.html#142597</htmlindex>
			<tiptext>Fore-body Parameters Dialogue</tiptext>
		</match>
		<match Key="HIDD_HDES_AFTBODY">
			<htmlindex>Form_op14.html#142623</htmlindex>
			<tiptext>Aft-body Parameters Dialogue</tiptext>
		</match>
		<match Key="HIDD_HDES_SONAR">
			<htmlindex>Form_op15.html#142634</htmlindex>
			<tiptext>Sonar Dome Parameters Dialogue</tiptext>
		</match>
		<match Key="HIDD_PRINDIM">
			<htmlindex>Form_op18.html#142806</htmlindex>
			<tiptext>Principal Dimensions Dialogue</tiptext>
		</match>
		<match Key="HIDD_CHANGEDIMS">
			<htmlindex>Form_op19.html#142830</htmlindex>
			<tiptext>Change Coefficients Dialogue</tiptext>
		</match>
		<match Key="HIDD_MIDSHIP_SECTION">
			<htmlindex>Form_op20.html#149245</htmlindex>
			<tiptext>Midship Section Dialogue</tiptext>
		</match>
		<match Key="HIDD_PATCH_CONFIG">
			<htmlindex>Form_op21.html#142871</htmlindex>
			<tiptext>Configuring the Patches</tiptext>
		</match>
		<match Key="HIDD_BULB_FAC">
			<htmlindex>Form_op22.html#142897</htmlindex>
			<tiptext>Bulb Facilities</tiptext>
		</match>
		<match Key="HIDD_GENBULB">
			<htmlindex>Form_op23.html#155911</htmlindex>
			<tiptext>Generate Bulb</tiptext>
		</match>
		<match Key="HIDD_DESIGNER_KNUCKLE">
			<htmlindex>Form_op24.html#142933</htmlindex>
			<tiptext>Designer Knuckles</tiptext>
		</match>
		<match Key="HIDD_HYDROSTATICS">
			<htmlindex>Form_op25.html#142958</htmlindex>
			<tiptext>Hydrostatics</tiptext>
		</match>
		<match Key="HIDD_GENERATEAPP">
			<htmlindex>Form_op27.html#142999</htmlindex>
			<tiptext>Generate Appendage</tiptext>
		</match>
		<match Key="HIDD_APPENDCONFIG">
			<htmlindex>Form_op28.html#143038</htmlindex>
			<tiptext>Appendage Configuration</tiptext>
		</match>
		<match Key="HIDD_CALCY">
			<htmlindex>Form_op29.html#143064</htmlindex>
			<tiptext>Calculate Y coordinates</tiptext>
		</match>
		<match Key="HIDD_DRAWING">
			<htmlindex>Form_op30.html#143091</htmlindex>
			<tiptext>Drawings</tiptext>
		</match>
		<match Key="HIDD_DRAWING1">
			<htmlindex>Form_op31.html#143117</htmlindex>
			<tiptext>Oblique</tiptext>
		</match>
		<match Key="HIDD_DISP_OPTS">
			<htmlindex>Form_op32.html#143119</htmlindex>
			<tiptext>Draw Options</tiptext>
		</match>
		<match Key="HIDD_REDRAW">
			<htmlindex>Form_op33.html#155207</htmlindex>
			<tiptext>Redraw</tiptext>
		</match>
		<match Key="HIDD_GETCURVE">
			<htmlindex>Form_op34.html#143182</htmlindex>
			<tiptext>Curve Selection Dialogue</tiptext>
		</match>
		<match Key="HIDD_CURVE_OPTS">
			<htmlindex>Form_op37.html#143278</htmlindex>
			<tiptext>Draw Options</tiptext>
		</match>
		<match Key="HIDD_CREATECAM">
			<htmlindex>Form_op38.html#155884</htmlindex>
			<tiptext>Create Camber</tiptext>
		</match>
		<match Key="HIDD_DEFDECK">
			<htmlindex>Form_op39.html#155246</htmlindex>
			<tiptext>Create Deck</tiptext>
		</match>
		<match Key="HIDD_REP_DK_PTS">
			<htmlindex>Form_op40.html#155249</htmlindex>
			<tiptext>Report Deck Points</tiptext>
		</match>
		<match Key="HIDD_USER_PROFILE">
			<htmlindex>Form_op41.html#143292</htmlindex>
			<tiptext>User Profile</tiptext>
		</match>
		<match Key="HIDD_FRAMEDEF">
			<htmlindex>Form_op42.html#143330</htmlindex>
			<tiptext>Frame Definition Dialogue</tiptext>
		</match>
		<match Key="HIDD_COLORSEL">
			<htmlindex>Form_op43.html#155864</htmlindex>
			<tiptext>Colour Selection Dialogue</tiptext>
		</match>
		<match Key="HIDD_HOFF_SHIPDATA">
			<htmlindex>Form_op44.html#143355</htmlindex>
			<tiptext>Ship Data Dialogue</tiptext>
		</match>
		<match Key="HIDD_GEN_PART">
			<htmlindex>Form_op45.html#143369</htmlindex>
			<tiptext>General Particulars Dialogue</tiptext>
		</match>
		<match Key="HIDD_HULLOFF">
			<htmlindex>Form_op46.html#148916</htmlindex>
			<tiptext>General Particulars Dialogue</tiptext>
		</match>
		<match Key="HIDD_FORBODY_GEN">
			<htmlindex>Form_op48.html#143390</htmlindex>
			<tiptext>Fore-body Generation</tiptext>
		</match>
		<match Key="HIDD_AFTBODY_GEN">
			<htmlindex>Form_op49.html#155303</htmlindex>
			<tiptext>Aft-body Generation</tiptext>
		</match>
		<match Key="HIDD_BRIT_MAKE_OPTS">
			<htmlindex>Form_op51.html#143648</htmlindex>
			<tiptext>Britfair Data Preparation</tiptext>
		</match>
		<match Key="HIDD_GDSOPTIONS">
			<htmlindex>Form_op53.html#143665</htmlindex>
			<tiptext>Geometry Datastore Conversion</tiptext>
		</match>
		<match Key="HIDD_HFD">
			<htmlindex>Form_op54.html#143680</htmlindex>
			<tiptext>HullForm Design File Conversion</tiptext>
		</match>
		<match Key="HIDD_HLINESOUT">
			<htmlindex>Form_op56.html#143697</htmlindex>
			<tiptext>Curves Output</tiptext>
		</match>
		<match Key="HIDD_IGES">
			<htmlindex>Form_op60.html#143738</htmlindex>
			<tiptext>Surface Output</tiptext>
		</match>
		<match Key="HIDD_IGES2">
			<htmlindex>Form_op61.html#155461</htmlindex>
			<tiptext>Surface Output</tiptext>
		</match>
		<match Key="HIDD_PLOTTER1">
			<htmlindex>Form_op62.html#143755</htmlindex>
			<tiptext>Plotting</tiptext>
		</match>
		<match Key="HIDD_HULLBLD">
			<htmlindex>Form_op64.html#155939</htmlindex>
			<tiptext>HullBld Units</tiptext>
		</match>
		<match Key="HIDD_BOUNDARIES">
			<htmlindex>Form_op65.html#143816</htmlindex>
			<tiptext>Defining Boundaries</tiptext>
		</match>
		<match Key="HIDD_CREATEKNUCKLE1">
			<htmlindex>Form_op67.html#155378</htmlindex>
			<tiptext>Create Knuckle</tiptext>
		</match>
		<match Key="HIDD_CREATEKNUCKVAL">
			<htmlindex>Form_op68.html#155382</htmlindex>
			<tiptext>Create Knuckle by Value</tiptext>
		</match>
		<match Key="HIDD_GETKNUCKLE">
			<htmlindex>Form_op69.html#155388</htmlindex>
			<tiptext>Select Knuckle</tiptext>
		</match>
		<match Key="HIDD_SELBOUNDARY">
			<htmlindex>Form_op70.html#155397</htmlindex>
			<tiptext>Select Patch</tiptext>
		</match>
		<match Key="HIDD_ANALKNUCK">
			<htmlindex>Form_op71.html#155404</htmlindex>
			<tiptext>Analyse Knuckle</tiptext>
		</match>
		<match Key="HIDD_LSQPATCH">
			<htmlindex>Form_op72.html#143883</htmlindex>
			<tiptext>LSQ</tiptext>
		</match>
		<match Key="HIDD_GENAFT_END">
			<htmlindex>Form_op75.html#143931</htmlindex>
			<tiptext>Aft-body End-Surface</tiptext>
		</match>
		<match Key="HIDD_PRINDIMBLD">
			<htmlindex>Form_op76.html#143958</htmlindex>
			<tiptext>Principal Dimensions</tiptext>
		</match>
		<match Key="HIDD_PATCHWATERLINES">
			<htmlindex>Form_op77.html#155824</htmlindex>
			<tiptext>Patch Waterlines</tiptext>
		</match>
		<match Key="HIDD_EXAMPATTANS">
			<htmlindex>Form_op78.html#155832</htmlindex>
			<tiptext>Examine Patch Tangents</tiptext>
		</match>
		<match Key="HIDD_EXAMPATCURVES">
			<htmlindex>Form_op79.html#155843</htmlindex>
			<tiptext>Examine Patch Curvature</tiptext>
		</match>
		<match Key="HIDD_ANALSHIP_PATCH">
			<htmlindex>Form_op80.html#155836</htmlindex>
			<tiptext>Analyse Ship Patches</tiptext>
		</match>
		<match Key="HIDD_INPUTINT">
			<htmlindex>Form_op81.html#155844</htmlindex>
			<tiptext>Input Integer</tiptext>
		</match>
		<match Key="HIDD_VALUE">
			<htmlindex>Form_op82.html#155848</htmlindex>
			<tiptext>Input Real</tiptext>
		</match>
		<match Key="HIDD_SPLITBOUNDARY">
			<htmlindex>Form_op83.html#155825</htmlindex>
			<tiptext>Split Boundary</tiptext>
		</match>
	</book>
	<book Name="Hull_Nesting.chm" ID="TBNEST">
		<match Key="4008">
			<htmlindex>Hull_Nesting_op3.html#152934</htmlindex>
			<tiptext>New</tiptext>
		</match>
		<match Key="4065">
			<htmlindex>Hull_Nesting_op4.html#152957</htmlindex>
			<tiptext>Open</tiptext>
		</match>
		<match Key="4044">
			<htmlindex>Hull_Nesting_op5.html#152980</htmlindex>
			<tiptext>Save</tiptext>
		</match>
		<match Key="4108">
			<htmlindex>Hull_Nesting_op6.html#153003</htmlindex>
			<tiptext>Rename</tiptext>
		</match>
		<match Key="4121">
			<htmlindex>Hull_Nesting_op8.html#153027</htmlindex>
			<tiptext>List</tiptext>
		</match>
		<match Key="4109">
			<htmlindex>Hull_Nesting_op9.html#153050</htmlindex>
			<tiptext>Delete</tiptext>
		</match>
		<match Key="4009">
			<htmlindex>Hull_Nesting_op10.html#153073</htmlindex>
			<tiptext>Return to Drawing</tiptext>
		</match>
		<match Key="4019">
			<htmlindex>Hull_Nesting_op14.html#149592</htmlindex>
			<tiptext>Nest</tiptext>
		</match>
		<match Key="4052">
			<htmlindex>Hull_Nesting_op15.html#149618</htmlindex>
			<tiptext>Parts Menu</tiptext>
		</match>
		<match Key="4016">
			<htmlindex>Hull_Nesting_op16.html#149694</htmlindex>
			<tiptext>Transform</tiptext>
		</match>
		<match Key="4081">
			<htmlindex>Hull_Nesting_op17.html#149640</htmlindex>
			<tiptext>Verify</tiptext>
		</match>
		<match Key="4434">
			<htmlindex>Hull_Nesting_op19.html#149775</htmlindex>
			<tiptext>On</tiptext>
		</match>
		<match Key="4435">
			<htmlindex>Hull_Nesting_op20.html#149824</htmlindex>
			<tiptext>Off</tiptext>
		</match>
		<match Key="4096">
			<htmlindex>Hull_Nesting_op25.html#150034</htmlindex>
			<tiptext>Raw Plate</tiptext>
		</match>
		<match Key="4095">
			<htmlindex>Hull_Nesting_op26.html#150031</htmlindex>
			<tiptext>Part</tiptext>
		</match>
		<match Key="4510">
			<htmlindex>Hull_Nesting_op27.html#150040</htmlindex>
			<tiptext>Autonest</tiptext>
		</match>
		<match Key="4059">
			<htmlindex>Hull_Nesting_op29.html#150093</htmlindex>
			<tiptext>Bridge</tiptext>
		</match>
		<match Key="4042">
			<htmlindex>Hull_Nesting_op31.html#150137</htmlindex>
			<tiptext>Burn</tiptext>
		</match>
		<match Key="4043">
			<htmlindex>Hull_Nesting_op32.html#150159</htmlindex>
			<tiptext>Mark</tiptext>
		</match>
		<match Key="4783">
			<htmlindex>Hull_Nesting_op33.html#150181</htmlindex>
			<tiptext>Label</tiptext>
		</match>
		<match Key="Automatic4648">
			<htmlindex>Hull_Nesting_op34.html#150203</htmlindex>
			<tiptext>Automatic4648</tiptext>
		</match>
		<match Key="4171">
			<htmlindex>Hull_Nesting_op35.html#150259</htmlindex>
			<tiptext>Start</tiptext>
		</match>
		<match Key="4064">
			<htmlindex>Hull_Nesting_op36.html#150225</htmlindex>
			<tiptext>Sequence</tiptext>
		</match>
		<match Key="4784">
			<htmlindex>Hull_Nesting_op38.html#150310</htmlindex>
			<tiptext>Key in</tiptext>
		</match>
		<match Key="4790">
			<htmlindex>Hull_Nesting_op39.html#150332</htmlindex>
			<tiptext>Plate</tiptext>
		</match>
		<match Key="4791">
			<htmlindex>Hull_Nesting_op40.html#150354</htmlindex>
			<tiptext>Part</tiptext>
		</match>
		<match Key="4785">
			<htmlindex>Hull_Nesting_op41.html#152014</htmlindex>
			<tiptext>Pos. No.</tiptext>
		</match>
		<match Key="4782">
			<htmlindex>Hull_Nesting_op42.html#152037</htmlindex>
			<tiptext>Prod Info.</tiptext>
		</match>
		<match Key="Copy4787">
			<htmlindex>Hull_Nesting_op43.html#152086</htmlindex>
			<tiptext>Copy4787</tiptext>
		</match>
		<match Key="4788">
			<htmlindex>Hull_Nesting_op44.html#152060</htmlindex>
			<tiptext>Label Text</tiptext>
		</match>
		<match Key="4796">
			<htmlindex>Hull_Nesting_op46.html#150452</htmlindex>
			<tiptext>Symbol</tiptext>
		</match>
		<match Key="4797">
			<htmlindex>Hull_Nesting_op47.html#150474</htmlindex>
			<tiptext>Pos. No.</tiptext>
		</match>
		<match Key="4887">
			<htmlindex>Hull_Nesting_op48.html#152117</htmlindex>
			<tiptext>Copy</tiptext>
		</match>
		<match Key="4888">
			<htmlindex>Hull_Nesting_op49.html#151981</htmlindex>
			<tiptext>Move</tiptext>
		</match>
		<match Key="4055">
			<htmlindex>Hull_Nesting_op50.html#150508</htmlindex>
			<tiptext>Auxiliary Function</tiptext>
		</match>
		<match Key="4113">
			<htmlindex>Hull_Nesting_op51.html#150534</htmlindex>
			<tiptext>Corner Loop</tiptext>
		</match>
		<match Key="4083">
			<htmlindex>Hull_Nesting_op53.html#150584</htmlindex>
			<tiptext>Open</tiptext>
		</match>
		<match Key="4084">
			<htmlindex>Hull_Nesting_op54.html#150606</htmlindex>
			<tiptext>Save</tiptext>
		</match>
		<match Key="10350">
			<htmlindex>Hull_Nesting_op56.html#153778</htmlindex>
			<tiptext>Plate</tiptext>
		</match>
		<match Key="10360">
			<htmlindex>Hull_Nesting_op57.html#153822</htmlindex>
			<tiptext>Profile - Create</tiptext>
		</match>
		<match Key="10361">
			<htmlindex>Hull_Nesting_op58.html#153827</htmlindex>
			<tiptext>Profile - Edit</tiptext>
		</match>
		<match Key="10362">
			<htmlindex>Hull_Nesting_op59.html#153863</htmlindex>
			<tiptext>Profile - Delete</tiptext>
		</match>
		<match Key="4482">
			<htmlindex>Hull_Nesting_op60.html#153969</htmlindex>
			<tiptext>Check Post Processor</tiptext>
		</match>
		<match Key="4060">
			<htmlindex>Hull_Nesting_op62.html#151374</htmlindex>
			<tiptext>Bridge</tiptext>
		</match>
		<match Key="4063">
			<htmlindex>Hull_Nesting_op63.html#151396</htmlindex>
			<tiptext>Start</tiptext>
		</match>
		<match Key="4092">
			<htmlindex>Hull_Nesting_op64.html#151511</htmlindex>
			<tiptext>Part</tiptext>
		</match>
		<match Key="4056">
			<htmlindex>Hull_Nesting_op65.html#151541</htmlindex>
			<tiptext>Auxiliary Function</tiptext>
		</match>
		<match Key="4082">
			<htmlindex>Hull_Nesting_op66.html#151563</htmlindex>
			<tiptext>Sketch</tiptext>
		</match>
		<match Key="4789">
			<htmlindex>Hull_Nesting_op67.html#151418</htmlindex>
			<tiptext>Label Text</tiptext>
		</match>
		<match Key="4889">
			<htmlindex>Hull_Nesting_op68.html#151440</htmlindex>
			<tiptext>Label Symbol</tiptext>
		</match>
		<match Key="4119">
			<htmlindex>Hull_Nesting_op70.html#153104</htmlindex>
			<tiptext>Display Sketch</tiptext>
		</match>
		<match Key="4119">
			<htmlindex>Hull_Nesting_op70.html#153104</htmlindex>
			<tiptext>Display Sketch</tiptext>
		</match>
		<match Key="4119">
			<htmlindex>Hull_Nesting_op70.html#153104</htmlindex>
			<tiptext>Display Sketch</tiptext>
		</match>
		<match Key="4118">
			<htmlindex>Hull_Nesting_op71.html#153127</htmlindex>
			<tiptext>Display Plate</tiptext>
		</match>
		<match Key="4387">
			<htmlindex>Hull_Nesting_op72.html#153173</htmlindex>
			<tiptext>First Page</tiptext>
		</match>
		<match Key="4089">
			<htmlindex>Hull_Nesting_op72.html#153173</htmlindex>
			<tiptext>Add</tiptext>
		</match>
		<match Key="4099">
			<htmlindex>Hull_Nesting_op73.html#153196</htmlindex>
			<tiptext>Get</tiptext>
		</match>
		<match Key="4098">
			<htmlindex>Hull_Nesting_op74.html#153219</htmlindex>
			<tiptext>Open</tiptext>
		</match>
		<match Key="4170">
			<htmlindex>Hull_Nesting_op75.html#153242</htmlindex>
			<tiptext>Overlap Check</tiptext>
		</match>
		<match Key="4388">
			<htmlindex>Hull_Nesting_op76.html#153268</htmlindex>
			<tiptext>Next Page</tiptext>
		</match>
		<match Key="4389">
			<htmlindex>Hull_Nesting_op76.html#153268</htmlindex>
			<tiptext>Previous Page</tiptext>
		</match>
		<match Key="4390">
			<htmlindex>Hull_Nesting_op76.html#153268</htmlindex>
			<tiptext>Last Page</tiptext>
		</match>
		<match Key="4070">
			<htmlindex>Hull_Nesting_op78.html#154717</htmlindex>
			<tiptext>Production Information</tiptext>
		</match>
		<match Key="4032">
			<htmlindex>Hull_Nesting_op80.html#151620</htmlindex>
			<tiptext>Text</tiptext>
		</match>
		<match Key="4026">
			<htmlindex>Hull_Nesting_op81.html#151781</htmlindex>
			<tiptext>Shop Dwg Text</tiptext>
		</match>
		<match Key="4028">
			<htmlindex>Hull_Nesting_op82.html#151749</htmlindex>
			<tiptext>Starts / Plate name</tiptext>
		</match>
		<match Key="4035">
			<htmlindex>Hull_Nesting_op84.html#151717</htmlindex>
			<tiptext>Put</tiptext>
		</match>
		<match Key="4290">
			<htmlindex>Hull_Nesting_op85.html#151812</htmlindex>
			<tiptext>Shop Dwg Symbol</tiptext>
		</match>
		<match Key="4142">
			<htmlindex>Hull_Nesting_op86.html#151708</htmlindex>
			<tiptext>Pos. No.</tiptext>
		</match>
		<match Key="4250">
			<htmlindex>Hull_Nesting_op88.html#152425</htmlindex>
			<tiptext>Create</tiptext>
		</match>
		<match Key="4253">
			<htmlindex>Hull_Nesting_op89.html#152418</htmlindex>
			<tiptext>Delete</tiptext>
		</match>
		<match Key="4070">
			<htmlindex>Hull_Nesting_op91.html#152276</htmlindex>
			<tiptext>Production Information</tiptext>
		</match>
		<match Key="4136">
			<htmlindex>Hull_Nesting_op92.html#152278</htmlindex>
			<tiptext>Defaults</tiptext>
		</match>
		<match Key="4103">
			<htmlindex>Hull_Nesting_op93.html#152271</htmlindex>
			<tiptext>Calculate Distance</tiptext>
		</match>
		<match Key="4116">
			<htmlindex>Hull_Nesting_op95.html#152199</htmlindex>
			<tiptext>Dump Plate</tiptext>
		</match>
		<match Key="32008">
			<htmlindex>Hull_Nesting_op96.html#152171</htmlindex>
			<tiptext>Dump BumpStructure</tiptext>
		</match>
		<match Key="32010">
			<htmlindex>Hull_Nesting_op97.html#152193</htmlindex>
			<tiptext>Check Object</tiptext>
		</match>
		<match Key="4184">
			<htmlindex>Hull_Nesting_op100.html#153300</htmlindex>
			<tiptext>Bump</tiptext>
		</match>
		<match Key="4039">
			<htmlindex>Hull_Nesting_op101.html#153323</htmlindex>
			<tiptext>Parallel</tiptext>
		</match>
		<match Key="4040">
			<htmlindex>Hull_Nesting_op102.html#153346</htmlindex>
			<tiptext>DX DY</tiptext>
		</match>
		<match Key="4033">
			<htmlindex>Hull_Nesting_op103.html#153369</htmlindex>
			<tiptext>Two Positions</tiptext>
		</match>
		<match Key="4020">
			<htmlindex>Hull_Nesting_op104.html#153392</htmlindex>
			<tiptext>Dynamically</tiptext>
		</match>
		<match Key="4041">
			<htmlindex>Hull_Nesting_op106.html#153416</htmlindex>
			<tiptext>Angle</tiptext>
		</match>
		<match Key="4037">
			<htmlindex>Hull_Nesting_op108.html#153462</htmlindex>
			<tiptext>Two Positions</tiptext>
		</match>
		<match Key="4037">
			<htmlindex>Hull_Nesting_op111.html#153529</htmlindex>
			<tiptext>Two Positions</tiptext>
		</match>
		<match Key="4051">
			<htmlindex>Hull_Nesting_op112.html#153552</htmlindex>
			<tiptext>Dynamically</tiptext>
		</match>
		<match Key="4038">
			<htmlindex>Hull_Nesting_op113.html#153575</htmlindex>
			<tiptext>Define Centre</tiptext>
		</match>
		<match Key="4046">
			<htmlindex>Hull_Nesting_op118.html#153686</htmlindex>
			<tiptext>4 Positionst</tiptext>
		</match>
	</book>
	<book Name="Hydro.chm" ID="TBHYDRO">
		<match Key="IDH_APPENDAGES_NODE">
			<htmlindex>Hydro_op4.html#152420</htmlindex>
			<tiptext>Appendages</tiptext>
		</match>
		<match Key="HIDD_APPENDAGES_GENERAL">
			<htmlindex>Hydro_op4.html#141741</htmlindex>
			<tiptext>Appendages - General</tiptext>
		</match>
		<match Key="HIDD_APPENDAGES_RESISTANCE_FACTORS">
			<htmlindex>Hydro_op4.html#160549</htmlindex>
			<tiptext>Appendages - Resistance Factors</tiptext>
		</match>
		<match Key="HIDD_APPENDAGES_RUDDERS">
			<htmlindex>Hydro_op4.html#161051</htmlindex>
			<tiptext>Appendages - Rudders</tiptext>
		</match>
		<match Key="HIDD_APPENDAGES_BILGE_KEELS">
			<htmlindex>Hydro_op4.html#162596</htmlindex>
			<tiptext>Appendages - Bilge Keels</tiptext>
		</match>
		<match Key="HIDD_APPENDAGES_STABILISER_FINS">
			<htmlindex>Hydro_op4.html#184323</htmlindex>
			<tiptext>Appendages - Stabiliser Fins</tiptext>
		</match>
		<match Key="HIDD_APPENDAGES_BOW_THRUSTER">
			<htmlindex>Hydro_op4.html#163128</htmlindex>
			<tiptext>Appendages - Bow Thruster</tiptext>
		</match>
		<match Key="IDH_POWERING_NODE">
			<htmlindex>Hydro_op5.html#141886</htmlindex>
			<tiptext>Powering</tiptext>
		</match>
		<match Key="IDH_POWERING_CALC_NODE">
			<htmlindex>Hydro_op6.html#163231</htmlindex>
			<tiptext>Powering Calculation</tiptext>
		</match>
		<match Key="HIDD_POWERING_DATA_GENERAL">
			<htmlindex>Hydro_op6.html#164332</htmlindex>
			<tiptext>Powering Data - General</tiptext>
		</match>
		<match Key="HIDD_POWERING_DATA_LOADING_CONDITIONS">
			<htmlindex>Hydro_op6.html#165066</htmlindex>
			<tiptext>Powering Data - Loading Conditions</tiptext>
		</match>
		<match Key="HIDD_POWERING_R_AND_P">
			<htmlindex>Hydro_op6.html#166895</htmlindex>
			<tiptext>Powering Data - Resistance and Propulsion</tiptext>
		</match>
		<match Key="HIDD_POWERING_DATA_PROPELLER_DESIGN">
			<htmlindex>Hydro_op6.html#166897</htmlindex>
			<tiptext>Powering Data - Propeller Design Condition</tiptext>
		</match>
		<match Key="IDH_RATIO_CALC_NODE">
			<htmlindex>Hydro_op7.html#170747</htmlindex>
			<tiptext>Ratio Calculation</tiptext>
		</match>
		<match Key="HIDD_RATIOS_GENERAL">
			<htmlindex>Hydro_op7.html#170735</htmlindex>
			<tiptext>Ratios - General</tiptext>
		</match>
		<match Key="HIDD_RATIOS_LOADING_CONDITION">
			<htmlindex>Hydro_op7.html#171393</htmlindex>
			<tiptext>Ratios - Loading Condition</tiptext>
		</match>
		<match Key="HIDD_RATIOS_MODEL_TEST_RESISTANCE_DATA">
			<htmlindex>Hydro_op7.html#170693</htmlindex>
			<tiptext>Ratios - Model Test Resistance Data</tiptext>
		</match>
		<match Key="IDH_MANOEUVRING_NODE">
			<htmlindex>Hydro_op8.html#142103</htmlindex>
			<tiptext>Tankplan</tiptext>
		</match>
		<match Key="IDH_MANOEUVRING_CALCS_NODE">
			<htmlindex>Hydro_op9.html#172109</htmlindex>
			<tiptext>Manoeuvring Calculations</tiptext>
		</match>
		<match Key="HIDD_MANOEUVRING_DATA_GENERAL">
			<htmlindex>Hydro_op9.html#172197</htmlindex>
			<tiptext>Manoeuvring Data - General</tiptext>
		</match>
		<match Key="HIDD_MANOEUVRING_DATA_WATER_CONDITION">
			<htmlindex>Hydro_op9.html#174285</htmlindex>
			<tiptext>Manoeuvring Data - Water Conditions / Speed</tiptext>
		</match>
		<match Key="HIDD_MANOEUVRING_DATA_PROPELLER_DATA">
			<htmlindex>Hydro_op9.html#174289</htmlindex>
			<tiptext>Manoeuvring Data - Propeller Data</tiptext>
		</match>
		<match Key="HIDD_MANOEUVRING_DATA_HYDRODYNAMIC_DERIVATIVES">
			<htmlindex>Hydro_op9.html#192142</htmlindex>
			<tiptext>Manoeuvring Data - Hydrodynamic Derivatives</tiptext>
		</match>
		<match Key="HIDD_MANOEUVRING_DATA_CRASH_STOP">
			<htmlindex>Hydro_op9.html#142151</htmlindex>
			<tiptext>Manoeuvring Data - Crash Stop</tiptext>
		</match>
		<match Key="HIDD_MANOEUVRING_DATA_TURNING_CIRCLE">
			<htmlindex>Hydro_op9.html#142165</htmlindex>
			<tiptext>Manoeuvring Data - Turning Circle</tiptext>
		</match>
		<match Key="HIDD_MANOEUVRING_DATA_ZIGZAG">
			<htmlindex>Hydro_op9.html#142183</htmlindex>
			<tiptext>Manoeuvring Data - Zig-Zag </tiptext>
		</match>
		<match Key="IDH_SEAKEEPING_NODE">
			<htmlindex>Hydro_op10.html#152111</htmlindex>
			<tiptext>Seakeeping</tiptext>
		</match>
		<match Key="IDH_SEAKEEPING_CALCS_NODE">
			<htmlindex>Hydro_opa11.html#175537</htmlindex>
			<tiptext>Seakeeping Calculations</tiptext>
		</match>
		<match Key="HIDD_SEAKEEPING_DATA_GENERAL">
			<htmlindex>Hydro_opa12.html#142267</htmlindex>
			<tiptext>Seakeeping - General</tiptext>
		</match>
		<match Key="HIDD_SEAKEEPING_DATA_LOADING_CONDITION">
			<htmlindex>Hydro_opa13.html#151635</htmlindex>
			<tiptext>Seakeeping - Loading Condition</tiptext>
		</match>
		<match Key="HIDD_SEAKEEPING_DATA_HYDRODYNAMICS">
			<htmlindex>Hydro_op14.html#193794</htmlindex>
			<tiptext>Seakeeping - Hydrodynamics</tiptext>
		</match>
		<match Key="HIDD_SEAKEEPING_DATA_REGULAR_DATA">
			<htmlindex>Hydro_opa15.html#177862</htmlindex>
			<tiptext>Seakeeping - Regular Data</tiptext>
		</match>
		<match Key="HIDD_SEAKEEPING_DATA_IRREGULAR_DATA">
			<htmlindex>Hydro_opa16.html#185895</htmlindex>
			<tiptext>Seakeeping - Irregular Data</tiptext>
		</match>
		<match Key="HIDD_SEAKEEPING_DATA_ADDITIONAL_CALCULATIONS">
			<htmlindex>Hydro_opa17.html#142380</htmlindex>
			<tiptext>Seakeeping - Additional Calculations</tiptext>
		</match>
		<match Key="HIDD_SEAKEEPING_DATA_LONG_TERM">
			<htmlindex>Hydro_opa18.html#187827</htmlindex>
			<tiptext>Seakeeping - Long Term</tiptext>
		</match>
		<match Key="HIDD_SEAKEEPING_DATA_RAO_OUTPUT">
			<htmlindex>Hydro_op19.html#187838</htmlindex>
			<tiptext>Seakeeping - RAO Output</tiptext>
		</match>
		<match Key="HIDD_SEAKEEPING_DATA_RMS_OUTPUT">
			<htmlindex>Hydro_opa20.html#189479</htmlindex>
			<tiptext>Seakeeping - RMS Output</tiptext>
		</match>
		<match Key="HIDD_SEAKEEPING_USER_DEFINED_SPECTRA">
			<htmlindex>Hydro_opa21.html#190217</htmlindex>
			<tiptext>Seakeeping - User Defined Spectra</tiptext>
		</match>
		<match Key="IDH_DYNAMIC_POSITIONING_NODE">
			<htmlindex>Hydro_opa22.html#191894</htmlindex>
			<tiptext>Dynamic Positioning</tiptext>
		</match>
		<match Key="IDH_DYNAMIC_POSITIONING_CALCS_NODE">
			<htmlindex>Hydro_op23.html#205358</htmlindex>
			<tiptext>Dynamic Positioning Calculations</tiptext>
		</match>
		<match Key="HIDD_DP_GENERAL_PG">
			<htmlindex>Hydro_opa24.html#205378</htmlindex>
			<tiptext>Dynamic Positioning Data - General</tiptext>
		</match>
		<match Key="HIDD_DP_THRUSTERS_PG">
			<htmlindex>Hydro_opa25.html#205382</htmlindex>
			<tiptext>Dynamic Positioning Data - Thrusters</tiptext>
		</match>
		<match Key="HIDD_DP_THRUSTER_POWER_PG">
			<htmlindex>Hydro_opa26.html#205386</htmlindex>
			<tiptext>Dynamic Positioning Data - Thruster-Power</tiptext>
		</match>
		<match Key="HIDD_DP_SPEED_EFFECTS_ON_TUNNEL_THRUSTERS_PG">
			<htmlindex>Hydro_opa27.html#205390</htmlindex>
			<tiptext>Dynamic Positioning Data - Speed Effects on Tunnel Thrusters</tiptext>
		</match>
		<match Key="HIDD_DP_SPEED_EFFECTS_ON_STEERABLE_THRUSTERS_PG">
			<htmlindex>Hydro_opa28.html#205394</htmlindex>
			<tiptext>Dynamic Positioning Data - Speed Effects on Steerable Thrusters</tiptext>
		</match>
		<match Key="HIDD_DP_RUDDERS_PG">
			<htmlindex>Hydro_opa29.html#205398</htmlindex>
			<tiptext>Dynamic Positioning Data - Rudders</tiptext>
		</match>
		<match Key="HIDD_DP_MISCELLANEOUS_PG">
			<htmlindex>Hydro_opa30.html#205402</htmlindex>
			<tiptext>Dynamic Positioning Data - Miscellaneous</tiptext>
		</match>
		<match Key="HIDD_DP_THRUST_CALCULATION_PG">
			<htmlindex>Hydro_opa31.html#205406</htmlindex>
			<tiptext>Dynamic Positioning Data - Thrust Calculation</tiptext>
		</match>
		<match Key="HIDD_DP_ENVIRO_CALCULATION_PG">
			<htmlindex>Hydro_opa32.html#205410</htmlindex>
			<tiptext>Dynamic Positioning Data - Enviro Calculation</tiptext>
		</match>
		<match Key="HIDD_DP_CURRENT_COEFFICIENTS_PG">
			<htmlindex>Hydro_opb33.html#205414</htmlindex>
			<tiptext>Dynamic Positioning Data - Current</tiptext>
		</match>
		<match Key="HIDD_DP_WIND_COEFFICIENTS_PG">
			<htmlindex>Hydro_opb34.html#205418</htmlindex>
			<tiptext>Dynamic Positioning Data - Wind</tiptext>
		</match>
		<match Key="HIDD_DP_WAVE_COEFFICIENTS_PG">
			<htmlindex>Hydro_opb35.html#205422</htmlindex>
			<tiptext>Dynamic Positioning Data - Wave</tiptext>
		</match>
		<match Key="HIDD_DP_OUTPUT_PG">
			<htmlindex>Hydro_opa36.html#205426</htmlindex>
			<tiptext>Dynamic Positioning Data - Output</tiptext>
		</match>
	</book>
	<book Name="Init_Hull.chm" ID="TBINITHULL">
		<match Key="TihFlange">
			<htmlindex>Init_Hull_op2.html#142460</htmlindex>
			<tiptext>Folded flange</tiptext>
		</match>
		<match Key="TihPartName">
			<htmlindex>Init_Hull_op4.html#141208</htmlindex>
			<tiptext>Part name control</tiptext>
		</match>
		<match Key="TihCheckQualityCode">
			<htmlindex>Init_Hull_op6.html#141236</htmlindex>
			<tiptext>Qualities, check</tiptext>
		</match>
		<match Key="TihCheckConnectionCode">
			<htmlindex>Init_Hull_op8.html#141264</htmlindex>
			<tiptext>Connection codes, check</tiptext>
		</match>
		<match Key="TihCheckIntBracketDefault">
			<htmlindex>Init_Hull_op10.html#141292</htmlindex>
			<tiptext>Interactive bracket default file, check</tiptext>
		</match>
		<match Key="TihCheckSetupDefCutouts">
			<htmlindex>Init_Hull_op12.html#142357</htmlindex>
			<tiptext>Autom. cutout Sel., check</tiptext>
		</match>
		<match Key="TihCheckSetupEndcuts">
			<htmlindex>Init_Hull_op14.html#141347</htmlindex>
			<tiptext>Endcut selection, check</tiptext>
		</match>
		<match Key="TihCheckSetupProfiles">
			<htmlindex>Init_Hull_op16.html#141375</htmlindex>
			<tiptext>Profiles for project, check</tiptext>
		</match>
		<match Key="TihGentab">
			<htmlindex>Init_Hull_op18.html#141403</htmlindex>
			<tiptext>Frame/long. positions, create</tiptext>
		</match>
		<match Key="TihStructRefHullStructCheck">
			<htmlindex>Init_Hull_op20.html#141430</htmlindex>
			<tiptext>Structref, create</tiptext>
		</match>
		<match Key="TihFuncProperty">
			<htmlindex>Init_Hull_op22.html#141457</htmlindex>
			<tiptext>Functional prop., create</tiptext>
		</match>
		<match Key="TihCheckBracketChar">
			<htmlindex>Init_Hull_op24.html#141484</htmlindex>
			<tiptext>Bracket characteristics file, check</tiptext>
		</match>
		<match Key="TihCheckQualityExchange">
			<htmlindex>Init_Hull_op26.html#141511</htmlindex>
			<tiptext>Quality exchange, check</tiptext>
		</match>
		<match Key="TihTILBevelCtrl">
			<htmlindex>Init_Hull_op28.html#141538</htmlindex>
			<tiptext>Bevel std, create</tiptext>
		</match>
		<match Key="TihCheckKnuckleCtrl">
			<htmlindex>Init_Hull_op30.html#145782</htmlindex>
			<tiptext>Knuckle ctrl, check</tiptext>
		</match>
		<match Key="TihCheckCutoutMacros">
			<htmlindex>Init_Hull_op32.html#141594</htmlindex>
			<tiptext>Cutouts via macro, check</tiptext>
		</match>
		<match Key="TihCheckTransProf2Plate">
			<htmlindex>Init_Hull_op34.html#141622</htmlindex>
			<tiptext>Profiles to plate, check</tiptext>
		</match>
		<match Key="TihCheckClipMacros">
			<htmlindex>Init_Hull_op36.html#141650</htmlindex>
			<tiptext>Clips via macro, check</tiptext>
		</match>
		<match Key="TihCheckIUBars">
			<htmlindex>Init_Hull_op38.html#141678</htmlindex>
			<tiptext>U- and I- bar set-up, check</tiptext>
		</match>
		<match Key="TihCheckProfRestrict">
			<htmlindex>Init_Hull_op40.html#141706</htmlindex>
			<tiptext>Profile restrictions, check</tiptext>
		</match>
		<match Key="TihCheckCurvedShellProf">
			<htmlindex>Init_Hull_op42.html#141734</htmlindex>
			<tiptext>Shell stiff, curvature, check</tiptext>
		</match>
		<match Key="TihTILCreatePlanes">
			<htmlindex>Init_Hull_op44.html#141762</htmlindex>
			<tiptext>Reference plane, create</tiptext>
		</match>
		<match Key="TihTILSetupBrkts">
			<htmlindex>Init_Hull_op46.html#146249</htmlindex>
			<tiptext>Brackets std, create</tiptext>
		</match>
		<match Key="TihTILSetupBrkts">
			<htmlindex>Init_Hull_op48.html#146330</htmlindex>
			<tiptext>Brackets std, create</tiptext>
		</match>
		<match Key="TihHullRefCheck">
			<htmlindex>Init_Hull_op50.html#146302</htmlindex>
			<tiptext>Hullref, create</tiptext>
		</match>
		<match Key="TihTILPosNo">
			<htmlindex>Init_Hull_op52.html#141845</htmlindex>
			<tiptext>Autopos set-up</tiptext>
		</match>
		<match Key="TihTILShrinkageObj">
			<htmlindex>Init_Hull_op54.html#141872</htmlindex>
			<tiptext>Shrinkage obj., create</tiptext>
		</match>
		<match Key="TihTILCutoutDefObj">
			<htmlindex>Init_Hull_op56.html#141901</htmlindex>
			<tiptext>Cutouts, ext. def., create</tiptext>
		</match>
		<match Key="TihTILEndCutTable">
			<htmlindex>Init_Hull_op58.html#141929</htmlindex>
			<tiptext>Endcut table, create</tiptext>
		</match>
		<match Key="TihTILSwedging">
			<htmlindex>Init_Hull_op60.html#141957</htmlindex>
			<tiptext>Swedging, create</tiptext>
		</match>
		<match Key="TihBlocksCheck">
			<htmlindex>Init_Hull_op62.html#141985</htmlindex>
			<tiptext>Blocks, manipulate</tiptext>
		</match>
		<match Key="TihFpDialog">
			<htmlindex>Init_Hull_op64.html#142033</htmlindex>
			<tiptext>Functional properties wizard</tiptext>
		</match>
		<match Key="TihPwPage1">
			<htmlindex>Init_Hull_op65.html#142055</htmlindex>
			<tiptext>Part names wizard step 1</tiptext>
		</match>
		<match Key="TihPwPage2">
			<htmlindex>Init_Hull_op66.html#142077</htmlindex>
			<tiptext>Part names wizard step 2</tiptext>
		</match>
		<match Key="TihPwPage3">
			<htmlindex>Init_Hull_op67.html#142099</htmlindex>
			<tiptext>Part names wizard step 3</tiptext>
		</match>
		<match Key="TihPwPage4">
			<htmlindex>Init_Hull_op68.html#142121</htmlindex>
			<tiptext>Part names wizard step 4</tiptext>
		</match>
		<match Key="TihPwPage5">
			<htmlindex>Init_Hull_op69.html#142143</htmlindex>
			<tiptext>Part names wizard step 5</tiptext>
		</match>
		<match Key="TihMessageBox">
			<htmlindex>Init_Hull_op70.html#142165</htmlindex>
			<tiptext>Result dialog</tiptext>
		</match>
		<match Key="TihHullRefSurfNames">
			<htmlindex>Init_Hull_op71.html#142187</htmlindex>
			<tiptext>Hull Reference Object Surfaces</tiptext>
		</match>
	</book>
	<book Name="Lines.chm" ID="TBLINES">
		<match Key="HIDD_INDEX">
			<htmlindex>Linesfp.html#140746</htmlindex>
			<tiptext></tiptext>
		</match>
		<match Key="HIDD_MAINFRAME">
			<htmlindex>Lines_op2.html#1023942</htmlindex>
			<tiptext>Main Window</tiptext>
		</match>
		<match Key="HIDD_OUTPUTWINDOW">
			<htmlindex>Lines_op6.html#1024003</htmlindex>
			<tiptext>Output Window</tiptext>
		</match>
		<match Key="HIDD_CURVETREE">
			<htmlindex>Lines_op7.html#1024584</htmlindex>
			<tiptext>Curve Tree</tiptext>
		</match>
		<match Key="HIDD_HULLFORMTREE">
			<htmlindex>Lines_op8.html#1047740</htmlindex>
			<tiptext>Hull Form Tree</tiptext>
		</match>
		<match Key="HIDD_POINTSBUFFER">
			<htmlindex>Lines_op9.html#1024640</htmlindex>
			<tiptext>Points Buffer</tiptext>
		</match>
		<match Key="HIDD_CUSTOMIZE">
			<htmlindex>Lines_op11.html#1023878</htmlindex>
			<tiptext>Customize Dialogue</tiptext>
		</match>
		<match Key="HIDD_CUSTOMIZETOOLBARS">
			<htmlindex>Lines_op12.html#1023705</htmlindex>
			<tiptext>Toolbars Page</tiptext>
		</match>
		<match Key="HIDD_CUSTOMIZECOMMANDS">
			<htmlindex>Lines_op13.html#1023710</htmlindex>
			<tiptext>Commands Page</tiptext>
		</match>
		<match Key="HIDD_GENPART">
			<htmlindex>Lines_op14.html#1002033</htmlindex>
			<tiptext>General Particulars Dialogue</tiptext>
		</match>
		<match Key="HIDD_ACCEPT_CURVE">
			<htmlindex>Lines_op15.html#1002076</htmlindex>
			<tiptext>Accept Curve Dialogue</tiptext>
		</match>
		<match Key="HIDD_COPY_CURVE">
			<htmlindex>Lines_op16.html#1046784</htmlindex>
			<tiptext>Copy Curve Dialogue</tiptext>
		</match>
		<match Key="HIDD_DATASETPAGE">
			<htmlindex>Lines_op18.html#1002308</htmlindex>
			<tiptext>Data Set Tab</tiptext>
		</match>
		<match Key="HIDD_AXESPAGE">
			<htmlindex>Lines_op19.html#1002320</htmlindex>
			<tiptext>Axes Tab</tiptext>
		</match>
		<match Key="HIDD_PARTICULARSPAGE">
			<htmlindex>Lines_op20.html#1013483</htmlindex>
			<tiptext>Particulars Tab</tiptext>
		</match>
		<match Key="HIDD_PROJECTPAGE">
			<htmlindex>Lines_op21.html#1008985</htmlindex>
			<tiptext>Sort Tab</tiptext>
		</match>
		<match Key="HIDD_UNITSPAGE">
			<htmlindex>Lines_op22.html#1002377</htmlindex>
			<tiptext>Units Tab</tiptext>
		</match>
		<match Key="HIDD_CURVEPARAMSPAGE">
			<htmlindex>Lines_op23.html#1002388</htmlindex>
			<tiptext>Curve Tab</tiptext>
		</match>
		<match Key="HIDD_PATCHPARAMSPAGE">
			<htmlindex>Lines_op26.html#1013923</htmlindex>
			<tiptext>Patch Tab</tiptext>
		</match>
		<match Key="HIDD_CURVEPOINTSPAGE">
			<htmlindex>Lines_op29.html#1013968</htmlindex>
			<tiptext>Curve Points Tab</tiptext>
		</match>
		<match Key="HIDD_IGESPAGE">
			<htmlindex>Lines_op30.html#1013982</htmlindex>
			<tiptext>Iges Tab</tiptext>
		</match>
		<match Key="HIDD_DESIGNPAGE">
			<htmlindex>Lines_op31.html#1013986</htmlindex>
			<tiptext>Design Tab</tiptext>
		</match>
		<match Key="HIDD_VIEWORIENTATIONPAGE">
			<htmlindex>Lines_op33.html#1002479</htmlindex>
			<tiptext>Orientation Tab</tiptext>
		</match>
		<match Key="HIDD_VIEWGRIDPAGE">
			<htmlindex>Lines_op34.html#1002488</htmlindex>
			<tiptext>Grid Tab</tiptext>
		</match>
		<match Key="HIDD_VIEWDISPLAYPAGE">
			<htmlindex>Lines_op35.html#1002494</htmlindex>
			<tiptext>Display Tab</tiptext>
		</match>
		<match Key="HIDD_CURVESWITCH">
			<htmlindex>Lines_op36.html#1002502</htmlindex>
			<tiptext>Select Curves Dialogue</tiptext>
		</match>
		<match Key="HIDD_FAIRING_FIT">
			<htmlindex>Lines_op38.html#1002553</htmlindex>
			<tiptext>Fit Tab</tiptext>
		</match>
		<match Key="HIDD_FAIRING_DIAGONAL">
			<htmlindex>Lines_op39.html#1002560</htmlindex>
			<tiptext>Diagonal Tab</tiptext>
		</match>
		<match Key="HIDD_FAIRING_GET">
			<htmlindex>Lines_op40.html#1007838</htmlindex>
			<tiptext>Get Tab</tiptext>
		</match>
		<match Key="HIDD_FTCLINE">
			<htmlindex>Lines_op42.html#1002593</htmlindex>
			<tiptext>Cline Tab</tiptext>
		</match>
		<match Key="HIDD_FTCAMBSHEAR">
			<htmlindex>Lines_op48.html#1002655</htmlindex>
			<tiptext>Camber/Sheer Tab</tiptext>
		</match>
		<match Key="HIDD_FTANGLE">
			<htmlindex>Lines_op49.html#1002672</htmlindex>
			<tiptext>Angle Tab</tiptext>
		</match>
		<match Key="HIDD_INTERSECTPAGE">
			<htmlindex>Lines_op51.html#1002696</htmlindex>
			<tiptext>Intersect Tab</tiptext>
		</match>
		<match Key="HIDD_PROJFAIRPAGE">
			<htmlindex>Lines_op52.html#1002717</htmlindex>
			<tiptext>Project and Fair Tab</tiptext>
		</match>
		<match Key="HIDD_MERGEPAGE">
			<htmlindex>Lines_op53.html#1002736</htmlindex>
			<tiptext>Merge Tab</tiptext>
		</match>
		<match Key="HIDD_OPENPACE">
			<htmlindex>Lines_op54.html#1040227</htmlindex>
			<tiptext>Open PACE Dialog</tiptext>
		</match>
		<match Key="HIDD_ACE_INDEX">
			<htmlindex>Lines_op55.html#1053617</htmlindex>
			<tiptext>Patch and Curve Editor</tiptext>
		</match>
		<match Key="HIDD_PACE_TREE">
			<htmlindex>Lines_op58.html#1053632</htmlindex>
			<tiptext>PACE Curve Tree</tiptext>
		</match>
		<match Key="HIDD_PACE_PATCH_TREE">
			<htmlindex>Lines_op61.html#1053795</htmlindex>
			<tiptext>PACE Patch Tree</tiptext>
		</match>
		<match Key="HIDD_PACE_OUTPUTWINDOW">
			<htmlindex>Lines_op66.html#1054018</htmlindex>
			<tiptext>Output Window</tiptext>
		</match>
		<match Key="HIDD_PACE_DATABAR">
			<htmlindex>Lines_op67.html#1054076</htmlindex>
			<tiptext>PACE Data Bar</tiptext>
		</match>
		<match Key="HIDD_DLG_SNAP_TO_GRID">
			<htmlindex>Lines_op72.html#1054743</htmlindex>
			<tiptext>PACE Snap To Grid Dialog</tiptext>
		</match>
		<match Key="HIDD_DLG_ADV_SETTINGS">
			<htmlindex>Lines_op73.html#1054755</htmlindex>
			<tiptext>Advanced Settings Dialog</tiptext>
		</match>
		<match Key="HIDD_DLG_ARC_TOOL">
			<htmlindex>Lines_op74.html#1054767</htmlindex>
			<tiptext>Arc Tool Dialog</tiptext>
		</match>
		<match Key="HIDD_NONE">
			<htmlindex>Lines_op75.html#1054775</htmlindex>
			<tiptext>PACE None</tiptext>
		</match>
		<match Key="HIDD_RESOLUTION_DIALOG">
			<htmlindex>Lines_op75.html#1054775</htmlindex>
			<tiptext>Set Curve Precision Dialog Reference</tiptext>
		</match>
		<match Key="HIDD_PACE_SURFACE_DISPLAY_OPTIONS">
			<htmlindex>Lines_op76.html#1054784</htmlindex>
			<tiptext>PACE Surface Display Options Dialog</tiptext>
		</match>
		<match Key="HIDD_NUDGESIZE_DIALOG">
			<htmlindex>Lines_op78.html#1054798</htmlindex>
			<tiptext>Set Arrow Key Nudge Size Dialog</tiptext>
		</match>
		<match Key="HIDD_PACE_NEWCURVE">
			<htmlindex>Lines_op79.html#1054805</htmlindex>
			<tiptext>New Curve Dialog</tiptext>
		</match>
		<match Key="HIDD_DLG_DATA_SNAPPING">
			<htmlindex>Lines_op83.html#1054839</htmlindex>
			<tiptext>Data Point Snapping Dialog</tiptext>
		</match>
		<match Key="HIDD_PACE_BUILD_SURFACE">
			<htmlindex>Lines_op84.html#1054856</htmlindex>
			<tiptext>PACE Build Surface Dialog</tiptext>
		</match>
		<match Key="HIDD_DLG_ADD_CUT_CURVES">
			<htmlindex>Lines_op89.html#1054888</htmlindex>
			<tiptext>Add Cut Curves Dialog</tiptext>
		</match>
		<match Key="HIDD_DLG_HYDROSTATICS">
			<htmlindex>Lines_op92.html#1054906</htmlindex>
			<tiptext>Hydrostatics Dialog</tiptext>
		</match>
		<match Key="HIDD_CORNERTANGENCY">
			<htmlindex>Lines_op93.html#1054921</htmlindex>
			<tiptext>PACE Corner Tangency Options Dialog</tiptext>
		</match>
		<match Key="HIDD_EDGETANGENCY">
			<htmlindex>Lines_op94.html#1054935</htmlindex>
			<tiptext>PACE Edge Tangency Options Dialog</tiptext>
		</match>
		<match Key="HIDD_PACE_CONTROLVERTEX">
			<htmlindex>Lines_op95.html#1054952</htmlindex>
			<tiptext>PACE Patch Control Vertex Dialog</tiptext>
		</match>
		<match Key="HIDD_PACE_ESOPTIONS">
			<htmlindex>Lines_op96.html#1065952</htmlindex>
			<tiptext>End Surface Options</tiptext>
		</match>
		<match Key="HIDD_PACE_ESDATAPOINTATHEIGHT">
			<htmlindex>Lines_op100.html#1065965</htmlindex>
			<tiptext>Insert Data Point</tiptext>
		</match>
		<match Key="HIDD_FITTINGPAGE">
			<htmlindex>Lines_op104.html#1003091</htmlindex>
			<tiptext>Fitting Tab</tiptext>
		</match>
		<match Key="HIDD_POINTSPAGE">
			<htmlindex>Lines_op105.html#1003129</htmlindex>
			<tiptext>Points Tab</tiptext>
		</match>
		<match Key="HIDD_CURVEPAGE">
			<htmlindex>Lines_op106.html#1003145</htmlindex>
			<tiptext>Curve Tab</tiptext>
		</match>
		<match Key="HIDD_QUERYPAGE">
			<htmlindex>Lines_op107.html#1003162</htmlindex>
			<tiptext>Query Tab</tiptext>
		</match>
		<match Key="HIDD_PMBDEF">
			<htmlindex>Lines_op108.html#1003178</htmlindex>
			<tiptext>Parallel MidBody Dialogue</tiptext>
		</match>
		<match Key="HIDD_DIRECTORY">
			<htmlindex>Lines_op109.html#1003198</htmlindex>
			<tiptext>Directory Dialogue</tiptext>
		</match>
		<match Key="HIDD_ANALYSE_CURVEINTERSECTION_PAGE">
			<htmlindex>Lines_op111.html#1003246</htmlindex>
			<tiptext>Curve Intersection Tab</tiptext>
		</match>
		<match Key="HIDD_ANALYSE_GIRTHINGCURVES_PAGE">
			<htmlindex>Lines_op112.html#1003257</htmlindex>
			<tiptext>Girthing Curves Tab</tiptext>
		</match>
		<match Key="HIDD_ANALYSE_SECTIONAREAS_PAGE">
			<htmlindex>Lines_op113.html#1007719</htmlindex>
			<tiptext>Section Areas Tab</tiptext>
		</match>
		<match Key="HIDD_DECKPAGE">
			<htmlindex>Lines_op115.html#1003286</htmlindex>
			<tiptext>Deck Tab</tiptext>
		</match>
		<match Key="HIDD_GUNWHALEPAGE">
			<htmlindex>Lines_op116.html#1003288</htmlindex>
			<tiptext>Gunwhale Tab</tiptext>
		</match>
		<match Key="HIDD_PLOTTING">
			<htmlindex>Lines_op117.html#1003296</htmlindex>
			<tiptext>Plotting Dialogue</tiptext>
		</match>
		<match Key="HIDD_LOFTBOOK">
			<htmlindex>Lines_op118.html#1003321</htmlindex>
			<tiptext>Loft Book Generation Dialogue</tiptext>
		</match>
		<match Key="HIDD_SACPAGE">
			<htmlindex>Lines_op120.html#1003353</htmlindex>
			<tiptext>SAC Tab</tiptext>
		</match>
		<match Key="HIDD_DISTPAGE">
			<htmlindex>Lines_op121.html#1003366</htmlindex>
			<tiptext>Distort Tab</tiptext>
		</match>
		<match Key="HIDD_EXTRAPPAGE">
			<htmlindex>Lines_op122.html#1003377</htmlindex>
			<tiptext>Extrapolate Tab</tiptext>
		</match>
		<match Key="HIDD_SCALEPAGE">
			<htmlindex>Lines_op123.html#1003387</htmlindex>
			<tiptext>Scale Tab</tiptext>
		</match>
		<match Key="HIDD_TRANSLATEPAGE">
			<htmlindex>Lines_op124.html#1014457</htmlindex>
			<tiptext>Translate Tab</tiptext>
		</match>
		<match Key="HIDD_PMBPAGE">
			<htmlindex>Lines_op125.html#1046752</htmlindex>
			<tiptext>PMB Tab</tiptext>
		</match>
		<match Key="HIDD_SPGENERATE">
			<htmlindex>Lines_op127.html#1003410</htmlindex>
			<tiptext>Generate Tab</tiptext>
		</match>
		<match Key="HIDD_REGEN">
			<htmlindex>Lines_op128.html#1003426</htmlindex>
			<tiptext>Regenerate Tab</tiptext>
		</match>
		<match Key="HIDD_SPDISPLAY">
			<htmlindex>Lines_op129.html#1003435</htmlindex>
			<tiptext>Display Tab</tiptext>
		</match>
		<match Key="HIDD_SPCHECK">
			<htmlindex>Lines_op135.html#1003488</htmlindex>
			<tiptext>Check Tab</tiptext>
		</match>
		<match Key="HIDD_SPFIT">
			<htmlindex>Lines_op136.html#1003503</htmlindex>
			<tiptext>Fit Tab</tiptext>
		</match>
		<match Key="HIDD_SPTYPE">
			<htmlindex>Lines_op137.html#1003515</htmlindex>
			<tiptext>Type Tab</tiptext>
		</match>
		<match Key="HIDD_SPCONTINUITY">
			<htmlindex>Lines_op138.html#1003527</htmlindex>
			<tiptext>Continuity Tab</tiptext>
		</match>
		<match Key="HIDD_SPOFFSET">
			<htmlindex>Lines_op139.html#1003540</htmlindex>
			<tiptext>Offset Tab</tiptext>
		</match>
		<match Key="HIDD_SPOUTPUT">
			<htmlindex>Lines_op140.html#1003551</htmlindex>
			<tiptext>Output Tab</tiptext>
		</match>
		<match Key="HIDD_SPORDER">
			<htmlindex>Lines_op141.html#1016356</htmlindex>
			<tiptext>Order Tab</tiptext>
		</match>
		<match Key="HIDD_EXPORT">
			<htmlindex>Lines_op145.html#1003568</htmlindex>
			<tiptext>Export Dialogue</tiptext>
		</match>
		<match Key="HIDD_SPLIT">
			<htmlindex>Lines_op146.html#1003591</htmlindex>
			<tiptext>Split Dialogue</tiptext>
		</match>
		<match Key="HIDD_JOIN">
			<htmlindex>Lines_op147.html#1003610</htmlindex>
			<tiptext>Join Dialogue</tiptext>
		</match>
		<match Key="HIDD_CE_ENDTYPE">
			<htmlindex>Lines_op149.html#1003639</htmlindex>
			<tiptext>End Type Page</tiptext>
		</match>
		<match Key="HIDD_WLENDINGS_HSRAD">
			<htmlindex>Lines_op150.html#1003648</htmlindex>
			<tiptext>HS/Radius Page</tiptext>
		</match>
		<match Key="HIDD_WLENDINGS_ENDSURFACE">
			<htmlindex>Lines_op151.html#1003657</htmlindex>
			<tiptext>End Surface Page</tiptext>
		</match>
		<match Key="HIDD_WLENDINGS_UPDATE">
			<htmlindex>Lines_op152.html#1003670</htmlindex>
			<tiptext>Update Page</tiptext>
		</match>
		<match Key="HIDD_HULLFORMPROPS">
			<htmlindex>Lines_op153.html#1046839</htmlindex>
			<tiptext>Hull Form Properties Dialog</tiptext>
		</match>
		<match Key="HIDD_MACRO">
			<htmlindex>Lines_op154.html#997853</htmlindex>
			<tiptext>Macro Editor</tiptext>
		</match>
		<match Key="HIDD_SHADE">
			<htmlindex>Lines_op155.html#1013624</htmlindex>
			<tiptext>Shade Dialogue</tiptext>
		</match>
		<match Key="HIDD_PACE_TREE">
			<htmlindex>line_469.html#339744</htmlindex>
			<tiptext>PACE Curve Tree</tiptext>
		</match>
		<match Key="HIDD_PACE_TREE">
			<htmlindex>line_472.html#339904</htmlindex>
			<tiptext>PACE Curve Tree</tiptext>
		</match>
		<match Key="HIDD_COMMANDS">
			<htmlindex>Line_62.html#150553</htmlindex>
			<tiptext>Lines Commands</tiptext>
		</match>
	</book>
	<book Name="Pipe.chm" ID="TBPIPEMOD">
		<match Key="8302">
			<htmlindex>Pipe_op3.html#141529</htmlindex>
			<tiptext>New</tiptext>
		</match>
		<match Key="8302">
			<htmlindex>Pipe_op4.html#141576</htmlindex>
			<tiptext>Activate</tiptext>
		</match>
		<match Key="8302">
			<htmlindex>Pipe_op7.html#141667</htmlindex>
			<tiptext>Delete</tiptext>
		</match>
		<match Key="8319">
			<htmlindex>Pipe_op9.html#144934</htmlindex>
			<tiptext>Ready</tiptext>
		</match>
		<match Key="8497">
			<htmlindex>Pipe_op10.html#141774</htmlindex>
			<tiptext>Colour</tiptext>
		</match>
		<match Key="8330">
			<htmlindex>Pipe_op11.html#141817</htmlindex>
			<tiptext>Change user id</tiptext>
		</match>
		<match Key="8617">
			<htmlindex>Pipe_op14.html#141906</htmlindex>
			<tiptext>Pipe group move</tiptext>
		</match>
		<match Key="8616">
			<htmlindex>Pipe_op16.html#147022</htmlindex>
			<tiptext>Pipe group duplicate</tiptext>
		</match>
		<match Key="8682">
			<htmlindex>Pipe_op17.html#142018</htmlindex>
			<tiptext>Pipe group rename</tiptext>
		</match>
		<match Key="8683">
			<htmlindex>Pipe_op18.html#142061</htmlindex>
			<tiptext>Pipe group recalculate box</tiptext>
		</match>
		<match Key="8683">
			<htmlindex>Pipe_op19.html#140245</htmlindex>
			<tiptext>Pipe group colour</tiptext>
		</match>
		<match Key="8683">
			<htmlindex>Pipe_op20.html#142124</htmlindex>
			<tiptext>Pipe group delete</tiptext>
		</match>
		<match Key="8683">
			<htmlindex>Pipe_op21.html#142169</htmlindex>
			<tiptext>Pipe group regenerate</tiptext>
		</match>
		<match Key="8479">
			<htmlindex>Pipe_op24.html#142205</htmlindex>
			<tiptext>Component menu</tiptext>
		</match>
		<match Key="8669">
			<htmlindex>Pipe_op25.html#142234</htmlindex>
			<tiptext>Splitting background</tiptext>
		</match>
		<match Key="6396">
			<htmlindex>Pipe_op26.html#142277</htmlindex>
			<tiptext>Modelling background</tiptext>
		</match>
		<match Key="8327">
			<htmlindex>Pipe_op28.html#142344</htmlindex>
			<tiptext>Ident update</tiptext>
		</match>
		<match Key="8352">
			<htmlindex>Pipe_op31.html#142427</htmlindex>
			<tiptext>Route</tiptext>
		</match>
		<match Key="8341">
			<htmlindex>Pipe_op32.html#142468</htmlindex>
			<tiptext>Bend create</tiptext>
		</match>
		<match Key="8340">
			<htmlindex>Pipe_op33.html#142497</htmlindex>
			<tiptext>Bends move free</tiptext>
		</match>
		<match Key="8340">
			<htmlindex>Pipe_op34.html#142526</htmlindex>
			<tiptext>Bends move abs length</tiptext>
		</match>
		<match Key="8340">
			<htmlindex>Pipe_op35.html#142555</htmlindex>
			<tiptext>Bends move coord length</tiptext>
		</match>
		<match Key="8340">
			<htmlindex>Pipe_op36.html#142584</htmlindex>
			<tiptext>Bends move rel length</tiptext>
		</match>
		<match Key="8340">
			<htmlindex>Pipe_op37.html#142627</htmlindex>
			<tiptext>Bends move angle</tiptext>
		</match>
		<match Key="8340">
			<htmlindex>Pipe_op38.html#142656</htmlindex>
			<tiptext>Bends delete 0-bends</tiptext>
		</match>
		<match Key="8357">
			<htmlindex>Pipe_op40.html#142819</htmlindex>
			<tiptext>Material frame to pipe</tiptext>
		</match>
		<match Key="8355">
			<htmlindex>Pipe_op41.html#142797</htmlindex>
			<tiptext>Material substitute straight</tiptext>
		</match>
		<match Key="8351">
			<htmlindex>Pipe_op42.html#142844</htmlindex>
			<tiptext>Material substitute bend</tiptext>
		</match>
		<match Key="8369">
			<htmlindex>Pipe_op43.html#142876</htmlindex>
			<tiptext>Remove material pipe to frame</tiptext>
		</match>
		<match Key="8369">
			<htmlindex>Pipe_op44.html#142913</htmlindex>
			<tiptext>Remove material branch to frame</tiptext>
		</match>
		<match Key="8351">
			<htmlindex>Pipe_op47.html#142962</htmlindex>
			<tiptext>Part add</tiptext>
		</match>
		<match Key="8354">
			<htmlindex>Pipe_op49.html#143029</htmlindex>
			<tiptext>Part delete</tiptext>
		</match>
		<match Key="8351">
			<htmlindex>Pipe_op50.html#157058</htmlindex>
			<tiptext>Part insert</tiptext>
		</match>
		<match Key="8358">
			<htmlindex>Pipe_op53.html#157082</htmlindex>
			<tiptext>Part flip</tiptext>
		</match>
		<match Key="8353">
			<htmlindex>Pipe_op54.html#143145</htmlindex>
			<tiptext>Part rotate</tiptext>
		</match>
		<match Key="8332">
			<htmlindex>Pipe_op56.html#143174</htmlindex>
			<tiptext>Part split</tiptext>
		</match>
		<match Key="8358">
			<htmlindex>Pipe_op57.html#156260</htmlindex>
			<tiptext>Part info</tiptext>
		</match>
		<match Key="8605">
			<htmlindex>Pipe_op58.html#143246</htmlindex>
			<tiptext>Group transform</tiptext>
		</match>
		<match Key="8608">
			<htmlindex>Pipe_op59.html#143275</htmlindex>
			<tiptext>Group rotate</tiptext>
		</match>
		<match Key="8606">
			<htmlindex>Pipe_op60.html#143382</htmlindex>
			<tiptext>Group add to conn</tiptext>
		</match>
		<match Key="8607">
			<htmlindex>Pipe_op61.html#143357</htmlindex>
			<tiptext>Group change length</tiptext>
		</match>
		<match Key="8621">
			<htmlindex>Pipe_op62.html#143417</htmlindex>
			<tiptext>Group rename</tiptext>
		</match>
		<match Key="8333">
			<htmlindex>Pipe_op63.html#143460</htmlindex>
			<tiptext>Connect</tiptext>
		</match>
		<match Key="8332">
			<htmlindex>Pipe_op64.html#143503</htmlindex>
			<tiptext>Disconnect</tiptext>
		</match>
		<match Key="8335">
			<htmlindex>Pipe_op65.html#143532</htmlindex>
			<tiptext>Boss connect</tiptext>
		</match>
		<match Key="8651">
			<htmlindex>Pipe_op66.html#143567</htmlindex>
			<tiptext>Struct conn connect</tiptext>
		</match>
		<match Key="8651">
			<htmlindex>Pipe_op67.html#145331</htmlindex>
			<tiptext>Struct conn disconnect</tiptext>
		</match>
		<match Key="8656">
			<htmlindex>Pipe_op68.html#145354</htmlindex>
			<tiptext>Struct conn show conn</tiptext>
		</match>
		<match Key="8656">
			<htmlindex>Pipe_op69.html#152386</htmlindex>
			<tiptext>Struct conn show conn</tiptext>
		</match>
		<match Key="8656">
			<htmlindex>Pipe_op70.html#157250</htmlindex>
			<tiptext>Struct conn show conn</tiptext>
		</match>
		<match Key="8610">
			<htmlindex>Pipe_op72.html#157275</htmlindex>
			<tiptext>Display unconnected</tiptext>
		</match>
		<match Key="8611">
			<htmlindex>Pipe_op74.html#145255</htmlindex>
			<tiptext>Remaining pipe</tiptext>
		</match>
		<match Key="8612">
			<htmlindex>Pipe_op75.html#143796</htmlindex>
			<tiptext>Remaining valve</tiptext>
		</match>
		<match Key="8613">
			<htmlindex>Pipe_op76.html#143821</htmlindex>
			<tiptext>Remaining connection</tiptext>
		</match>
		<match Key="8367">
			<htmlindex>Pipe_op77.html#143851</htmlindex>
			<tiptext>Pressure drop</tiptext>
		</match>
		<match Key="8361">
			<htmlindex>Pipe_op78.html#143890</htmlindex>
			<tiptext>Bend object id</tiptext>
		</match>
		<match Key="8337">
			<htmlindex>Pipe_op80.html#143660</htmlindex>
			<tiptext>Dump model</tiptext>
		</match>
		<match Key="8337">
			<htmlindex>Pipe_op81.html#144001</htmlindex>
			<tiptext>Dump picture</tiptext>
		</match>
		<match Key="8311">
			<htmlindex>Pipe_op83.html#144054</htmlindex>
			<tiptext>Pipe info update</tiptext>
		</match>
		<match Key="8311">
			<htmlindex>Pipe_op84.html#144096</htmlindex>
			<tiptext>Pipe info display</tiptext>
		</match>
		<match Key="8515">
			<htmlindex>Pipe_op85.html#144121</htmlindex>
			<tiptext>Spool name update auto</tiptext>
		</match>
		<match Key="8516">
			<htmlindex>Pipe_op86.html#144146</htmlindex>
			<tiptext>Spool name remove auto</tiptext>
		</match>
		<match Key="8325">
			<htmlindex>Pipe_op87.html#144029</htmlindex>
			<tiptext>Spool info update</tiptext>
		</match>
		<match Key="8325">
			<htmlindex>Pipe_op88.html#144216</htmlindex>
			<tiptext>Spool info delete</tiptext>
		</match>
		<match Key="8325">
			<htmlindex>Pipe_op89.html#144241</htmlindex>
			<tiptext>Spool info display</tiptext>
		</match>
		<match Key="8324">
			<htmlindex>Pipe_op90.html#144263</htmlindex>
			<tiptext>Spool limits set</tiptext>
		</match>
		<match Key="8324">
			<htmlindex>Pipe_op91.html#144285</htmlindex>
			<tiptext>Spool limits reset</tiptext>
		</match>
		<match Key="8323">
			<htmlindex>Pipe_op92.html#144307</htmlindex>
			<tiptext>Spool limits display</tiptext>
		</match>
		<match Key="8315">
			<htmlindex>Pipe_op94.html#144331</htmlindex>
			<tiptext>Part info update</tiptext>
		</match>
		<match Key="8315">
			<htmlindex>Pipe_op95.html#144353</htmlindex>
			<tiptext>Part info display</tiptext>
		</match>
		<match Key="8347">
			<htmlindex>Pipe_op96.html#144436</htmlindex>
			<tiptext>Feed min</tiptext>
		</match>
		<match Key="8336">
			<htmlindex>Pipe_op97.html#144461</htmlindex>
			<tiptext>Excess end set</tiptext>
		</match>
		<match Key="8336">
			<htmlindex>Pipe_op98.html#144483</htmlindex>
			<tiptext>Excess end reset</tiptext>
		</match>
		<match Key="8334">
			<htmlindex>Pipe_op99.html#144505</htmlindex>
			<tiptext>Excess feed set</tiptext>
		</match>
		<match Key="8334">
			<htmlindex>Pipe_op100.html#144527</htmlindex>
			<tiptext>Excess feed reset</tiptext>
		</match>
		<match Key="8328">
			<htmlindex>Pipe_op117.html#144398</htmlindex>
			<tiptext>Check on off display</tiptext>
		</match>
		<match Key="8328">
			<htmlindex>Pipe_op118.html#144835</htmlindex>
			<tiptext>Check on off extrude</tiptext>
		</match>
		<match Key="8328">
			<htmlindex>Pipe_op119.html#144857</htmlindex>
			<tiptext>Chack on off bend</tiptext>
		</match>
		<match Key="8328">
			<htmlindex>Pipe_op120.html#144879</htmlindex>
			<tiptext>Check on off spool name</tiptext>
		</match>
		<match Key="8664">
			<htmlindex>Pipe_op121.html#144901</htmlindex>
			<tiptext>Assembly</tiptext>
		</match>
		<match Key="TBPIPESPEC">
			<htmlindex>Pipe_Tbspec_op.html#1014194</htmlindex>
			<tiptext>PIPESPEC</tiptext>
		</match>
	</book>
	<book Name="Planar_Modelling.chm" ID="TBPHULLM">
		<match Key="14343">
			<htmlindex>Planar_Modelling_op3.html#150581</htmlindex>
			<tiptext>Divide</tiptext>
		</match>
		<match Key="14342">
			<htmlindex>Planar_Modelling_op4.html#150606</htmlindex>
			<tiptext>Split STI</tiptext>
		</match>
		<match Key="14343">
			<htmlindex>Planar_Modelling_op6.html#149550</htmlindex>
			<tiptext>Divide</tiptext>
		</match>
		<match Key="14342">
			<htmlindex>Planar_Modelling_op7.html#145756</htmlindex>
			<tiptext>Split STI</tiptext>
		</match>
		<match Key="14344">
			<htmlindex>Planar_Modelling_op8.html#145778</htmlindex>
			<tiptext>Delete</tiptext>
		</match>
		<match Key="14204">
			<htmlindex>Planar_Modelling_op10.html#147117</htmlindex>
			<tiptext>Copy</tiptext>
		</match>
		<match Key="14032">
			<htmlindex>Planar_Modelling_op11.html#145866</htmlindex>
			<tiptext>Split</tiptext>
		</match>
		<match Key="14145">
			<htmlindex>Planar_Modelling_op14.html#149753</htmlindex>
			<tiptext>Detail</tiptext>
		</match>
		<match Key="14066">
			<htmlindex>Planar_Modelling_op15.html#146539</htmlindex>
			<tiptext>Recreate</tiptext>
		</match>
		<match Key="14054">
			<htmlindex>Planar_Modelling_op16.html#146562</htmlindex>
			<tiptext>Modify</tiptext>
		</match>
		<match Key="14607">
			<htmlindex>Planar_Modelling_op17.html#146673</htmlindex>
			<tiptext>Select multiple views</tiptext>
		</match>
		<match Key="9642">
			<htmlindex>Planar_Modelling_op21.html#146728</htmlindex>
			<tiptext>Scope</tiptext>
		</match>
		<match Key="14441">
			<htmlindex>Planar_Modelling_op22.html#146751</htmlindex>
			<tiptext>Change</tiptext>
		</match>
		<match Key="14442">
			<htmlindex>Planar_Modelling_op23.html#146774</htmlindex>
			<tiptext>Copy</tiptext>
		</match>
		<match Key="14200">
			<htmlindex>Planar_Modelling_op28.html#146028</htmlindex>
			<tiptext>Activate</tiptext>
		</match>
		<match Key="14201">
			<htmlindex>Planar_Modelling_op29.html#146050</htmlindex>
			<tiptext>Store</tiptext>
		</match>
		<match Key="14315">
			<htmlindex>Planar_Modelling_op33.html#146877</htmlindex>
			<tiptext>Assembly</tiptext>
		</match>
		<match Key="14323">
			<htmlindex>Planar_Modelling_op34.html#146899</htmlindex>
			<tiptext>Bevel</tiptext>
		</match>
		<match Key="14301">
			<htmlindex>Planar_Modelling_op36.html#146927</htmlindex>
			<tiptext>Create Curve</tiptext>
		</match>
		<match Key="10610">
			<htmlindex>Planar_Modelling_op37.html#146949</htmlindex>
			<tiptext>Store Curve</tiptext>
		</match>
		<match Key="14430">
			<htmlindex>Planar_Modelling_op38.html#147030</htmlindex>
			<tiptext>Shrinkage Information</tiptext>
		</match>
		<match Key="14340">
			<htmlindex>Planar_Modelling_op39.html#146976</htmlindex>
			<tiptext>Panel Intersection</tiptext>
		</match>
		<match Key="14007">
			<htmlindex>Planar_Modelling_op40.html#147057</htmlindex>
			<tiptext>Re-initialise</tiptext>
		</match>
		<match Key="14551">
			<htmlindex>Planar_Modelling_op42.html#149592</htmlindex>
			<tiptext>Create Input</tiptext>
		</match>
		<match Key="14750">
			<htmlindex>Planar_Modelling_op43.html#149659</htmlindex>
			<tiptext>Calculate</tiptext>
		</match>
	</book>
	<book Name="Proddata_Interface.chm" ID="">
	</book>
	<book Name="Production_Manager.chm" ID="TBPRODM">
	</book>
	<book Name="Setup_Customisation.chm" ID="SETUPCUST">
	</book>
	<book Name="Structure.chm" ID="TBSTRUCT">
		<match Key="20113">
			<htmlindex>Structure_op3.html#141327</htmlindex>
			<tiptext>New</tiptext>
		</match>
		<match Key="20110">
			<htmlindex>Structure_op4.html#145332</htmlindex>
			<tiptext>Activate</tiptext>
		</match>
		<match Key="20104">
			<htmlindex>Structure_op5.html#145369</htmlindex>
			<tiptext>Save</tiptext>
		</match>
		<match Key="20115">
			<htmlindex>Structure_op6.html#145406</htmlindex>
			<tiptext>Cancel</tiptext>
		</match>
		<match Key="20114">
			<htmlindex>Structure_op7.html#145443</htmlindex>
			<tiptext>Delete</tiptext>
		</match>
		<match Key="20104">
			<htmlindex>Structure_op8.html#145480</htmlindex>
			<tiptext>List activated</tiptext>
		</match>
		<match Key="20161">
			<htmlindex>Structure_op9.html#154964</htmlindex>
			<tiptext>Part Transform</tiptext>
		</match>
		<match Key="20165">
			<htmlindex>Structure_op18.html#147159</htmlindex>
			<tiptext>Part New</tiptext>
		</match>
		<match Key="20164">
			<htmlindex>Structure_op19.html#147136</htmlindex>
			<tiptext>Part delete</tiptext>
		</match>
		<match Key="20163">
			<htmlindex>Structure_op20.html#145594</htmlindex>
			<tiptext>Part Modify</tiptext>
		</match>
		<match Key="20167">
			<htmlindex>Structure_op21.html#145644</htmlindex>
			<tiptext>Part Rename</tiptext>
		</match>
		<match Key="20168">
			<htmlindex>Structure_op22.html#150099</htmlindex>
			<tiptext>Part Flip</tiptext>
		</match>
		<match Key="20166">
			<htmlindex>Structure_op23.html#145717</htmlindex>
			<tiptext>Part Move</tiptext>
		</match>
		<match Key="20162">
			<htmlindex>Structure_op24.html#145791</htmlindex>
			<tiptext>Part Copy</tiptext>
		</match>
		<match Key="20170">
			<htmlindex>Structure_op25.html#145829</htmlindex>
			<tiptext>Part Hole</tiptext>
		</match>
		<match Key="20172">
			<htmlindex>Structure_op27.html#149385</htmlindex>
			<tiptext>Part Add Endcut</tiptext>
		</match>
		<match Key="20180">
			<htmlindex>Structure_op30.html#149408</htmlindex>
			<tiptext>Part attach to model</tiptext>
		</match>
		<match Key="20180">
			<htmlindex>Structure_op31.html#149431</htmlindex>
			<tiptext>Part attach to 2 D line</tiptext>
		</match>
		<match Key="20796">
			<htmlindex>Structure_op35.html#150332</htmlindex>
			<tiptext>Part Information</tiptext>
		</match>
		<match Key="20181">
			<htmlindex>Structure_op36.html#146061</htmlindex>
			<tiptext>Multiple parts transform</tiptext>
		</match>
		<match Key="20181">
			<htmlindex>Structure_op37.html#146117</htmlindex>
			<tiptext>Multiple parts duplicate</tiptext>
		</match>
		<match Key="20181">
			<htmlindex>Structure_op38.html#150546</htmlindex>
			<tiptext>Multiple parts delete</tiptext>
		</match>
		<match Key="20181">
			<htmlindex>Structure_op39.html#150657</htmlindex>
			<tiptext>Multiple parts stretch</tiptext>
		</match>
		<match Key="20181">
			<htmlindex>Structure_op40.html#150753</htmlindex>
			<tiptext>Multiple parts split</tiptext>
		</match>
		<match Key="18437">
			<htmlindex>Structure_op41.html#150857</htmlindex>
			<tiptext>Multiple parts put to</tiptext>
		</match>
		<match Key="20181">
			<htmlindex>Structure_op42.html#150922</htmlindex>
			<tiptext>Multiple parts copy from</tiptext>
		</match>
		<match Key="20181">
			<htmlindex>Structure_op43.html#151012</htmlindex>
			<tiptext>Multiple parts duplicate</tiptext>
		</match>
		<match Key="20181">
			<htmlindex>Structure_op44.html#148458</htmlindex>
			<tiptext>Multiple parts duplicate</tiptext>
		</match>
		<match Key="18437">
			<htmlindex>Structure_op48.html#146352</htmlindex>
			<tiptext>Standard unit general seat inside</tiptext>
		</match>
		<match Key="20182">
			<htmlindex>Structure_op49.html#146467</htmlindex>
			<tiptext>Standard unit general seat outside</tiptext>
		</match>
		<match Key="20111">
			<htmlindex>Structure_op50.html#146489</htmlindex>
			<tiptext>Transform</tiptext>
		</match>
		<match Key="20133">
			<htmlindex>Structure_op51.html#146514</htmlindex>
			<tiptext>Insert</tiptext>
		</match>
		<match Key="20117">
			<htmlindex>Structure_op52.html#146558</htmlindex>
			<tiptext>Rename</tiptext>
		</match>
		<match Key="20118">
			<htmlindex>Structure_op53.html#146600</htmlindex>
			<tiptext>Production information assembly model</tiptext>
		</match>
		<match Key="20171">
			<htmlindex>Structure_op54.html#146637</htmlindex>
			<tiptext>Production information assembly part</tiptext>
		</match>
		<match Key="20169">
			<htmlindex>Structure_op55.html#146663</htmlindex>
			<tiptext>production information position number</tiptext>
		</match>
		<match Key="20131">
			<htmlindex>Structure_op56.html#146717</htmlindex>
			<tiptext>Production information create hull marking on</tiptext>
		</match>
		<match Key="20131">
			<htmlindex>Structure_op57.html#146763</htmlindex>
			<tiptext>Production information create hull marking off</tiptext>
		</match>
		<match Key="20131">
			<htmlindex>Structure_op58.html#155673</htmlindex>
			<tiptext>Production information create hull marking check</tiptext>
		</match>
		<match Key="20112">
			<htmlindex>Structure_op63.html#155698</htmlindex>
			<tiptext>Production information split</tiptext>
		</match>
		<match Key="20120">
			<htmlindex>Structure_op64.html#146892</htmlindex>
			<tiptext>Group move</tiptext>
		</match>
		<match Key="20199">
			<htmlindex>Structure_op65.html#146931</htmlindex>
			<tiptext>Group duplicate</tiptext>
		</match>
		<match Key="20130">
			<htmlindex>Structure_op66.html#146965</htmlindex>
			<tiptext>Group standard output</tiptext>
		</match>
		<match Key="20130">
			<htmlindex>Structure_op67.html#153571</htmlindex>
			<tiptext>Group standard output</tiptext>
		</match>
		<match Key="20127">
			<htmlindex>Structure_op68.html#151317</htmlindex>
			<tiptext>Standard input</tiptext>
		</match>
		<match Key="20128">
			<htmlindex>Structure_op70.html#151395</htmlindex>
			<tiptext>Standard replace</tiptext>
		</match>
		<match Key="20127">
			<htmlindex>Structure_op71.html#149195</htmlindex>
			<tiptext>Standard input</tiptext>
		</match>
		<match Key="20128">
			<htmlindex>Structure_op72.html#148898</htmlindex>
			<tiptext>Standard replace</tiptext>
		</match>
		<match Key="20128">
			<htmlindex>Structure_op73.html#148968</htmlindex>
			<tiptext>Standard replace</tiptext>
		</match>
		<match Key="20128">
			<htmlindex>Structure_op74.html#149041</htmlindex>
			<tiptext>Standard replace</tiptext>
		</match>
		<match Key="20128">
			<htmlindex>Structure_op75.html#149072</htmlindex>
			<tiptext>Standard replace</tiptext>
		</match>
		<match Key="20128">
			<htmlindex>Structure_op76.html#154491</htmlindex>
			<tiptext>Standard replace</tiptext>
		</match>
		<match Key="20128">
			<htmlindex>Structure_op77.html#154551</htmlindex>
			<tiptext>Standard replace</tiptext>
		</match>
		<match Key="20128">
			<htmlindex>Structure_op78.html#148872</htmlindex>
			<tiptext>Standard replace</tiptext>
		</match>
	</book>
	<book Name="Surface.chm" ID="TBSURFACE">
		<match Key="HIDD_MAINFRAME">
			<htmlindex>Surface_opa2.html#156923</htmlindex>
			<tiptext>Layout</tiptext>
		</match>
		<match Key="HIDD_SURFACE_TREE">
			<htmlindex>Surface_opa3.html#156935</htmlindex>
			<tiptext>Full Tree View</tiptext>
		</match>
		<match Key="HIDD_FACETING_PARAMETERS">
			<htmlindex>Surface_opa4.html#184467</htmlindex>
			<tiptext>Faceting Parameters</tiptext>
		</match>
		<match Key="HIDD_PRIMITIVE_DEFINITION">
			<htmlindex>Surface_opa5.html#184483</htmlindex>
			<tiptext>Primitive Definition</tiptext>
		</match>
		<match Key="HIDD_RENDER_MATERIALS">
			<htmlindex>Surface_op6.html#157124</htmlindex>
			<tiptext>Materials</tiptext>
		</match>
		<match Key="HIDD_COMPARTMENT_TREE">
			<htmlindex>Surface_op7.html#185292</htmlindex>
			<tiptext>Compartment Tree View</tiptext>
		</match>
		<match Key="HIDD_CONTAINER_TREE">
			<htmlindex>Surface_op8.html#157130</htmlindex>
			<tiptext>Container Tree View</tiptext>
		</match>
		<match Key="HIDD_OUTPUT_WINDOW">
			<htmlindex>Surface_op9.html#157252</htmlindex>
			<tiptext>Output Window</tiptext>
		</match>
		<match Key="HIDD_DML_IMPORT_OPTIONS">
			<htmlindex>Surface_op11.html#198250</htmlindex>
			<tiptext>Hull Form Options</tiptext>
		</match>
		<match Key="HIDD_LOFTBOOK_SHEET">
			<htmlindex>Surface_op12.html#157337</htmlindex>
			<tiptext>Loftbook Sheet</tiptext>
		</match>
		<match Key="HIDD_LOFTBOOK_CROSSINGS_PAGE">
			<htmlindex>Surface_op13.html#177131</htmlindex>
			<tiptext>Crossings Page</tiptext>
		</match>
		<match Key="HIDD_LOFTBOOK_FORMAT_PAGE">
			<htmlindex>Surface_op14.html#181111</htmlindex>
			<tiptext>Format Page</tiptext>
		</match>
		<match Key="HIDD_PLOT_SHEET">
			<htmlindex>Surface_op15.html#157353</htmlindex>
			<tiptext>Plot Sheet</tiptext>
		</match>
		<match Key="HIDD_PLOT_GENERAL_PAGE">
			<htmlindex>Surface_op16.html#157360</htmlindex>
			<tiptext>Media Page</tiptext>
		</match>
		<match Key="HIDD_PLOT_PENS_COLOUR_PAGE">
			<htmlindex>Surface_op17.html#188850</htmlindex>
			<tiptext>Pens Page</tiptext>
		</match>
		<match Key="HIDD_PLOT_POSITION_PAGE">
			<htmlindex>Surface_op18.html#157364</htmlindex>
			<tiptext>Position Page</tiptext>
		</match>
		<match Key="HIDD_PLOT_DISPLAY_PAGE">
			<htmlindex>Surface_op19.html#157371</htmlindex>
			<tiptext>Display Page</tiptext>
		</match>
		<match Key="HIDD_PLOT_PRESETS_PAGE">
			<htmlindex>Surface_op20.html#184365</htmlindex>
			<tiptext>Presets Page</tiptext>
		</match>
		<match Key="HIDD_LAYER_NO">
			<htmlindex>Surface_op22.html#157385</htmlindex>
			<tiptext>Current Layer</tiptext>
		</match>
		<match Key="HIDD_REPORT_OPTIONS">
			<htmlindex>Surface_op23.html#175595</htmlindex>
			<tiptext>Report Options</tiptext>
		</match>
		<match Key="HIDD_VIEW_OPTIONS_SHEET">
			<htmlindex>Surface_op25.html#157389</htmlindex>
			<tiptext>View Options Sheet</tiptext>
		</match>
		<match Key="HIDD_VIEW_OPTIONS_DISPLAY_PAGE">
			<htmlindex>Surface_op26.html#157392</htmlindex>
			<tiptext>Display Page</tiptext>
		</match>
		<match Key="HIDD_VIEW_OPTIONS_MODES_PAGE">
			<htmlindex>Surface_op27.html#157404</htmlindex>
			<tiptext>Modes Page</tiptext>
		</match>
		<match Key="HIDD_VIEW_OPTIONS_LAYERS_PAGE">
			<htmlindex>Surface_op28.html#157414</htmlindex>
			<tiptext>Layers Page</tiptext>
		</match>
		<match Key="HIDD_CUSTOMIZETOOLBARS">
			<htmlindex>Surface_op30.html#157437</htmlindex>
			<tiptext>Toolbars Page</tiptext>
		</match>
		<match Key="HIDD_CUSTOMIZECOMMANDS">
			<htmlindex>Surface_op31.html#157439</htmlindex>
			<tiptext>Commands Page</tiptext>
		</match>
		<match Key="HIDD_CUSTOMIZESHORTCUTS">
			<htmlindex>Surface_op32.html#180320</htmlindex>
			<tiptext>Shortcut Keys</tiptext>
		</match>
		<match Key="HIDD_LINE_POINT">
			<htmlindex>Surface_op34.html#184594</htmlindex>
			<tiptext>Point</tiptext>
		</match>
		<match Key="HIDD_LINE_SEGMENT">
			<htmlindex>Surface_op35.html#157444</htmlindex>
			<tiptext>Segment</tiptext>
		</match>
		<match Key="HIDD_LINE_RECTANGLE">
			<htmlindex>Surface_op36.html#184614</htmlindex>
			<tiptext>Rectangle</tiptext>
		</match>
		<match Key="HIDD_LINE_ARC">
			<htmlindex>Surface_op37.html#157452</htmlindex>
			<tiptext>Arc</tiptext>
		</match>
		<match Key="HIDD_LINE_ELLIPSE">
			<htmlindex>Surface_op38.html#157459</htmlindex>
			<tiptext>Ellipse</tiptext>
		</match>
		<match Key="HIDD_LINE_TABULAR">
			<htmlindex>Surface_op39.html#157463</htmlindex>
			<tiptext>Tabulated Polyline/Spline</tiptext>
		</match>
		<match Key="HIDD_LINE_NACA">
			<htmlindex>Surface_op40.html#157472</htmlindex>
			<tiptext>NACA</tiptext>
		</match>
		<match Key="HIDD_LINE_MODEL_CUTS">
			<htmlindex>Surface_op41.html#157478</htmlindex>
			<tiptext>Model Cuts</tiptext>
		</match>
		<match Key="HIDD_LINE_FILLET">
			<htmlindex>Surface_op43.html#157481</htmlindex>
			<tiptext>Fillet</tiptext>
		</match>
		<match Key="HIDD_LINE_OFFSET_CURVE">
			<htmlindex>Surface_op44.html#157484</htmlindex>
			<tiptext>Offset Wire</tiptext>
		</match>
		<match Key="HIDD_LINE_EXTEND">
			<htmlindex>Surface_op45.html#184700</htmlindex>
			<tiptext>Extend</tiptext>
		</match>
		<match Key="HIDD_LINE_SPLIT">
			<htmlindex>Surface_op46.html#203247</htmlindex>
			<tiptext>Split</tiptext>
		</match>
		<match Key="HIDD_SURFACES_PLANE">
			<htmlindex>Surface_op48.html#157489</htmlindex>
			<tiptext>Plane</tiptext>
		</match>
		<match Key="HIDD_SURFACES_CYLINDER">
			<htmlindex>Surface_op49.html#157492</htmlindex>
			<tiptext>Cylinder</tiptext>
		</match>
		<match Key="HIDD_SURFACES_CONE">
			<htmlindex>Surface_op50.html#157495</htmlindex>
			<tiptext>Cone</tiptext>
		</match>
		<match Key="HIDD_SURFACES_SPHERE">
			<htmlindex>Surface_op51.html#157498</htmlindex>
			<tiptext>Sphere</tiptext>
		</match>
		<match Key="HIDD_SURFACES_TORUS">
			<htmlindex>Surface_op52.html#157501</htmlindex>
			<tiptext>Torus</tiptext>
		</match>
		<match Key="HIDD_SURFACES_REVOLVED">
			<htmlindex>Surface_op53.html#157504</htmlindex>
			<tiptext>Revolved</tiptext>
		</match>
		<match Key="HIDD_SURFACES_SWEPT">
			<htmlindex>Surface_op54.html#157507</htmlindex>
			<tiptext>Swept</tiptext>
		</match>
		<match Key="HIDD_SURFACES_OFFSET">
			<htmlindex>Surface_op55.html#157516</htmlindex>
			<tiptext>Offset</tiptext>
		</match>
		<match Key="HIDD_SURFACE_MESH_MANIPULATION">
			<htmlindex>Surface_op56.html#157519</htmlindex>
			<tiptext>Mesh</tiptext>
		</match>
		<match Key="HIDD_SOLIDS_BLOCK">
			<htmlindex>Surface_op59.html#157545</htmlindex>
			<tiptext>Block</tiptext>
		</match>
		<match Key="HIDD_SOLIDS_PYRAMID">
			<htmlindex>Surface_op60.html#157548</htmlindex>
			<tiptext>Pyramid</tiptext>
		</match>
		<match Key="HIDD_SOLIDS_CYLINDER">
			<htmlindex>Surface_op61.html#157551</htmlindex>
			<tiptext>Cylinder</tiptext>
		</match>
		<match Key="HIDD_SOLIDS_CONE">
			<htmlindex>Surface_op62.html#157554</htmlindex>
			<tiptext>Cone</tiptext>
		</match>
		<match Key="HIDD_SOLIDS_SPHERE">
			<htmlindex>Surface_op63.html#157557</htmlindex>
			<tiptext>Sphere</tiptext>
		</match>
		<match Key="HIDD_SOLIDS_TORUS">
			<htmlindex>Surface_op64.html#157560</htmlindex>
			<tiptext>Torus</tiptext>
		</match>
		<match Key="HIDD_TRANSFORMATIONS_SCALE">
			<htmlindex>Surface_op66.html#157565</htmlindex>
			<tiptext>Scale</tiptext>
		</match>
		<match Key="HIDD_TRANSFORMATIONS_TRANSLATE">
			<htmlindex>Surface_op67.html#157570</htmlindex>
			<tiptext>Translate</tiptext>
		</match>
		<match Key="HIDD_TRANSFORMATIONS_ROTATE">
			<htmlindex>Surface_op68.html#157578</htmlindex>
			<tiptext>Rotate</tiptext>
		</match>
		<match Key="HIDD_TRANSFORMATIONS_ALIGN">
			<htmlindex>Surface_op69.html#157585</htmlindex>
			<tiptext>Align</tiptext>
		</match>
		<match Key="HIDD_TRANSFORMATIONS_REFLECT">
			<htmlindex>Surface_op70.html#157589</htmlindex>
			<tiptext>Reflect</tiptext>
		</match>
		<match Key="HIDD_BLENDING_SHEET">
			<htmlindex>Surface_op71.html#157593</htmlindex>
			<tiptext>Blending Sheet</tiptext>
		</match>
		<match Key="HIDD_BLENDING_EDGE">
			<htmlindex>Surface_op72.html#157603</htmlindex>
			<tiptext>Edge Page</tiptext>
		</match>
		<match Key="HIDD_BLENDING_VERTEX">
			<htmlindex>Surface_op73.html#157612</htmlindex>
			<tiptext>Vertex Page</tiptext>
		</match>
		<match Key="HIDD_OPERATIONS_THICKEN">
			<htmlindex>Surface_op75.html#195425</htmlindex>
			<tiptext>Thicken</tiptext>
		</match>
		<match Key="HIDD_GROUPS_INSTANCES_SHEET">
			<htmlindex>Surface_op76.html#184834</htmlindex>
			<tiptext>Groups and Instances Editor</tiptext>
		</match>
		<match Key="HIDD_GROUPS_PAGE">
			<htmlindex>Surface_op77.html#184851</htmlindex>
			<tiptext>Groups Page</tiptext>
		</match>
		<match Key="HIDD_INSTANCES_PAGE">
			<htmlindex>Surface_op78.html#184873</htmlindex>
			<tiptext>Instances Page</tiptext>
		</match>
		<match Key="HIDD_HULL_SURFACE_GENERATION">
			<htmlindex>Surface_op80.html#158112</htmlindex>
			<tiptext>Automatic Surface Generation</tiptext>
		</match>
		<match Key="HIDD_HULL_SCALING">
			<htmlindex>Surface_op80.html#158112</htmlindex>
			<tiptext>Hull Scaling</tiptext>
		</match>
		<match Key="HIDD_HULL_DISTORTION">
			<htmlindex>Surface_op81.html#158120</htmlindex>
			<tiptext>Hull Distortion</tiptext>
		</match>
		<match Key="HIDD_HULL_SAC_VIEW">
			<htmlindex>Surface_op82.html#158126</htmlindex>
			<tiptext>Sectional Area Curve</tiptext>
		</match>
		<match Key="HIDD_OPTIONS_SHEET">
			<htmlindex>Surface_op85.html#158198</htmlindex>
			<tiptext>Options Sheet</tiptext>
		</match>
		<match Key="HIDD_OPTIONS_PROJECT">
			<htmlindex>Surface_op86.html#158201</htmlindex>
			<tiptext>Project Page</tiptext>
		</match>
		<match Key="HIDD_OPTIONS_PARTICULARS">
			<htmlindex>Surface_op87.html#158205</htmlindex>
			<tiptext>General Particulars Page</tiptext>
		</match>
		<match Key="HIDD_OPTIONS_FRAME_TABLE_PAGE">
			<htmlindex>Surface_op88.html#158208</htmlindex>
			<tiptext>Frame Table Page</tiptext>
		</match>
		<match Key="HIDD_OPTIONS_UNITS_PAGE">
			<htmlindex>Surface_op89.html#158213</htmlindex>
			<tiptext>Units Page</tiptext>
		</match>
		<match Key="HIDD_OPTIONS_AXES_PAGE">
			<htmlindex>Surface_op90.html#158218</htmlindex>
			<tiptext>Axes Page</tiptext>
		</match>
		<match Key="HIDD_OPTIONS_WCS_PAGE">
			<htmlindex>Surface_op91.html#158221</htmlindex>
			<tiptext>Working Coordinate System Page</tiptext>
		</match>
		<match Key="HIDD_OPTIONS_COLOURS">
			<htmlindex>Surface_op92.html#158231</htmlindex>
			<tiptext>Colours Page</tiptext>
		</match>
		<match Key="HIDD_OPTIONS_FACETER_PAGE">
			<htmlindex>Surface_op93.html#158235</htmlindex>
			<tiptext>Faceter Page</tiptext>
		</match>
		<match Key="HIDD_OPTIONS_GAUSS_BANDS_PAGE">
			<htmlindex>Surface_op94.html#158245</htmlindex>
			<tiptext>Gaussian Bands Page</tiptext>
		</match>
		<match Key="HIDD_OPTIONS_GRAPHICS_PAGE">
			<htmlindex>Surface_op95.html#185109</htmlindex>
			<tiptext>Graphics Features</tiptext>
		</match>
		<match Key="HIDD_POINT_DISPLAY_TYPE">
			<htmlindex>Surface_op96.html#197740</htmlindex>
			<tiptext>Point Display Type</tiptext>
		</match>
		<match Key="HIDD_OPTIONS_INTERNALS_PAGE">
			<htmlindex>Surface_op97.html#185119</htmlindex>
			<tiptext>Internals</tiptext>
		</match>
		<match Key="HIDD_COORDINATES">
			<htmlindex>Surface_op98.html#158251</htmlindex>
			<tiptext>Co-ordinates</tiptext>
		</match>
		<match Key="HIDD_COMMANDS">
			<htmlindex>Surface_62.html#140727</htmlindex>
			<tiptext>Surface Commands</tiptext>
		</match>
	</book>
	<book Name="TID_Proj.chm" ID="TIDPROJ">
		<match Key="HIDD_COMMENT">
			<htmlindex>TID_Proj_op2.html#1011617</htmlindex>
			<tiptext>Comment</tiptext>
		</match>
		<match Key="HIDD_FIND_SHEET">
			<htmlindex>TID_Proj_op3.html#1020133</htmlindex>
			<tiptext>Find Files</tiptext>
		</match>
		<match Key="HIDD_FIND_NAME">
			<htmlindex>TID_Proj_op4.html#1011627</htmlindex>
			<tiptext>Name and Location page</tiptext>
		</match>
		<match Key="HIDD_FIND_DATE">
			<htmlindex>TID_Proj_op5.html#1011631</htmlindex>
			<tiptext>Date Modified page</tiptext>
		</match>
		<match Key="HIDD_LOCK">
			<htmlindex>TID_Proj_op6.html#1011639</htmlindex>
			<tiptext>Design in Use</tiptext>
		</match>
		<match Key="HIDD_UPDATE_DEFAULTS">
			<htmlindex>TID_Proj_op7.html#1011650</htmlindex>
			<tiptext>Update from Design Defaults</tiptext>
		</match>
		<match Key="HIDD_UPDATE_DEPENDENCIES">
			<htmlindex>TID_Proj_op8.html#1011654</htmlindex>
			<tiptext>Update Dependencies</tiptext>
		</match>
		<match Key="HIDD_DEFAULTS_SHEET">
			<htmlindex>TID_Proj_op9.html#1011660</htmlindex>
			<tiptext>Design Defaults</tiptext>
		</match>
		<match Key="HIDD_DEFAULTS_SHIPDETAILS">
			<htmlindex>TID_Proj_op10.html#1011664</htmlindex>
			<tiptext>Ship Details page</tiptext>
		</match>
		<match Key="HIDD_DEFAULTS_PARTICULARS">
			<htmlindex>TID_Proj_op11.html#1011668</htmlindex>
			<tiptext>Particulars page</tiptext>
		</match>
		<match Key="HIDD_DEFAULTS_PMB">
			<htmlindex>TID_Proj_op12.html#1011672</htmlindex>
			<tiptext>Parallel Midbody page</tiptext>
		</match>
		<match Key="HIDD_DEFAULTS_AXES">
			<htmlindex>TID_Proj_op13.html#1011676</htmlindex>
			<tiptext>Axes page</tiptext>
		</match>
		<match Key="HIDD_DEFAULTS_FRAMETABLE">
			<htmlindex>TID_Proj_op14.html#1011689</htmlindex>
			<tiptext>Frame Table page</tiptext>
		</match>
		<match Key="HIDD_DEFAULTS_UNITS">
			<htmlindex>TID_Proj_op15.html#1013315</htmlindex>
			<tiptext>Units page</tiptext>
		</match>
		<match Key="HIDD_DEFAULTS_FACETER">
			<htmlindex>TID_Proj_op16.html#1013425</htmlindex>
			<tiptext>Faceter page</tiptext>
		</match>
		<match Key="HIDD_DBUTILS">
			<htmlindex>TID_Proj_op17.html#1011718</htmlindex>
			<tiptext>Initial Design Project Tool</tiptext>
		</match>
		<match Key="HIDD_EXPORT_PROJECT">
			<htmlindex>TID_Proj_op18.html#1019709</htmlindex>
			<tiptext>Export Project</tiptext>
		</match>
		<match Key="HIDD_IMPORT_PROJECT">
			<htmlindex>TID_Proj_op19.html#1019730</htmlindex>
			<tiptext>Import Project</tiptext>
		</match>
		<match Key="HIDD_NEW_PROJECT">
			<htmlindex>TID_Proj_op20.html#1019722</htmlindex>
			<tiptext>New Project</tiptext>
		</match>
		<match Key="HIDD_USER_ALIAS">
			<htmlindex>TID_Proj_op21.html#1015971</htmlindex>
			<tiptext>User Alias</tiptext>
		</match>
		<match Key="HIDD_OPEN_NEW">
			<htmlindex>TID_Proj_op22.html#1015875</htmlindex>
			<tiptext>New Design</tiptext>
		</match>
		<match Key="HIDD_OPEN_OPEN">
			<htmlindex>TID_Proj_op23.html#1011769</htmlindex>
			<tiptext>Open Design</tiptext>
		</match>
		<match Key="HIDD_OPEN_SAVEAS">
			<htmlindex>TID_Proj_op24.html#1014153</htmlindex>
			<tiptext>Save Design</tiptext>
		</match>
		<match Key="HIDD_OPEN_USE">
			<htmlindex>TID_Proj_op25.html#1011783</htmlindex>
			<tiptext>Use Design</tiptext>
		</match>
		<match Key="HIDD_PROPERTIES_SHEET">
			<htmlindex>TID_Proj_op27.html#1011794</htmlindex>
			<tiptext>Properties</tiptext>
		</match>
		<match Key="HIDD_PROPERTIES_GENERAL">
			<htmlindex>TID_Proj_op28.html#1011798</htmlindex>
			<tiptext>General page</tiptext>
		</match>
		<match Key="HIDD_PROPERTIES_TRIBON">
			<htmlindex>TID_Proj_op29.html#1011802</htmlindex>
			<tiptext>Tribon page</tiptext>
		</match>
		<match Key="HIDD_PROPERTIES_DEPENDENCIES">
			<htmlindex>TID_Proj_op30.html#1011810</htmlindex>
			<tiptext>Dependencies page</tiptext>
		</match>
		<match Key="HIDD_PROPERTIES_HISTORY">
			<htmlindex>TID_Proj_op31.html#1011818</htmlindex>
			<tiptext>History page</tiptext>
		</match>
		<match Key="HIDD_RENAME">
			<htmlindex>TID_Proj_op32.html#1010792</htmlindex>
			<tiptext>Rename Design</tiptext>
		</match>
	</book>
	<book Name="TID_Utilities.chm" ID="TIDUTIL">
		<match Key="UTILITIES_BMT2DXF">
			<htmlindex>TID_Utilities_1.html#143376</htmlindex>
			<tiptext>BMT2DXF</tiptext>
		</match>
		<match Key="UTILITIES_Keyboard User Interface">
			<htmlindex>TID_Utilities_2.html#147115</htmlindex>
			<tiptext>Keyboard User Interface</tiptext>
		</match>
		<match Key="UTILITIES_Digit">
			<htmlindex>TID_Utilities_3.html#143376</htmlindex>
			<tiptext>Digit</tiptext>
		</match>
		<match Key="UTILITIES_Plot">
			<htmlindex>TID_Utilities_4.html#142694</htmlindex>
			<tiptext>Plot</tiptext>
		</match>
		<match Key="UTILITIES_LINES2AUTOKON">
			<htmlindex>TID_Utilities_5.html#143499</htmlindex>
			<tiptext>LINES2AUTOKON</tiptext>
		</match>
		<match Key="UTILITIES_AUTOKON2LINES">
			<htmlindex>TID_Utilities_6.html#142694</htmlindex>
			<tiptext>AUTOKON2LINES</tiptext>
		</match>
		<match Key="HIDD_EDITBRITFAIR">
			<htmlindex>TID_Utilities_7.html#146235</htmlindex>
			<tiptext>Edit Britfair</tiptext>
		</match>
		<match Key="HIDD_DXFTOBF">
			<htmlindex>TID_Utilities_8.html#146235</htmlindex>
			<tiptext>DXF to Britfair</tiptext>
		</match>
	</book>
	<book Name="Toolkit_Preference.chm" ID="TOOLPREF">
	</book>
	<book Name="Weld_Planning.chm" ID="TBWELDP">
	</book>
</tribon-help>
