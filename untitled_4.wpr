#!wing
#!version=2.0
##################################################################
# Wing IDE project file                                          #
##################################################################
[project attributes]
proj.file-list = [loc('.idea/inspectionProfiles/profiles_settings.xml'),
                  loc('.idea/misc.xml'),
                  loc('.idea/modules.xml'),
                  loc('.idea/Vitesse.iml'),
                  loc('.idea/workspace.xml'),
                  loc('aadAssDataExtraction.py'),
                  loc('aadAssDrawing.py'),
                  loc('aadAssemblyModel.py'),
                  loc('aadAssExtractData.py'),
                  loc('aadAssLblPositioner.py'),
                  loc('aadAssMain.py'),
                  loc('aadAssUtil.py'),
                  loc('aadAssVec2D.py'),
                  loc('aadAssView.py'),
                  loc('aadForm.sdb'),
                  loc('AddIns/WhereUsed/DwgDex.py'),
                  loc('AddIns/WhereUsed/DwgFindDlg.py'),
                  loc('AddIns/WhereUsed/Start.py'),
                  loc('AddIns/WhereUsed/__init__.py'),
                  loc('AddIns/__init__.py'),
                  loc('Basic_Design/basic_design_menu.py'),
                  loc('Basic_Design/KcsMenuTBD01Deck.py'),
                  loc('Basic_Design/KcsMenuTBD02LBHD.py'),
                  loc('Basic_Design/KcsMenuTBD03LBHDI.py'),
                  loc('Basic_Design/KcsMenuTBD04TBHD.py'),
                  loc('Basic_Design/KcsMenuTBD05Stringer.py'),
                  loc('Basic_Design/KcsMenuTBD06Girder.py'),
                  loc('Basic_Design/KcsMenuTBD07DWeb.py'),
                  loc('Basic_Design/KcsMenuTBD08Stiffener.py'),
                  loc('Basic_Design/KcsMenuTBD09Defaults.py'),
                  loc('Basic_Design/KcsMenuTBD10KBHD.py'),
                  loc('Context_Menu/CtxButtons.bmp'),
                  loc('Context_Menu/CtxContourPropDlg.py'),
                  loc('Context_Menu/CtxDeleteDlg.py'),
                  loc('Context_Menu/CtxProjection.bmp'),
                  loc('Context_Menu/CtxSubpictPropDlg.py'),
                  loc('Context_Menu/CtxSymbolPropDlg.py'),
                  loc('Context_Menu/CtxTextPropDlg.py'),
                  loc('Context_Menu/CtxValidators.py'),
                  loc('Context_Menu/CtxViewProjDlg.py'),
                  loc('Context_Menu/ctx_menu1.py'),
                  loc('Context_Menu/ctx_menu2.py'),
                  loc('Context_Menu/ctx_menu3.py'),
                  loc('Context_Menu/ctx_menu4.py'),
                  loc('Context_Menu/ctx_menu5.py'),
                  loc('Context_Menu/ctx_menu6.py'),
                  loc('Context_Menu/ctx_menu7.py'),
                  loc('Context_Menu/ctx_menu8.py'),
                  loc('Context_Menu/ctx_menu9.py'),
                  loc('Examples/.idea/Examples.iml'),
                  loc('Examples/.idea/inspectionProfiles/profiles_settings.xml'),
                  loc('Examples/.idea/misc.xml'),
                  loc('Examples/.idea/modules.xml'),
                  loc('Examples/.idea/workspace.xml'),
                  loc('Examples/Assembly/kcs_ex_ass01.py'),
                  loc('Examples/Assembly/kcs_ex_ass02.py'),
                  loc('Examples/Cable/kcs_ex_cable_01.py'),
                  loc('Examples/Cable/kcs_ex_cable_02.py'),
                  loc('Examples/Cable/kcs_ex_cway_01.py'),
                  loc('Examples/Cable/kcs_ex_cway_02.py'),
                  loc('Examples/Cable/kcs_ex_cway_03.py'),
                  loc('Examples/Cable/kcs_ex_cway_04.py'),
                  loc('Examples/Cable/kcs_ex_cway_05.py'),
                  loc('Examples/Cable/kcs_ex_cway_06.py'),
                  loc('Examples/Cable/kcs_ex_cway_07.py'),
                  loc('Examples/Cable/kcs_ex_cway_08.py'),
                  loc('Examples/Cable/kcs_ex_cway_09_compcheck.py'),
                  loc('Examples/Cable/kcs_ex_cway_09_comprestrict.dat'),
                  loc('Examples/Cable/kcs_ex_cway_09_cwaycheck.py'),
                  loc('Examples/Cable/kcs_ex_cway_09_preferences.py'),
                  loc('Examples/Cable/kcs_ex_cway_09_rulesfile.py'),
                  loc('Examples/Customer_Def_Attr/kcs_ex_att1.py'),
                  loc('Examples/Customer_Def_Attr/kcs_ex_att2.py'),
                  loc('Examples/Customer_Def_Attr/kcs_ex_att3.py'),
                  loc('Examples/Customisable_GUI/kcs_ex_gui1.py'),
                  loc('Examples/Customisable_GUI/kcs_ex_gui2.py'),
                  loc('Examples/Customisable_GUI/kcs_ex_gui3.py'),
                  loc('Examples/Customisable_GUI/kcs_ex_gui4.py'),
                  loc('Examples/Drafting/kcs_ex_draft1.py'),
                  loc('Examples/Drafting/kcs_ex_draft10.py'),
                  loc('Examples/Drafting/kcs_ex_draft11.py'),
                  loc('Examples/Drafting/kcs_ex_draft12.py'),
                  loc('Examples/Drafting/kcs_ex_draft13.py'),
                  loc('Examples/Drafting/kcs_ex_draft14.py'),
                  loc('Examples/Drafting/kcs_ex_draft16.py'),
                  loc('Examples/Drafting/kcs_ex_draft17.py'),
                  loc('Examples/Drafting/kcs_ex_draft18.py'),
                  loc('Examples/Drafting/kcs_ex_draft19.py'),
                  loc('Examples/Drafting/kcs_ex_draft2.py'),
                  loc('Examples/Drafting/kcs_ex_draft20.py'),
                  loc('Examples/Drafting/kcs_ex_draft21.py'),
                  loc('Examples/Drafting/kcs_ex_draft22.py'),
                  loc('Examples/Drafting/kcs_ex_draft23.py'),
                  loc('Examples/Drafting/kcs_ex_draft24.py'),
                  loc('Examples/Drafting/kcs_ex_draft25.py'),
                  loc('Examples/Drafting/kcs_ex_draft26.py'),
                  loc('Examples/Drafting/kcs_ex_draft27.py'),
                  loc('Examples/Drafting/kcs_ex_draft28.py'),
                  loc('Examples/Drafting/kcs_ex_draft29.py'),
                  loc('Examples/Drafting/kcs_ex_draft3.py'),
                  loc('Examples/Drafting/kcs_ex_draft30.py'),
                  loc('Examples/Drafting/kcs_ex_draft31.py'),
                  loc('Examples/Drafting/kcs_ex_draft32.py'),
                  loc('Examples/Drafting/kcs_ex_draft33.py'),
                  loc('Examples/Drafting/kcs_ex_draft34.py'),
                  loc('Examples/Drafting/kcs_ex_draft35.py'),
                  loc('Examples/Drafting/kcs_ex_draft36.py'),
                  loc('Examples/Drafting/kcs_ex_draft37.py'),
                  loc('Examples/Drafting/kcs_ex_draft38.py'),
                  loc('Examples/Drafting/kcs_ex_draft39.py'),
                  loc('Examples/Drafting/kcs_ex_draft4.py'),
                  loc('Examples/Drafting/kcs_ex_draft40.py'),
                  loc('Examples/Drafting/kcs_ex_draft41.py'),
                  loc('Examples/Drafting/kcs_ex_draft42.py'),
                  loc('Examples/Drafting/kcs_ex_draft5.py'),
                  loc('Examples/Drafting/kcs_ex_draft6.py'),
                  loc('Examples/Drafting/kcs_ex_draft7.py'),
                  loc('Examples/Drafting/kcs_ex_draft8.py'),
                  loc('Examples/Drafting/kcs_ex_draft9.py'),
                  loc('Examples/Drafting/kcs_ex_shd01.py'),
                  loc('Examples/Equipment/kcs_ex_equip01.py'),
                  loc('Examples/Equipment/kcs_ex_equip02.py'),
                  loc('Examples/Equipment/kcs_ex_equip03.py'),
                  loc('Examples/Equipment/kcs_ex_equip04.py'),
                  loc('Examples/Hull/kcs_ex_hullcurve1.py'),
                  loc('Examples/Hull/kcs_ex_hullcurve2.py'),
                  loc('Examples/Hull/kcs_ex_hullcurved_views01.py'),
                  loc('Examples/Hull/kcs_ex_hullpan01.py'),
                  loc('Examples/Hull/kcs_ex_hullpan02.py'),
                  loc('Examples/Hull/kcs_ex_hullpan03.py'),
                  loc('Examples/Hull/kcs_ex_hullpan04.py'),
                  loc('Examples/Hull/kcs_ex_hullpan05.py'),
                  loc('Examples/Hull/kcs_ex_hull_cpanhole.py'),
                  loc('Examples/Hull/kcs_ex_hull_shellcurves.py'),
                  loc('Examples/Hull/kcs_ex_hull_shellstiff.py'),
                  loc('Examples/Hull/kcs_ex_hull_sh_stiff_prop.py'),
                  loc('Examples/Hull/kcs_ex_hull_XML.py'),
                  loc('Examples/Misc_Tools/munDefineModelUnit.py'),
                  loc('Examples/Misc_Tools/munHighlightModelUnit.py'),
                  loc('Examples/Misc_Tools/munModelUnitMenu.py'),
                  loc('Examples/Misc_Tools/munModel_Unit.py'),
                  loc('Examples/Misc_Tools/muntrig_draft_init.py'),
                  loc('Examples/Misc_Tools/munUnDefineModelUnit.py'),
                  loc('Examples/Misc_Tools/munUnHighLightModelUnit.py'),
                  loc('Examples/Model/kcs_ex_model01.py'),
                  loc('Examples/Model/kcs_ex_model03.py'),
                  loc('Examples/Model/kcs_ex_model04.py'),
                  loc('Examples/Model/kcs_ex_model05.py'),
                  loc('Examples/Model/kcs_ex_model06.py'),
                  loc('Examples/Pipe/kcs_ex_pipe01.py'),
                  loc('Examples/Pipe/kcs_ex_pipe02.py'),
                  loc('Examples/Pipe/kcs_ex_pipe03.py'),
                  loc('Examples/Pipe/kcs_ex_pipe04.py'),
                  loc('Examples/Pipe/kcs_ex_pipe05.py'),
                  loc('Examples/Pipe/kcs_ex_pipe06.py'),
                  loc('Examples/Pipe/kcs_ex_pipe07.py'),
                  loc('Examples/Pipe/kcs_ex_pipe08.py'),
                  loc('Examples/Pipe/kcs_ex_pipe09.py'),
                  loc('Examples/Pipe/kcs_ex_pipe10.py'),
                  loc('Examples/Pipe/kcs_ex_pipe11.py'),
                  loc('Examples/Pipe/kcs_ex_pipe12.py'),
                  loc('Examples/Pipe/kcs_ex_pipe13.py'),
                  loc('Examples/Pipe/kcs_ex_pipe14.py'),
                  loc('Examples/Pipe/kcs_ex_pipe15.py'),
                  loc('Examples/Pipe/kcs_ex_pipe16.py'),
                  loc('Examples/Pipe/kcs_ex_pipe17.py'),
                  loc('Examples/Pipe/kcs_ex_pipe18.py'),
                  loc('Examples/ProjectCopy/export_cway.py'),
                  loc('Examples/Structure/kcs_ex_struct1.py'),
                  loc('Examples/Structure/kcs_ex_struct2.py'),
                  loc('Examples/Structure/kcs_ex_struct3.py'),
                  loc('Examples/Structure/kcs_ex_struct4.py'),
                  loc('Examples/Structure/kcs_ex_struct_test.py'),
                  loc('Examples/Util/kcs_ex_db01.py'),
                  loc('Examples/Util/kcs_ex_model01.py'),
                  loc('Examples/Util/kcs_ex_module01.py'),
                  loc('Examples/Util/kcs_ex_ui1.py'),
                  loc('Examples/Util/kcs_ex_ui2.py'),
                  loc('Examples/Util/kcs_ex_ui3.py'),
                  loc('Examples/Util/kcs_ex_ui4.py'),
                  loc('Examples/Util/kcs_ex_ui5.py'),
                  loc('Examples/Util/kcs_ex_ui6.py'),
                  loc('Examples/Util/kcs_ex_ui7.py'),
                  loc('Examples/Util/kcs_ex_util1.py'),
                  loc('Examples/Util/kcs_ex_util2.py'),
                  loc('Examples/Util/kcs_ex_util3.py'),
                  loc('Examples/Vol/kcs_ex_vol01.py'),
                  loc('Examples/Vol/kcs_ex_vol02.py'),
                  loc('Examples/Vol/kcs_ex_vol03.py'),
                  loc('Examples/Weld/kcs_ex_weldcalc1.py'),
                  loc('Examples/\xe6\x96\xb0\xe5\xbb\xba\xe6\x96\x87\xe6\x9c\xac\xe6\x96\x87\xe6\xa1\xa3 (2).txt'),
                  loc('Examples/\xe6\x96\xb0\xe5\xbb\xba\xe6\x96\x87\xe6\x9c\xac\xe6\x96\x87\xe6\xa1\xa3.txt'),
                  loc('kcs_att_DataContainers.py'),
                  loc('kcs_att_egfdgsd.py'),
                  loc('kcs_att_NODE.py'),
                  loc('kcs_att_POATEST.py'),
                  loc('kcs_ex_model05.py'),
                  loc('kcs_ex_struct_test.py'),
                  loc('Lib/cCoordRef.py'),
                  loc('Lib/CommonSample.py'),
                  loc('Lib/cPanel.py'),
                  loc('Lib/Defaults.py'),
                  loc('Lib/KcsArc2D.py'),
                  loc('Lib/KcsArc3D.py'),
                  loc('Lib/KcsAssembly.py'),
                  loc('Lib/KcsAssemblyKeyInItem.py'),
                  loc('Lib/KcsBodyPlanViewOptions.py'),
                  loc('Lib/KcsBox.py'),
                  loc('Lib/KcsButtonState.py'),
                  loc('Lib/KcsCap.py'),
                  loc('Lib/KcsCaptureRegion2D.py'),
                  loc('Lib/KcsCircle2D.py'),
                  loc('Lib/KcsCircle3D.py'),
                  loc('Lib/KcsColour.py'),
                  loc('Lib/KcsCommonProperties.py'),
                  loc('Lib/KcsCone.py'),
                  loc('Lib/KcsConfNoteRec.py'),
                  loc('Lib/KcsConic2D.py'),
                  loc('Lib/KcsConnection.py'),
                  loc('Lib/KcsContour2D.py'),
                  loc('Lib/KcsContourOperations.py'),
                  loc('Lib/KcsCopyPanOptions.py'),
                  loc('Lib/KcsCPanDist.py'),
                  loc('Lib/KcsCursorType.py'),
                  loc('Lib/KcsDate.py'),
                  loc('Lib/KcsDateTime.py'),
                  loc('Lib/KcsDiminutionList.py'),
                  loc('Lib/KcsDocumentReference.py'),
                  loc('Lib/KcsDrawTable.py'),
                  loc('Lib/KcsElementHandle.py'),
                  loc('Lib/KcsEllipse2D.py'),
                  loc('Lib/KcsGaugedList.py'),
                  loc('Lib/KcsGencylinder.py'),
                  loc('Lib/KcsGeoContour3D.py'),
                  loc('Lib/KcsHighlightSet.py'),
                  loc('Lib/KcsInterpretationObject.py'),
                  loc('Lib/KcsLayer.py'),
                  loc('Lib/KcsLine2D.py'),
                  loc('Lib/KcsLine3D.py'),
                  loc('Lib/KcsLinetype.py'),
                  loc('Lib/KcsLineTypeDisplaySettings.py'),
                  loc('Lib/KcsModel.py'),
                  loc('Lib/KcsModelDrawAssyCriteria.py'),
                  loc('Lib/KcsModelObjectRevision.py'),
                  loc('Lib/KcsMovePanOptions.py'),
                  loc('Lib/KcsObject.py'),
                  loc('Lib/KcsObjectCriteria.py'),
                  loc('Lib/KcsPanelSchema.py'),
                  loc('Lib/KcsPanHoleOptions.py'),
                  loc('Lib/KcsPictWinExt.py'),
                  loc('Lib/KcsPipeCheck.py'),
                  loc('Lib/KcsPipeCheckSettings.py'),
                  loc('Lib/KcsPipeJointAddCriteria.py'),
                  loc('Lib/KcsPipeMaterial.py'),
                  loc('Lib/KcsPipeName.py'),
                  loc('Lib/KcsPipePartAddCriteria.py'),
                  loc('Lib/KcsPipePartProp.py'),
                  loc('Lib/KcsPipeProp.py'),
                  loc('Lib/KcsPipeRoute.py'),
                  loc('Lib/KcsPipeSpoolProp.py'),
                  loc('Lib/KcsPlane3D.py'),
                  loc('Lib/KcsPoint2D.py'),
                  loc('Lib/KcsPoint3D.py'),
                  loc('Lib/KcsPolygon2D.py'),
                  loc('Lib/KcsPolygon3D.py'),
                  loc('Lib/KcsPredictList.py'),
                  loc('Lib/KcsPrintOptions.py'),
                  loc('Lib/KcsProfSymb.py'),
                  loc('Lib/KcsProjectCopyArg.py'),
                  loc('Lib/KcsPythonUtil.py'),
                  loc('Lib/KcsRectangle2D.py'),
                  loc('Lib/KcsResultPipeCheck.py'),
                  loc('Lib/KcsResultPipeStructConn.py'),
                  loc('Lib/KcsRline2D.py'),
                  loc('Lib/KcsRline3D.py'),
                  loc('Lib/KcsRunModeOptions.py'),
                  loc('Lib/KcsShellXViewOptions.py'),
                  loc('Lib/KcsShStiffProp.py'),
                  loc('Lib/KcsSpecSearch.py'),
                  loc('Lib/KcsSplitPanOptions.py'),
                  loc('Lib/KcsStat_point2D_req.py'),
                  loc('Lib/KcsStat_point3D_req.py'),
                  loc('Lib/KcsStringlist.py'),
                  loc('Lib/KcsSymbol.py'),
                  loc('Lib/KcsSymbollist.py'),
                  loc('Lib/KcsTbBaseException.py'),
                  loc('Lib/KcsTBDVitDef.py'),
                  loc('Lib/KcsText.py'),
                  loc('Lib/KcsTime.py'),
                  loc('Lib/KcsTMList.py'),
                  loc('Lib/KcsTMResult.py'),
                  loc('Lib/KcsTorus.py'),
                  loc('Lib/KcsTransformation2D.py'),
                  loc('Lib/KcsTransformation3D.py'),
                  loc('Lib/KcsUtilDexPan.py'),
                  loc('Lib/KcsUtilPan.py'),
                  loc('Lib/KcsUtilPanSch.py'),
                  loc('Lib/KcsUtilPos.py'),
                  loc('Lib/KcsVector2D.py'),
                  loc('Lib/KcsVector3D.py'),
                  loc('Lib/KcsVolConnection.py'),
                  loc('Lib/KcsVolPrimitiveBase.py'),
                  loc('Lib/KcsVolPrimitiveBlock.py'),
                  loc('Lib/KcsVolPrimitiveGeneralCylinder.py'),
                  loc('Lib/KcsVolPrimitiveRevolution.py'),
                  loc('Lib/KcsVolPrimitiveSphericalCap.py'),
                  loc('Lib/KcsVolPrimitiveTorusSegment.py'),
                  loc('Lib/KcsVolPrimitiveTruncatedCone.py'),
                  loc('Lib/KcsWeld.py'),
                  loc('Lib/KcsWeldedJoint.py'),
                  loc('Lib/KcsWeldTable.py'),
                  loc('Lib/kcs_gui_drafting.py'),
                  loc('Lib/kcs_gui_planar_hull.py'),
                  loc('Lib/kcs_gui_structure.py'),
                  loc('Lib/pipe_support.pyz'),
                  loc('Lib/stringutils.py'),
                  loc('Lib/_TBhook_AutoPanelName.py'),
                  loc('Lib/_TBhook_ConfigNote.py'),
                  loc('Lib/_TBhook_CustBracket.py'),
                  loc('Lib/_TBhook_CustHole.py'),
                  loc('Lib/_TBhook_CustNotch.py'),
                  loc('Lib/_TBhook_CustPartName.py'),
                  loc('Lib/_TBhook_Formula.py'),
                  loc('Lib/_TBhook_PPanScheme.py'),
                  loc('Model/kcs_ex_model01.py'),
                  loc('Model/kcs_ex_model03.py'),
                  loc('Model/kcs_ex_model04.py'),
                  loc('Model/kcs_ex_model05.py'),
                  loc('Model/kcs_ex_model06.py'),
                  loc('Model/kcs_ex_modelstruct01.py'),
                  loc('test.py'),
                  loc('Trigger/.idea/inspectionProfiles/profiles_settings.xml'),
                  loc('Trigger/.idea/misc.xml'),
                  loc('Trigger/.idea/modules.xml'),
                  loc('Trigger/.idea/Trigger.iml'),
                  loc('Trigger/.idea/workspace.xml'),
                  loc('Trigger/kcs_ex_trig01.py'),
                  loc('Trigger/kcs_ex_trig_cable_cway_comp_bent.py'),
                  loc('Trigger/kcs_ex_trig_cable_cway_comp_intermediate.py'),
                  loc('Trigger/kcs_ex_trig_cable_cway_comp_straight.py'),
                  loc('Trigger/kcs_ex_trig_cable_cway_new.py'),
                  loc('Trigger/kcs_ex_trig_cable_cway_rename.py'),
                  loc('Trigger/kcs_ex_trig_draft_insert_model_filter.py'),
                  loc('Trigger/trig_ap_parts_list_create.py'),
                  loc('Trigger/trig_dm_activate_model_filter.py'),
                  loc('Trigger/trig_dm_drop_model_filter.py'),
                  loc('Trigger/trig_dm_parts_list.py'),
                  loc('Trigger/trig_draft_init.py'),
                  loc('Trigger/trig_draft_popup_menu.py'),
                  loc('Trigger/trig_tm_prediction.py'),
                  loc('untitled-2.py'),
                  loc('wingdbstub.py')]
proj.file-type = 'normal'
[user attributes]
debug.breakpoints = {loc('kcs_ex_struct_test.py'): {124: (0,
        None,
        1,
        0),
        126: (0,
              None,
              1,
              0),
        183: (0,
              None,
              1,
              0)},
                     loc('test.py'): {29: (0,
        None,
        1,
        0)}}
guimgr.overall-gui-state = {'windowing-policy': 'combined-window',
                            'windows': [{'name': 'qr8BxmfK4r2oDBZSaC0L22XFYP'\
        'DUzzFn',
        'size-state': 'maximized',
        'type': 'dock',
        'view': {'area': 'tall',
                 'current_pages': [0,
                                   1],
                 'notebook_display': 'normal',
                 'notebook_percent': 0.25,
                 'override_title': None,
                 'pagelist': [('debug-stack',
                               'tall',
                               1,
                               None),
                              ('project',
                               'tall',
                               0,
                               {'tree-state': {'tree-states': {'deep': {'col'\
        'umn-widths': [1.0],
        'expanded-nodes': [(7,)],
        'selected-nodes': [(7,
                            12)],
        'top-node': (9,)}},
        'tree-style': 'deep'}}),
                              ('source-assistant',
                               'tall',
                               2,
                               {'docstring-during-complete': 0,
                                'wrap-lines': True}),
                              ('browser',
                               'tall',
                               0,
                               None),
                              ('templating#02EFWRQK9X23',
                               'tall',
                               0,
                               {'tree-states': {u'C:\\Program Files (x86)\\Wing IDE 2.1\\scripts\\templates': [],
        u'C:\\Users\\<USER>\\AppData\\Roaming\\Wing IDE 2\\templates': []}})],
                 'primary_view_state': {'area': 'wide',
        'current_pages': [3,
                          4],
        'notebook_display': 'normal',
        'notebook_percent': 0.30000000000000004,
        'override_title': None,
        'pagelist': [('bookmarks',
                      'wide',
                      1,
                      None),
                     ('debug-breakpoints',
                      'wide',
                      0,
                      None),
                     ('debug-io',
                      'wide',
                      1,
                      None),
                     ('debug-probe',
                      'wide',
                      2,
                      None),
                     ('debug-exceptions',
                      'wide',
                      0,
                      None),
                     ('debug-modules',
                      'wide',
                      1,
                      None),
                     ('python-shell',
                      'wide',
                      2,
                      {'attrib-starts': [],
                       'first-line': 0,
                       'sel-line': 2,
                       'sel-line-start': 141,
                       'selection_end': 145,
                       'selection_start': 145}),
                     ('interactive-search',
                      'wide',
                      0,
                      None),
                     ('batch-search',
                      'wide',
                      0,
                      {'fScope': {'fFileSetName': u'All Source Files',
                                  'fLocation': None,
                                  'fRecursive': True,
                                  'fType': 'current-file'},
                       'fSearchSpec': {'fEndPos': None,
                                       'fIncludeLinenos': True,
                                       'fInterpretBackslashes': False,
                                       'fMatchCase': False,
                                       'fOmitBinary': True,
                                       'fRegexFlags': 46,
                                       'fReplaceText': '',
                                       'fReverse': False,
                                       'fSearchText': '',
                                       'fStartPos': 0,
                                       'fStyle': 'text',
                                       'fWholeWords': False,
                                       'fWrap': True},
                       'fUIOptions': {'fAutoBackground': True,
                                      'fFilePrefix': 'short-file',
                                      'fFindAfterReplace': True,
                                      'fInSelection': False,
                                      'fIncremental': True,
                                      'fReplaceOnDisk': False,
                                      'fShowFirstMatch': False,
                                      'fShowLineno': True,
                                      'fShowReplaceWidgets': False},
                       'replace-entry-expanded': False,
                       'search-entry-expanded': False}),
                     ('debug-data',
                      'wide',
                      0,
                      {}),
                     ('debug-watch',
                      'wide',
                      1,
                      None)],
        'primary_view_state': {'editor_states': {'bookmarks': ([[loc('../../../Program Files (x86)/Wing IDE 2.1/resources/doc/en/TOC.idx'),
        {'displayed-index': -1,
         'viewer-state': {'history': [-1,
                                      0],
                          'history-pos': 0,
                          'index': -1,
                          'top': (0,
                                  0)}},
        1735702880.813],
        (loc('test.py'),
         {'attrib-starts': [],
          'first-line': 5,
          'sel-line': 0,
          'sel-line-start': 0,
          'selection_end': 0,
          'selection_start': 0},
         1735703012.862),
        (loc('test.py'),
         {'attrib-starts': [],
          'first-line': 5,
          'sel-line': 21,
          'sel-line-start': 491,
          'selection_end': 503,
          'selection_start': 503},
         1735703021.3510001),
        (loc('kcs_ex_struct_test.py'),
         {'attrib-starts': [],
          'first-line': 177,
          'sel-line': 0,
          'sel-line-start': 0,
          'selection_end': 0,
          'selection_start': 0},
         1735703038.4430001),
        (loc('test.py'),
         {'attrib-starts': [],
          'first-line': 4,
          'sel-line': 26,
          'sel-line-start': 513,
          'selection_end': 513,
          'selection_start': 513},
         1735703126.6600001),
        (loc('test.py'),
         {'attrib-starts': [],
          'first-line': 4,
          'sel-line': 20,
          'sel-line-start': 489,
          'selection_end': 489,
          'selection_start': 489},
         1735703369.4660001),
        (loc('test.py'),
         {'attrib-starts': [],
          'first-line': 4,
          'sel-line': 16,
          'sel-line-start': 464,
          'selection_end': 464,
          'selection_start': 464},
         1735703547.1630001),
        (loc('test.py'),
         {'attrib-starts': [],
          'first-line': 4,
          'sel-line': 28,
          'sel-line-start': 517,
          'selection_end': 517,
          'selection_start': 517},
         1735703705.598),
        (loc('kcs_ex_struct_test.py'),
         {'attrib-starts': [('CreateRouter',
                             121)],
          'first-line': 114,
          'sel-line': 122,
          'sel-line-start': 4471,
          'selection_end': 4478,
          'selection_start': 4478},
         1735703735.819),
        (loc('Trigger/trig_draft_init.py'),
         {'attrib-starts': [],
          'first-line': 0,
          'sel-line': 15,
          'sel-line-start': 335,
          'selection_end': 354,
          'selection_start': 354},
         1735703857.776),
        (loc('kcs_ex_struct_test.py'),
         {'attrib-starts': [('CreatePoint2DReqStatus',
                             33)],
          'first-line': 0,
          'sel-line': 48,
          'sel-line-start': 1481,
          'selection_end': 1510,
          'selection_start': 1510},
         1735703982.158),
        (loc('Trigger/trig_draft_init.py'),
         {'attrib-starts': [],
          'first-line': 0,
          'sel-line': 12,
          'sel-line-start': 249,
          'selection_end': 249,
          'selection_start': 249},
         1735704013.2049999),
        (loc('test.py'),
         {'attrib-starts': [],
          'first-line': 4,
          'sel-line': 28,
          'sel-line-start': 517,
          'selection_end': 517,
          'selection_start': 517},
         1735705004.698),
        (loc('Trigger/trig_draft_init.py'),
         {'attrib-starts': [],
          'first-line': 0,
          'sel-line': 8,
          'sel-line-start': 179,
          'selection_end': 193,
          'selection_start': 193},
         1735705010.0469999),
        (loc('test.py'),
         {'attrib-starts': [],
          'first-line': 4,
          'sel-line': 18,
          'sel-line-start': 468,
          'selection_end': 485,
          'selection_start': 485},
         1735705011.825),
        [loc('kcs_ex_struct_test.py'),
         {'attrib-starts': [('CreatePoint2DReqStatus',
                             33)],
          'first-line': 0,
          'sel-line': 48,
          'sel-line-start': 1481,
          'selection_end': 1510,
          'selection_start': 1510},
         1735705075.5639999]],
        15),
        'current-loc': loc('kcs_ex_struct_test.py'),
        'editor-states': {loc('kcs_ex_struct_test.py'): {'attrib-starts': [(''\
        'CreateRouter',
        121)],
        'first-line': 142,
        'sel-line': 153,
        'sel-line-start': 5604,
        'selection_end': 5636,
        'selection_start': 5636},
                          loc('test.py'): {'attrib-starts': [],
        'first-line': 4,
        'sel-line': 18,
        'sel-line-start': 468,
        'selection_end': 485,
        'selection_start': 485},
                          loc('Trigger/trig_draft_init.py'): {'attrib-starts': [],
        'first-line': 0,
        'sel-line': 8,
        'sel-line-start': 179,
        'selection_end': 193,
        'selection_start': 193}},
        'has-focus': True},
                               'open_files': [u'test.py',
        u'Trigger/trig_draft_init.py',
        u'kcs_ex_struct_test.py']},
        'split_percents': {0: 0.5},
        'splits': 2,
        'tab_location': 'top',
        'user_data': {}},
                 'split_percents': {0: 0.5},
                 'splits': 2,
                 'tab_location': 'left',
                 'user_data': {}},
        'window-alloc': (10,
                         10,
                         1900,
                         1030)}]}
guimgr.recent-documents = [loc('kcs_ex_struct_test.py')]
proj.env-vars = {None: ('default',
                        [''])}
proj.pyexec = {None: ('custom',
                      'c:\\Python23\\python.exe')}
proj.pypath = {None: ('custom',
                      'c:\\Python23\\')}
