File: TYG_CABLE_PATH   Function: run started

[Function]	kcs_draft.highlight_off
[Parameters]	(0,)
[Return value]	None

[Function]	kcs_draft.dwg_repaint
[Parameters]	()
[Return value]	None

[Function]	kcs_ui.app_window_refresh
[Parameters]	()
[Return value]	None

[Function]	kcs_draft.default_value_get
[Parameters]	('STRUCT_NAME',)
[Return value]	STRUCT_NAME                             : MOD-NAME

[Function]	kcs_draft.default_value_set
[Parameters]	('STRUCT_NAME=NAME',)
[Return value]	None

[Function]	kcs_draft.element_child_first_get
[Parameters]	()
[Return value]	handle: 2

[Function]	kcs_draft.element_is_view
[Parameters]	(handle: 2,)
[Return value]	1

[Function]	kcs_draft.element_sibling_next_get
[Parameters]	(handle: 2,)
[Error]		kcs_NotFound

[Function]	kcs_ui.answer_req
[Parameters]	('\xc7\xeb\xc8\xb7\xc8\xcf\xcd\xa8\xb5\xc0\xb5\xc8\xbc\xb6', "\xb5\xb1\xc7\xb0\xcd\xa8\xb5\xc0\xcb\xf9\xd4\xda\xbc\xd7\xb0\xe5\xce\xaa'OD'; \xb5\xc8\xbc\xb6\xce\xaa'1'\xbc\xb6\xcd\xa8\xb5\xc0,\xca\xc7\xb7\xf1\xbc\xcc\xd0\xf8\xa3\xbf")
[Return value]	201

[Function]	kcs_util.yes
[Parameters]	()
[Return value]	201

[Function]	kcs_ui.message_noconfirm
[Parameters]	('\xb4\xb4\xbd\xa8\xc4\xa3\xca\xbd : \xd7\xd4\xb6\xaf',)
[Return value]	None

[Function]	kcs_ui.point2D_req
[Parameters]	('\xd1\xa1\xd4\xf1\xb5\xe7\xd7\xb0\xbc\xfe', [X Y:0.0,0.0], Definition mode: ModeCursor       Cursor type: CrossHair, ButtonState:
		   Lock: Disabled
		   Checked lock: None
		   Options: Enabled)
[Return value]	(271, [X Y:145.05682373,173.415649414])

[Function]	kcs_util.ok
[Parameters]	()
[Return value]	271

[Function]	kcs_draft.model_identify
[Parameters]	([X Y:145.05682373,173.415649414], Model:
		   Type: struct
		   Name: 
		   PartType: 
		   PartId: 0
		   SubPartType: 
		   SubPartId: 0
		   ReflCode: 0)
[Return value]	(Model:
		   Type: struct
		   Name: EBFM-FB25-100/017
		   PartType: part
		   PartId: 1
		   SubPartType: 
		   SubPartId: 0
		   ReflCode: 0, handle: 1558, handle: 1561)

[Function]	kcs_dex.extract
[Parameters]	("STR.ITEM('EBFM-FB25-100/017').GRO(1).PART_ID(1).COMP_N",)
[Return value]	0

[Function]	kcs_util.success
[Parameters]	()
[Return value]	0

[Function]	kcs_dex.next_result
[Parameters]	()
[Return value]	3

[Function]	kcs_dex.get_string
[Parameters]	()
[Return value]	TYG_E-FB25-100

[Function]	kcs_dex.next_result
[Parameters]	()
[Return value]	-1

[Function]	kcs_ui.message_noconfirm
[Parameters]	('STRUCT: EBFM-FB25-100/017, COMP: TYG_E-FB25-100',)
[Return value]	None

[Function]	kcs_ui.point2D_req
[Parameters]	('\xd1\xa1\xd4\xf1\xb5\xe7\xd7\xb0\xbc\xfe', [X Y:0.0,0.0], Definition mode: ModeCursor       Cursor type: CrossHair, ButtonState:
		   Lock: Disabled
		   Checked lock: None
		   Options: Enabled)
[Return value]	(271, [X Y:145.05682373,172.360427856])

[Function]	kcs_util.ok
[Parameters]	()
[Return value]	271

[Function]	kcs_draft.model_identify
[Parameters]	([X Y:145.05682373,172.360427856], Model:
		   Type: struct
		   Name: 
		   PartType: 
		   PartId: 0
		   SubPartType: 
		   SubPartId: 0
		   ReflCode: 0)
[Return value]	(Model:
		   Type: struct
		   Name: ECA_N_OD58
		   PartType: part
		   PartId: 1
		   SubPartType: 
		   SubPartId: 0
		   ReflCode: 0, handle: 65931, handle: 65934)

[Function]	kcs_dex.extract
[Parameters]	("STR.ITEM('ECA_N_OD58').GRO(1).PART_ID(1).COMP_N",)
[Return value]	0

[Function]	kcs_util.success
[Parameters]	()
[Return value]	0

[Function]	kcs_dex.next_result
[Parameters]	()
[Return value]	3

[Function]	kcs_dex.get_string
[Parameters]	()
[Return value]	SQU#125

[Function]	kcs_dex.next_result
[Parameters]	()
[Return value]	-1

[Function]	kcs_ui.message_noconfirm
[Parameters]	('STRUCT: ECA_N_OD58, COMP: SQU#125',)
[Return value]	None

[Function]	kcs_dex.extract
[Parameters]	("STR.ITEM('ECA_N_OD58').GRO(1).PART_ID(1).COG",)
[Return value]	0

[Function]	kcs_util.success
[Parameters]	()
[Return value]	0

[Function]	kcs_dex.next_result
[Parameters]	()
[Return value]	4

[Function]	kcs_dex.get_reavec3d
[Parameters]	()
[Return value]	(183479.87807562543, -610.27724868407608, 28293.987347526865)

[Function]	kcs_dex.next_result
[Parameters]	()
[Return value]	-1

[Function]	kcs_draft.colour_get
[Parameters]	(ColourString: Green
		,)
[Return value]	ColourString: Black
		

[Function]	kcs_ic.linetypes_get
[Parameters]	()
[Return value]	{'SolidWide': 'SolidWide', 'System23': 'System23', 'System22': 'System22', 'System25': 'System25', 'System24': 'System24', 'System27': 'System27', 'System26': 'System26', 'Dashed': 'Dashed', 'DashedDottedXWide': 'DashedDottedXWide', 'ShortDashedWide': 'ShortDashedWide', 'DashedDoubleDotted': 'DashedDoubleDotted', 'Track': 'Track', 'ShortDashed': 'ShortDashed', 'DashedDoubleDottedWide': 'DashedDoubleDottedWide', 'DashedAndSolid': 'DashedAndSolid', 'Solid': 'Solid', 'DashedDoubleDottedXWide': 'DashedDoubleDottedXWide', 'DashedXWide': 'DashedXWide', 'SolidXWide': 'SolidXWide', 'System15': 'System15', 'DashedDottedWide': 'DashedDottedWide', 'System6': 'System6', 'System7': 'System7', 'System5': 'System5', 'System16': 'System16', 'System8': 'System8', 'System9': 'System9', 'DashedDotted': 'DashedDotted', 'DashedWide': 'DashedWide', 'ShortDashedXWide': 'ShortDashedXWide'}

[Function]	kcs_ic.linetypes_get
[Parameters]	()
[Return value]	{'SolidWide': 'SolidWide', 'System23': 'System23', 'System22': 'System22', 'System25': 'System25', 'System24': 'System24', 'System27': 'System27', 'System26': 'System26', 'Dashed': 'Dashed', 'DashedDottedXWide': 'DashedDottedXWide', 'ShortDashedWide': 'ShortDashedWide', 'DashedDoubleDotted': 'DashedDoubleDotted', 'Track': 'Track', 'ShortDashed': 'ShortDashed', 'DashedDoubleDottedWide': 'DashedDoubleDottedWide', 'DashedAndSolid': 'DashedAndSolid', 'Solid': 'Solid', 'DashedDoubleDottedXWide': 'DashedDoubleDottedXWide', 'DashedXWide': 'DashedXWide', 'SolidXWide': 'SolidXWide', 'System15': 'System15', 'DashedDottedWide': 'DashedDottedWide', 'System6': 'System6', 'System7': 'System7', 'System5': 'System5', 'System16': 'System16', 'System8': 'System8', 'System9': 'System9', 'DashedDotted': 'DashedDotted', 'DashedWide': 'DashedWide', 'ShortDashedXWide': 'ShortDashedXWide'}

[Function]	kcs_draft.linetype_get
[Parameters]	(LinetypeString: Solid
		,)

[Function]	kcs_ic.linetypes_get
[Parameters]	()
[Return value]	{'SolidWide': 'SolidWide', 'System23': 'System23', 'System22': 'System22', 'System25': 'System25', 'System24': 'System24', 'System27': 'System27', 'System26': 'System26', 'Dashed': 'Dashed', 'DashedDottedXWide': 'DashedDottedXWide', 'ShortDashedWide': 'ShortDashedWide', 'DashedDoubleDotted': 'DashedDoubleDotted', 'Track': 'Track', 'ShortDashed': 'ShortDashed', 'DashedDoubleDottedWide': 'DashedDoubleDottedWide', 'DashedAndSolid': 'DashedAndSolid', 'Solid': 'Solid', 'DashedDoubleDottedXWide': 'DashedDoubleDottedXWide', 'DashedXWide': 'DashedXWide', 'SolidXWide': 'SolidXWide', 'System15': 'System15', 'DashedDottedWide': 'DashedDottedWide', 'System6': 'System6', 'System7': 'System7', 'System5': 'System5', 'System16': 'System16', 'System8': 'System8', 'System9': 'System9', 'DashedDotted': 'DashedDotted', 'DashedWide': 'DashedWide', 'ShortDashedXWide': 'ShortDashedXWide'}
[Return value]	LinetypeString: Solid
		

[Function]	kcs_draft.colour_set
[Parameters]	(ColourString: Red
		,)
[Return value]	None

[Function]	kcs_ic.linetypes_get
[Parameters]	()
[Return value]	{'SolidWide': 'SolidWide', 'System23': 'System23', 'System22': 'System22', 'System25': 'System25', 'System24': 'System24', 'System27': 'System27', 'System26': 'System26', 'Dashed': 'Dashed', 'DashedDottedXWide': 'DashedDottedXWide', 'ShortDashedWide': 'ShortDashedWide', 'DashedDoubleDotted': 'DashedDoubleDotted', 'Track': 'Track', 'ShortDashed': 'ShortDashed', 'DashedDoubleDottedWide': 'DashedDoubleDottedWide', 'DashedAndSolid': 'DashedAndSolid', 'Solid': 'Solid', 'DashedDoubleDottedXWide': 'DashedDoubleDottedXWide', 'DashedXWide': 'DashedXWide', 'SolidXWide': 'SolidXWide', 'System15': 'System15', 'DashedDottedWide': 'DashedDottedWide', 'System6': 'System6', 'System7': 'System7', 'System5': 'System5', 'System16': 'System16', 'System8': 'System8', 'System9': 'System9', 'DashedDotted': 'DashedDotted', 'DashedWide': 'DashedWide', 'ShortDashedXWide': 'ShortDashedXWide'}

[Function]	kcs_ic.linetypes_get
[Parameters]	()
[Return value]	{'SolidWide': 'SolidWide', 'System23': 'System23', 'System22': 'System22', 'System25': 'System25', 'System24': 'System24', 'System27': 'System27', 'System26': 'System26', 'Dashed': 'Dashed', 'DashedDottedXWide': 'DashedDottedXWide', 'ShortDashedWide': 'ShortDashedWide', 'DashedDoubleDotted': 'DashedDoubleDotted', 'Track': 'Track', 'ShortDashed': 'ShortDashed', 'DashedDoubleDottedWide': 'DashedDoubleDottedWide', 'DashedAndSolid': 'DashedAndSolid', 'Solid': 'Solid', 'DashedDoubleDottedXWide': 'DashedDoubleDottedXWide', 'DashedXWide': 'DashedXWide', 'SolidXWide': 'SolidXWide', 'System15': 'System15', 'DashedDottedWide': 'DashedDottedWide', 'System6': 'System6', 'System7': 'System7', 'System5': 'System5', 'System16': 'System16', 'System8': 'System8', 'System9': 'System9', 'DashedDotted': 'DashedDotted', 'DashedWide': 'DashedWide', 'ShortDashedXWide': 'ShortDashedXWide'}

[Function]	kcs_draft.linetype_set
[Parameters]	(LinetypeString: SolidXWide
		,)
[Return value]	None

[Function]	kcs_draft.subpicture_current_set
[Parameters]	(handle: 2,)
[Return value]	None

[Function]	kcs_draft.point_transform
[Parameters]	(handle: 2, [X Y Z:183479.878076,-610.277248684,28293.9873475], [X Y:0.0,0.0])
[Return value]	[X Y:144.785736084,173.599685669]

[Function]	kcs_draft.element_transformation_get
[Parameters]	(handle: 2, Transformation2D : 1.0 0.0 0.0 0.0 1.0 0.0 0.0 0.0 1.0)
[Return value]	Transformation2D : 0.019999999553 0.0 0.0 0.0 0.019999999553 0.0 -2458.64244505 -1781.56324143 1.0

[Function]	kcs_ic.transf_decompose_2d
[Parameters]	(Transformation2D : 0.019999999553 0.0 0.0 0.0 0.019999999553 0.0 -2458.64244505 -1781.56324143 1.0,)
[Return value]	(1, (1.0, -0.0, -122932.125, -89078.1640625, 0.0, 0.019999999552965164, 0.019999999552965164))

[Function]	kcs_draft.line_new
[Parameters]	(Line Start: [X Y:144.0787361,174.306685653] End: [X Y:145.492736068,172.892685685],)
[Return value]	handle: 64553

[Function]	kcs_draft.line_new
[Parameters]	(Line Start: [X Y:144.0787361,172.892685685] End: [X Y:145.492736068,174.306685653],)
[Return value]	handle: 64640

[Function]	kcs_draft.colour_set
[Parameters]	(ColourString: Black
		,)
[Return value]	None

[Function]	kcs_draft.linetype_set
[Parameters]	(LinetypeString: Solid
		,)
[Return value]	None

[Function]	kcs_ui.point2D_req
[Parameters]	('\xd1\xa1\xd4\xf1\xb5\xe7\xd7\xb0\xbc\xfe', [X Y:0.0,0.0], Definition mode: ModeCursor       Cursor type: CrossHair, ButtonState:
		   Lock: Disabled
		   Checked lock: None
		   Options: Enabled)
[Return value]	(271, [X Y:161.148986816,164.71005249])

[Function]	kcs_util.ok
[Parameters]	()
[Return value]	271

[Function]	kcs_draft.model_identify
[Parameters]	([X Y:161.148986816,164.71005249], Model:
		   Type: struct
		   Name: 
		   PartType: 
		   PartId: 0
		   SubPartType: 
		   SubPartId: 0
		   ReflCode: 0)
[Return value]	(Model:
		   Type: struct
		   Name: EBFM-FB25-100/018
		   PartType: part
		   PartId: 1
		   SubPartType: 
		   SubPartId: 0
		   ReflCode: 0, handle: 1525, handle: 1528)

[Function]	kcs_dex.extract
[Parameters]	("STR.ITEM('EBFM-FB25-100/018').GRO(1).PART_ID(1).COMP_N",)
[Return value]	0

[Function]	kcs_util.success
[Parameters]	()
[Return value]	0

[Function]	kcs_dex.next_result
[Parameters]	()
[Return value]	3

[Function]	kcs_dex.get_string
[Parameters]	()
[Return value]	TYG_E-FB25-100

[Function]	kcs_dex.next_result
[Parameters]	()
[Return value]	-1

[Function]	kcs_ui.message_noconfirm
[Parameters]	('STRUCT: EBFM-FB25-100/018, COMP: TYG_E-FB25-100',)
[Return value]	None

[Function]	kcs_dex.extract
[Parameters]	("STR.ITEM('EBFM-FB25-100/018').GRO(1).PART_ID(1).TYPE",)
[Return value]	0

[Function]	kcs_util.success
[Parameters]	()
[Return value]	0

[Function]	kcs_dex.next_result
[Parameters]	()
[Return value]	3

[Function]	kcs_dex.get_string
[Parameters]	()
[Return value]	Component

[Function]	kcs_dex.next_result
[Parameters]	()
[Return value]	-1

[Function]	kcs_dex.extract
[Parameters]	("STR.ITEM('EBFM-FB25-100/018').GRO(1).PART_ID(1).ROT",)
[Return value]	0

[Function]	kcs_util.success
[Parameters]	()
[Return value]	0

[Function]	kcs_dex.next_result
[Parameters]	()
[Return value]	4

[Function]	kcs_dex.get_reavec3d
[Parameters]	()
[Return value]	(-2.7111702365800738e-005, 1.0, 1.0057678991870489e-006)

[Function]	kcs_dex.next_result
[Parameters]	()
[Return value]	-1

[Function]	kcs_dex.extract
[Parameters]	("STR.ITEM('EBFM-FB25-100/018').GRO(1).PART_ID(1).ROU",)
[Return value]	0

[Function]	kcs_util.success
[Parameters]	()
[Return value]	0

[Function]	kcs_dex.next_result
[Parameters]	()
[Return value]	4

[Function]	kcs_dex.get_reavec3d
[Parameters]	()
[Return value]	(-0.00145437300670892, 9.6751841738296207e-007, -0.99999898672103882)

[Function]	kcs_dex.next_result
[Parameters]	()
[Return value]	-1

[Function]	kcs_dex.extract
[Parameters]	("STR.ITEM('EBFM-FB25-100/018').GRO(1).PART_ID(1).POI",)
[Return value]	0

[Function]	kcs_util.success
[Parameters]	()
[Return value]	0

[Function]	kcs_dex.next_result
[Parameters]	()
[Return value]	4

[Function]	kcs_dex.get_reavec3d
[Parameters]	()
[Return value]	(183480.04589620294, -1513.8102630791896, 28393.986986771288)

[Function]	kcs_dex.next_result
[Parameters]	()
[Return value]	-1

[Function]	kcs_draft.element_highlight
[Parameters]	(handle: 1528,)
[Return value]	5

[Function]	kcs_draft.colour_get
[Parameters]	(ColourString: Green
		,)
[Return value]	ColourString: Black
		

[Function]	kcs_ic.linetypes_get
[Parameters]	()
[Return value]	{'SolidWide': 'SolidWide', 'System23': 'System23', 'System22': 'System22', 'System25': 'System25', 'System24': 'System24', 'System27': 'System27', 'System26': 'System26', 'Dashed': 'Dashed', 'DashedDottedXWide': 'DashedDottedXWide', 'ShortDashedWide': 'ShortDashedWide', 'DashedDoubleDotted': 'DashedDoubleDotted', 'Track': 'Track', 'ShortDashed': 'ShortDashed', 'DashedDoubleDottedWide': 'DashedDoubleDottedWide', 'DashedAndSolid': 'DashedAndSolid', 'Solid': 'Solid', 'DashedDoubleDottedXWide': 'DashedDoubleDottedXWide', 'DashedXWide': 'DashedXWide', 'SolidXWide': 'SolidXWide', 'System15': 'System15', 'DashedDottedWide': 'DashedDottedWide', 'System6': 'System6', 'System7': 'System7', 'System5': 'System5', 'System16': 'System16', 'System8': 'System8', 'System9': 'System9', 'DashedDotted': 'DashedDotted', 'DashedWide': 'DashedWide', 'ShortDashedXWide': 'ShortDashedXWide'}

[Function]	kcs_ic.linetypes_get
[Parameters]	()
[Return value]	{'SolidWide': 'SolidWide', 'System23': 'System23', 'System22': 'System22', 'System25': 'System25', 'System24': 'System24', 'System27': 'System27', 'System26': 'System26', 'Dashed': 'Dashed', 'DashedDottedXWide': 'DashedDottedXWide', 'ShortDashedWide': 'ShortDashedWide', 'DashedDoubleDotted': 'DashedDoubleDotted', 'Track': 'Track', 'ShortDashed': 'ShortDashed', 'DashedDoubleDottedWide': 'DashedDoubleDottedWide', 'DashedAndSolid': 'DashedAndSolid', 'Solid': 'Solid', 'DashedDoubleDottedXWide': 'DashedDoubleDottedXWide', 'DashedXWide': 'DashedXWide', 'SolidXWide': 'SolidXWide', 'System15': 'System15', 'DashedDottedWide': 'DashedDottedWide', 'System6': 'System6', 'System7': 'System7', 'System5': 'System5', 'System16': 'System16', 'System8': 'System8', 'System9': 'System9', 'DashedDotted': 'DashedDotted', 'DashedWide': 'DashedWide', 'ShortDashedXWide': 'ShortDashedXWide'}

[Function]	kcs_draft.linetype_get
[Parameters]	(LinetypeString: Solid
		,)

[Function]	kcs_ic.linetypes_get
[Parameters]	()
[Return value]	{'SolidWide': 'SolidWide', 'System23': 'System23', 'System22': 'System22', 'System25': 'System25', 'System24': 'System24', 'System27': 'System27', 'System26': 'System26', 'Dashed': 'Dashed', 'DashedDottedXWide': 'DashedDottedXWide', 'ShortDashedWide': 'ShortDashedWide', 'DashedDoubleDotted': 'DashedDoubleDotted', 'Track': 'Track', 'ShortDashed': 'ShortDashed', 'DashedDoubleDottedWide': 'DashedDoubleDottedWide', 'DashedAndSolid': 'DashedAndSolid', 'Solid': 'Solid', 'DashedDoubleDottedXWide': 'DashedDoubleDottedXWide', 'DashedXWide': 'DashedXWide', 'SolidXWide': 'SolidXWide', 'System15': 'System15', 'DashedDottedWide': 'DashedDottedWide', 'System6': 'System6', 'System7': 'System7', 'System5': 'System5', 'System16': 'System16', 'System8': 'System8', 'System9': 'System9', 'DashedDotted': 'DashedDotted', 'DashedWide': 'DashedWide', 'ShortDashedXWide': 'ShortDashedXWide'}
[Return value]	LinetypeString: Solid
		

[Function]	kcs_draft.colour_set
[Parameters]	(ColourString: Red
		,)
[Return value]	None

[Function]	kcs_ic.linetypes_get
[Parameters]	()
[Return value]	{'SolidWide': 'SolidWide', 'System23': 'System23', 'System22': 'System22', 'System25': 'System25', 'System24': 'System24', 'System27': 'System27', 'System26': 'System26', 'Dashed': 'Dashed', 'DashedDottedXWide': 'DashedDottedXWide', 'ShortDashedWide': 'ShortDashedWide', 'DashedDoubleDotted': 'DashedDoubleDotted', 'Track': 'Track', 'ShortDashed': 'ShortDashed', 'DashedDoubleDottedWide': 'DashedDoubleDottedWide', 'DashedAndSolid': 'DashedAndSolid', 'Solid': 'Solid', 'DashedDoubleDottedXWide': 'DashedDoubleDottedXWide', 'DashedXWide': 'DashedXWide', 'SolidXWide': 'SolidXWide', 'System15': 'System15', 'DashedDottedWide': 'DashedDottedWide', 'System6': 'System6', 'System7': 'System7', 'System5': 'System5', 'System16': 'System16', 'System8': 'System8', 'System9': 'System9', 'DashedDotted': 'DashedDotted', 'DashedWide': 'DashedWide', 'ShortDashedXWide': 'ShortDashedXWide'}

[Function]	kcs_ic.linetypes_get
[Parameters]	()
[Return value]	{'SolidWide': 'SolidWide', 'System23': 'System23', 'System22': 'System22', 'System25': 'System25', 'System24': 'System24', 'System27': 'System27', 'System26': 'System26', 'Dashed': 'Dashed', 'DashedDottedXWide': 'DashedDottedXWide', 'ShortDashedWide': 'ShortDashedWide', 'DashedDoubleDotted': 'DashedDoubleDotted', 'Track': 'Track', 'ShortDashed': 'ShortDashed', 'DashedDoubleDottedWide': 'DashedDoubleDottedWide', 'DashedAndSolid': 'DashedAndSolid', 'Solid': 'Solid', 'DashedDoubleDottedXWide': 'DashedDoubleDottedXWide', 'DashedXWide': 'DashedXWide', 'SolidXWide': 'SolidXWide', 'System15': 'System15', 'DashedDottedWide': 'DashedDottedWide', 'System6': 'System6', 'System7': 'System7', 'System5': 'System5', 'System16': 'System16', 'System8': 'System8', 'System9': 'System9', 'DashedDotted': 'DashedDotted', 'DashedWide': 'DashedWide', 'ShortDashedXWide': 'ShortDashedXWide'}

[Function]	kcs_draft.linetype_set
[Parameters]	(LinetypeString: SolidXWide
		,)
[Return value]	None

[Function]	kcs_draft.subpicture_current_set
[Parameters]	(handle: 2,)
[Return value]	None

[Function]	kcs_draft.point_transform
[Parameters]	(handle: 2, [X Y Z:183479.893681,-1263.81016654,28293.987344], [X Y:0.0,0.0])
[Return value]	[X Y:154.02822876,168.263748169]

[Function]	kcs_draft.point_transform
[Parameters]	(handle: 2, [X Y Z:183479.878076,-610.277248684,28293.9873475], [X Y:0.0,0.0])
[Return value]	[X Y:144.785736084,173.599685669]

[Function]	kcs_draft.line_new
[Parameters]	(Line Start: [X Y:154.02822876,168.263748169] End: [X Y:144.785736084,173.599685669],)
[Return value]	handle: 64293

[Function]	kcs_draft.element_transformation_get
[Parameters]	(handle: 2, Transformation2D : 1.0 0.0 0.0 0.0 1.0 0.0 0.0 0.0 1.0)
[Return value]	Transformation2D : 0.019999999553 0.0 0.0 0.0 0.019999999553 0.0 -2458.64244505 -1781.56324143 1.0

[Function]	kcs_ic.transf_decompose_2d
[Parameters]	(Transformation2D : 0.019999999553 0.0 0.0 0.0 0.019999999553 0.0 -2458.64244505 -1781.56324143 1.0,)
[Return value]	(1, (1.0, -0.0, -122932.125, -89078.1640625, 0.0, 0.019999999552965164, 0.019999999552965164))

[Function]	kcs_draft.line_new
[Parameters]	(Line Start: [X Y:153.321228776,168.970748153] End: [X Y:154.735228744,167.556748185],)
[Return value]	handle: 64466

[Function]	kcs_draft.line_new
[Parameters]	(Line Start: [X Y:153.321228776,167.556748185] End: [X Y:154.735228744,168.970748153],)
[Return value]	handle: 64379

[Function]	kcs_draft.colour_set
[Parameters]	(ColourString: Black
		,)
[Return value]	None

[Function]	kcs_draft.linetype_set
[Parameters]	(LinetypeString: Solid
		,)
[Return value]	None

[Function]	kcs_draft.colour_get
[Parameters]	(ColourString: Green
		,)
[Return value]	ColourString: Black
		

[Function]	kcs_ic.linetypes_get
[Parameters]	()
[Return value]	{'SolidWide': 'SolidWide', 'System23': 'System23', 'System22': 'System22', 'System25': 'System25', 'System24': 'System24', 'System27': 'System27', 'System26': 'System26', 'Dashed': 'Dashed', 'DashedDottedXWide': 'DashedDottedXWide', 'ShortDashedWide': 'ShortDashedWide', 'DashedDoubleDotted': 'DashedDoubleDotted', 'Track': 'Track', 'ShortDashed': 'ShortDashed', 'DashedDoubleDottedWide': 'DashedDoubleDottedWide', 'DashedAndSolid': 'DashedAndSolid', 'Solid': 'Solid', 'DashedDoubleDottedXWide': 'DashedDoubleDottedXWide', 'DashedXWide': 'DashedXWide', 'SolidXWide': 'SolidXWide', 'System15': 'System15', 'DashedDottedWide': 'DashedDottedWide', 'System6': 'System6', 'System7': 'System7', 'System5': 'System5', 'System16': 'System16', 'System8': 'System8', 'System9': 'System9', 'DashedDotted': 'DashedDotted', 'DashedWide': 'DashedWide', 'ShortDashedXWide': 'ShortDashedXWide'}

[Function]	kcs_ic.linetypes_get
[Parameters]	()
[Return value]	{'SolidWide': 'SolidWide', 'System23': 'System23', 'System22': 'System22', 'System25': 'System25', 'System24': 'System24', 'System27': 'System27', 'System26': 'System26', 'Dashed': 'Dashed', 'DashedDottedXWide': 'DashedDottedXWide', 'ShortDashedWide': 'ShortDashedWide', 'DashedDoubleDotted': 'DashedDoubleDotted', 'Track': 'Track', 'ShortDashed': 'ShortDashed', 'DashedDoubleDottedWide': 'DashedDoubleDottedWide', 'DashedAndSolid': 'DashedAndSolid', 'Solid': 'Solid', 'DashedDoubleDottedXWide': 'DashedDoubleDottedXWide', 'DashedXWide': 'DashedXWide', 'SolidXWide': 'SolidXWide', 'System15': 'System15', 'DashedDottedWide': 'DashedDottedWide', 'System6': 'System6', 'System7': 'System7', 'System5': 'System5', 'System16': 'System16', 'System8': 'System8', 'System9': 'System9', 'DashedDotted': 'DashedDotted', 'DashedWide': 'DashedWide', 'ShortDashedXWide': 'ShortDashedXWide'}

[Function]	kcs_draft.linetype_get
[Parameters]	(LinetypeString: Solid
		,)

[Function]	kcs_ic.linetypes_get
[Parameters]	()
[Return value]	{'SolidWide': 'SolidWide', 'System23': 'System23', 'System22': 'System22', 'System25': 'System25', 'System24': 'System24', 'System27': 'System27', 'System26': 'System26', 'Dashed': 'Dashed', 'DashedDottedXWide': 'DashedDottedXWide', 'ShortDashedWide': 'ShortDashedWide', 'DashedDoubleDotted': 'DashedDoubleDotted', 'Track': 'Track', 'ShortDashed': 'ShortDashed', 'DashedDoubleDottedWide': 'DashedDoubleDottedWide', 'DashedAndSolid': 'DashedAndSolid', 'Solid': 'Solid', 'DashedDoubleDottedXWide': 'DashedDoubleDottedXWide', 'DashedXWide': 'DashedXWide', 'SolidXWide': 'SolidXWide', 'System15': 'System15', 'DashedDottedWide': 'DashedDottedWide', 'System6': 'System6', 'System7': 'System7', 'System5': 'System5', 'System16': 'System16', 'System8': 'System8', 'System9': 'System9', 'DashedDotted': 'DashedDotted', 'DashedWide': 'DashedWide', 'ShortDashedXWide': 'ShortDashedXWide'}
[Return value]	LinetypeString: Solid
		

[Function]	kcs_draft.colour_set
[Parameters]	(ColourString: Red
		,)
[Return value]	None

[Function]	kcs_ic.linetypes_get
[Parameters]	()
[Return value]	{'SolidWide': 'SolidWide', 'System23': 'System23', 'System22': 'System22', 'System25': 'System25', 'System24': 'System24', 'System27': 'System27', 'System26': 'System26', 'Dashed': 'Dashed', 'DashedDottedXWide': 'DashedDottedXWide', 'ShortDashedWide': 'ShortDashedWide', 'DashedDoubleDotted': 'DashedDoubleDotted', 'Track': 'Track', 'ShortDashed': 'ShortDashed', 'DashedDoubleDottedWide': 'DashedDoubleDottedWide', 'DashedAndSolid': 'DashedAndSolid', 'Solid': 'Solid', 'DashedDoubleDottedXWide': 'DashedDoubleDottedXWide', 'DashedXWide': 'DashedXWide', 'SolidXWide': 'SolidXWide', 'System15': 'System15', 'DashedDottedWide': 'DashedDottedWide', 'System6': 'System6', 'System7': 'System7', 'System5': 'System5', 'System16': 'System16', 'System8': 'System8', 'System9': 'System9', 'DashedDotted': 'DashedDotted', 'DashedWide': 'DashedWide', 'ShortDashedXWide': 'ShortDashedXWide'}

[Function]	kcs_ic.linetypes_get
[Parameters]	()
[Return value]	{'SolidWide': 'SolidWide', 'System23': 'System23', 'System22': 'System22', 'System25': 'System25', 'System24': 'System24', 'System27': 'System27', 'System26': 'System26', 'Dashed': 'Dashed', 'DashedDottedXWide': 'DashedDottedXWide', 'ShortDashedWide': 'ShortDashedWide', 'DashedDoubleDotted': 'DashedDoubleDotted', 'Track': 'Track', 'ShortDashed': 'ShortDashed', 'DashedDoubleDottedWide': 'DashedDoubleDottedWide', 'DashedAndSolid': 'DashedAndSolid', 'Solid': 'Solid', 'DashedDoubleDottedXWide': 'DashedDoubleDottedXWide', 'DashedXWide': 'DashedXWide', 'SolidXWide': 'SolidXWide', 'System15': 'System15', 'DashedDottedWide': 'DashedDottedWide', 'System6': 'System6', 'System7': 'System7', 'System5': 'System5', 'System16': 'System16', 'System8': 'System8', 'System9': 'System9', 'DashedDotted': 'DashedDotted', 'DashedWide': 'DashedWide', 'ShortDashedXWide': 'ShortDashedXWide'}

[Function]	kcs_draft.linetype_set
[Parameters]	(LinetypeString: SolidXWide
		,)
[Return value]	None

[Function]	kcs_draft.subpicture_current_set
[Parameters]	(handle: 2,)
[Return value]	None

[Function]	kcs_draft.point_transform
[Parameters]	(handle: 2, [X Y Z:183479.907237,-1763.81016635,28293.9868411], [X Y:0.0,0.0])
[Return value]	[X Y:161.099487305,164.181411743]

[Function]	kcs_draft.point_transform
[Parameters]	(handle: 2, [X Y Z:183479.893681,-1263.81016654,28293.987344], [X Y:0.0,0.0])
[Return value]	[X Y:154.02822876,168.263748169]

[Function]	kcs_draft.line_new
[Parameters]	(Line Start: [X Y:161.099487305,164.181411743] End: [X Y:154.02822876,168.263748169],)
[Return value]	handle: 64003

[Function]	kcs_draft.element_transformation_get
[Parameters]	(handle: 2, Transformation2D : 1.0 0.0 0.0 0.0 1.0 0.0 0.0 0.0 1.0)
[Return value]	Transformation2D : 0.019999999553 0.0 0.0 0.0 0.019999999553 0.0 -2458.64244505 -1781.56324143 1.0

[Function]	kcs_ic.transf_decompose_2d
[Parameters]	(Transformation2D : 0.019999999553 0.0 0.0 0.0 0.019999999553 0.0 -2458.64244505 -1781.56324143 1.0,)
[Return value]	(1, (1.0, -0.0, -122932.125, -89078.1640625, 0.0, 0.019999999552965164, 0.019999999552965164))

[Function]	kcs_draft.line_new
[Parameters]	(Line Start: [X Y:160.39248732,164.888411727] End: [X Y:161.806487289,163.474411759],)
[Return value]	handle: 63829

[Function]	kcs_draft.line_new
[Parameters]	(Line Start: [X Y:160.39248732,163.474411759] End: [X Y:161.806487289,164.888411727],)
[Return value]	handle: 63916

[Function]	kcs_draft.colour_set
[Parameters]	(ColourString: Black
		,)
[Return value]	None

[Function]	kcs_draft.linetype_set
[Parameters]	(LinetypeString: Solid
		,)
[Return value]	None

[Function]	kcs_draft.highlight_off
[Parameters]	(5,)
[Return value]	None

[Function]	kcs_ui.point2D_req
[Parameters]	('\xd1\xa1\xd4\xf1\xb5\xe7\xd7\xb0\xbc\xfe', [X Y:0.0,0.0], Definition mode: ModeCursor       Cursor type: CrossHair, ButtonState:
		   Lock: Disabled
		   Checked lock: None
		   Options: Enabled)
[Return value]	(254, [X Y:0.0,0.0])

[Function]	kcs_util.ok
[Parameters]	()
[Return value]	271

[Function]	kcs_util.operation_complete
[Parameters]	()
[Return value]	254

[Function]	kcs_draft.highlight_off
[Parameters]	(0,)
[Return value]	None

[Function]	kcs_draft.element_delete
[Parameters]	(handle: 64553,)
[Return value]	None

[Function]	kcs_draft.element_delete
[Parameters]	(handle: 64640,)
[Return value]	None

[Function]	kcs_draft.element_delete
[Parameters]	(handle: 64293,)
[Return value]	None

[Function]	kcs_draft.element_delete
[Parameters]	(handle: 64466,)
[Return value]	None

[Function]	kcs_draft.element_delete
[Parameters]	(handle: 64379,)
[Return value]	None

[Function]	kcs_draft.element_delete
[Parameters]	(handle: 64003,)
[Return value]	None

[Function]	kcs_draft.element_delete
[Parameters]	(handle: 63829,)
[Return value]	None

[Function]	kcs_draft.element_delete
[Parameters]	(handle: 63916,)
[Return value]	None

[Function]	kcs_draft.dwg_purge
[Parameters]	()
[Return value]	3

[Function]	kcs_draft.dwg_repaint
[Parameters]	()
[Return value]	None

[Function]	kcs_ui.app_window_refresh
[Parameters]	()
[Return value]	None

[Function]	kcs_draft.default_value_set
[Parameters]	('STRUCT_NAME                             : MOD-NAME',)
[Return value]	None
